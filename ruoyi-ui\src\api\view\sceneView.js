import request from '@/utils/request'

// 获取行业场景视图配置信息（参数方式）
export function getSceneViewConfig(params) {
  return request({
    url: '/sceneConfig/detail',
    method: 'get',
    params
  })
}

// 获取主题列表
export function getThemeList(params = {}) {
  return request({
    url: '/sceneConfig/theme/list',
    method: 'get',
    params: {
      page: 1,
      limit: 20,
      ...params
    }
  })
}

export function sceneViewUpd(data) {
  return request({
    url: '/sceneConfig/upd',
    method: 'post',
    data
  })
}

export function sceneFileInfoDel(data) {
  return request({
    url: '/sceneConfig/sceneFileInfo/del',
    method: 'post',
    data
  })
}


export function backgroundFileDel(data) {
  return request({
    url: '/sceneConfig/background/file/del',
    method: 'post',
    data
  })
}


export function fileBindDel(data) {
  return request({
    url: '/sceneConfig/file/bind/del',
    method: 'post',
    data
  })
}

export function synchronizationFile(data) {
  return request({
    url: '/sceneConfig/synchronization/file',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传文件接口
export function uploadSceneFile(fileOrFormData) {
  let formData
  if (fileOrFormData instanceof FormData) {
    formData = fileOrFormData
  } else {
    formData = new FormData()
    formData.append('file', fileOrFormData)
  }
  
  return request({
    url: '/sceneConfig/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}


