(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-447a10ac","chunk-2d0dece3"],{"1dac":function(e,t,i){"use strict";i("95c3")},"4b51":function(e,t,i){"use strict";i.r(t);var o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-card",{staticClass:"mini-block",attrs:{shadow:"never"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("span",[e._v(e._s(e.node.name))]),i("el-switch",{staticStyle:{"margin-left":"16px"},attrs:{"active-value":"0","inactive-value":"1"},on:{change:e.onStatusChange},model:{value:e.node.status,callback:function(t){e.$set(e.node,"status",t)},expression:"node.status"}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"0"===e.node.status,expression:"node.status === '0'"}],staticClass:"sub-category-body"},[i("el-form",{attrs:{"label-width":"120px"}},[i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"场景编码"}},[i("el-input",{attrs:{disabled:"",type:"text"},model:{value:e.node.code,callback:function(t){e.$set(e.node,"code",t)},expression:"node.code"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"场景名称"}},[i("el-input",{attrs:{disabled:"",type:"text"},model:{value:e.node.name,callback:function(t){e.$set(e.node,"name",t)},expression:"node.name"}})],1)],1)],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"坐标X",required:""}},[i("el-input",{attrs:{placeholder:"请输入坐标X（百分比）",type:"text"},model:{value:e.node.x,callback:function(t){e.$set(e.node,"x",t)},expression:"node.x"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"坐标Y",required:""}},[i("el-input",{attrs:{placeholder:"请输入坐标Y（百分比）",type:"text"},model:{value:e.node.y,callback:function(t){e.$set(e.node,"y",t)},expression:"node.y"}})],1)],1)],1),i("el-form-item",{attrs:{label:"场景类型",required:""}},[i("el-select",{attrs:{placeholder:"请选择类型"},model:{value:e.node.type,callback:function(t){e.$set(e.node,"type",t)},expression:"node.type"}},[i("el-option",{attrs:{label:"默认",value:"1"}}),i("el-option",{attrs:{label:"AI",value:"2"}}),i("el-option",{attrs:{label:"三化",value:"3"}}),i("el-option",{attrs:{label:"AI+三化",value:"4"}})],1)],1),e.hasChildren?i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"是否展开下级",required:""}},[i("el-radio-group",{model:{value:e.node.isUnfold,callback:function(t){e.$set(e.node,"isUnfold",t)},expression:"node.isUnfold"}},[i("el-radio",{attrs:{label:"0"}},[e._v("展示")]),i("el-radio",{attrs:{label:"1"}},[e._v("关闭")])],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"下级展示位置",required:""}},[i("el-radio-group",{model:{value:e.node.displayLocation,callback:function(t){e.$set(e.node,"displayLocation",t)},expression:"node.displayLocation"}},[i("el-radio",{attrs:{label:"0"}},[e._v("默认")]),i("el-radio",{attrs:{label:"1"}},[e._v("右下")])],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"下级菜单分类",required:""}},[i("el-radio-group",{model:{value:e.node.treeClassification,callback:function(t){e.$set(e.node,"treeClassification",t)},expression:"node.treeClassification"}},[i("el-radio",{attrs:{label:"1"}},[e._v("传统")]),i("el-radio",{attrs:{label:"2"}},[e._v("5G")]),i("el-radio",{attrs:{label:"3"}},[e._v("全部")])],1)],1)],1)],1):e._e()],1),i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:12}},[i("el-card",{staticClass:"mini-block",attrs:{shadow:"never"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("span",[e._v("介绍视频")]),i("el-switch",{staticStyle:{float:"right"},attrs:{"active-value":"0","inactive-value":"1"},on:{change:e.onIntroduceVideoStatusChange},model:{value:e.node.introduceVideoVo.status,callback:function(t){e.$set(e.node.introduceVideoVo,"status",t)},expression:"node.introduceVideoVo.status"}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"0"===e.node.introduceVideoVo.status,expression:"node.introduceVideoVo.status === '0'"}]},[i("el-form",{attrs:{"label-width":"120px"}},[i("el-form-item",{attrs:{label:"介绍视频首帧"}},[i("el-upload",{staticClass:"upload image-upload",attrs:{action:"#","show-file-list":!1,"list-type":"picture-card",accept:"image/*","before-upload":function(t){return e.beforeUploadSceneConfigImg(t,e.node,"introduceVideoVo","backgroundImgFileUrl",null,null)},"http-request":function(){}}},[e.node.introduceVideoVo.backgroundImgFileUrl?i("div",{staticClass:"image-preview-container"},[i("img",{staticClass:"upload-image",attrs:{src:e.node.introduceVideoVo.backgroundImgFileUrl}}),i("div",{staticClass:"image-overlay"},[i("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览"},on:{click:function(t){return t.stopPropagation(),e.previewImage(e.node.introduceVideoVo.backgroundImgFileUrl)}}}),i("i",{staticClass:"el-icon-delete delete-icon",attrs:{title:"删除"},on:{click:function(t){return t.stopPropagation(),e.deleteIntroduceVideoImg(t)}}})])]):i("i",{staticClass:"el-icon-plus"})])],1),i("el-form-item",{attrs:{label:"介绍视频"}},[i("div",{staticStyle:{"margin-bottom":"8px"}},[i("el-radio-group",{attrs:{value:e.uploadModes.introduceVideo||"upload",size:"small"},on:{input:function(t){return e.setUploadMode("introduceVideo",t)}}},[i("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),i("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===(e.uploadModes.introduceVideo||"upload")?i("el-upload",{attrs:{action:"#","show-file-list":!0,"file-list":e.getIntroduceVideoFileList(),accept:".mp4","before-upload":function(t){return e.beforeUploadSceneConfigFile(t,e.node,"introduceVideoVo","backgroundFileUrl",null,null)},"http-request":function(){},"on-remove":e.handleRemoveIntroduceVideo}},[i("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传mp4文件")])],1):i("el-input",{attrs:{placeholder:"请输入视频链接"},on:{input:e.handleIntroduceVideoUrlInput},model:{value:e.node.introduceVideoVo.backgroundFileUrl,callback:function(t){e.$set(e.node.introduceVideoVo,"backgroundFileUrl",t)},expression:"node.introduceVideoVo.backgroundFileUrl"}})],1)],1)],1)])],1),i("el-col",{attrs:{span:12}},[i("el-card",{staticClass:"mini-block",attrs:{shadow:"never"}},[i("div",{attrs:{slot:"header"},slot:"header"},[i("span",[e._v("成本预估")]),i("el-switch",{staticStyle:{float:"right"},attrs:{"active-value":"0","inactive-value":"1"},model:{value:e.node.costEstimate.status,callback:function(t){e.$set(e.node.costEstimate,"status",t)},expression:"node.costEstimate.status"}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"0"===e.node.costEstimate.status,expression:"node.costEstimate.status === '0'"}]},[i("el-form",{attrs:{"label-width":"120px"}},[i("el-form-item",{attrs:{label:"大标题",required:""}},[i("el-input",{attrs:{placeholder:"请输入大标题",type:"text"},model:{value:e.node.costEstimate.title,callback:function(t){e.$set(e.node.costEstimate,"title",t)},expression:"node.costEstimate.title"}})],1),i("el-form-item",{attrs:{label:"内容",required:""}},[e._l(e.node.costEstimate.contents,(function(t,o){return i("div",{key:"costEstimate-content-"+o,staticStyle:{display:"flex","align-items":"center","margin-bottom":"8px"}},[i("el-input",{staticStyle:{width:"calc(100% - 40px)","margin-right":"8px"},attrs:{placeholder:"请输入内容",type:"text"},model:{value:e.node.costEstimate.contents[o],callback:function(t){e.$set(e.node.costEstimate.contents,o,t)},expression:"node.costEstimate.contents[cidx]"}}),i("el-button",{attrs:{icon:"el-icon-delete",circle:"",size:"mini"},on:{click:function(t){return e.removeCostContent(o)}}})],1)})),i("el-button",{attrs:{type:"primary",plain:""},on:{click:e.addCostContent}},[e._v("增加内容")])],2)],1)],1)])],1)],1),i("el-card",{staticClass:"mini-block",attrs:{shadow:"never"}},[i("div",{attrs:{slot:"header"},slot:"header"},[e._v("传统")]),i("el-form",{attrs:{"label-width":"120px"}},[i("el-form-item",{attrs:{label:"名称",required:""}},[i("el-input",{attrs:{placeholder:"请输入名称",type:"text"},model:{value:e.node.tradition.name,callback:function(t){e.$set(e.node.tradition,"name",t)},expression:"node.tradition.name"}})],1),i("el-form-item",{attrs:{label:"全景图唯一标识",required:""}},[i("el-input",{attrs:{placeholder:"请输入全景图唯一标识（仅英文）",type:"text"},on:{input:function(t){return e.validateXmlKey(t,"tradition")}},model:{value:e.node.tradition.panoramicViewXmlKey,callback:function(t){e.$set(e.node.tradition,"panoramicViewXmlKey",t)},expression:"node.tradition.panoramicViewXmlKey"}})],1),i("div",{staticClass:"mini-block",staticStyle:{"margin-bottom":"0"}},[i("div",{staticStyle:{"font-weight":"bold","margin-bottom":"8px"}},[e._v("背景资源")]),e._l(e.node.tradition.backgroundResources,(function(t,o){return i("div",{key:o,staticClass:"background-resource-item"},[i("div",{staticClass:"resource-header"},[i("span",{staticClass:"resource-title"},[e._v("背景资源 "+e._s(o+1))]),i("el-button",{attrs:{type:"danger",size:"mini",plain:""},on:{click:function(t){return e.removeBackgroundResource("tradition",o)}}},[e._v("删除")])],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"坐标组",required:""}},[e._l(t.coordinates,(function(t,n){return i("div",{key:n,staticClass:"coordinate-group"},[i("div",{staticClass:"coordinate-header"},[i("span",[e._v("坐标组 "+e._s(n+1))]),i("el-button",{attrs:{type:"danger",size:"mini",plain:""},on:{click:function(t){return e.removeCoordinate("tradition",o,n)}}},[e._v(" 删除 ")])],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"X坐标"}},[i("el-input",{attrs:{placeholder:"请输入X坐标",type:"text"},model:{value:t.x,callback:function(i){e.$set(t,"x",i)},expression:"coord.x"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"Y坐标"}},[i("el-input",{attrs:{placeholder:"请输入Y坐标",type:"text"},model:{value:t.y,callback:function(i){e.$set(t,"y",i)},expression:"coord.y"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"绑定场景"}},[i("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.sceneTreeOptions,props:e.sceneCascaderProps,filterable:"","check-strictly":"",placeholder:"选择场景"},on:{change:function(t){return e.handleSceneCascaderChange(t,"tradition",o,n)}},model:{value:t.sceneId,callback:function(i){e.$set(t,"sceneId",i)},expression:"coord.sceneId"}})],1)],1)],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"宽度"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{value:t.wide,min:0,max:999,placeholder:"请输入宽度"},on:{input:function(i){return e.handleCoordNumberInput(i,t,"wide")}}})],1)],1),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"高度"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{value:t.high,min:0,max:999,placeholder:"请输入高度"},on:{input:function(i){return e.handleCoordNumberInput(i,t,"high")}}})],1)],1),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"全景xml标签"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入全景xml标签"},model:{value:t.xmlKey,callback:function(i){e.$set(t,"xmlKey",i)},expression:"coord.xmlKey"}})],1)],1)],1)],1)})),i("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(t){return e.addCoordinate("tradition",o)}}},[e._v(" 增加坐标组 ")])],2)],1)],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"背景图片首帧"}},[i("el-upload",{staticClass:"upload image-upload",attrs:{action:"#","show-file-list":!1,"list-type":"picture-card",accept:"image/*","before-upload":function(t){return e.beforeUploadSceneConfigImg(t,e.node,"tradition","backgroundResources",o,"bgImg")},"http-request":function(){}}},[t.bgImg?i("div",{staticClass:"image-preview-container"},[i("img",{staticClass:"upload-image",attrs:{src:t.bgImg}}),i("div",{staticClass:"image-overlay"},[i("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览"},on:{click:function(i){return i.stopPropagation(),e.previewImage(t.bgImg)}}}),i("i",{staticClass:"el-icon-delete delete-icon",attrs:{title:"删除"},on:{click:function(t){return t.stopPropagation(),e.deleteBackgroundImg("tradition",o)}}})])]):i("i",{staticClass:"el-icon-plus"})])],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"背景文件",required:""}},[i("div",{staticStyle:{"margin-bottom":"8px"}},[i("el-radio-group",{attrs:{size:"small"},on:{input:function(t){return e.setUploadMode("tradition",o,t)}},model:{value:e.uploadModes.tradition[o]||"upload",callback:function(t){e.$set(e.uploadModes,"tradition[idx] || 'upload'",t)},expression:"uploadModes.tradition[idx] || 'upload'"}},[i("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),i("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===(e.uploadModes.tradition[o]||"upload")?i("el-upload",{attrs:{action:"#","show-file-list":!0,"file-list":e.getFileList("tradition",o),"before-upload":function(t){return e.beforeUploadSceneConfigFile(t,e.node,"tradition","backgroundResources",o,"bgFile")},"http-request":function(){},"on-remove":function(t,i){return e.handleRemoveBackgroundFile(e.node,"tradition",o)}}},[i("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")])],1):i("el-input",{attrs:{placeholder:"请输入文件链接"},on:{input:function(t){return e.handleUrlInput("tradition",o,t)}},model:{value:t.bgFile,callback:function(i){e.$set(t,"bgFile",i)},expression:"resource.bgFile"}})],1)],1)],1)],1)})),i("el-form-item",[i("el-button",{attrs:{type:"primary",plain:""},on:{click:function(t){return e.addBackgroundResource("tradition")}}},[e._v("增加背景资源")])],1)],2),i("div",{staticClass:"mini-block",staticStyle:{"margin-bottom":"0"}},[i("div",{staticStyle:{"font-weight":"bold","margin-bottom":"8px"}},[e._v("痛点价值")]),e._l(e.node.tradition.painPoints,(function(t,o){return i("div",{key:"tradition-"+o,staticClass:"pain-point-block"},[i("div",{staticClass:"resource-header"},[i("span",{staticClass:"resource-title"},[e._v("痛点价值 "+e._s(o+1))]),i("el-button",{attrs:{type:"danger",size:"mini",plain:""},on:{click:function(t){return e.removePainPoint("tradition",o)}}},[e._v("删除")])],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:16}},[i("el-form-item",{attrs:{label:"大标题",required:""}},[i("el-input",{attrs:{placeholder:"请输入大标题",type:"text"},model:{value:t.title,callback:function(i){e.$set(t,"title",i)},expression:"point.title"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"展示时间",required:""}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0},model:{value:t.showTime,callback:function(i){e.$set(t,"showTime",i)},expression:"point.showTime"}})],1)],1)],1),i("el-form-item",{attrs:{label:"内容",required:""}},[i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:16}},[e._l(t.contents,(function(n,a){return i("div",{key:"tradition-content-"+o+"-"+a,staticStyle:{display:"flex","align-items":"center","margin-bottom":"8px"}},[i("el-input",{staticStyle:{width:"calc(100% - 40px)","margin-right":"8px"},attrs:{placeholder:"请输入内容",type:"text"},model:{value:t.contents[a],callback:function(i){e.$set(t.contents,a,i)},expression:"point.contents[cidx]"}}),i("el-button",{attrs:{icon:"el-icon-delete",circle:"",size:"mini"},on:{click:function(t){return e.removePainContent("tradition",o,a)}}})],1)})),i("el-button",{attrs:{type:"primary",plain:""},on:{click:function(t){return e.addPainContent("tradition",o)}}},[e._v("增加内容")])],2)],1)],1)],1)})),i("el-form-item",[i("el-button",{attrs:{type:"primary",plain:""},on:{click:function(t){return e.addPainPoint("tradition")}}},[e._v("增加痛点价值")])],1)],2)],1)],1),i("el-card",{staticClass:"mini-block",attrs:{shadow:"never"}},[i("div",{attrs:{slot:"header"},slot:"header"},[e._v("5G智慧")]),i("el-form",{attrs:{"label-width":"120px"}},[i("el-form-item",{attrs:{label:"名称",required:""}},[i("el-input",{attrs:{placeholder:"请输入名称",type:"text"},model:{value:e.node.wisdom5g.name,callback:function(t){e.$set(e.node.wisdom5g,"name",t)},expression:"node.wisdom5g.name"}})],1),i("el-form-item",{attrs:{label:"全景图唯一标识",required:""}},[i("el-input",{attrs:{placeholder:"请输入全景图唯一标识（仅英文）",type:"text"},on:{input:function(t){return e.validateXmlKey(t,"wisdom5g")}},model:{value:e.node.wisdom5g.panoramicViewXmlKey,callback:function(t){e.$set(e.node.wisdom5g,"panoramicViewXmlKey",t)},expression:"node.wisdom5g.panoramicViewXmlKey"}})],1),i("div",{staticClass:"mini-block",staticStyle:{"margin-bottom":"0"}},[i("div",{staticStyle:{"font-weight":"bold","margin-bottom":"8px"}},[e._v("背景资源")]),e._l(e.node.wisdom5g.backgroundResources,(function(t,o){return i("div",{key:"wisdom5g-bg-"+o,staticClass:"background-resource-item"},[i("div",{staticClass:"resource-header"},[i("span",{staticClass:"resource-title"},[e._v("背景资源 "+e._s(o+1))]),i("el-button",{attrs:{type:"danger",size:"mini",plain:""},on:{click:function(t){return e.removeBackgroundResource("wisdom5g",o)}}},[e._v("删除")])],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"坐标组",required:""}},[e._l(t.coordinates,(function(t,n){return i("div",{key:n,staticClass:"coordinate-group"},[i("div",{staticClass:"coordinate-header"},[i("span",[e._v("坐标组 "+e._s(n+1))]),i("el-button",{attrs:{type:"danger",size:"mini",plain:""},on:{click:function(t){return e.removeCoordinate("wisdom5g",o,n)}}},[e._v(" 删除 ")])],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"X坐标"}},[i("el-input",{attrs:{placeholder:"请输入X坐标",type:"text"},model:{value:t.x,callback:function(i){e.$set(t,"x",i)},expression:"coord.x"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"Y坐标"}},[i("el-input",{attrs:{placeholder:"请输入Y坐标",type:"text"},model:{value:t.y,callback:function(i){e.$set(t,"y",i)},expression:"coord.y"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"绑定场景"}},[i("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.sceneTreeOptions,props:e.sceneCascaderProps,filterable:"","check-strictly":"",placeholder:"选择场景"},on:{change:function(t){return e.handleSceneCascaderChange(t,"wisdom5g",o,n)}},model:{value:t.sceneId,callback:function(i){e.$set(t,"sceneId",i)},expression:"coord.sceneId"}})],1)],1)],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"宽度"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{value:t.wide,min:0,max:999,placeholder:"请输入宽度"},on:{input:function(i){return e.handleCoordNumberInput(i,t,"wide")}}})],1)],1),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"高度"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{value:t.high,min:0,max:999,placeholder:"请输入高度"},on:{input:function(i){return e.handleCoordNumberInput(i,t,"high")}}})],1)],1),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"全景xml标签"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入全景xml标签"},model:{value:t.xmlKey,callback:function(i){e.$set(t,"xmlKey",i)},expression:"coord.xmlKey"}})],1)],1)],1)],1)})),i("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(t){return e.addCoordinate("wisdom5g",o)}}},[e._v(" 增加坐标组 ")])],2)],1)],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"背景图片首帧"}},[i("el-upload",{staticClass:"upload image-upload",attrs:{action:"#","show-file-list":!1,"list-type":"picture-card",accept:"image/*","before-upload":function(t){return e.beforeUploadSceneConfigImg(t,e.node,"wisdom5g","backgroundResources",o,"bgImg")},"http-request":function(){}}},[t.bgImg?i("div",{staticClass:"image-preview-container"},[i("img",{staticClass:"upload-image",attrs:{src:t.bgImg}}),i("div",{staticClass:"image-overlay"},[i("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览"},on:{click:function(i){return i.stopPropagation(),e.previewImage(t.bgImg)}}}),i("i",{staticClass:"el-icon-delete delete-icon",attrs:{title:"删除"},on:{click:function(t){return t.stopPropagation(),e.deleteBackgroundImg("wisdom5g",o)}}})])]):i("i",{staticClass:"el-icon-plus"})])],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"背景文件",required:""}},[i("div",{staticStyle:{"margin-bottom":"8px"}},[i("el-radio-group",{attrs:{size:"small"},on:{input:function(t){return e.setUploadMode("wisdom5g",o,t)}},model:{value:e.uploadModes.wisdom5g[o]||"upload",callback:function(t){e.$set(e.uploadModes,"wisdom5g[idx] || 'upload'",t)},expression:"uploadModes.wisdom5g[idx] || 'upload'"}},[i("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),i("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===(e.uploadModes.wisdom5g[o]||"upload")?i("el-upload",{attrs:{action:"#","show-file-list":!0,"file-list":e.getFileList("wisdom5g",o),"before-upload":function(t){return e.beforeUploadSceneConfigFile(t,e.node,"wisdom5g","backgroundResources",o,"bgFile")},"http-request":function(){},"on-remove":function(t,i){return e.handleRemoveBackgroundFile(e.node,"wisdom5g",o)}}},[i("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")])],1):i("el-input",{attrs:{placeholder:"请输入文件链接"},on:{input:function(t){return e.handleUrlInput("wisdom5g",o,t)}},model:{value:t.bgFile,callback:function(i){e.$set(t,"bgFile",i)},expression:"resource.bgFile"}})],1)],1)],1)],1)})),i("el-form-item",[i("el-button",{attrs:{type:"primary",plain:""},on:{click:function(t){return e.addBackgroundResource("wisdom5g")}}},[e._v("增加背景资源")])],1)],2),i("div",{staticClass:"mini-block",staticStyle:{"margin-bottom":"0"}},[i("div",{staticStyle:{"font-weight":"bold","margin-bottom":"8px"}},[e._v("痛点价值")]),e._l(e.node.wisdom5g.painPoints,(function(t,o){return i("div",{key:"wisdom5g-"+o,staticClass:"pain-point-block"},[i("div",{staticClass:"resource-header"},[i("span",{staticClass:"resource-title"},[e._v("痛点价值 "+e._s(o+1))]),i("el-button",{attrs:{type:"danger",size:"mini",plain:""},on:{click:function(t){return e.removePainPoint("wisdom5g",o)}}},[e._v("删除")])],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:16}},[i("el-form-item",{attrs:{label:"大标题",required:""}},[i("el-input",{attrs:{placeholder:"请输入大标题",type:"text"},model:{value:t.title,callback:function(i){e.$set(t,"title",i)},expression:"point.title"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"展示时间",required:""}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0},model:{value:t.showTime,callback:function(i){e.$set(t,"showTime",i)},expression:"point.showTime"}})],1)],1)],1),i("el-form-item",{attrs:{label:"内容",required:""}},[i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:16}},[e._l(t.contents,(function(n,a){return i("div",{key:"wisdom5g-content-"+o+"-"+a,staticStyle:{display:"flex","align-items":"center","margin-bottom":"8px"}},[i("el-input",{staticStyle:{width:"calc(100% - 40px)","margin-right":"8px"},attrs:{placeholder:"请输入内容",type:"text"},model:{value:t.contents[a],callback:function(i){e.$set(t.contents,a,i)},expression:"point.contents[cidx]"}}),i("el-button",{attrs:{icon:"el-icon-delete",circle:"",size:"mini"},on:{click:function(t){return e.removePainContent("wisdom5g",o,a)}}})],1)})),i("el-button",{attrs:{type:"primary",plain:""},on:{click:function(t){return e.addPainContent("wisdom5g",o)}}},[e._v("增加内容")])],2)],1)],1)],1)})),i("el-form-item",[i("el-button",{attrs:{type:"primary",plain:""},on:{click:function(t){return e.addPainPoint("wisdom5g")}}},[e._v("增加痛点价值")])],1)],2)],1)],1)],1),i("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览",width:"60%","append-to-body":""},on:{"update:visible":function(t){e.previewVisible=t}}},[i("div",{staticClass:"preview-container"},[i("img",{staticClass:"preview-image",attrs:{src:e.previewImageUrl}})])])],1)},n=[],a=i("c7eb"),s=i("1da1"),r=i("b85c"),l=(i("a15b"),i("d81d"),i("14d9"),i("a434"),i("e9c4"),i("b64b"),i("d3b7"),i("ac1f"),i("5319"),i("2ca0"),i("498a"),i("159b"),i("86e4")),c={name:"SceneConfigNode",props:{node:{type:Object,required:!0},rootTree:{type:Array,default:function(){return[]}},sceneTreeOptions:{type:Array,default:function(){return[]}},leftTreeIndustryCode:{type:String,default:""}},watch:{"node.status":function(e){"0"===e&&this.findAndOpenParent(this.node.id,this.rootTree)},node:{handler:function(e,t){var i=this;e&&e!==t&&this.$nextTick((function(){i.initNodeData(),e.introduceVideoVo&&e.introduceVideoVo.hasOwnProperty("status")?i.updateIntroduceVideoFileList():e.introduceVideoVo||i.initIntroduceVideo(),i.initFileLists()}))},immediate:!0,deep:!0},"node.introduceVideoVo":{handler:function(e){e&&void 0!==e.status&&this.updateIntroduceVideoFileList()},immediate:!0,deep:!0}},data:function(){return{fileLists:{tradition:{},wisdom5g:{},introduceVideo:[]},uploadModes:{tradition:{},wisdom5g:{},introduceVideo:"upload"},introduceVideoFileList:[],previewVisible:!1,previewImageUrl:"",sceneCascaderProps:{label:"sceneName",value:"id",children:"children",emitPath:!1,checkStrictly:!0}}},created:function(){this.initIntroduceVideo(),this.initNodeData()},mounted:function(){this.initData(),this.initFileLists()},methods:{initData:function(){if(this.node.wisdom5g){var e=JSON.parse(JSON.stringify(this.node.wisdom5g));this.$set(this.node,"wisdom5g",e),this.node.wisdom5g.backgroundResources&&this.node.wisdom5g.backgroundResources.forEach((function(e){e.backgroundImgFileUrl&&!e.bgImg&&(e.bgImg=e.backgroundImgFileUrl),e.backgroundFileUrl&&!e.bgFile&&(e.bgFile=e.backgroundFileUrl),e.tag&&!e.label&&(e.label=e.tag)}))}else this.$set(this.node,"wisdom5g",{name:"",panoramicViewXmlKey:"",backgroundResources:[],painPoints:[]});if(this.node.tradition){var t=JSON.parse(JSON.stringify(this.node.tradition));this.$set(this.node,"tradition",t)}else this.$set(this.node,"tradition",{name:"",panoramicViewXmlKey:"",backgroundResources:[],painPoints:[]})},initFileLists:function(){var e=this;this.fileLists={tradition:{},wisdom5g:{},introduceVideo:[]},this.node.tradition&&this.node.tradition.backgroundResources&&this.node.tradition.backgroundResources.forEach((function(t,i){if(t.bgFile&&t.bgFile.trim()){var o=t.bgFile.split("/").pop();e.$set(e.fileLists.tradition,i,[{name:o,url:t.bgFile,uid:Date.now()+i}])}else e.$set(e.fileLists.tradition,i,[])})),this.node.wisdom5g&&this.node.wisdom5g.backgroundResources&&this.node.wisdom5g.backgroundResources.forEach((function(t,i){if(t.bgFile&&t.bgFile.trim()){var o=t.bgFile.split("/").pop();e.$set(e.fileLists.wisdom5g,i,[{name:o,url:t.bgFile,uid:Date.now()+i+1e3}])}else e.$set(e.fileLists.wisdom5g,i,[])}))},initIntroduceVideo:function(){this.node.introduceVideoVo||this.$set(this.node,"introduceVideoVo",{id:"",type:"",viewInfoId:"",status:"0",backgroundImgFileUrl:"",backgroundFileUrl:""}),this.updateIntroduceVideoFileList()},initNodeData:function(){void 0!==this.node.isUnfold&&null!==this.node.isUnfold&&""!==this.node.isUnfold||this.$set(this.node,"isUnfold","1"),void 0!==this.node.displayLocation&&null!==this.node.displayLocation&&""!==this.node.displayLocation||this.$set(this.node,"displayLocation","0"),void 0!==this.node.treeClassification&&null!==this.node.treeClassification&&""!==this.node.treeClassification||this.$set(this.node,"treeClassification","3"),this.node.tradition||this.$set(this.node,"tradition",{name:"",panoramicViewXmlKey:"",backgroundResources:[],painPoints:[]}),this.node.wisdom5g?this.node.wisdom5g.backgroundResources&&this.node.wisdom5g.backgroundResources.forEach((function(e){e.backgroundImgFileUrl&&!e.bgImg&&(e.bgImg=e.backgroundImgFileUrl),e.backgroundFileUrl&&!e.bgFile&&(e.bgFile=e.backgroundFileUrl),e.tag&&!e.label&&(e.label=e.tag)})):this.$set(this.node,"wisdom5g",{name:"",panoramicViewXmlKey:"",backgroundResources:[],painPoints:[]}),this.node.costEstimate||this.$set(this.node,"costEstimate",{status:"0",title:"",contents:[]}),this.node.costEstimate&&void 0===this.node.costEstimate.status&&this.$set(this.node.costEstimate,"status","0")},onStatusChange:function(e){if("0"===e){var t=this.parent;while(t)"0"!==t.status&&(t.status="0"),t=t.parent}else{var i=function e(t){t.children&&t.children.length&&t.children.forEach((function(t){t.status="1",e(t)}))};i(this.node)}},findAndOpenParent:function(e,t){function i(t,o){var n,a=Object(r["a"])(t);try{for(a.s();!(n=a.n()).done;){var s=n.value;if(s.id===e)return o&&(o.status="0"),!0;if(s.children&&s.children.length&&i(s.children,s))return o&&(o.status="0"),!0}}catch(l){a.e(l)}finally{a.f()}return!1}i(t,null)},addBackgroundResource:function(e){this.node[e].backgroundResources||this.$set(this.node[e],"backgroundResources",[]);var t=this.node[e].backgroundResources.length,i={id:null,tag:"",status:"",type:"",viewInfoId:"",bgImg:"",bgFile:"",coordinates:[]},o=JSON.parse(JSON.stringify(i));this.node[e].backgroundResources.push(o),this.$set(this.fileLists[e],t,[]),console.log("添加新背景资源:",o)},removeBackgroundResource:function(e,t){var i=this;return Object(s["a"])(Object(a["a"])().mark((function o(){var n;return Object(a["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:n=i.node[e].backgroundResources[t],i.$confirm("确定删除此背景资源吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(s["a"])(Object(a["a"])().mark((function o(){var s;return Object(a["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(!n.id){o.next=17;break}return o.prev=1,i.$modal.loading("正在删除背景资源，请稍候..."),o.next=5,Object(l["a"])({id:n.id});case 5:s=o.sent,0===s.code?(i.node[e].backgroundResources.splice(t,1),i.$message.success("删除成功")):i.$message.error(s.msg||"删除失败"),o.next=12;break;case 9:o.prev=9,o.t0=o["catch"](1),i.$message.error("删除失败");case 12:return o.prev=12,i.$modal.closeLoading(),o.finish(12);case 15:o.next=19;break;case 17:i.node[e].backgroundResources.splice(t,1),i.$message.success("删除成功");case 19:case"end":return o.stop()}}),o,null,[[1,9,12,15]])})))).catch((function(){}));case 2:case"end":return o.stop()}}),o)})))()},addCoordinate:function(e,t){var i=this.node[e].backgroundResources[t];i.coordinates||this.$set(i,"coordinates",[]),i.coordinates.push({id:0,fileId:0,x:"",y:"",wide:"",high:"",sceneId:"",sceneCode:"",xmlKey:""})},removeCoordinate:function(e,t,i){var o=this;return Object(s["a"])(Object(a["a"])().mark((function n(){var r;return Object(a["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:r=o.node[e].backgroundResources[t].coordinates[i],o.$confirm("确定删除此坐标组吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(s["a"])(Object(a["a"])().mark((function n(){var s;return Object(a["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!r.id){n.next=17;break}return n.prev=1,o.$modal.loading("正在删除坐标组，请稍候..."),n.next=5,Object(l["b"])({id:r.id});case 5:s=n.sent,0===s.code?(o.node[e].backgroundResources[t].coordinates.splice(i,1),o.$message.success("删除成功")):o.$message.error(s.msg||"删除失败"),n.next=12;break;case 9:n.prev=9,n.t0=n["catch"](1),o.$message.error("删除失败");case 12:return n.prev=12,o.$modal.closeLoading(),n.finish(12);case 15:n.next=19;break;case 17:o.node[e].backgroundResources[t].coordinates.splice(i,1),o.$message.success("删除成功");case 19:case"end":return n.stop()}}),n,null,[[1,9,12,15]])})))).catch((function(){}));case 2:case"end":return n.stop()}}),n)})))()},handleSceneCascaderChange:function(e,t,i,o){var n=this.findSceneById(this.sceneTreeOptions,e);if(n){var a=this.node[t].backgroundResources[i].coordinates[o];a.sceneCode=n.sceneCode||"",console.log("选择的场景:",n,"设置sceneCode:",a.sceneCode)}},findSceneById:function(e,t){if(!e||!Array.isArray(e))return null;var i,o=Object(r["a"])(e);try{for(o.s();!(i=o.n()).done;){var n=i.value;if(n.id===t)return n;if(n.children&&n.children.length){var a=this.findSceneById(n.children,t);if(a)return a}}}catch(s){o.e(s)}finally{o.f()}return null},formatCoordinatesForSubmit:function(e){if(!e||!Array.isArray(e))return{clickX:"",clickY:""};var t=e.map((function(e){return e.x||"0"})).join(","),i=e.map((function(e){return e.y||"0"})).join(",");return{clickX:t,clickY:i}},beforeUploadSceneConfigImg:function(e,t,i,o,n,r){var c=this;return Object(s["a"])(Object(a["a"])().mark((function s(){var d,u,p;return Object(a["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.type.startsWith("image/")){a.next=3;break}return c.$message.error("只能上传图片文件！"),a.abrupt("return",!1);case 3:return a.prev=3,c.$modal.loading("正在上传图片，请稍候..."),d=new FormData,d.append("file",e),d.append("industryCode",c.leftTreeIndustryCode),d.append("sceneCode",t.code),a.next=11,Object(l["h"])(d);case 11:u=a.sent,0===u.code&&u.data?(p=u.data.fileUrl,"number"===typeof n&&r?(c.$set(t[i][o][n],r,p),console.log("上传成功，设置图片URL:",p),console.log("当前resource:",t[i][o][n]),c.$message.success("上传成功")):(c.$set(t[i],o,p),c.$message.success("上传成功"))):c.$message.error(u.msg||"上传失败"),a.next=19;break;case 15:a.prev=15,a.t0=a["catch"](3),console.error("上传错误:",a.t0),c.$message.error("上传失败");case 19:return a.prev=19,c.$modal.closeLoading(),a.finish(19);case 22:return a.abrupt("return",!1);case 23:case"end":return a.stop()}}),s,null,[[3,15,19,22]])})))()},beforeUploadSceneConfigFile:function(e,t,i,o,n,r){var c=this;return Object(s["a"])(Object(a["a"])().mark((function s(){var d,u,p;return Object(a["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,c.$modal.loading("正在上传文件，请稍候..."),d=new FormData,d.append("file",e),d.append("industryCode",c.leftTreeIndustryCode),d.append("sceneCode",t.code),a.next=8,Object(l["h"])(d);case 8:u=a.sent,0===u.code&&u.data?"number"===typeof n&&r?(c.$set(t[i][o][n],r,u.data.fileUrl),p=u.data.fileUrl.split("/").pop(),c.$set(c.fileLists[i],n,[{name:p,url:u.data.fileUrl,uid:Date.now()}]),"video/mp4"===e.type&&u.data.imgUrl?(c.$set(t[i][o][n],"bgImg",u.data.imgUrl),c.$message.success("上传成功，已自动生成背景图片首帧")):c.$message.success("上传成功")):(c.$set(t[i],o,u.data.fileUrl),"introduceVideoVo"===i&&"backgroundFileUrl"===o?(c.updateIntroduceVideoFileList(),"video/mp4"===e.type&&u.data.imgUrl?(c.$set(t[i],"backgroundImgFileUrl",u.data.imgUrl),c.$message.success("上传成功，已自动生成介绍视频首帧")):c.$message.success("上传成功")):c.$message.success("上传成功")):c.$message.error(u.msg||"上传失败"),a.next=15;break;case 12:a.prev=12,a.t0=a["catch"](0),c.$message.error("上传失败");case 15:return a.prev=15,c.$modal.closeLoading(),a.finish(15);case 18:return a.abrupt("return",!1);case 19:case"end":return a.stop()}}),s,null,[[0,12,15,18]])})))()},addPainPoint:function(e){this.node[e].painPoints=this.node[e].painPoints||[],this.node[e].painPoints.push({title:"",contents:[""],showTime:""})},removePainPoint:function(e,t){this.node[e].painPoints.splice(t,1)},addPainContent:function(e,t){this.node[e].painPoints[t].contents.push("")},removePainContent:function(e,t,i){this.node[e].painPoints[t].contents.splice(i,1)},addCostContent:function(){this.node.costEstimate.contents.push("")},removeCostContent:function(e){this.node.costEstimate.contents.splice(e,1)},getBackgroundFileList:function(e,t,i){var o=e[t].backgroundResources[i];if(o&&o.bgFile){var n=o.bgFile.split("/").pop();return[{name:n,url:o.bgFile,uid:Date.now()+i}]}return[]},handleRemoveBackgroundFile:function(e,t,i){var o=e[t].backgroundResources[i];o.bgFile="",o.bgImg="",this.$set(this.fileLists[t],i,[]),this.$message.success("文件已删除")},getFileList:function(e,t){var i=this.node[e]&&this.node[e].backgroundResources&&this.node[e].backgroundResources[t];if(!i||!i.bgFile||!i.bgFile.trim())return[];if(this.fileLists[e]&&this.fileLists[e][t]&&this.fileLists[e][t].length>0){var o=this.fileLists[e][t];if(o[0].url===i.bgFile)return o}if(i.bgFile&&i.bgFile.trim()){var n=i.bgFile.split("/").pop(),a=[{name:n,url:i.bgFile,uid:Date.now()}];return this.$set(this.fileLists[e],t,a),a}return[]},getUploadMode:function(e,t){if(!this.uploadModes[e][t]){var i=this.node[e].backgroundResources[t];i&&i.bgFile;this.$set(this.uploadModes[e],t,"upload")}return this.uploadModes[e][t]},setUploadMode:function(e,t,i){"string"===typeof t?this.$set(this.uploadModes,e,t):this.$set(this.uploadModes[e],t,i)},handleUrlInput:function(e,t,i){if(this.$set(this.fileLists[e],t,[]),i){var o=i.split("/").pop()||"外部链接文件";this.$set(this.fileLists[e],t,[{name:o,url:i,uid:Date.now()}])}},updateIntroduceVideoFileList:function(){if(this.node.introduceVideoVo&&this.node.introduceVideoVo.backgroundFileUrl){var e=this.node.introduceVideoVo.backgroundFileUrl.split("/").pop();this.introduceVideoFileList=[{name:e,url:this.node.introduceVideoVo.backgroundFileUrl,uid:Date.now()}]}else this.introduceVideoFileList=[]},getIntroduceVideoFileList:function(){return this.introduceVideoFileList},handleRemoveIntroduceVideo:function(){this.node.introduceVideoVo&&(this.node.introduceVideoVo.backgroundFileUrl="",this.node.introduceVideoVo.backgroundImgFileUrl="",this.$message.success("介绍视频已删除"),this.updateIntroduceVideoFileList())},onIntroduceVideoStatusChange:function(e){},previewImage:function(e){e&&(this.previewImageUrl=e,this.previewVisible=!0)},deleteIntroduceVideoImg:function(){var e=this;this.$confirm("确定删除此图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.node.introduceVideoVo.backgroundImgFileUrl="",e.$message.success("图片已删除")})).catch((function(){}))},deleteBackgroundImg:function(e,t){var i=this;this.$confirm("确定删除此图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i.node[e].backgroundResources[t].bgImg="",i.$message.success("图片已删除")})).catch((function(){}))},validateXmlKey:function(e,t){var i=e.replace(/[^a-zA-Z0-9_]/g,"");this.node[t].panoramicViewXmlKey=i},handleIntroduceVideoUrlInput:function(e){if(this.introduceVideoFileList=[],e){var t=e.split("/").pop()||"外部链接文件";this.introduceVideoFileList=[{name:t,url:e,uid:Date.now()}]}this.updateIntroduceVideoFileList()},handleCoordNumberInput:function(e,t,i){t[i]=null===e||void 0===e?"":e}},computed:{hasChildren:function(){return this.node&&this.node.children&&this.node.children.length>0}},components:{SceneConfigNode:null}},d=c,u=(i("1dac"),i("2877")),p=Object(u["a"])(d,o,n,!1,null,"2879166c",null);t["default"]=p.exports},"86e4":function(e,t,i){"use strict";i.d(t,"c",(function(){return a})),i.d(t,"d",(function(){return s})),i.d(t,"f",(function(){return r})),i.d(t,"e",(function(){return l})),i.d(t,"a",(function(){return c})),i.d(t,"b",(function(){return d})),i.d(t,"g",(function(){return u})),i.d(t,"h",(function(){return p}));var o=i("5530"),n=i("b775");function a(e){return Object(n["a"])({url:"/sceneConfig/detail",method:"get",params:e})}function s(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(n["a"])({url:"/sceneConfig/theme/list",method:"get",params:Object(o["a"])({page:1,limit:20},e)})}function r(e){return Object(n["a"])({url:"/sceneConfig/upd",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/sceneConfig/sceneFileInfo/del",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/sceneConfig/background/file/del",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/sceneConfig/file/bind/del",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/sceneConfig/synchronization/file",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function p(e){var t;return e instanceof FormData?t=e:(t=new FormData,t.append("file",e)),Object(n["a"])({url:"/sceneConfig/upload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}},"95c3":function(e,t,i){}}]);