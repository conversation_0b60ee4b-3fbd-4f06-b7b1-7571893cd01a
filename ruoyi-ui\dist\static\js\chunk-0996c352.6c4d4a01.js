(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0996c352","chunk-2d0dece3"],{1113:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"business-value-config"},[i("div",{staticClass:"config-header"},[i("span",[e._v("商业价值配置")]),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.addBusinessValue}},[i("i",{staticClass:"el-icon-plus"}),e._v(" 添加商业价值 ")])],1),i("div",{staticClass:"value-list"},[i("el-row",{attrs:{gutter:20}},e._l(e.businessValues,(function(t,a){return i("el-col",{key:a,attrs:{span:12}},[i("el-card",{staticClass:"value-item",attrs:{shadow:"hover"}},[i("div",{staticClass:"value-header",attrs:{slot:"header"},slot:"header"},[i("span",[e._v("商业价值 "+e._s(a+1))]),i("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.removeValue(a)}}})],1),i("el-form",{attrs:{model:t,"label-width":"80px",size:"small"}},[i("el-form-item",{attrs:{label:"名称",required:""}},[i("el-input",{attrs:{placeholder:"请输入商业价值名称"},on:{input:e.debouncedEmitChange},model:{value:t.tag,callback:function(i){e.$set(t,"tag",i)},expression:"value.tag"}})],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"图片"}},[i("el-upload",{key:"image-"+a+"-"+(t.id||"new"),staticClass:"image-upload",attrs:{action:"#","show-file-list":!1,"list-type":"picture-card",accept:"image/*","before-upload":function(t){return e.beforeUploadImage(t,a)},"http-request":function(){}}},[t.imageUrl?i("div",{staticClass:"image-preview-container"},[i("img",{staticClass:"upload-image",attrs:{src:t.imageUrl}}),i("div",{staticClass:"image-overlay"},[i("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览"},on:{click:function(i){return i.stopPropagation(),e.previewImage(t.imageUrl)}}}),i("i",{staticClass:"el-icon-delete delete-icon",attrs:{title:"删除"},on:{click:function(t){return t.stopPropagation(),e.deleteValueImage(a)}}})])]):i("i",{staticClass:"el-icon-plus"})]),i("div",{staticClass:"upload-tip"},[e._v("支持jpg/png，最大20MB")])],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"视频文件"}},[i("div",{staticStyle:{"margin-bottom":"8px"}},[i("el-radio-group",{attrs:{value:e.getUploadMode("video",a),size:"small"},on:{input:function(t){return e.setUploadMode("video",a,t)}}},[i("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),i("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===e.getUploadMode("video",a)?i("el-upload",{key:"video-"+a+"-"+(t.id||"new")+"-"+(t.videoUrl?"has":"empty"),attrs:{action:"#","show-file-list":!0,"file-list":t.videoFileList,accept:"video/*","before-upload":function(t){return e.beforeUploadVideo(t,a)},"http-request":function(){},"on-remove":function(t){return e.handleRemoveVideo(t,a)}}},[i("el-button",{attrs:{size:"small",type:"primary"}},[e._v("选择视频")]),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持mp4格式，最大200MB")])],1):i("el-input",{attrs:{value:t.videoUrl,placeholder:"请输入视频链接"},on:{input:function(t){return e.handleVideoUrlInput(t,a)}}})],1)],1)],1)],1)],1)],1)})),1),e.businessValues.length?e._e():i("div",{staticClass:"empty-state"},[i("i",{staticClass:"el-icon-document-add"}),i("p",[e._v("暂无商业价值，点击上方按钮添加")])])],1),i("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览",width:"60%","append-to-body":""},on:{"update:visible":function(t){e.previewVisible=t}}},[i("div",{staticClass:"preview-container"},[i("img",{staticClass:"preview-image",attrs:{src:e.previewImageUrl}})])])],1)},n=[],r=i("c7eb"),s=i("1da1"),o=(i("d81d"),i("14d9"),i("a434"),i("b0c0"),i("e9c4"),i("8a79"),i("2ca0"),i("86e4")),l={name:"BusinessValueConfig",props:{value:{type:Array,default:function(){return[]}},leftTreeIndustryCode:{type:String,default:""}},data:function(){return{businessValues:[],isUpdating:!1,emitTimer:null,previewVisible:!1,previewImageUrl:"",videoUploadModes:{}}},watch:{value:{handler:function(e){if(!this.isUpdating&&e&&Array.isArray(e)){var t=e.map((function(e){return{id:e.id||null,viewInfoId:e.viewInfoId||null,tag:e.tag||"",imageUrl:e.backgroundImgFileUrl||"",videoUrl:e.backgroundFileUrl||"",videoFileList:e.backgroundFileUrl?[{name:e.backgroundFileUrl.split("/").pop(),url:e.backgroundFileUrl,uid:Date.now()}]:[]}}));JSON.stringify(this.businessValues)!==JSON.stringify(t)&&(this.businessValues=t)}},immediate:!0,deep:!1}},methods:{debouncedEmitChange:function(){var e=this;this.emitTimer&&clearTimeout(this.emitTimer),this.emitTimer=setTimeout((function(){e.emitChange()}),200)},emitChange:function(){var e=this;this.emitTimer&&clearTimeout(this.emitTimer),this.emitTimer=setTimeout((function(){e.isUpdating=!0;var t=e.businessValues.map((function(e){return{id:e.id||null,viewInfoId:e.viewInfoId||null,type:5,tag:e.tag||null,backgroundImgFileUrl:e.imageUrl||null,backgroundFileUrl:e.videoUrl||null}}));e.$emit("input",t),e.$nextTick((function(){setTimeout((function(){e.isUpdating=!1}),50)}))}),200)},addBusinessValue:function(){var e={tag:"",imageUrl:"",videoUrl:"",videoFileList:[]};this.businessValues.push(e),this.emitChange()},removeValue:function(e){var t=this;return Object(s["a"])(Object(r["a"])().mark((function i(){var a;return Object(r["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:a=t.businessValues[e],t.$confirm("确定删除此商业价值吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(s["a"])(Object(r["a"])().mark((function i(){var n;return Object(r["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!a.id){i.next=17;break}return i.prev=1,t.$modal.loading("正在删除商业价值，请稍候..."),i.next=5,Object(o["e"])({id:a.id});case 5:n=i.sent,0===n.code?(t.businessValues.splice(e,1),t.emitChange(),t.$message.success("删除成功")):t.$message.error(n.msg||"删除失败"),i.next=12;break;case 9:i.prev=9,i.t0=i["catch"](1),t.$message.error("删除失败");case 12:return i.prev=12,t.$modal.closeLoading(),i.finish(12);case 15:i.next=20;break;case 17:t.businessValues.splice(e,1),t.emitChange(),t.$message.success("删除成功");case 20:case"end":return i.stop()}}),i,null,[[1,9,12,15]])})))).catch((function(){}));case 2:case"end":return i.stop()}}),i)})))()},beforeUploadImage:function(e,t){var i=this;return Object(s["a"])(Object(r["a"])().mark((function a(){var n,s,l;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.type.startsWith("image/")){a.next=3;break}return i.$message.error("只能上传图片文件！"),a.abrupt("return",!1);case 3:if(!(e.size>20971520)){a.next=6;break}return i.$message.error("图片大小不能超过20MB！"),a.abrupt("return",!1);case 6:return a.prev=6,i.$modal.loading("正在上传图片，请稍候..."),n=new FormData,n.append("file",e),n.append("industryCode",i.leftTreeIndustryCode),a.next=13,Object(o["h"])(n);case 13:s=a.sent,0===s.code&&s.data?(l=i.businessValues[t],l.imageUrl=s.data.fileUrl,i.debouncedEmitChange(),i.$message.success("上传成功")):i.$message.error(s.msg||"上传失败"),a.next=20;break;case 17:a.prev=17,a.t0=a["catch"](6),i.$message.error("上传失败");case 20:return a.prev=20,i.$modal.closeLoading(),a.finish(20);case 23:return a.abrupt("return",!1);case 24:case"end":return a.stop()}}),a,null,[[6,17,20,23]])})))()},beforeUploadVideo:function(e,t){var i=this;return Object(s["a"])(Object(r["a"])().mark((function a(){var n,s,l,u;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.type.startsWith("video/")||e.name.endsWith(".mp4")){a.next=3;break}return i.$message.error("只能上传MP4视频文件！"),a.abrupt("return",!1);case 3:if(!(e.size>209715200)){a.next=6;break}return i.$message.error("视频大小不能超过200MB！"),a.abrupt("return",!1);case 6:return a.prev=6,i.$modal.loading("正在上传视频，请稍候..."),n=new FormData,n.append("file",e),n.append("industryCode",i.leftTreeIndustryCode),a.next=13,Object(o["h"])(n);case 13:s=a.sent,0===s.code&&s.data?(l=i.businessValues[t],u=s.data.fileUrl.split("/").pop(),l.videoUrl=s.data.fileUrl,l.videoFileList=[{name:u,url:s.data.fileUrl,uid:Date.now()}],i.debouncedEmitChange(),i.$message.success("上传成功")):i.$message.error(s.msg||"上传失败"),a.next=20;break;case 17:a.prev=17,a.t0=a["catch"](6),i.$message.error("上传失败");case 20:return a.prev=20,i.$modal.closeLoading(),a.finish(20);case 23:return a.abrupt("return",!1);case 24:case"end":return a.stop()}}),a,null,[[6,17,20,23]])})))()},handleRemoveVideo:function(e,t){var i=this.businessValues[t];i.videoUrl="",i.videoFileList=[],this.emitChange()},previewImage:function(e){e&&(this.previewImageUrl=e,this.previewVisible=!0)},deleteValueImage:function(e){var t=this;this.$confirm("确定删除此图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.businessValues[e].imageUrl="",t.debouncedEmitChange(),t.$message.success("图片已删除")})).catch((function(){}))},getUploadMode:function(e,t){return this.videoUploadModes[t]||this.$set(this.videoUploadModes,t,"upload"),this.videoUploadModes[t]},setUploadMode:function(e,t,i){this.$set(this.videoUploadModes,t,i)},handleVideoUrlInput:function(e,t){var i=this.businessValues[t];if(i.videoUrl=e,i.videoFileList=[],e){var a=e.split("/").pop()||"外部链接文件";i.videoFileList=[{name:a,url:e,uid:Date.now()}]}this.debouncedEmitChange()}}},u=l,c=(i("578d"),i("2877")),d=Object(c["a"])(u,a,n,!1,null,"283b8466",null);t["default"]=d.exports},"296f":function(e,t,i){},"578d":function(e,t,i){"use strict";i("296f")},"86e4":function(e,t,i){"use strict";i.d(t,"c",(function(){return r})),i.d(t,"d",(function(){return s})),i.d(t,"f",(function(){return o})),i.d(t,"e",(function(){return l})),i.d(t,"a",(function(){return u})),i.d(t,"b",(function(){return c})),i.d(t,"g",(function(){return d})),i.d(t,"h",(function(){return p}));var a=i("5530"),n=i("b775");function r(e){return Object(n["a"])({url:"/sceneConfig/detail",method:"get",params:e})}function s(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(n["a"])({url:"/sceneConfig/theme/list",method:"get",params:Object(a["a"])({page:1,limit:20},e)})}function o(e){return Object(n["a"])({url:"/sceneConfig/upd",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/sceneConfig/sceneFileInfo/del",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/sceneConfig/background/file/del",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/sceneConfig/file/bind/del",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/sceneConfig/synchronization/file",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function p(e){var t;return e instanceof FormData?t=e:(t=new FormData,t.append("file",e)),Object(n["a"])({url:"/sceneConfig/upload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}},"8a79":function(e,t,i){"use strict";var a=i("23e7"),n=i("4625"),r=i("06cf").f,s=i("50c4"),o=i("577e"),l=i("5a34"),u=i("1d80"),c=i("ab13"),d=i("c430"),p=n("".slice),m=Math.min,f=c("endsWith"),g=!d&&!f&&!!function(){var e=r(String.prototype,"endsWith");return e&&!e.writable}();a({target:"String",proto:!0,forced:!g&&!f},{endsWith:function(e){var t=o(u(this));l(e);var i=arguments.length>1?arguments[1]:void 0,a=t.length,n=void 0===i?a:m(s(i),a),r=o(e);return p(t,n-r.length,n)===r}})}}]);