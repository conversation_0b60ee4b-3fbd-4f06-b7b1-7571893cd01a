// 外部token管理工具
export class ExternalAuth {
  static TOKEN_KEY = 'external-token'
  
  // 从URL获取token
  static getTokenFromUrl() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('token')
  }
  
  // 设置token
  static setToken(token) {
    if (token) {
      localStorage.setItem(this.TOKEN_KEY, token)
      return true
    }
    return false
  }
  
  // 获取token
  static getToken() {
    return localStorage.getItem(this.TOKEN_KEY)
  }
  
  // 清除token
  static removeToken() {
    localStorage.removeItem(this.TOKEN_KEY)
  }
  
  // 初始化token（从URL获取并设置）
  static initToken() {
    const urlToken = this.getTokenFromUrl()
    if (urlToken) {
      this.setToken(urlToken)
      // 清除URL中的token参数（可选）
      this.clearTokenFromUrl()
      return urlToken
    }
    return this.getToken()
  }
  
  // 清除URL中的token参数
  static clearTokenFromUrl() {
    const url = new URL(window.location)
    url.searchParams.delete('token')
    window.history.replaceState({}, document.title, url.toString())
  }
}