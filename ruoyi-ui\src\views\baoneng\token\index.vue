<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="凭证类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="凭证类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.token_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="使用情况" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="凭证类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.token_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['baoneng:token:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['baoneng:token:upd']"
        >修改
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tokenList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="缴费凭证" align="center" prop="token">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.token"
            raw-content
            placement="top-start"
            v-if="scope.row.token"
          >
            <span v-if="scope.row.token && scope.row.token.length <= 15">
               {{ scope.row.token }}
          </span>
            <span v-if="scope.row.token&& scope.row.token.length > 15">
               {{ scope.row.token.substr(0, 15) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.address== null"> NA </span>
        </template>
      </el-table-column>

      <el-table-column label="凭证类型" align="center" prop="typeDesc"/>
      <el-table-column label="归属人微信" align="center" prop="tkAffiliation"/>
      <el-table-column label="使用状态" align="center" prop="statusDesc"/>
      <el-table-column label="获取时间" align="center" prop="captureTime"/>
      <el-table-column label="过期时间" align="center" prop="expirationTime"/>
      <el-table-column label="备注" align="center" prop="remark"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="showTokenDetail(scope.row)"
            v-hasPermi="['baoneng:token:detail']"
          >使用详情
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['baoneng:token:upd']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['baoneng:token:del']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.limit"
      @pagination="getList"
    />

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="缴费凭证" prop="token">
          <el-input v-model="form.token" placeholder="请输入缴费凭证"/>
        </el-form-item>
        <el-form-item label="凭证类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择凭证类型">
            <el-option
              v-for="dict in dict.type.token_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="微信" prop="tkAffiliation">
          <el-input v-model="form.tkAffiliation" placeholder="请输入微信号"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style>
</style>

<script>
import {tokenList, addToken, getTokenDetail, updToken, delToken} from "@/api/baoneng/token/token";

export default {
  name: "token",
  dicts: ['token_type', 'token_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      tokenList: [],
      // 弹出层标题
      title: "",
      deptInfoList: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 1,
        limit: 10,
        tokenType: null,
        tokenStatus: null
      },
      // 表单参数
      form: {
        token: null,
        type: null,
        tkAffiliation: null,
        remark: null
      },
      // 表单校验
      rules: {
        token: [
          {required: true, message: "缴费凭证不能为空", trigger: "blur"}
        ],
        type: [
          {required: true, message: "凭证类型不能为空", trigger: "blur"}
        ],
        tkAffiliation: [
          {required: true, message: "凭证所属客户不能为空", trigger: "blur"}
        ]
      }
    };
  },
  mounted() {
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      tokenList(this.queryParams).then(response => {
        this.tokenList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        carNumber: null,
        carAffiliation: null,
        clientAffiliation: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      console.log(this.ids)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加凭证";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const tokenId = row.id || this.ids[0]
      getTokenDetail({tokenId: tokenId}).then(response => {
        this.form = response.data;
        this.form.type = response.data.type.toString();
        this.open = true;
        this.title = "修改凭证";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updToken(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addToken(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const tokenId = row.id || this.ids;
      this.$modal.confirm('是否确认删除数据项？').then(function () {
        return delToken({tokenId: tokenId});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
  },
};
</script>
