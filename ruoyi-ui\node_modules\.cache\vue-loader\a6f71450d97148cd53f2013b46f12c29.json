{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=style&index=0&id=dd143a2a&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754893070949}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743599735798}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743599729946}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743599731247}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5wYWdlLWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGhlaWdodDogMTAwdmg7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5tZW51LXBhbmVsIHsNCiAgd2lkdGg6IDI1MHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KfQ0KDQoubWVudS1zZWFyY2ggew0KICBwYWRkaW5nOiAxNnB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U2ZTZlNjsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgcG9zaXRpb246IHN0aWNreTsNCiAgdG9wOiAwOw0KICB6LWluZGV4OiAxMDsNCn0NCg0KLm1lbnUtdHJlZSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGZsZXg6IDE7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIHBhZGRpbmc6IDhweCAwOw0KfQ0KDQoubWVudS10cmVlIDo6di1kZWVwIC5lbC10cmVlLW5vZGVfX2NvbnRlbnQgew0KICBoZWlnaHQ6IDQwcHg7DQogIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICBwYWRkaW5nLWxlZnQ6IDEwcHg7DQp9DQoNCi5tZW51LXRyZWUgOjp2LWRlZXAgLmVsLXRyZWUtbm9kZV9fY29udGVudDpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNlNmY3ZmY7DQp9DQoNCi5tZW51LXRyZWUgOjp2LWRlZXAgLmVsLXRyZWUtbm9kZS5pcy1jdXJyZW50ID4gLmVsLXRyZWUtbm9kZV9fY29udGVudCB7DQogIGJhY2tncm91bmQtY29sb3I6ICM0MDlFRkY7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLyog6Ieq5a6a5LmJ5rua5Yqo5p2h5qC35byPICovDQoubWVudS10cmVlOjotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiA2cHg7DQp9DQoNCi5tZW51LXRyZWU6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2YxZjFmMTsNCiAgYm9yZGVyLXJhZGl1czogM3B4Ow0KfQ0KDQoubWVudS10cmVlOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNjMGMwYzA7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCn0NCg0KLm1lbnUtdHJlZTo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjYTBhMGEwOw0KfQ0KDQouY29udGVudC1wYW5lbCB7DQogIGZsZXg6IDE7DQogIHBhZGRpbmc6IDIwcHggMjBweCA4MHB4IDIwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCi5taW5pLWJsb2NrIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLmNhdGVnb3J5LWJsb2NrIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2ViZWVmNTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBwYWRkaW5nOiAxNXB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjlmOWY5Ow0KfQ0KDQouY2F0ZWdvcnktaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQouY2F0ZWdvcnktdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB1c2VyLXNlbGVjdDogbm9uZTsNCiAgcGFkZGluZzogNHB4IDhweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnM7DQp9DQoNCi5jYXRlZ29yeS10aXRsZTpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmMGYwZjA7DQp9DQoNCi5jYXRlZ29yeS10aXRsZSBzcGFuIHsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICBtaW4td2lkdGg6IDEwMHB4Ow0KfQ0KDQouY2F0ZWdvcnktYm9keSB7DQogIHBhZGRpbmc6IDEycHg7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIGJvcmRlcjogMXB4IGRhc2hlZCAjZGNkZmU2Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi5zdWItY2F0ZWdvcnktYmxvY2sgew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KLnN1Yi1jYXRlZ29yeS1ibG9jazpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLnN1Yi1jYXRlZ29yeS1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDEwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0KDQouc3ViLWNhdGVnb3J5LXRpdGxlIHsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLnN1Yi1jYXRlZ29yeS1ib2R5IHsNCiAgcGFkZGluZzogMTVweDsNCn0NCg0KLnNlZ21lbnQtaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5wYWluLXBvaW50LWJsb2NrIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLnBhaW4tcG9pbnQtYmxvY2s6bGFzdC1jaGlsZCB7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQoNCi5taW5pLWJsb2NrIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgbWluLWhlaWdodDogNDUwcHg7IC8qIOiuvue9rue7n+S4gOeahOacgOWwj+mrmOW6piAqLw0KfQ0KDQoubWluaS1ibG9jazpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLyog56Gu5L+d5Y2h54mH5YaF5a655Yy65Z+f5Lmf5pyJ5ZCI6YCC55qE6auY5bqmICovDQoubWluaS1ibG9jayAuZWwtY2FyZF9fYm9keSB7DQogIG1pbi1oZWlnaHQ6IDQ1MHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KfQ0KDQovKiDop4bpopHljaHniYfkv53mjIHljp/mnInmoLflvI8gKi8NCi52aWRlby1jYXJkIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgbWluLWhlaWdodDogNDUwcHg7DQp9DQoNCi52aWRlby1jYXJkIC5lbC1jYXJkX19ib2R5IHsNCiAgZmxleDogMTsNCiAgb3ZlcmZsb3c6IGF1dG87DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCiAgbWluLWhlaWdodDogNDUwcHg7DQp9DQoudmlkZW8tY2FyZC15dXsNCiAgbWluLWhlaWdodDogMzAwcHg7DQp9DQoudmlkZW8tY2FyZCAuZWwtY2FyZF9fYm9keSB7DQogIGZsZXg6IDE7DQogIG92ZXJmbG93OiBhdXRvOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQp9DQouc2VnbWVudC1zY3JvbGwgew0KICBtYXgtaGVpZ2h0OiAxNTBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgYm9yZGVyOiAxcHggc29saWQgI2VlZTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBwYWRkaW5nOiA4cHg7DQogIGJhY2tncm91bmQ6ICNmYWZiZmM7DQp9DQouc2NlbmUtY29uZmlnLWNvbnRhaW5lciB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQp9DQoNCi8qIOmZkOWItuS4iuS8oOWbvueJh+eahOaYvuekuuWkp+WwjyAqLw0KLmltYWdlLXVwbG9hZCAuZWwtdXBsb2FkLS1waWN0dXJlLWNhcmQgew0KICB3aWR0aDogMTQ4cHg7DQogIGhlaWdodDogMTQ4cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLnVwbG9hZC1pbWFnZSB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIG9iamVjdC1maXQ6IGNvdmVyOw0KICBkaXNwbGF5OiBibG9jazsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KfQ0KDQovKiDku4vnu43op4bpopHpppbluKflm77niYflpKflsI/mjqfliLYgKi8NCi5pbWFnZS11cGxvYWQgLmVsLXVwbG9hZC1saXN0X19pdGVtLXRodW1ibmFpbCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIG9iamVjdC1maXQ6IGNvdmVyOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQp9DQoNCi8qIOS4iuS8oOahhuS5n+a3u+WKoOWchuinkiAqLw0KLmltYWdlLXVwbG9hZCAuZWwtdXBsb2FkLS1waWN0dXJlLWNhcmQgew0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi8qIOWbvueJh+mihOiniOagt+W8jyAqLw0KLnByZXZpZXctY29udGFpbmVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQoucHJldmlldy1pbWFnZSB7DQogIG1heC13aWR0aDogMTAwJTsNCiAgbWF4LWhlaWdodDogNzB2aDsNCiAgb2JqZWN0LWZpdDogY29udGFpbjsNCn0NCg0KLyog5Zu+54mH5oKs5YGc5pON5L2c5qC35byPICovDQouaW1hZ2UtcHJldmlldy1jb250YWluZXIgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQouaW1hZ2Utb3ZlcmxheSB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiAwOw0KICBsZWZ0OiAwOw0KICByaWdodDogMDsNCiAgYm90dG9tOiAwOw0KICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNSk7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBvcGFjaXR5OiAwOw0KICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3M7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCn0NCg0KLmltYWdlLXByZXZpZXctY29udGFpbmVyOmhvdmVyIC5pbWFnZS1vdmVybGF5IHsNCiAgb3BhY2l0eTogMTsNCn0NCg0KLnByZXZpZXctaWNvbiwNCi5kZWxldGUtaWNvbiB7DQogIGNvbG9yOiB3aGl0ZTsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBtYXJnaW46IDAgMTBweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4yczsNCn0NCg0KLnByZXZpZXctaWNvbjpob3ZlciwNCi5kZWxldGUtaWNvbjpob3ZlciB7DQogIHRyYW5zZm9ybTogc2NhbGUoMS4yKTsNCn0NCg0KLnN1Ym1pdC1mb290ZXIgew0KICBwb3NpdGlvbjogZml4ZWQ7DQogIGJvdHRvbTogMDsNCiAgcmlnaHQ6IDA7DQogIGxlZnQ6IDI1MHB4Ow0KICBoZWlnaHQ6IDYwcHg7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOw0KICBwYWRkaW5nOiAwIDIwcHg7DQogIGJveC1zaGFkb3c6IDAgLTJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICB6LWluZGV4OiAxMDAwOw0KfQ0KDQouc3VibWl0LWZvb3RlciAuZWwtYnV0dG9uIHsNCiAgbWluLXdpZHRoOiAxMDBweDsNCn0NCg0KLm1lbnUtc2VhcmNoIHsNCiAgcGFkZGluZzogMTZweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNmU2ZTY7DQp9DQoNCi5tZW51LXNlYXJjaCAuZWwtaW5wdXQgew0KICBib3JkZXItcmFkaXVzOiAyMHB4Ow0KfQ0KDQoubWVudS1zZWFyY2ggLmVsLWlucHV0X19pbm5lciB7DQogIGJvcmRlci1yYWRpdXM6IDIwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQp9DQoNCi5oaWdobGlnaHQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZlYjNiOw0KICBjb2xvcjogIzMzMzsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5tZW51LWxpc3Qgew0KICBib3JkZXItcmlnaHQ6IG5vbmU7DQp9DQoNCi5tZW51LXRyZWUgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQoubWVudS10cmVlIDo6di1kZWVwIC5lbC10cmVlLW5vZGVfX2NvbnRlbnQgew0KICBoZWlnaHQ6IDQwcHg7DQogIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICBwYWRkaW5nLWxlZnQ6IDEwcHg7DQp9DQoNCi5tZW51LXRyZWUgOjp2LWRlZXAgLmVsLXRyZWUtbm9kZV9fY29udGVudDpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNlNmY3ZmY7DQp9DQoNCi5tZW51LXRyZWUgOjp2LWRlZXAgLmVsLXRyZWUtbm9kZS5pcy1jdXJyZW50ID4gLmVsLXRyZWUtbm9kZV9fY29udGVudCB7DQogIGJhY2tncm91bmQtY29sb3I6ICM0MDlFRkY7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLmN1c3RvbS10cmVlLW5vZGUgew0KICBmbGV4OiAxOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgcGFkZGluZy1yaWdodDogOHB4Ow0KfQ0KDQouaGlnaGxpZ2h0IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogeWVsbG93Ow0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg=="}, null]}