(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ebe39df2","chunk-2d0dece3"],{5046:function(e,t,r){"use strict";r("7789")},7789:function(e,t,r){},"86e4":function(e,t,r){"use strict";r.d(t,"c",(function(){return s})),r.d(t,"d",(function(){return i})),r.d(t,"f",(function(){return c})),r.d(t,"e",(function(){return o})),r.d(t,"a",(function(){return d})),r.d(t,"b",(function(){return u})),r.d(t,"g",(function(){return l})),r.d(t,"h",(function(){return f}));var n=r("5530"),a=r("b775");function s(e){return Object(a["a"])({url:"/sceneConfig/detail",method:"get",params:e})}function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(a["a"])({url:"/sceneConfig/theme/list",method:"get",params:Object(n["a"])({page:1,limit:20},e)})}function c(e){return Object(a["a"])({url:"/sceneConfig/upd",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/sceneConfig/sceneFileInfo/del",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/sceneConfig/background/file/del",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/sceneConfig/file/bind/del",method:"post",data:e})}function l(e){return Object(a["a"])({url:"/sceneConfig/synchronization/file",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function f(e){var t;return e instanceof FormData?t=e:(t=new FormData,t.append("file",e)),Object(a["a"])({url:"/sceneConfig/upload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}},fa87:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"vr-scene-config"},[r("div",{staticClass:"config-header"},[r("span",[e._v("VR看现场配置")]),r("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.addVr}},[r("i",{staticClass:"el-icon-plus"}),e._v(" 添加VR场景 ")])],1),r("div",{staticClass:"vr-list"},[r("div",{staticClass:"vr-scroll-container"},[r("el-row",{attrs:{gutter:20}},e._l(e.vrList,(function(t,n){return r("el-col",{key:n,attrs:{span:8}},[r("el-card",{staticClass:"vr-item",attrs:{shadow:"hover"}},[r("div",{staticClass:"vr-header",attrs:{slot:"header"},slot:"header"},[r("span",[e._v("VR场景 "+e._s(n+1))]),r("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.removeVr(n)}}})],1),r("el-form",{attrs:{"label-width":"60px",size:"small"}},[r("el-form-item",{attrs:{label:"名称",required:""}},[r("el-input",{attrs:{placeholder:"请输入VR场景名称"},on:{input:e.emitChange},model:{value:t.name,callback:function(r){e.$set(t,"name",r)},expression:"vr.name"}})],1),r("el-form-item",{attrs:{label:"地址",required:""}},[r("el-input",{attrs:{placeholder:"请输入VR场景地址（http://或https://开头）"},on:{input:function(r){return e.validateAddress(t,n)},blur:function(r){return e.validateAddressOnBlur(t,n)},paste:function(r){return e.handlePaste(t,n,r)}},model:{value:t.address,callback:function(r){e.$set(t,"address",r)},expression:"vr.address"}}),t.addressError?r("div",{staticClass:"error-text"},[e._v(" "+e._s(t.addressError)+" ")]):e._e()],1)],1)],1)],1)})),1)],1),e.vrList.length?e._e():r("div",{staticClass:"empty-state"},[r("i",{staticClass:"el-icon-document-add"}),r("p",[e._v("暂无VR场景，点击上方按钮添加")])])])])},a=[],s=r("c7eb"),i=r("1da1"),c=(r("14d9"),r("a434"),r("e9c4"),r("b64b"),r("ac1f"),r("00b4"),r("2ca0"),r("86e4")),o={name:"VrSceneConfig",props:{value:{type:Array,default:function(){return[]}}},data:function(){return{vrList:[]}},watch:{value:{handler:function(e){this.vrList=Array.isArray(e)?JSON.parse(JSON.stringify(e)):[]},immediate:!0,deep:!0}},methods:{addVr:function(){this.vrList.push({id:null,industryId:null,type:null,viewInfoId:null,name:"",address:"",addressError:""}),this.emitChange()},removeVr:function(e){var t=this;return Object(i["a"])(Object(s["a"])().mark((function r(){var n;return Object(s["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:n=t.vrList[e],t.$confirm("确定删除此VR场景吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(i["a"])(Object(s["a"])().mark((function r(){var a;return Object(s["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!n.id){r.next=17;break}return r.prev=1,t.$modal.loading("正在删除VR场景，请稍候..."),r.next=5,Object(c["e"])({id:n.id});case 5:a=r.sent,0===a.code?(t.vrList.splice(e,1),t.emitChange(),t.$message.success("删除成功")):t.$message.error(a.msg||"删除失败"),r.next=12;break;case 9:r.prev=9,r.t0=r["catch"](1),t.$message.error("删除失败");case 12:return r.prev=12,t.$modal.closeLoading(),r.finish(12);case 15:r.next=20;break;case 17:t.vrList.splice(e,1),t.emitChange(),t.$message.success("删除成功");case 20:case"end":return r.stop()}}),r,null,[[1,9,12,15]])})))).catch((function(){}));case 2:case"end":return r.stop()}}),r)})))()},emitChange:function(){this.$emit("input",this.vrList)},validateAddress:function(e,t){this.$set(e,"addressError",""),this.emitChange()},validateAddressOnBlur:function(e,t){if(e.address){var r=/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;r.test(e.address)?this.$set(e,"addressError",""):this.$set(e,"addressError","请输入有效的链接地址（必须以http://或https://开头）")}else this.$set(e,"addressError","请输入VR场景地址")},handlePaste:function(e,t,r){var n=r.clipboardData.getData("text");if(n&&!n.startsWith("http://")&&!n.startsWith("https://"))return r.preventDefault(),this.$message.warning("请粘贴有效的链接地址（以http://或https://开头）"),!1}}},d=o,u=(r("5046"),r("2877")),l=Object(u["a"])(d,n,a,!1,null,"5d23cdcd",null);t["default"]=l.exports}}]);