<template>
  <div class="business-value-config">
    <!-- 商业价值配置 -->
    <div class="config-header">
      <span>商业价值配置</span>
      <el-button type="primary" size="small" @click="addBusinessValue">
        <i class="el-icon-plus"></i> 添加商业价值
      </el-button>
    </div>
    
    <div class="value-list">
      <el-row :gutter="20">
        <el-col 
          :span="12" 
          v-for="(value, index) in businessValues" 
          :key="index"
        >
          <el-card class="value-item" shadow="hover">
            <div slot="header" class="value-header">
              <span>商业价值 {{ index + 1 }}</span>
              <el-button 
                type="danger" 
                size="mini" 
                icon="el-icon-delete" 
                circle 
                @click="removeValue(index)"
              />
            </div>
            
            <el-form :model="value" label-width="80px" size="small">
              <el-form-item label="名称" required>
                <el-input 
                  v-model="value.tag" 
                  placeholder="请输入商业价值名称"
                  @input="debouncedEmitChange"
                />
              </el-form-item>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="图片">
                    <el-upload
                      :key="`image-${index}-${value.id || 'new'}`"
                      class="image-upload"
                      action="#"
                      :show-file-list="false"
                      list-type="picture-card"
                      accept="image/*"
                      :before-upload="file => beforeUploadImage(file, index)"
                      :http-request="() => {}"
                    >
                      <div v-if="value.imageUrl" class="image-preview-container">
                        <img :src="value.imageUrl" class="upload-image" />
                        <div class="image-overlay">
                          <i class="el-icon-zoom-in preview-icon" @click.stop="previewImage(value.imageUrl)" title="预览"></i>
                          <i class="el-icon-delete delete-icon" @click.stop="deleteValueImage(index)" title="删除"></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus"></i>
                    </el-upload>
                    <div class="upload-tip">支持jpg/png，最大20MB</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="视频文件">
                    <div style="margin-bottom: 8px;">
                      <el-radio-group :value="getUploadMode('video', index)" @input="value => setUploadMode('video', index, value)" size="small">
                        <el-radio-button label="upload">上传文件</el-radio-button>
                        <el-radio-button label="url">填写链接</el-radio-button>
                      </el-radio-group>
                    </div>
                    
                    <!-- 上传模式 -->
                    <el-upload
                      v-if="getUploadMode('video', index) === 'upload'"
                      :key="`video-${index}-${value.id || 'new'}-${value.videoUrl ? 'has' : 'empty'}`"
                      action="#"
                      :show-file-list="true"
                      :file-list="value.videoFileList"
                      accept="video/*"
                      :before-upload="file => beforeUploadVideo(file, index)"
                      :http-request="() => {}"
                      :on-remove="file => handleRemoveVideo(file, index)"
                    >
                      <el-button size="small" type="primary">选择视频</el-button>
                      <div slot="tip" class="el-upload__tip">支持mp4格式，最大200MB</div>
                    </el-upload>
                    
                    <!-- 链接模式 -->
                    <el-input
                      v-else
                      :value="value.videoUrl"
                      placeholder="请输入视频链接"
                      @input="val => handleVideoUrlInput(val, index)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
      
      <div v-if="!businessValues.length" class="empty-state">
        <i class="el-icon-document-add"></i>
        <p>暂无商业价值，点击上方按钮添加</p>
      </div>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      title="图片预览"
      width="60%"
      append-to-body
    >
      <div class="preview-container">
        <img :src="previewImageUrl" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { uploadSceneFile, sceneFileInfoDel } from '@/api/view/sceneView'

export default {
  name: 'BusinessValueConfig',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    leftTreeIndustryCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      businessValues: [],
      isUpdating: false,
      emitTimer: null,
      // 图片预览
      previewVisible: false,
      previewImageUrl: '',
      videoUploadModes: {}
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.isUpdating) return
        
        if (newVal && Array.isArray(newVal)) {
          const newBusinessValues = newVal.map(item => ({
            id: item.id || null,
            viewInfoId: item.viewInfoId || null,
            tag: item.tag || '',
            imageUrl: item.backgroundImgFileUrl || '',
            videoUrl: item.backgroundFileUrl || '',
            videoFileList: item.backgroundFileUrl ? [{
              name: item.backgroundFileUrl.split('/').pop(),
              url: item.backgroundFileUrl,
              uid: Date.now()
            }] : []
          }))
          
          if (JSON.stringify(this.businessValues) !== JSON.stringify(newBusinessValues)) {
            this.businessValues = newBusinessValues
          }
        }
      },
      immediate: true,
      deep: false
    }
  },
  methods: {
    debouncedEmitChange() {
      if (this.emitTimer) {
        clearTimeout(this.emitTimer)
      }
      this.emitTimer = setTimeout(() => {
        this.emitChange()
      }, 200)
    },
    
    emitChange() {
      if (this.emitTimer) {
        clearTimeout(this.emitTimer)
      }
      
      this.emitTimer = setTimeout(() => {
        this.isUpdating = true
        
        const data = this.businessValues.map(value => ({
          id: value.id || null,
          viewInfoId: value.viewInfoId || null,
          type: 5,
          tag: value.tag || null,
          backgroundImgFileUrl: value.imageUrl || null,
          backgroundFileUrl: value.videoUrl || null
        }))
        
        this.$emit('input', data)
        
        this.$nextTick(() => {
          setTimeout(() => {
            this.isUpdating = false
          }, 50)
        })
      }, 200)
    },
    
    addBusinessValue() {
      const newValue = {
        tag: '',
        imageUrl: '',
        videoUrl: '',
        videoFileList: []
      }
      this.businessValues.push(newValue)
      this.emitChange()
    },
    
    async removeValue(index) {
      const value = this.businessValues[index]
      
      this.$confirm('确定删除此商业价值吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // 如果商业价值有ID，调用删除接口
        if (value.id) {
          try {
            this.$modal.loading("正在删除商业价值，请稍候...")
            const res = await sceneFileInfoDel({ id: value.id })
            if (res.code === 0) {
              this.businessValues.splice(index, 1)
              this.emitChange()
              this.$message.success('删除成功')
            } else {
              this.$message.error(res.msg || '删除失败')
            }
          } catch (error) {
            this.$message.error('删除失败')
          } finally {
            this.$modal.closeLoading()
          }
        } else {
          // 没有ID的新商业价值，直接从数组中移除
          this.businessValues.splice(index, 1)
          this.emitChange()
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },
    
    async beforeUploadImage(file, index) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件！')
        return false
      }
      
      if (file.size > 20 * 1024 * 1024) {
        this.$message.error('图片大小不能超过20MB！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传图片，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.leftTreeIndustryCode)
        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          const value = this.businessValues[index]
          value.imageUrl = res.data.fileUrl
          
          this.debouncedEmitChange()
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    
    async beforeUploadVideo(file, index) {
      if (!file.type.startsWith('video/') && !file.name.endsWith('.mp4')) {
        this.$message.error('只能上传MP4视频文件！')
        return false
      }
      
      if (file.size > 200 * 1024 * 1024) {
        this.$message.error('视频大小不能超过200MB！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传视频，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.leftTreeIndustryCode)
        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          const value = this.businessValues[index]
          const fileName = res.data.fileUrl.split('/').pop()
          
          value.videoUrl = res.data.fileUrl
          value.videoFileList = [{
            name: fileName,
            url: res.data.fileUrl,
            uid: Date.now()
          }]
          
          this.debouncedEmitChange()
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    
    handleRemoveVideo(file, index) {
      const value = this.businessValues[index]
      value.videoUrl = ''
      value.videoFileList = []
      this.emitChange()
    },
    
    // 图片预览
    previewImage(url) {
      if (url) {
        this.previewImageUrl = url
        this.previewVisible = true
      }
    },
    
    // 删除商业价值图片
    deleteValueImage(index) {
      this.$confirm('确定删除此图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.businessValues[index].imageUrl = ''
        this.debouncedEmitChange()
        this.$message.success('图片已删除')
      }).catch(() => {})
    },
    // 获取上传模式
    getUploadMode(type, index) {
      if (!this.videoUploadModes[index]) {
        this.$set(this.videoUploadModes, index, 'upload')
      }
      return this.videoUploadModes[index]
    },
    
    // 设置上传模式
    setUploadMode(type, index, mode) {
      this.$set(this.videoUploadModes, index, mode)
    },
    
    // 视频链接输入处理
    handleVideoUrlInput(val, index) {
      const value = this.businessValues[index]
      value.videoUrl = val
      value.videoFileList = []
      
      if (val) {
        const fileName = val.split('/').pop() || '外部链接文件'
        value.videoFileList = [{
          name: fileName,
          url: val,
          uid: Date.now()
        }]
      }
      
      this.debouncedEmitChange()
    }
  }
}
</script>

<style scoped>
.business-value-config {
  padding: 20px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

.value-list {
  min-height: 200px;
}

.value-item {
  margin-bottom: 20px;
}

.value-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-upload {
  width: 100%;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

/* 图片预览样式 */
.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

/* 图片悬停操作样式 */
.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 6px;
}

.image-preview-container:hover .image-overlay {
  opacity: 1;
}

.preview-icon,
.delete-icon {
  color: white;
  font-size: 20px;
  margin: 0 10px;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-icon:hover,
.delete-icon:hover {
  transform: scale(1.2);
}
</style>







