(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-774cc61d","chunk-6746b265","chunk-31eae13f","chunk-78d2d26b","chunk-2d20955d"],{2855:function(e,t,a){"use strict";a.r(t);var o,l,n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},[a("div",{staticClass:"left-board"},[a("div",{staticClass:"logo-wrapper"},[a("div",{staticClass:"logo"},[a("img",{attrs:{src:e.logo,alt:"logo"}}),e._v(" Form Generator ")])]),a("el-scrollbar",{staticClass:"left-scrollbar"},[a("div",{staticClass:"components-list"},[a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),e._v("输入型组件 ")],1),a("draggable",{staticClass:"components-draggable",attrs:{list:e.inputComponents,group:{name:"componentsGroup",pull:"clone",put:!1},clone:e.cloneComponent,draggable:".components-item",sort:!1},on:{end:e.onEnd}},e._l(e.inputComponents,(function(t,o){return a("div",{key:o,staticClass:"components-item",on:{click:function(a){return e.addComponent(t)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":t.tagIcon}}),e._v(" "+e._s(t.label)+" ")],1)])})),0),a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),e._v("选择型组件 ")],1),a("draggable",{staticClass:"components-draggable",attrs:{list:e.selectComponents,group:{name:"componentsGroup",pull:"clone",put:!1},clone:e.cloneComponent,draggable:".components-item",sort:!1},on:{end:e.onEnd}},e._l(e.selectComponents,(function(t,o){return a("div",{key:o,staticClass:"components-item",on:{click:function(a){return e.addComponent(t)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":t.tagIcon}}),e._v(" "+e._s(t.label)+" ")],1)])})),0),a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),e._v(" 布局型组件 ")],1),a("draggable",{staticClass:"components-draggable",attrs:{list:e.layoutComponents,group:{name:"componentsGroup",pull:"clone",put:!1},clone:e.cloneComponent,draggable:".components-item",sort:!1},on:{end:e.onEnd}},e._l(e.layoutComponents,(function(t,o){return a("div",{key:o,staticClass:"components-item",on:{click:function(a){return e.addComponent(t)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":t.tagIcon}}),e._v(" "+e._s(t.label)+" ")],1)])})),0)],1)])],1),a("div",{staticClass:"center-board"},[a("div",{staticClass:"action-bar"},[a("el-button",{attrs:{icon:"el-icon-download",type:"text"},on:{click:e.download}},[e._v(" 导出vue文件 ")]),a("el-button",{staticClass:"copy-btn-main",attrs:{icon:"el-icon-document-copy",type:"text"},on:{click:e.copy}},[e._v(" 复制代码 ")]),a("el-button",{staticClass:"delete-btn",attrs:{icon:"el-icon-delete",type:"text"},on:{click:e.empty}},[e._v(" 清空 ")])],1),a("el-scrollbar",{staticClass:"center-scrollbar"},[a("el-row",{staticClass:"center-board-row",attrs:{gutter:e.formConf.gutter}},[a("el-form",{attrs:{size:e.formConf.size,"label-position":e.formConf.labelPosition,disabled:e.formConf.disabled,"label-width":e.formConf.labelWidth+"px"}},[a("draggable",{staticClass:"drawing-board",attrs:{list:e.drawingList,animation:340,group:"componentsGroup"}},e._l(e.drawingList,(function(t,o){return a("draggable-item",{key:t.renderKey,attrs:{"drawing-list":e.drawingList,element:t,index:o,"active-id":e.activeId,"form-conf":e.formConf},on:{activeItem:e.activeFormItem,copyItem:e.drawingItemCopy,deleteItem:e.drawingItemDelete}})})),1),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.drawingList.length,expression:"!drawingList.length"}],staticClass:"empty-info"},[e._v(" 从左侧拖入或点选组件进行表单设计 ")])],1)],1)],1)],1),a("right-panel",{attrs:{"active-data":e.activeData,"form-conf":e.formConf,"show-field":!!e.drawingList.length},on:{"tag-change":e.tagChange}}),a("code-type-dialog",{attrs:{visible:e.dialogVisible,title:"选择生成类型","show-file-name":e.showFileName},on:{"update:visible":function(t){e.dialogVisible=t},confirm:e.generate}}),a("input",{attrs:{id:"copyNode",type:"hidden"}})],1)},i=[],c=a("53ca"),r=a("5530"),s=(a("c740"),a("d81d"),a("14d9"),a("a434"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("5319"),a("159b"),a("b76a")),d=a.n(s),u=a("e552"),p=a.n(u),m=a("b311"),f=a.n(m),v=a("a85b"),b=a("766b"),h=a("2e2a"),_=a("ed08");a("99af"),a("a15b"),a("b0c0");function g(e){return'<el-dialog v-bind="$attrs" v-on="$listeners" @open="onOpen" @close="onClose" title="Dialog Title">\n    '.concat(e,'\n    <div slot="footer">\n      <el-button @click="close">取消</el-button>\n      <el-button type="primary" @click="handleConfirm">确定</el-button>\n    </div>\n  </el-dialog>')}function D(e){return"<template>\n    <div>\n      ".concat(e,"\n    </div>\n  </template>")}function y(e){return"<script>\n    ".concat(e,"\n  <\/script>")}function w(e){return"<style>\n    ".concat(e,"\n  </style>")}function x(e,t,a){var o="";"right"!==e.labelPosition&&(o='label-position="'.concat(e.labelPosition,'"'));var n=e.disabled?':disabled="'.concat(e.disabled,'"'):"",i='<el-form ref="'.concat(e.formRef,'" :model="').concat(e.formModel,'" :rules="').concat(e.formRules,'" size="').concat(e.size,'" ').concat(n,' label-width="').concat(e.labelWidth,'px" ').concat(o,">\n      ").concat(t,"\n      ").concat(k(e,a),"\n    </el-form>");return l&&(i='<el-row :gutter="'.concat(e.gutter,'">\n        ').concat(i,"\n      </el-row>")),i}function k(e,t){var a="";return e.formBtns&&"file"===t&&(a='<el-form-item size="large">\n          <el-button type="primary" @click="submitForm">提交</el-button>\n          <el-button @click="resetForm">重置</el-button>\n        </el-form-item>',l&&(a='<el-col :span="24">\n          '.concat(a,"\n        </el-col>"))),a}function C(e,t){return l||24!==e.span?'<el-col :span="'.concat(e.span,'">\n      ').concat(t,"\n    </el-col>"):t}var O={colFormItem:function(e){var t="";e.labelWidth&&e.labelWidth!==o.labelWidth&&(t='label-width="'.concat(e.labelWidth,'px"'));var a=!h["e"][e.tag]&&e.required?"required":"",l=M[e.tag]?M[e.tag](e):null,n="<el-form-item ".concat(t,' label="').concat(e.label,'" prop="').concat(e.vModel,'" ').concat(a,">\n        ").concat(l,"\n      </el-form-item>");return n=C(e,n),n},rowFormItem:function(e){var t="default"===e.type?"":'type="'.concat(e.type,'"'),a="default"===e.type?"":'justify="'.concat(e.justify,'"'),o="default"===e.type?"":'align="'.concat(e.align,'"'),l=e.gutter?'gutter="'.concat(e.gutter,'"'):"",n=e.children.map((function(e){return O[e.layout](e)})),i="<el-row ".concat(t," ").concat(a," ").concat(o," ").concat(l,">\n      ").concat(n.join("\n"),"\n    </el-row>");return i=C(e,i),i}},M={"el-button":function(e){var t=$(e),a=(t.tag,t.disabled),o=e.type?'type="'.concat(e.type,'"'):"",l=e.icon?'icon="'.concat(e.icon,'"'):"",n=e.size?'size="'.concat(e.size,'"'):"",i=I(e);return i&&(i="\n".concat(i,"\n")),"<".concat(e.tag," ").concat(o," ").concat(l," ").concat(n," ").concat(a,">").concat(i,"</").concat(e.tag,">")},"el-input":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=t.clearable,n=t.placeholder,i=t.width,c=e.maxlength?':maxlength="'.concat(e.maxlength,'"'):"",r=e["show-word-limit"]?"show-word-limit":"",s=e.readonly?"readonly":"",d=e["prefix-icon"]?"prefix-icon='".concat(e["prefix-icon"],"'"):"",u=e["suffix-icon"]?"suffix-icon='".concat(e["suffix-icon"],"'"):"",p=e["show-password"]?"show-password":"",m=e.type?'type="'.concat(e.type,'"'):"",f=e.autosize&&e.autosize.minRows?':autosize="{minRows: '.concat(e.autosize.minRows,", maxRows: ").concat(e.autosize.maxRows,'}"'):"",v=T(e);return v&&(v="\n".concat(v,"\n")),"<".concat(e.tag," ").concat(o," ").concat(m," ").concat(n," ").concat(c," ").concat(r," ").concat(s," ").concat(a," ").concat(l," ").concat(d," ").concat(u," ").concat(p," ").concat(f," ").concat(i,">").concat(v,"</").concat(e.tag,">")},"el-input-number":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=t.placeholder,n=e["controls-position"]?"controls-position=".concat(e["controls-position"]):"",i=e.min?":min='".concat(e.min,"'"):"",c=e.max?":max='".concat(e.max,"'"):"",r=e.step?":step='".concat(e.step,"'"):"",s=e["step-strictly"]?"step-strictly":"",d=e.precision?":precision='".concat(e.precision,"'"):"";return"<".concat(e.tag," ").concat(o," ").concat(l," ").concat(r," ").concat(s," ").concat(d," ").concat(n," ").concat(i," ").concat(c," ").concat(a,"></").concat(e.tag,">")},"el-select":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=t.clearable,n=t.placeholder,i=t.width,c=e.filterable?"filterable":"",r=e.multiple?"multiple":"",s=j(e);return s&&(s="\n".concat(s,"\n")),"<".concat(e.tag," ").concat(o," ").concat(n," ").concat(a," ").concat(r," ").concat(c," ").concat(l," ").concat(i,">").concat(s,"</").concat(e.tag,">")},"el-radio-group":function(e){var t=$(e),a=t.disabled,o=t.vModel,l='size="'.concat(e.size,'"'),n=E(e);return n&&(n="\n".concat(n,"\n")),"<".concat(e.tag," ").concat(o," ").concat(l," ").concat(a,">").concat(n,"</").concat(e.tag,">")},"el-checkbox-group":function(e){var t=$(e),a=t.disabled,o=t.vModel,l='size="'.concat(e.size,'"'),n=e.min?':min="'.concat(e.min,'"'):"",i=e.max?':max="'.concat(e.max,'"'):"",c=z(e);return c&&(c="\n".concat(c,"\n")),"<".concat(e.tag," ").concat(o," ").concat(n," ").concat(i," ").concat(l," ").concat(a,">").concat(c,"</").concat(e.tag,">")},"el-switch":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=e["active-text"]?'active-text="'.concat(e["active-text"],'"'):"",n=e["inactive-text"]?'inactive-text="'.concat(e["inactive-text"],'"'):"",i=e["active-color"]?'active-color="'.concat(e["active-color"],'"'):"",c=e["inactive-color"]?'inactive-color="'.concat(e["inactive-color"],'"'):"",r=!0!==e["active-value"]?":active-value='".concat(JSON.stringify(e["active-value"]),"'"):"",s=!1!==e["inactive-value"]?":inactive-value='".concat(JSON.stringify(e["inactive-value"]),"'"):"";return"<".concat(e.tag," ").concat(o," ").concat(l," ").concat(n," ").concat(i," ").concat(c," ").concat(r," ").concat(s," ").concat(a,"></").concat(e.tag,">")},"el-cascader":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=t.clearable,n=t.placeholder,i=t.width,c=e.options?':options="'.concat(e.vModel,'Options"'):"",r=e.props?':props="'.concat(e.vModel,'Props"'):"",s=e["show-all-levels"]?"":':show-all-levels="false"',d=e.filterable?"filterable":"",u="/"===e.separator?"":'separator="'.concat(e.separator,'"');return"<".concat(e.tag," ").concat(o," ").concat(c," ").concat(r," ").concat(i," ").concat(s," ").concat(n," ").concat(u," ").concat(d," ").concat(l," ").concat(a,"></").concat(e.tag,">")},"el-slider":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=e.min?":min='".concat(e.min,"'"):"",n=e.max?":max='".concat(e.max,"'"):"",i=e.step?":step='".concat(e.step,"'"):"",c=e.range?"range":"",r=e["show-stops"]?':show-stops="'.concat(e["show-stops"],'"'):"";return"<".concat(e.tag," ").concat(l," ").concat(n," ").concat(i," ").concat(o," ").concat(c," ").concat(r," ").concat(a,"></").concat(e.tag,">")},"el-time-picker":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=t.clearable,n=t.placeholder,i=t.width,c=e["start-placeholder"]?'start-placeholder="'.concat(e["start-placeholder"],'"'):"",r=e["end-placeholder"]?'end-placeholder="'.concat(e["end-placeholder"],'"'):"",s=e["range-separator"]?'range-separator="'.concat(e["range-separator"],'"'):"",d=e["is-range"]?"is-range":"",u=e.format?'format="'.concat(e.format,'"'):"",p=e["value-format"]?'value-format="'.concat(e["value-format"],'"'):"",m=e["picker-options"]?":picker-options='".concat(JSON.stringify(e["picker-options"]),"'"):"";return"<".concat(e.tag," ").concat(o," ").concat(d," ").concat(u," ").concat(p," ").concat(m," ").concat(i," ").concat(n," ").concat(c," ").concat(r," ").concat(s," ").concat(l," ").concat(a,"></").concat(e.tag,">")},"el-date-picker":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=t.clearable,n=t.placeholder,i=t.width,c=e["start-placeholder"]?'start-placeholder="'.concat(e["start-placeholder"],'"'):"",r=e["end-placeholder"]?'end-placeholder="'.concat(e["end-placeholder"],'"'):"",s=e["range-separator"]?'range-separator="'.concat(e["range-separator"],'"'):"",d=e.format?'format="'.concat(e.format,'"'):"",u=e["value-format"]?'value-format="'.concat(e["value-format"],'"'):"",p="date"===e.type?"":'type="'.concat(e.type,'"'),m=e.readonly?"readonly":"";return"<".concat(e.tag," ").concat(p," ").concat(o," ").concat(d," ").concat(u," ").concat(i," ").concat(n," ").concat(c," ").concat(r," ").concat(s," ").concat(l," ").concat(m," ").concat(a,"></").concat(e.tag,">")},"el-rate":function(e){var t=$(e),a=t.disabled,o=t.vModel,l=(e.max&&":max='".concat(e.max,"'"),e["allow-half"]?"allow-half":""),n=e["show-text"]?"show-text":"",i=e["show-score"]?"show-score":"";return"<".concat(e.tag," ").concat(o," ").concat(l," ").concat(n," ").concat(i," ").concat(a,"></").concat(e.tag,">")},"el-color-picker":function(e){var t=$(e),a=t.disabled,o=t.vModel,l='size="'.concat(e.size,'"'),n=e["show-alpha"]?"show-alpha":"",i=e["color-format"]?'color-format="'.concat(e["color-format"],'"'):"";return"<".concat(e.tag," ").concat(o," ").concat(l," ").concat(n," ").concat(i," ").concat(a,"></").concat(e.tag,">")},"el-upload":function(e){var t=e.disabled?":disabled='true'":"",a=e.action?':action="'.concat(e.vModel,'Action"'):"",o=e.multiple?"multiple":"",l="text"!==e["list-type"]?'list-type="'.concat(e["list-type"],'"'):"",n=e.accept?'accept="'.concat(e.accept,'"'):"",i="file"!==e.name?'name="'.concat(e.name,'"'):"",c=!1===e["auto-upload"]?':auto-upload="false"':"",r=':before-upload="'.concat(e.vModel,'BeforeUpload"'),s=':file-list="'.concat(e.vModel,'fileList"'),d='ref="'.concat(e.vModel,'"'),u=L(e);return u&&(u="\n".concat(u,"\n")),"<".concat(e.tag," ").concat(d," ").concat(s," ").concat(a," ").concat(c," ").concat(o," ").concat(r," ").concat(l," ").concat(n," ").concat(i," ").concat(t,">").concat(u,"</").concat(e.tag,">")}};function $(e){return{vModel:'v-model="'.concat(o.formModel,".").concat(e.vModel,'"'),clearable:e.clearable?"clearable":"",placeholder:e.placeholder?'placeholder="'.concat(e.placeholder,'"'):"",width:e.style&&e.style.width?":style=\"{width: '100%'}\"":"",disabled:e.disabled?":disabled='true'":""}}function I(e){var t=[];return e.default&&t.push(e.default),t.join("\n")}function T(e){var t=[];return e.prepend&&t.push('<template slot="prepend">'.concat(e.prepend,"</template>")),e.append&&t.push('<template slot="append">'.concat(e.append,"</template>")),t.join("\n")}function j(e){var t=[];return e.options&&e.options.length&&t.push('<el-option v-for="(item, index) in '.concat(e.vModel,'Options" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>')),t.join("\n")}function E(e){var t=[];if(e.options&&e.options.length){var a="button"===e.optionType?"el-radio-button":"el-radio",o=e.border?"border":"";t.push("<".concat(a,' v-for="(item, index) in ').concat(e.vModel,'Options" :key="index" :label="item.value" :disabled="item.disabled" ').concat(o,">{{item.label}}</").concat(a,">"))}return t.join("\n")}function z(e){var t=[];if(e.options&&e.options.length){var a="button"===e.optionType?"el-checkbox-button":"el-checkbox",o=e.border?"border":"";t.push("<".concat(a,' v-for="(item, index) in ').concat(e.vModel,'Options" :key="index" :label="item.value" :disabled="item.disabled" ').concat(o,">{{item.label}}</").concat(a,">"))}return t.join("\n")}function L(e){var t=[];return"picture-card"===e["list-type"]?t.push('<i class="el-icon-plus"></i>'):t.push('<el-button size="small" type="primary" icon="el-icon-upload">'.concat(e.buttonText,"</el-button>")),e.showTip&&t.push('<div slot="tip" class="el-upload__tip">只能上传不超过 '.concat(e.fileSize).concat(e.sizeUnit," 的").concat(e.accept,"文件</div>")),t.join("\n")}function N(e,t){var a=[];o=e,l=e.fields.some((function(e){return 24!==e.span})),e.fields.forEach((function(e){a.push(O[e.layout](e))}));var n=a.join("\n"),i=x(e,n,t);return"dialog"===t&&(i=g(i)),o=null,i}var P=a("80de"),R={"el-rate":".el-rate{display: inline-block; vertical-align: text-top;}","el-upload":".el-upload__tip{line-height: 1.2;}"};function A(e,t){var a=R[t.tag];a&&-1===e.indexOf(a)&&e.push(a),t.children&&t.children.forEach((function(t){return A(e,t)}))}function V(e){var t=[];return e.fields.forEach((function(e){return A(t,e)})),t.join("\n")}var W,q,K=[{layout:"colFormItem",tagIcon:"input",label:"手机号",vModel:"mobile",formId:6,tag:"el-input",placeholder:"请输入手机号",defaultValue:"",span:24,style:{width:"100%"},clearable:!0,prepend:"",append:"","prefix-icon":"el-icon-mobile","suffix-icon":"",maxlength:11,"show-word-limit":!0,readonly:!1,disabled:!1,required:!0,changeTag:!0,regList:[{pattern:"/^1(3|4|5|7|8|9)\\d{9}$/",message:"手机号格式错误"}]}],B=a("81a5"),F=a.n(B),S=a("a92a"),U=a("4923"),G={components:{draggable:d.a,render:v["a"],RightPanel:b["default"],CodeTypeDialog:S["default"],DraggableItem:U["default"]},data:function(){return{logo:F.a,idGlobal:100,formConf:h["a"],inputComponents:h["b"],selectComponents:h["d"],layoutComponents:h["c"],labelWidth:100,drawingList:K,drawingData:{},activeId:K[0].formId,drawerVisible:!1,formData:{},dialogVisible:!1,generateConf:null,showFileName:!1,activeData:K[0]}},created:function(){document.body.ondrop=function(e){e.preventDefault(),e.stopPropagation()}},watch:{"activeData.label":function(e,t){void 0!==this.activeData.placeholder&&this.activeData.tag&&W===this.activeId&&(this.activeData.placeholder=this.activeData.placeholder.replace(t,"")+e)},activeId:{handler:function(e){W=e},immediate:!0}},mounted:function(){var e=this,t=new f.a("#copyNode",{text:function(t){var a=e.generateCode();return e.$notify({title:"成功",message:"代码已复制到剪切板，可粘贴。",type:"success"}),a}});t.on("error",(function(t){e.$message.error("代码复制失败")}))},methods:{activeFormItem:function(e){this.activeData=e,this.activeId=e.formId},onEnd:function(e,t){e.from!==e.to&&(this.activeData=q,this.activeId=this.idGlobal)},addComponent:function(e){var t=this.cloneComponent(e);this.drawingList.push(t),this.activeFormItem(t)},cloneComponent:function(e){var t=JSON.parse(JSON.stringify(e));return t.formId=++this.idGlobal,t.span=h["a"].span,t.renderKey=+new Date,t.layout||(t.layout="colFormItem"),"colFormItem"===t.layout?(t.vModel="field".concat(this.idGlobal),void 0!==t.placeholder&&(t.placeholder+=t.label),q=t):"rowFormItem"===t.layout&&(delete t.label,t.componentName="row".concat(this.idGlobal),t.gutter=this.formConf.gutter,q=t),q},AssembleFormData:function(){this.formData=Object(r["a"])({fields:JSON.parse(JSON.stringify(this.drawingList))},this.formConf)},generate:function(e){var t=this["exec".concat(Object(_["f"])(this.operationType))];this.generateConf=e,t&&t(e)},execRun:function(e){this.AssembleFormData(),this.drawerVisible=!0},execDownload:function(e){var t=this.generateCode(),a=new Blob([t],{type:"text/plain;charset=utf-8"});this.$download.saveAs(a,e.fileName)},execCopy:function(e){document.getElementById("copyNode").click()},empty:function(){var e=this;this.$confirm("确定要清空所有组件吗？","提示",{type:"warning"}).then((function(){e.drawingList=[]}))},drawingItemCopy:function(e,t){var a=JSON.parse(JSON.stringify(e));a=this.createIdAndKey(a),t.push(a),this.activeFormItem(a)},createIdAndKey:function(e){var t=this;return e.formId=++this.idGlobal,e.renderKey=+new Date,"colFormItem"===e.layout?e.vModel="field".concat(this.idGlobal):"rowFormItem"===e.layout&&(e.componentName="row".concat(this.idGlobal)),Array.isArray(e.children)&&(e.children=e.children.map((function(e){return t.createIdAndKey(e)}))),e},drawingItemDelete:function(e,t){var a=this;t.splice(e,1),this.$nextTick((function(){var e=a.drawingList.length;e&&a.activeFormItem(a.drawingList[e-1])}))},generateCode:function(){var e=this.generateConf.type;this.AssembleFormData();var t=y(Object(P["a"])(this.formData,e)),a=D(N(this.formData,e)),o=w(V(this.formData));return p.a.html(a+t+o,_["a"].html)},download:function(){this.dialogVisible=!0,this.showFileName=!0,this.operationType="download"},run:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType="run"},copy:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType="copy"},tagChange:function(e){var t=this;e=this.cloneComponent(e),e.vModel=this.activeData.vModel,e.formId=this.activeId,e.span=this.activeData.span,delete this.activeData.tag,delete this.activeData.tagIcon,delete this.activeData.document,Object.keys(e).forEach((function(a){void 0!==t.activeData[a]&&Object(c["a"])(t.activeData[a])===Object(c["a"])(e[a])&&(e[a]=t.activeData[a])})),this.activeData=e,this.updateDrawingList(e,this.drawingList)},updateDrawingList:function(e,t){var a=this,o=t.findIndex((function(e){return e.formId===a.activeId}));o>-1?t.splice(o,1,e):t.forEach((function(t){Array.isArray(t.children)&&a.updateDrawingList(e,t.children)}))}}},J=G,H=(a("e625"),a("2877")),Q=Object(H["a"])(J,n,i,!1,null,null,null);t["default"]=Q.exports},"2e2a":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"e",(function(){return c}));var o={formRef:"elForm",formModel:"formData",size:"medium",labelPosition:"right",labelWidth:100,formRules:"rules",gutter:15,disabled:!1,span:24,formBtns:!0},l=[{label:"单行文本",tag:"el-input",tagIcon:"input",placeholder:"请输入",defaultValue:void 0,span:24,labelWidth:null,style:{width:"100%"},clearable:!0,prepend:"",append:"","prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/input"},{label:"多行文本",tag:"el-input",tagIcon:"textarea",type:"textarea",placeholder:"请输入",defaultValue:void 0,span:24,labelWidth:null,autosize:{minRows:4,maxRows:4},style:{width:"100%"},maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/input"},{label:"密码",tag:"el-input",tagIcon:"password",placeholder:"请输入",defaultValue:void 0,span:24,"show-password":!0,labelWidth:null,style:{width:"100%"},clearable:!0,prepend:"",append:"","prefix-icon":"","suffix-icon":"",maxlength:null,"show-word-limit":!1,readonly:!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/input"},{label:"计数器",tag:"el-input-number",tagIcon:"number",placeholder:"",defaultValue:void 0,span:24,labelWidth:null,min:void 0,max:void 0,step:void 0,"step-strictly":!1,precision:void 0,"controls-position":"",disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/input-number"}],n=[{label:"下拉选择",tag:"el-select",tagIcon:"select",placeholder:"请选择",defaultValue:void 0,span:24,labelWidth:null,style:{width:"100%"},clearable:!0,disabled:!1,required:!0,filterable:!1,multiple:!1,options:[{label:"选项一",value:1},{label:"选项二",value:2}],regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/select"},{label:"级联选择",tag:"el-cascader",tagIcon:"cascader",placeholder:"请选择",defaultValue:[],span:24,labelWidth:null,style:{width:"100%"},props:{props:{multiple:!1}},"show-all-levels":!0,disabled:!1,clearable:!0,filterable:!1,required:!0,options:[{id:1,value:1,label:"选项1",children:[{id:2,value:2,label:"选项1-1"}]}],dataType:"dynamic",labelKey:"label",valueKey:"value",childrenKey:"children",separator:"/",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/cascader"},{label:"单选框组",tag:"el-radio-group",tagIcon:"radio",defaultValue:void 0,span:24,labelWidth:null,style:{},optionType:"default",border:!1,size:"medium",disabled:!1,required:!0,options:[{label:"选项一",value:1},{label:"选项二",value:2}],regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/radio"},{label:"多选框组",tag:"el-checkbox-group",tagIcon:"checkbox",defaultValue:[],span:24,labelWidth:null,style:{},optionType:"default",border:!1,size:"medium",disabled:!1,required:!0,options:[{label:"选项一",value:1},{label:"选项二",value:2}],regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/checkbox"},{label:"开关",tag:"el-switch",tagIcon:"switch",defaultValue:!1,span:24,labelWidth:null,style:{},disabled:!1,required:!0,"active-text":"","inactive-text":"","active-color":null,"inactive-color":null,"active-value":!0,"inactive-value":!1,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/switch"},{label:"滑块",tag:"el-slider",tagIcon:"slider",defaultValue:null,span:24,labelWidth:null,disabled:!1,required:!0,min:0,max:100,step:1,"show-stops":!1,range:!1,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/slider"},{label:"时间选择",tag:"el-time-picker",tagIcon:"time",placeholder:"请选择",defaultValue:null,span:24,labelWidth:null,style:{width:"100%"},disabled:!1,clearable:!0,required:!0,"picker-options":{selectableRange:"00:00:00-23:59:59"},format:"HH:mm:ss","value-format":"HH:mm:ss",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/time-picker"},{label:"时间范围",tag:"el-time-picker",tagIcon:"time-range",defaultValue:null,span:24,labelWidth:null,style:{width:"100%"},disabled:!1,clearable:!0,required:!0,"is-range":!0,"range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"HH:mm:ss","value-format":"HH:mm:ss",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/time-picker"},{label:"日期选择",tag:"el-date-picker",tagIcon:"date",placeholder:"请选择",defaultValue:null,type:"date",span:24,labelWidth:null,style:{width:"100%"},disabled:!1,clearable:!0,required:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",readonly:!1,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/date-picker"},{label:"日期范围",tag:"el-date-picker",tagIcon:"date-range",defaultValue:null,span:24,labelWidth:null,style:{width:"100%"},type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:!1,clearable:!0,required:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",readonly:!1,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/date-picker"},{label:"评分",tag:"el-rate",tagIcon:"rate",defaultValue:0,span:24,labelWidth:null,style:{},max:5,"allow-half":!1,"show-text":!1,"show-score":!1,disabled:!1,required:!0,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/rate"},{label:"颜色选择",tag:"el-color-picker",tagIcon:"color",defaultValue:null,labelWidth:null,"show-alpha":!1,"color-format":"",disabled:!1,required:!0,size:"medium",regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/color-picker"},{label:"上传",tag:"el-upload",tagIcon:"upload",action:"https://jsonplaceholder.typicode.com/posts/",defaultValue:null,labelWidth:null,disabled:!1,required:!0,accept:"",name:"file","auto-upload":!0,showTip:!1,buttonText:"点击上传",fileSize:2,sizeUnit:"MB","list-type":"text",multiple:!1,regList:[],changeTag:!0,document:"https://element.eleme.cn/#/zh-CN/component/upload"}],i=[{layout:"rowFormItem",tagIcon:"row",type:"default",justify:"start",align:"top",label:"行容器",layoutTree:!0,children:[],document:"https://element.eleme.cn/#/zh-CN/component/layout"},{layout:"colFormItem",label:"按钮",changeTag:!0,labelWidth:null,tag:"el-button",tagIcon:"button",span:24,default:"主要按钮",type:"primary",icon:"el-icon-search",size:"medium",disabled:!1,document:"https://element.eleme.cn/#/zh-CN/component/button"}],c={"el-input":"blur","el-input-number":"blur","el-select":"change","el-radio-group":"change","el-checkbox-group":"change","el-cascader":"change","el-time-picker":"change","el-date-picker":"change","el-rate":"change"}},"35c4":function(e,t,a){},4923:function(e,t,a){"use strict";a.r(t);a("d9e2"),a("d81d");var o=a("b76a"),l=a.n(o),n=a("a85b"),i={itemBtns:function(e,t,a,o){var l=this.$listeners,n=l.copyItem,i=l.deleteItem;return[e("span",{class:"drawing-item-copy",attrs:{title:"复制"},on:{click:function(e){n(t,o),e.stopPropagation()}}},[e("i",{class:"el-icon-copy-document"})]),e("span",{class:"drawing-item-delete",attrs:{title:"删除"},on:{click:function(e){i(a,o),e.stopPropagation()}}},[e("i",{class:"el-icon-delete"})])]}},c={colFormItem:function(e,t,a,o){var l=this,c=this.$listeners.activeItem,r=this.activeId===t.formId?"drawing-item active-from-item":"drawing-item";return this.formConf.unFocusedComponentBorder&&(r+=" unfocus-bordered"),e("el-col",{attrs:{span:t.span},class:r,nativeOn:{click:function(e){c(t),e.stopPropagation()}}},[e("el-form-item",{attrs:{"label-width":t.labelWidth?"".concat(t.labelWidth,"px"):null,label:t.label,required:t.required}},[e(n["a"],{key:t.renderKey,attrs:{conf:t},on:{input:function(e){l.$set(t,"defaultValue",e)}}})]),i.itemBtns.apply(this,arguments)])},rowFormItem:function(e,t,a,o){var n=this.$listeners.activeItem,c=this.activeId===t.formId?"drawing-row-item active-from-item":"drawing-row-item",s=r.apply(this,arguments);return"flex"===t.type&&(s=e("el-row",{attrs:{type:t.type,justify:t.justify,align:t.align}},[s])),e("el-col",{attrs:{span:t.span}},[e("el-row",{attrs:{gutter:t.gutter},class:c,nativeOn:{click:function(e){n(t),e.stopPropagation()}}},[e("span",{class:"component-name"},[t.componentName]),e(l.a,{attrs:{list:t.children,animation:340,group:"componentsGroup"},class:"drag-wrapper"},[s]),i.itemBtns.apply(this,arguments)])])}};function r(e,t,a,o){var l=this;return Array.isArray(t.children)?t.children.map((function(a,o){var n=c[a.layout];return n?n.call(l,e,a,o,t.children):s()})):null}function s(){throw new Error("没有与".concat(this.element.layout,"匹配的layout"))}var d,u,p={components:{render:n["a"],draggable:l.a},props:["element","index","drawingList","activeId","formConf"],render:function(e){var t=c[this.element.layout];return t?t.call(this,e,this.element,this.index,this.drawingList):s()}},m=p,f=a("2877"),v=Object(f["a"])(m,d,u,!1,null,null,null);t["default"]=v.exports},"6d81":function(e,t,a){},"766b":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-board"},[a("el-tabs",{staticClass:"center-tabs",model:{value:e.currentTab,callback:function(t){e.currentTab=t},expression:"currentTab"}},[a("el-tab-pane",{attrs:{label:"组件属性",name:"field"}}),a("el-tab-pane",{attrs:{label:"表单属性",name:"form"}})],1),a("div",{staticClass:"field-box"},[a("a",{staticClass:"document-link",attrs:{target:"_blank",href:e.documentLink,title:"查看组件文档"}},[a("i",{staticClass:"el-icon-link"})]),a("el-scrollbar",{staticClass:"right-scrollbar"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:"field"===e.currentTab&&e.showField,expression:"currentTab==='field' && showField"}],attrs:{size:"small","label-width":"90px"}},[e.activeData.changeTag?a("el-form-item",{attrs:{label:"组件类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择组件类型"},on:{change:e.tagChange},model:{value:e.activeData.tagIcon,callback:function(t){e.$set(e.activeData,"tagIcon",t)},expression:"activeData.tagIcon"}},e._l(e.tagList,(function(t){return a("el-option-group",{key:t.label,attrs:{label:t.label}},e._l(t.options,(function(t){return a("el-option",{key:t.label,attrs:{label:t.label,value:t.tagIcon}},[a("svg-icon",{staticClass:"node-icon",attrs:{"icon-class":t.tagIcon}}),a("span",[e._v(" "+e._s(t.label))])],1)})),1)})),1)],1):e._e(),void 0!==e.activeData.vModel?a("el-form-item",{attrs:{label:"字段名"}},[a("el-input",{attrs:{placeholder:"请输入字段名（v-model）"},model:{value:e.activeData.vModel,callback:function(t){e.$set(e.activeData,"vModel",t)},expression:"activeData.vModel"}})],1):e._e(),void 0!==e.activeData.componentName?a("el-form-item",{attrs:{label:"组件名"}},[e._v(" "+e._s(e.activeData.componentName)+" ")]):e._e(),void 0!==e.activeData.label?a("el-form-item",{attrs:{label:"标题"}},[a("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.activeData.label,callback:function(t){e.$set(e.activeData,"label",t)},expression:"activeData.label"}})],1):e._e(),void 0!==e.activeData.placeholder?a("el-form-item",{attrs:{label:"占位提示"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData.placeholder,callback:function(t){e.$set(e.activeData,"placeholder",t)},expression:"activeData.placeholder"}})],1):e._e(),void 0!==e.activeData["start-placeholder"]?a("el-form-item",{attrs:{label:"开始占位"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData["start-placeholder"],callback:function(t){e.$set(e.activeData,"start-placeholder",t)},expression:"activeData['start-placeholder']"}})],1):e._e(),void 0!==e.activeData["end-placeholder"]?a("el-form-item",{attrs:{label:"结束占位"}},[a("el-input",{attrs:{placeholder:"请输入占位提示"},model:{value:e.activeData["end-placeholder"],callback:function(t){e.$set(e.activeData,"end-placeholder",t)},expression:"activeData['end-placeholder']"}})],1):e._e(),void 0!==e.activeData.span?a("el-form-item",{attrs:{label:"表单栅格"}},[a("el-slider",{attrs:{max:24,min:1,marks:{12:""}},on:{change:e.spanChange},model:{value:e.activeData.span,callback:function(t){e.$set(e.activeData,"span",t)},expression:"activeData.span"}})],1):e._e(),"rowFormItem"===e.activeData.layout?a("el-form-item",{attrs:{label:"栅格间隔"}},[a("el-input-number",{attrs:{min:0,placeholder:"栅格间隔"},model:{value:e.activeData.gutter,callback:function(t){e.$set(e.activeData,"gutter",t)},expression:"activeData.gutter"}})],1):e._e(),"rowFormItem"===e.activeData.layout?a("el-form-item",{attrs:{label:"布局模式"}},[a("el-radio-group",{model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},[a("el-radio-button",{attrs:{label:"default"}}),a("el-radio-button",{attrs:{label:"flex"}})],1)],1):e._e(),void 0!==e.activeData.justify&&"flex"===e.activeData.type?a("el-form-item",{attrs:{label:"水平排列"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择水平排列"},model:{value:e.activeData.justify,callback:function(t){e.$set(e.activeData,"justify",t)},expression:"activeData.justify"}},e._l(e.justifyOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0!==e.activeData.align&&"flex"===e.activeData.type?a("el-form-item",{attrs:{label:"垂直排列"}},[a("el-radio-group",{model:{value:e.activeData.align,callback:function(t){e.$set(e.activeData,"align",t)},expression:"activeData.align"}},[a("el-radio-button",{attrs:{label:"top"}}),a("el-radio-button",{attrs:{label:"middle"}}),a("el-radio-button",{attrs:{label:"bottom"}})],1)],1):e._e(),void 0!==e.activeData.labelWidth?a("el-form-item",{attrs:{label:"标签宽度"}},[a("el-input",{attrs:{type:"number",placeholder:"请输入标签宽度"},model:{value:e.activeData.labelWidth,callback:function(t){e.$set(e.activeData,"labelWidth",e._n(t))},expression:"activeData.labelWidth"}})],1):e._e(),e.activeData.style&&void 0!==e.activeData.style.width?a("el-form-item",{attrs:{label:"组件宽度"}},[a("el-input",{attrs:{placeholder:"请输入组件宽度",clearable:""},model:{value:e.activeData.style.width,callback:function(t){e.$set(e.activeData.style,"width",t)},expression:"activeData.style.width"}})],1):e._e(),void 0!==e.activeData.vModel?a("el-form-item",{attrs:{label:"默认值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData.defaultValue),placeholder:"请输入默认值"},on:{input:e.onDefaultValueInput}})],1):e._e(),"el-checkbox-group"===e.activeData.tag?a("el-form-item",{attrs:{label:"至少应选"}},[a("el-input-number",{attrs:{value:e.activeData.min,min:0,placeholder:"至少应选"},on:{input:function(t){return e.$set(e.activeData,"min",t||void 0)}}})],1):e._e(),"el-checkbox-group"===e.activeData.tag?a("el-form-item",{attrs:{label:"最多可选"}},[a("el-input-number",{attrs:{value:e.activeData.max,min:0,placeholder:"最多可选"},on:{input:function(t){return e.$set(e.activeData,"max",t||void 0)}}})],1):e._e(),void 0!==e.activeData.prepend?a("el-form-item",{attrs:{label:"前缀"}},[a("el-input",{attrs:{placeholder:"请输入前缀"},model:{value:e.activeData.prepend,callback:function(t){e.$set(e.activeData,"prepend",t)},expression:"activeData.prepend"}})],1):e._e(),void 0!==e.activeData.append?a("el-form-item",{attrs:{label:"后缀"}},[a("el-input",{attrs:{placeholder:"请输入后缀"},model:{value:e.activeData.append,callback:function(t){e.$set(e.activeData,"append",t)},expression:"activeData.append"}})],1):e._e(),void 0!==e.activeData["prefix-icon"]?a("el-form-item",{attrs:{label:"前图标"}},[a("el-input",{attrs:{placeholder:"请输入前图标名称"},model:{value:e.activeData["prefix-icon"],callback:function(t){e.$set(e.activeData,"prefix-icon",t)},expression:"activeData['prefix-icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("prefix-icon")}},slot:"append"},[e._v(" 选择 ")])],1)],1):e._e(),void 0!==e.activeData["suffix-icon"]?a("el-form-item",{attrs:{label:"后图标"}},[a("el-input",{attrs:{placeholder:"请输入后图标名称"},model:{value:e.activeData["suffix-icon"],callback:function(t){e.$set(e.activeData,"suffix-icon",t)},expression:"activeData['suffix-icon']"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:function(t){return e.openIconsDialog("suffix-icon")}},slot:"append"},[e._v(" 选择 ")])],1)],1):e._e(),"el-cascader"===e.activeData.tag?a("el-form-item",{attrs:{label:"选项分隔符"}},[a("el-input",{attrs:{placeholder:"请输入选项分隔符"},model:{value:e.activeData.separator,callback:function(t){e.$set(e.activeData,"separator",t)},expression:"activeData.separator"}})],1):e._e(),void 0!==e.activeData.autosize?a("el-form-item",{attrs:{label:"最小行数"}},[a("el-input-number",{attrs:{min:1,placeholder:"最小行数"},model:{value:e.activeData.autosize.minRows,callback:function(t){e.$set(e.activeData.autosize,"minRows",t)},expression:"activeData.autosize.minRows"}})],1):e._e(),void 0!==e.activeData.autosize?a("el-form-item",{attrs:{label:"最大行数"}},[a("el-input-number",{attrs:{min:1,placeholder:"最大行数"},model:{value:e.activeData.autosize.maxRows,callback:function(t){e.$set(e.activeData.autosize,"maxRows",t)},expression:"activeData.autosize.maxRows"}})],1):e._e(),void 0!==e.activeData.min?a("el-form-item",{attrs:{label:"最小值"}},[a("el-input-number",{attrs:{placeholder:"最小值"},model:{value:e.activeData.min,callback:function(t){e.$set(e.activeData,"min",t)},expression:"activeData.min"}})],1):e._e(),void 0!==e.activeData.max?a("el-form-item",{attrs:{label:"最大值"}},[a("el-input-number",{attrs:{placeholder:"最大值"},model:{value:e.activeData.max,callback:function(t){e.$set(e.activeData,"max",t)},expression:"activeData.max"}})],1):e._e(),void 0!==e.activeData.step?a("el-form-item",{attrs:{label:"步长"}},[a("el-input-number",{attrs:{placeholder:"步数"},model:{value:e.activeData.step,callback:function(t){e.$set(e.activeData,"step",t)},expression:"activeData.step"}})],1):e._e(),"el-input-number"===e.activeData.tag?a("el-form-item",{attrs:{label:"精度"}},[a("el-input-number",{attrs:{min:0,placeholder:"精度"},model:{value:e.activeData.precision,callback:function(t){e.$set(e.activeData,"precision",t)},expression:"activeData.precision"}})],1):e._e(),"el-input-number"===e.activeData.tag?a("el-form-item",{attrs:{label:"按钮位置"}},[a("el-radio-group",{model:{value:e.activeData["controls-position"],callback:function(t){e.$set(e.activeData,"controls-position",t)},expression:"activeData['controls-position']"}},[a("el-radio-button",{attrs:{label:""}},[e._v(" 默认 ")]),a("el-radio-button",{attrs:{label:"right"}},[e._v(" 右侧 ")])],1)],1):e._e(),void 0!==e.activeData.maxlength?a("el-form-item",{attrs:{label:"最多输入"}},[a("el-input",{attrs:{placeholder:"请输入字符长度"},model:{value:e.activeData.maxlength,callback:function(t){e.$set(e.activeData,"maxlength",t)},expression:"activeData.maxlength"}},[a("template",{slot:"append"},[e._v(" 个字符 ")])],2)],1):e._e(),void 0!==e.activeData["active-text"]?a("el-form-item",{attrs:{label:"开启提示"}},[a("el-input",{attrs:{placeholder:"请输入开启提示"},model:{value:e.activeData["active-text"],callback:function(t){e.$set(e.activeData,"active-text",t)},expression:"activeData['active-text']"}})],1):e._e(),void 0!==e.activeData["inactive-text"]?a("el-form-item",{attrs:{label:"关闭提示"}},[a("el-input",{attrs:{placeholder:"请输入关闭提示"},model:{value:e.activeData["inactive-text"],callback:function(t){e.$set(e.activeData,"inactive-text",t)},expression:"activeData['inactive-text']"}})],1):e._e(),void 0!==e.activeData["active-value"]?a("el-form-item",{attrs:{label:"开启值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData["active-value"]),placeholder:"请输入开启值"},on:{input:function(t){return e.onSwitchValueInput(t,"active-value")}}})],1):e._e(),void 0!==e.activeData["inactive-value"]?a("el-form-item",{attrs:{label:"关闭值"}},[a("el-input",{attrs:{value:e.setDefaultValue(e.activeData["inactive-value"]),placeholder:"请输入关闭值"},on:{input:function(t){return e.onSwitchValueInput(t,"inactive-value")}}})],1):e._e(),void 0!==e.activeData.type&&"el-date-picker"===e.activeData.tag?a("el-form-item",{attrs:{label:"时间类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择时间类型"},on:{change:e.dateTypeChange},model:{value:e.activeData.type,callback:function(t){e.$set(e.activeData,"type",t)},expression:"activeData.type"}},e._l(e.dateOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0!==e.activeData.name?a("el-form-item",{attrs:{label:"文件字段名"}},[a("el-input",{attrs:{placeholder:"请输入上传文件字段名"},model:{value:e.activeData.name,callback:function(t){e.$set(e.activeData,"name",t)},expression:"activeData.name"}})],1):e._e(),void 0!==e.activeData.accept?a("el-form-item",{attrs:{label:"文件类型"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择文件类型",clearable:""},model:{value:e.activeData.accept,callback:function(t){e.$set(e.activeData,"accept",t)},expression:"activeData.accept"}},[a("el-option",{attrs:{label:"图片",value:"image/*"}}),a("el-option",{attrs:{label:"视频",value:"video/*"}}),a("el-option",{attrs:{label:"音频",value:"audio/*"}}),a("el-option",{attrs:{label:"excel",value:".xls,.xlsx"}}),a("el-option",{attrs:{label:"word",value:".doc,.docx"}}),a("el-option",{attrs:{label:"pdf",value:".pdf"}}),a("el-option",{attrs:{label:"txt",value:".txt"}})],1)],1):e._e(),void 0!==e.activeData.fileSize?a("el-form-item",{attrs:{label:"文件大小"}},[a("el-input",{attrs:{placeholder:"请输入文件大小"},model:{value:e.activeData.fileSize,callback:function(t){e.$set(e.activeData,"fileSize",e._n(t))},expression:"activeData.fileSize"}},[a("el-select",{style:{width:"66px"},attrs:{slot:"append"},slot:"append",model:{value:e.activeData.sizeUnit,callback:function(t){e.$set(e.activeData,"sizeUnit",t)},expression:"activeData.sizeUnit"}},[a("el-option",{attrs:{label:"KB",value:"KB"}}),a("el-option",{attrs:{label:"MB",value:"MB"}}),a("el-option",{attrs:{label:"GB",value:"GB"}})],1)],1)],1):e._e(),void 0!==e.activeData.action?a("el-form-item",{attrs:{label:"上传地址"}},[a("el-input",{attrs:{placeholder:"请输入上传地址",clearable:""},model:{value:e.activeData.action,callback:function(t){e.$set(e.activeData,"action",t)},expression:"activeData.action"}})],1):e._e(),void 0!==e.activeData["list-type"]?a("el-form-item",{attrs:{label:"列表类型"}},[a("el-radio-group",{attrs:{size:"small"},model:{value:e.activeData["list-type"],callback:function(t){e.$set(e.activeData,"list-type",t)},expression:"activeData['list-type']"}},[a("el-radio-button",{attrs:{label:"text"}},[e._v(" text ")]),a("el-radio-button",{attrs:{label:"picture"}},[e._v(" picture ")]),a("el-radio-button",{attrs:{label:"picture-card"}},[e._v(" picture-card ")])],1)],1):e._e(),void 0!==e.activeData.buttonText?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"picture-card"!==e.activeData["list-type"],expression:"'picture-card' !== activeData['list-type']"}],attrs:{label:"按钮文字"}},[a("el-input",{attrs:{placeholder:"请输入按钮文字"},model:{value:e.activeData.buttonText,callback:function(t){e.$set(e.activeData,"buttonText",t)},expression:"activeData.buttonText"}})],1):e._e(),void 0!==e.activeData["range-separator"]?a("el-form-item",{attrs:{label:"分隔符"}},[a("el-input",{attrs:{placeholder:"请输入分隔符"},model:{value:e.activeData["range-separator"],callback:function(t){e.$set(e.activeData,"range-separator",t)},expression:"activeData['range-separator']"}})],1):e._e(),void 0!==e.activeData["picker-options"]?a("el-form-item",{attrs:{label:"时间段"}},[a("el-input",{attrs:{placeholder:"请输入时间段"},model:{value:e.activeData["picker-options"].selectableRange,callback:function(t){e.$set(e.activeData["picker-options"],"selectableRange",t)},expression:"activeData['picker-options'].selectableRange"}})],1):e._e(),void 0!==e.activeData.format?a("el-form-item",{attrs:{label:"时间格式"}},[a("el-input",{attrs:{value:e.activeData.format,placeholder:"请输入时间格式"},on:{input:function(t){return e.setTimeValue(t)}}})],1):e._e(),["el-checkbox-group","el-radio-group","el-select"].indexOf(e.activeData.tag)>-1?[a("el-divider",[e._v("选项")]),a("draggable",{attrs:{list:e.activeData.options,animation:340,group:"selectItem",handle:".option-drag"}},e._l(e.activeData.options,(function(t,o){return a("div",{key:o,staticClass:"select-item"},[a("div",{staticClass:"select-line-icon option-drag"},[a("i",{staticClass:"el-icon-s-operation"})]),a("el-input",{attrs:{placeholder:"选项名",size:"small"},model:{value:t.label,callback:function(a){e.$set(t,"label",a)},expression:"item.label"}}),a("el-input",{attrs:{placeholder:"选项值",size:"small",value:t.value},on:{input:function(a){return e.setOptionValue(t,a)}}}),a("div",{staticClass:"close-btn select-line-icon",on:{click:function(t){return e.activeData.options.splice(o,1)}}},[a("i",{staticClass:"el-icon-remove-outline"})])],1)})),0),a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{staticStyle:{"padding-bottom":"0"},attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addSelectItem}},[e._v(" 添加选项 ")])],1),a("el-divider")]:e._e(),["el-cascader"].indexOf(e.activeData.tag)>-1?[a("el-divider",[e._v("选项")]),a("el-form-item",{attrs:{label:"数据类型"}},[a("el-radio-group",{attrs:{size:"small"},model:{value:e.activeData.dataType,callback:function(t){e.$set(e.activeData,"dataType",t)},expression:"activeData.dataType"}},[a("el-radio-button",{attrs:{label:"dynamic"}},[e._v(" 动态数据 ")]),a("el-radio-button",{attrs:{label:"static"}},[e._v(" 静态数据 ")])],1)],1),"dynamic"===e.activeData.dataType?[a("el-form-item",{attrs:{label:"标签键名"}},[a("el-input",{attrs:{placeholder:"请输入标签键名"},model:{value:e.activeData.labelKey,callback:function(t){e.$set(e.activeData,"labelKey",t)},expression:"activeData.labelKey"}})],1),a("el-form-item",{attrs:{label:"值键名"}},[a("el-input",{attrs:{placeholder:"请输入值键名"},model:{value:e.activeData.valueKey,callback:function(t){e.$set(e.activeData,"valueKey",t)},expression:"activeData.valueKey"}})],1),a("el-form-item",{attrs:{label:"子级键名"}},[a("el-input",{attrs:{placeholder:"请输入子级键名"},model:{value:e.activeData.childrenKey,callback:function(t){e.$set(e.activeData,"childrenKey",t)},expression:"activeData.childrenKey"}})],1)]:e._e(),"static"===e.activeData.dataType?a("el-tree",{attrs:{draggable:"",data:e.activeData.options,"node-key":"id","expand-on-click-node":!1,"render-content":e.renderContent}}):e._e(),"static"===e.activeData.dataType?a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{staticStyle:{"padding-bottom":"0"},attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addTreeItem}},[e._v(" 添加父级 ")])],1):e._e(),a("el-divider")]:e._e(),void 0!==e.activeData.optionType?a("el-form-item",{attrs:{label:"选项样式"}},[a("el-radio-group",{model:{value:e.activeData.optionType,callback:function(t){e.$set(e.activeData,"optionType",t)},expression:"activeData.optionType"}},[a("el-radio-button",{attrs:{label:"default"}},[e._v(" 默认 ")]),a("el-radio-button",{attrs:{label:"button"}},[e._v(" 按钮 ")])],1)],1):e._e(),void 0!==e.activeData["active-color"]?a("el-form-item",{attrs:{label:"开启颜色"}},[a("el-color-picker",{model:{value:e.activeData["active-color"],callback:function(t){e.$set(e.activeData,"active-color",t)},expression:"activeData['active-color']"}})],1):e._e(),void 0!==e.activeData["inactive-color"]?a("el-form-item",{attrs:{label:"关闭颜色"}},[a("el-color-picker",{model:{value:e.activeData["inactive-color"],callback:function(t){e.$set(e.activeData,"inactive-color",t)},expression:"activeData['inactive-color']"}})],1):e._e(),void 0!==e.activeData["allow-half"]?a("el-form-item",{attrs:{label:"允许半选"}},[a("el-switch",{model:{value:e.activeData["allow-half"],callback:function(t){e.$set(e.activeData,"allow-half",t)},expression:"activeData['allow-half']"}})],1):e._e(),void 0!==e.activeData["show-text"]?a("el-form-item",{attrs:{label:"辅助文字"}},[a("el-switch",{on:{change:e.rateTextChange},model:{value:e.activeData["show-text"],callback:function(t){e.$set(e.activeData,"show-text",t)},expression:"activeData['show-text']"}})],1):e._e(),void 0!==e.activeData["show-score"]?a("el-form-item",{attrs:{label:"显示分数"}},[a("el-switch",{on:{change:e.rateScoreChange},model:{value:e.activeData["show-score"],callback:function(t){e.$set(e.activeData,"show-score",t)},expression:"activeData['show-score']"}})],1):e._e(),void 0!==e.activeData["show-stops"]?a("el-form-item",{attrs:{label:"显示间断点"}},[a("el-switch",{model:{value:e.activeData["show-stops"],callback:function(t){e.$set(e.activeData,"show-stops",t)},expression:"activeData['show-stops']"}})],1):e._e(),void 0!==e.activeData.range?a("el-form-item",{attrs:{label:"范围选择"}},[a("el-switch",{on:{change:e.rangeChange},model:{value:e.activeData.range,callback:function(t){e.$set(e.activeData,"range",t)},expression:"activeData.range"}})],1):e._e(),void 0!==e.activeData.border&&"default"===e.activeData.optionType?a("el-form-item",{attrs:{label:"是否带边框"}},[a("el-switch",{model:{value:e.activeData.border,callback:function(t){e.$set(e.activeData,"border",t)},expression:"activeData.border"}})],1):e._e(),"el-color-picker"===e.activeData.tag?a("el-form-item",{attrs:{label:"颜色格式"}},[a("el-select",{style:{width:"100%"},attrs:{placeholder:"请选择颜色格式"},on:{change:e.colorFormatChange},model:{value:e.activeData["color-format"],callback:function(t){e.$set(e.activeData,"color-format",t)},expression:"activeData['color-format']"}},e._l(e.colorFormatOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),void 0===e.activeData.size||"button"!==e.activeData.optionType&&!e.activeData.border&&"el-color-picker"!==e.activeData.tag?e._e():a("el-form-item",{attrs:{label:"选项尺寸"}},[a("el-radio-group",{model:{value:e.activeData.size,callback:function(t){e.$set(e.activeData,"size",t)},expression:"activeData.size"}},[a("el-radio-button",{attrs:{label:"medium"}},[e._v(" 中等 ")]),a("el-radio-button",{attrs:{label:"small"}},[e._v(" 较小 ")]),a("el-radio-button",{attrs:{label:"mini"}},[e._v(" 迷你 ")])],1)],1),void 0!==e.activeData["show-word-limit"]?a("el-form-item",{attrs:{label:"输入统计"}},[a("el-switch",{model:{value:e.activeData["show-word-limit"],callback:function(t){e.$set(e.activeData,"show-word-limit",t)},expression:"activeData['show-word-limit']"}})],1):e._e(),"el-input-number"===e.activeData.tag?a("el-form-item",{attrs:{label:"严格步数"}},[a("el-switch",{model:{value:e.activeData["step-strictly"],callback:function(t){e.$set(e.activeData,"step-strictly",t)},expression:"activeData['step-strictly']"}})],1):e._e(),"el-cascader"===e.activeData.tag?a("el-form-item",{attrs:{label:"是否多选"}},[a("el-switch",{model:{value:e.activeData.props.props.multiple,callback:function(t){e.$set(e.activeData.props.props,"multiple",t)},expression:"activeData.props.props.multiple"}})],1):e._e(),"el-cascader"===e.activeData.tag?a("el-form-item",{attrs:{label:"展示全路径"}},[a("el-switch",{model:{value:e.activeData["show-all-levels"],callback:function(t){e.$set(e.activeData,"show-all-levels",t)},expression:"activeData['show-all-levels']"}})],1):e._e(),"el-cascader"===e.activeData.tag?a("el-form-item",{attrs:{label:"可否筛选"}},[a("el-switch",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,"filterable",t)},expression:"activeData.filterable"}})],1):e._e(),void 0!==e.activeData.clearable?a("el-form-item",{attrs:{label:"能否清空"}},[a("el-switch",{model:{value:e.activeData.clearable,callback:function(t){e.$set(e.activeData,"clearable",t)},expression:"activeData.clearable"}})],1):e._e(),void 0!==e.activeData.showTip?a("el-form-item",{attrs:{label:"显示提示"}},[a("el-switch",{model:{value:e.activeData.showTip,callback:function(t){e.$set(e.activeData,"showTip",t)},expression:"activeData.showTip"}})],1):e._e(),void 0!==e.activeData.multiple?a("el-form-item",{attrs:{label:"多选文件"}},[a("el-switch",{model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,"multiple",t)},expression:"activeData.multiple"}})],1):e._e(),void 0!==e.activeData["auto-upload"]?a("el-form-item",{attrs:{label:"自动上传"}},[a("el-switch",{model:{value:e.activeData["auto-upload"],callback:function(t){e.$set(e.activeData,"auto-upload",t)},expression:"activeData['auto-upload']"}})],1):e._e(),void 0!==e.activeData.readonly?a("el-form-item",{attrs:{label:"是否只读"}},[a("el-switch",{model:{value:e.activeData.readonly,callback:function(t){e.$set(e.activeData,"readonly",t)},expression:"activeData.readonly"}})],1):e._e(),void 0!==e.activeData.disabled?a("el-form-item",{attrs:{label:"是否禁用"}},[a("el-switch",{model:{value:e.activeData.disabled,callback:function(t){e.$set(e.activeData,"disabled",t)},expression:"activeData.disabled"}})],1):e._e(),"el-select"===e.activeData.tag?a("el-form-item",{attrs:{label:"是否可搜索"}},[a("el-switch",{model:{value:e.activeData.filterable,callback:function(t){e.$set(e.activeData,"filterable",t)},expression:"activeData.filterable"}})],1):e._e(),"el-select"===e.activeData.tag?a("el-form-item",{attrs:{label:"是否多选"}},[a("el-switch",{on:{change:e.multipleChange},model:{value:e.activeData.multiple,callback:function(t){e.$set(e.activeData,"multiple",t)},expression:"activeData.multiple"}})],1):e._e(),void 0!==e.activeData.required?a("el-form-item",{attrs:{label:"是否必填"}},[a("el-switch",{model:{value:e.activeData.required,callback:function(t){e.$set(e.activeData,"required",t)},expression:"activeData.required"}})],1):e._e(),e.activeData.layoutTree?[a("el-divider",[e._v("布局结构树")]),a("el-tree",{attrs:{data:[e.activeData],props:e.layoutTreeProps,"node-key":"renderKey","default-expand-all":"",draggable:""},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.node,l=t.data;return a("span",{},[a("span",{staticClass:"node-label"},[a("svg-icon",{staticClass:"node-icon",attrs:{"icon-class":l.tagIcon}}),e._v(" "+e._s(o.label)+" ")],1)])}}],null,!1,921874089)})]:e._e(),"colFormItem"===e.activeData.layout&&"el-button"!==e.activeData.tag?[a("el-divider",[e._v("正则校验")]),e._l(e.activeData.regList,(function(t,o){return a("div",{key:o,staticClass:"reg-item"},[a("span",{staticClass:"close-btn",on:{click:function(t){return e.activeData.regList.splice(o,1)}}},[a("i",{staticClass:"el-icon-close"})]),a("el-form-item",{attrs:{label:"表达式"}},[a("el-input",{attrs:{placeholder:"请输入正则"},model:{value:t.pattern,callback:function(a){e.$set(t,"pattern",a)},expression:"item.pattern"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"错误提示"}},[a("el-input",{attrs:{placeholder:"请输入错误提示"},model:{value:t.message,callback:function(a){e.$set(t,"message",a)},expression:"item.message"}})],1)],1)})),a("div",{staticStyle:{"margin-left":"20px"}},[a("el-button",{attrs:{icon:"el-icon-circle-plus-outline",type:"text"},on:{click:e.addReg}},[e._v(" 添加规则 ")])],1)]:e._e()],2),a("el-form",{directives:[{name:"show",rawName:"v-show",value:"form"===e.currentTab,expression:"currentTab === 'form'"}],attrs:{size:"small","label-width":"90px"}},[a("el-form-item",{attrs:{label:"表单名"}},[a("el-input",{attrs:{placeholder:"请输入表单名（ref）"},model:{value:e.formConf.formRef,callback:function(t){e.$set(e.formConf,"formRef",t)},expression:"formConf.formRef"}})],1),a("el-form-item",{attrs:{label:"表单模型"}},[a("el-input",{attrs:{placeholder:"请输入数据模型"},model:{value:e.formConf.formModel,callback:function(t){e.$set(e.formConf,"formModel",t)},expression:"formConf.formModel"}})],1),a("el-form-item",{attrs:{label:"校验模型"}},[a("el-input",{attrs:{placeholder:"请输入校验模型"},model:{value:e.formConf.formRules,callback:function(t){e.$set(e.formConf,"formRules",t)},expression:"formConf.formRules"}})],1),a("el-form-item",{attrs:{label:"表单尺寸"}},[a("el-radio-group",{model:{value:e.formConf.size,callback:function(t){e.$set(e.formConf,"size",t)},expression:"formConf.size"}},[a("el-radio-button",{attrs:{label:"medium"}},[e._v(" 中等 ")]),a("el-radio-button",{attrs:{label:"small"}},[e._v(" 较小 ")]),a("el-radio-button",{attrs:{label:"mini"}},[e._v(" 迷你 ")])],1)],1),a("el-form-item",{attrs:{label:"标签对齐"}},[a("el-radio-group",{model:{value:e.formConf.labelPosition,callback:function(t){e.$set(e.formConf,"labelPosition",t)},expression:"formConf.labelPosition"}},[a("el-radio-button",{attrs:{label:"left"}},[e._v(" 左对齐 ")]),a("el-radio-button",{attrs:{label:"right"}},[e._v(" 右对齐 ")]),a("el-radio-button",{attrs:{label:"top"}},[e._v(" 顶部对齐 ")])],1)],1),a("el-form-item",{attrs:{label:"标签宽度"}},[a("el-input-number",{attrs:{placeholder:"标签宽度"},model:{value:e.formConf.labelWidth,callback:function(t){e.$set(e.formConf,"labelWidth",t)},expression:"formConf.labelWidth"}})],1),a("el-form-item",{attrs:{label:"栅格间隔"}},[a("el-input-number",{attrs:{min:0,placeholder:"栅格间隔"},model:{value:e.formConf.gutter,callback:function(t){e.$set(e.formConf,"gutter",t)},expression:"formConf.gutter"}})],1),a("el-form-item",{attrs:{label:"禁用表单"}},[a("el-switch",{model:{value:e.formConf.disabled,callback:function(t){e.$set(e.formConf,"disabled",t)},expression:"formConf.disabled"}})],1),a("el-form-item",{attrs:{label:"表单按钮"}},[a("el-switch",{model:{value:e.formConf.formBtns,callback:function(t){e.$set(e.formConf,"formBtns",t)},expression:"formConf.formBtns"}})],1),a("el-form-item",{attrs:{label:"显示未选中组件边框"}},[a("el-switch",{model:{value:e.formConf.unFocusedComponentBorder,callback:function(t){e.$set(e.formConf,"unFocusedComponentBorder",t)},expression:"formConf.unFocusedComponentBorder"}})],1)],1)],1)],1),a("treeNode-dialog",{attrs:{visible:e.dialogVisible,title:"添加选项"},on:{"update:visible":function(t){e.dialogVisible=t},commit:e.addNode}}),a("icons-dialog",{attrs:{visible:e.iconsVisible,current:e.activeData[e.currentIconModel]},on:{"update:visible":function(t){e.iconsVisible=t},select:e.setIcon}})],1)},l=[],n=(a("99af"),a("7db0"),a("c740"),a("a15b"),a("d81d"),a("14d9"),a("a434"),a("b64b"),a("d3b7"),a("3022")),i=a("b76a"),c=a.n(i),r=a("c81a"),s=a("ed08"),d=a("d0b2"),u=a("2e2a"),p={date:"yyyy-MM-dd",week:"yyyy 第 WW 周",month:"yyyy-MM",year:"yyyy",datetime:"yyyy-MM-dd HH:mm:ss",daterange:"yyyy-MM-dd",monthrange:"yyyy-MM",datetimerange:"yyyy-MM-dd HH:mm:ss"},m={components:{draggable:c.a,TreeNodeDialog:r["default"],IconsDialog:d["default"]},props:["showField","activeData","formConf"],data:function(){return{currentTab:"field",currentNode:null,dialogVisible:!1,iconsVisible:!1,currentIconModel:null,dateTypeOptions:[{label:"日(date)",value:"date"},{label:"周(week)",value:"week"},{label:"月(month)",value:"month"},{label:"年(year)",value:"year"},{label:"日期时间(datetime)",value:"datetime"}],dateRangeTypeOptions:[{label:"日期范围(daterange)",value:"daterange"},{label:"月范围(monthrange)",value:"monthrange"},{label:"日期时间范围(datetimerange)",value:"datetimerange"}],colorFormatOptions:[{label:"hex",value:"hex"},{label:"rgb",value:"rgb"},{label:"rgba",value:"rgba"},{label:"hsv",value:"hsv"},{label:"hsl",value:"hsl"}],justifyOptions:[{label:"start",value:"start"},{label:"end",value:"end"},{label:"center",value:"center"},{label:"space-around",value:"space-around"},{label:"space-between",value:"space-between"}],layoutTreeProps:{label:function(e,t){return e.componentName||"".concat(e.label,": ").concat(e.vModel)}}}},computed:{documentLink:function(){return this.activeData.document||"https://element.eleme.cn/#/zh-CN/component/installation"},dateOptions:function(){return void 0!==this.activeData.type&&"el-date-picker"===this.activeData.tag?void 0===this.activeData["start-placeholder"]?this.dateTypeOptions:this.dateRangeTypeOptions:[]},tagList:function(){return[{label:"输入型组件",options:u["b"]},{label:"选择型组件",options:u["d"]}]}},methods:{addReg:function(){this.activeData.regList.push({pattern:"",message:""})},addSelectItem:function(){this.activeData.options.push({label:"",value:""})},addTreeItem:function(){++this.idGlobal,this.dialogVisible=!0,this.currentNode=this.activeData.options},renderContent:function(e,t){var a=this,o=t.node,l=t.data;t.store;return e("div",{class:"custom-tree-node"},[e("span",[o.label]),e("span",{class:"node-operation"},[e("i",{on:{click:function(){return a.append(l)}},class:"el-icon-plus",attrs:{title:"添加"}}),e("i",{on:{click:function(){return a.remove(o,l)}},class:"el-icon-delete",attrs:{title:"删除"}})])])},append:function(e){e.children||this.$set(e,"children",[]),this.dialogVisible=!0,this.currentNode=e.children},remove:function(e,t){var a=e.parent,o=a.data.children||a.data,l=o.findIndex((function(e){return e.id===t.id}));o.splice(l,1)},addNode:function(e){this.currentNode.push(e)},setOptionValue:function(e,t){e.value=Object(s["d"])(t)?+t:t},setDefaultValue:function(e){return Array.isArray(e)?e.join(","):["string","number"].indexOf(e)>-1?e:"boolean"===typeof e?"".concat(e):e},onDefaultValueInput:function(e){Object(n["isArray"])(this.activeData.defaultValue)?this.$set(this.activeData,"defaultValue",e.split(",").map((function(e){return Object(s["d"])(e)?+e:e}))):["true","false"].indexOf(e)>-1?this.$set(this.activeData,"defaultValue",JSON.parse(e)):this.$set(this.activeData,"defaultValue",Object(s["d"])(e)?+e:e)},onSwitchValueInput:function(e,t){["true","false"].indexOf(e)>-1?this.$set(this.activeData,t,JSON.parse(e)):this.$set(this.activeData,t,Object(s["d"])(e)?+e:e)},setTimeValue:function(e,t){var a="week"===t?p.date:e;this.$set(this.activeData,"defaultValue",null),this.$set(this.activeData,"value-format",a),this.$set(this.activeData,"format",e)},spanChange:function(e){this.formConf.span=e},multipleChange:function(e){this.$set(this.activeData,"defaultValue",e?[]:"")},dateTypeChange:function(e){this.setTimeValue(p[e],e)},rangeChange:function(e){this.$set(this.activeData,"defaultValue",e?[this.activeData.min,this.activeData.max]:this.activeData.min)},rateTextChange:function(e){e&&(this.activeData["show-score"]=!1)},rateScoreChange:function(e){e&&(this.activeData["show-text"]=!1)},colorFormatChange:function(e){this.activeData.defaultValue=null,this.activeData["show-alpha"]=e.indexOf("a")>-1,this.activeData.renderKey=+new Date},openIconsDialog:function(e){this.iconsVisible=!0,this.currentIconModel=e},setIcon:function(e){this.activeData[this.currentIconModel]=e},tagChange:function(e){var t=u["b"].find((function(t){return t.tagIcon===e}));t||(t=u["d"].find((function(t){return t.tagIcon===e}))),this.$emit("tag-change",t)}}},f=m,v=(a("822d"),a("2877")),b=Object(v["a"])(f,o,l,!1,null,"78f2d993",null);t["default"]=b.exports},"80de":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"a",(function(){return makeUpJs}));var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("99af"),core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("a15b"),core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("14d9"),core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es_array_push_js__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("e9c4"),core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_3__),core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("b64b"),core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default=__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__),core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("d3b7"),core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5___default=__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__),core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("159b"),core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6___default=__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__),_utils_index__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__("ed08"),_config__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__("2e2a"),units={KB:"1024",MB:"1024 / 1024",GB:"1024 / 1024 / 1024"},confGlobal,inheritAttrs={file:"",dialog:"inheritAttrs: false,"};function makeUpJs(e,t){confGlobal=e=JSON.parse(JSON.stringify(e));var a=[],o=[],l=[],n=[],i=mixinMethod(t),c=[];e.fields.forEach((function(e){buildAttributes(e,a,o,l,i,n,c)}));var r=buildexport(e,t,a.join("\n"),o.join("\n"),l.join("\n"),c.join("\n"),n.join("\n"),i.join("\n"));return confGlobal=null,r}function buildAttributes(e,t,a,o,l,n,i){if(buildData(e,t),buildRules(e,a),e.options&&e.options.length&&(buildOptions(e,o),"dynamic"===e.dataType)){var c="".concat(e.vModel,"Options"),r=Object(_utils_index__WEBPACK_IMPORTED_MODULE_7__["f"])(c);buildOptionMethod("get".concat(r),c,l)}e.props&&e.props.props&&buildProps(e,n),e.action&&"el-upload"===e.tag&&(i.push("".concat(e.vModel,"Action: '").concat(e.action,"',\n      ").concat(e.vModel,"fileList: [],")),l.push(buildBeforeUpload(e)),e["auto-upload"]||l.push(buildSubmitUpload(e))),e.children&&e.children.forEach((function(e){buildAttributes(e,t,a,o,l,n,i)}))}function mixinMethod(e){var t=[],a={file:confGlobal.formBtns?{submitForm:"submitForm() {\n        this.$refs['".concat(confGlobal.formRef,"'].validate(valid => {\n          if(!valid) return\n          // TODO 提交表单\n        })\n      },"),resetForm:"resetForm() {\n        this.$refs['".concat(confGlobal.formRef,"'].resetFields()\n      },")}:null,dialog:{onOpen:"onOpen() {},",onClose:"onClose() {\n        this.$refs['".concat(confGlobal.formRef,"'].resetFields()\n      },"),close:"close() {\n        this.$emit('update:visible', false)\n      },",handleConfirm:"handleConfirm() {\n        this.$refs['".concat(confGlobal.formRef,"'].validate(valid => {\n          if(!valid) return\n          this.close()\n        })\n      },")}},o=a[e];return o&&Object.keys(o).forEach((function(e){t.push(o[e])})),t}function buildData(e,t){var a;void 0!==e.vModel&&(a="string"!==typeof e.defaultValue||e.multiple?"".concat(JSON.stringify(e.defaultValue)):"'".concat(e.defaultValue,"'"),t.push("".concat(e.vModel,": ").concat(a,",")))}function buildRules(conf,ruleList){if(void 0!==conf.vModel){var rules=[];if(_config__WEBPACK_IMPORTED_MODULE_8__["e"][conf.tag]){if(conf.required){var type=Array.isArray(conf.defaultValue)?"type: 'array',":"",message=Array.isArray(conf.defaultValue)?"请至少选择一个".concat(conf.vModel):conf.placeholder;void 0===message&&(message="".concat(conf.label,"不能为空")),rules.push("{ required: true, ".concat(type," message: '").concat(message,"', trigger: '").concat(_config__WEBPACK_IMPORTED_MODULE_8__["e"][conf.tag],"' }"))}conf.regList&&Array.isArray(conf.regList)&&conf.regList.forEach((function(item){item.pattern&&rules.push("{ pattern: ".concat(eval(item.pattern),", message: '").concat(item.message,"', trigger: '").concat(_config__WEBPACK_IMPORTED_MODULE_8__["e"][conf.tag],"' }"))})),ruleList.push("".concat(conf.vModel,": [").concat(rules.join(","),"],"))}}}function buildOptions(e,t){if(void 0!==e.vModel){"dynamic"===e.dataType&&(e.options=[]);var a="".concat(e.vModel,"Options: ").concat(JSON.stringify(e.options),",");t.push(a)}}function buildProps(e,t){"dynamic"===e.dataType&&("value"!==e.valueKey&&(e.props.props.value=e.valueKey),"label"!==e.labelKey&&(e.props.props.label=e.labelKey),"children"!==e.childrenKey&&(e.props.props.children=e.childrenKey));var a="".concat(e.vModel,"Props: ").concat(JSON.stringify(e.props.props),",");t.push(a)}function buildBeforeUpload(e){var t=units[e.sizeUnit],a="",o="",l=[];e.fileSize&&(a="let isRightSize = file.size / ".concat(t," < ").concat(e.fileSize,"\n    if(!isRightSize){\n      this.$message.error('文件大小超过 ").concat(e.fileSize).concat(e.sizeUnit,"')\n    }"),l.push("isRightSize")),e.accept&&(o="let isAccept = new RegExp('".concat(e.accept,"').test(file.type)\n    if(!isAccept){\n      this.$message.error('应该选择").concat(e.accept,"类型的文件')\n    }"),l.push("isAccept"));var n="".concat(e.vModel,"BeforeUpload(file) {\n    ").concat(a,"\n    ").concat(o,"\n    return ").concat(l.join("&&"),"\n  },");return l.length?n:""}function buildSubmitUpload(e){var t="submitUpload() {\n    this.$refs['".concat(e.vModel,"'].submit()\n  },");return t}function buildOptionMethod(e,t,a){var o="".concat(e,"() {\n    // TODO 发起请求获取数据\n    this.").concat(t,"\n  },");a.push(o)}function buildexport(e,t,a,o,l,n,i,c){var r="".concat(_utils_index__WEBPACK_IMPORTED_MODULE_7__["c"],"{\n  ").concat(inheritAttrs[t],"\n  components: {},\n  props: [],\n  data () {\n    return {\n      ").concat(e.formModel,": {\n        ").concat(a,"\n      },\n      ").concat(e.formRules,": {\n        ").concat(o,"\n      },\n      ").concat(n,"\n      ").concat(l,"\n      ").concat(i,"\n    }\n  },\n  computed: {},\n  watch: {},\n  created () {},\n  mounted () {},\n  methods: {\n    ").concat(c,"\n  }\n}");return r}},"822d":function(e,t,a){"use strict";a("6d81")},a2e1:function(e,t,a){},a85b:function(e,t,a){"use strict";a("14d9"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var o=a("ed08"),l=Object(o["e"])("accept,accept-charset,accesskey,action,align,alt,async,autocomplete,autofocus,autoplay,autosave,bgcolor,border,buffered,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,http-equiv,name,contenteditable,contextmenu,controls,coords,data,datetime,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,method,for,form,formaction,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,ismap,itemprop,keytype,kind,label,lang,language,list,loop,low,manifest,max,maxlength,media,method,GET,POST,min,multiple,email,file,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,seamless,selected,shape,size,type,text,password,sizes,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,type,usemap,value,width,wrap");function n(e,t,a){t.props.value=a,t.on.input=function(t){e.$emit("input",t)}}var i={"el-button":{default:function(e,t,a){return t[a]}},"el-input":{prepend:function(e,t,a){return e("template",{slot:"prepend"},[t[a]])},append:function(e,t,a){return e("template",{slot:"append"},[t[a]])}},"el-select":{options:function(e,t,a){var o=[];return t.options.forEach((function(t){o.push(e("el-option",{attrs:{label:t.label,value:t.value,disabled:t.disabled}}))})),o}},"el-radio-group":{options:function(e,t,a){var o=[];return t.options.forEach((function(a){"button"===t.optionType?o.push(e("el-radio-button",{attrs:{label:a.value}},[a.label])):o.push(e("el-radio",{attrs:{label:a.value,border:t.border}},[a.label]))})),o}},"el-checkbox-group":{options:function(e,t,a){var o=[];return t.options.forEach((function(a){"button"===t.optionType?o.push(e("el-checkbox-button",{attrs:{label:a.value}},[a.label])):o.push(e("el-checkbox",{attrs:{label:a.value,border:t.border}},[a.label]))})),o}},"el-upload":{"list-type":function(e,t,a){var o=[];return"picture-card"===t["list-type"]?o.push(e("i",{class:"el-icon-plus"})):o.push(e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload"}},[t.buttonText])),t.showTip&&o.push(e("div",{slot:"tip",class:"el-upload__tip"},["只能上传不超过 ",t.fileSize,t.sizeUnit," 的",t.accept,"文件"])),o}}};t["a"]={render:function(e){var t=this,a={attrs:{},props:{},on:{},style:{}},o=JSON.parse(JSON.stringify(this.conf)),c=[],r=i[o.tag];return r&&Object.keys(r).forEach((function(t){var a=r[t];o[t]&&c.push(a(e,o,t))})),Object.keys(o).forEach((function(e){var i=o[e];"vModel"===e?n(t,a,o.defaultValue):a[e]?a[e]=i:l(e)?a.attrs[e]=i:a.props[e]=i})),e(this.conf.tag,a,c)},props:["conf"]}},a92a:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({attrs:{width:"500px","close-on-click-modal":!1,"modal-append-to-body":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[a("el-row",{attrs:{gutter:15}},[a("el-form",{ref:"elForm",attrs:{model:e.formData,rules:e.rules,size:"medium","label-width":"100px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"生成类型",prop:"type"}},[a("el-radio-group",{model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},e._l(e.typeOptions,(function(t,o){return a("el-radio-button",{key:o,attrs:{label:t.value,disabled:t.disabled}},[e._v(" "+e._s(t.label)+" ")])})),1)],1),e.showFileName?a("el-form-item",{attrs:{label:"文件名",prop:"fileName"}},[a("el-input",{attrs:{placeholder:"请输入文件名",clearable:""},model:{value:e.formData.fileName,callback:function(t){e.$set(e.formData,"fileName",t)},expression:"formData.fileName"}})],1):e._e()],1)],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v(" 确定 ")])],1)],1)],1)},l=[],n=a("5530"),i={inheritAttrs:!1,props:["showFileName"],data:function(){return{formData:{fileName:void 0,type:"file"},rules:{fileName:[{required:!0,message:"请输入文件名",trigger:"blur"}],type:[{required:!0,message:"生成类型不能为空",trigger:"change"}]},typeOptions:[{label:"页面",value:"file"},{label:"弹窗",value:"dialog"}]}},computed:{},watch:{},mounted:function(){},methods:{onOpen:function(){this.showFileName&&(this.formData.fileName="".concat(+new Date,".vue"))},onClose:function(){},close:function(e){this.$emit("update:visible",!1)},handleConfirm:function(){var e=this;this.$refs.elForm.validate((function(t){t&&(e.$emit("confirm",Object(n["a"])({},e.formData)),e.close())}))}}},c=i,r=a("2877"),s=Object(r["a"])(c,o,l,!1,null,null,null);t["default"]=s.exports},c81a:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[a("el-row",{attrs:{gutter:0}},[a("el-form",{ref:"elForm",attrs:{model:e.formData,rules:e.rules,size:"small","label-width":"100px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"选项名",prop:"label"}},[a("el-input",{attrs:{placeholder:"请输入选项名",clearable:""},model:{value:e.formData.label,callback:function(t){e.$set(e.formData,"label",t)},expression:"formData.label"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"选项值",prop:"value"}},[a("el-input",{attrs:{placeholder:"请输入选项值",clearable:""},model:{value:e.formData.value,callback:function(t){e.$set(e.formData,"value",t)},expression:"formData.value"}},[a("el-select",{style:{width:"100px"},attrs:{slot:"append"},slot:"append",model:{value:e.dataType,callback:function(t){e.dataType=t},expression:"dataType"}},e._l(e.dataTypeOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value,disabled:e.disabled}})})),1)],1)],1)],1)],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v(" 确定 ")]),a("el-button",{on:{click:e.close}},[e._v(" 取消 ")])],1)],1)],1)},l=[],n=a("ed08"),i={components:{},inheritAttrs:!1,props:[],data:function(){return{id:100,formData:{label:void 0,value:void 0},rules:{label:[{required:!0,message:"请输入选项名",trigger:"blur"}],value:[{required:!0,message:"请输入选项值",trigger:"blur"}]},dataType:"string",dataTypeOptions:[{label:"字符串",value:"string"},{label:"数字",value:"number"}]}},computed:{},watch:{"formData.value":function(e){this.dataType=Object(n["d"])(e)?"number":"string"}},created:function(){},mounted:function(){},methods:{onOpen:function(){this.formData={label:void 0,value:void 0}},onClose:function(){},close:function(){this.$emit("update:visible",!1)},handleConfirm:function(){var e=this;this.$refs.elForm.validate((function(t){t&&("number"===e.dataType&&(e.formData.value=parseFloat(e.formData.value)),e.formData.id=e.id++,e.$emit("commit",e.formData),e.close())}))}}},c=i,r=a("2877"),s=Object(r["a"])(c,o,l,!1,null,null,null);t["default"]=s.exports},d0b2:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"icon-dialog"},[a("el-dialog",e._g(e._b({attrs:{width:"980px","modal-append-to-body":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[a("div",{attrs:{slot:"title"},slot:"title"},[e._v(" 选择图标 "),a("el-input",{style:{width:"260px"},attrs:{size:"mini",placeholder:"请输入图标名称","prefix-icon":"el-icon-search",clearable:""},model:{value:e.key,callback:function(t){e.key=t},expression:"key"}})],1),a("ul",{staticClass:"icon-ul"},e._l(e.iconList,(function(t){return a("li",{key:t,class:e.active===t?"active-item":"",on:{click:function(a){return e.onSelect(t)}}},[a("i",{class:t}),a("div",[e._v(e._s(t))])])})),0)])],1)},l=[],n=(a("4de4"),a("d81d"),a("d3b7"),a("de0a")),i=n.map((function(e){return"el-icon-".concat(e)})),c={inheritAttrs:!1,props:["current"],data:function(){return{iconList:i,active:null,key:""}},watch:{key:function(e){this.iconList=e?i.filter((function(t){return t.indexOf(e)>-1})):i}},methods:{onOpen:function(){this.active=this.current,this.key=""},onClose:function(){},onSelect:function(e){this.active=e,this.$emit("select",e),this.$emit("update:visible",!1)}}},r=c,s=(a("de89"),a("2877")),d=Object(s["a"])(r,o,l,!1,null,"2fa68d6e",null);t["default"]=d.exports},de0a:function(e){e.exports=JSON.parse('["platform-eleme","eleme","delete-solid","delete","s-tools","setting","user-solid","user","phone","phone-outline","more","more-outline","star-on","star-off","s-goods","goods","warning","warning-outline","question","info","remove","circle-plus","success","error","zoom-in","zoom-out","remove-outline","circle-plus-outline","circle-check","circle-close","s-help","help","minus","plus","check","close","picture","picture-outline","picture-outline-round","upload","upload2","download","camera-solid","camera","video-camera-solid","video-camera","message-solid","bell","s-cooperation","s-order","s-platform","s-fold","s-unfold","s-operation","s-promotion","s-home","s-release","s-ticket","s-management","s-open","s-shop","s-marketing","s-flag","s-comment","s-finance","s-claim","s-custom","s-opportunity","s-data","s-check","s-grid","menu","share","d-caret","caret-left","caret-right","caret-bottom","caret-top","bottom-left","bottom-right","back","right","bottom","top","top-left","top-right","arrow-left","arrow-right","arrow-down","arrow-up","d-arrow-left","d-arrow-right","video-pause","video-play","refresh","refresh-right","refresh-left","finished","sort","sort-up","sort-down","rank","loading","view","c-scale-to-original","date","edit","edit-outline","folder","folder-opened","folder-add","folder-remove","folder-delete","folder-checked","tickets","document-remove","document-delete","document-copy","document-checked","document","document-add","printer","paperclip","takeaway-box","search","monitor","attract","mobile","scissors","umbrella","headset","brush","mouse","coordinate","magic-stick","reading","data-line","data-board","pie-chart","data-analysis","collection-tag","film","suitcase","suitcase-1","receiving","collection","files","notebook-1","notebook-2","toilet-paper","office-building","school","table-lamp","house","no-smoking","smoking","shopping-cart-full","shopping-cart-1","shopping-cart-2","shopping-bag-1","shopping-bag-2","sold-out","sell","present","box","bank-card","money","coin","wallet","discount","price-tag","news","guide","male","female","thumb","cpu","link","connection","open","turn-off","set-up","chat-round","chat-line-round","chat-square","chat-dot-round","chat-dot-square","chat-line-square","message","postcard","position","turn-off-microphone","microphone","close-notification","bangzhu","time","odometer","crop","aim","switch-button","full-screen","copy-document","mic","stopwatch","medal-1","medal","trophy","trophy-1","first-aid-kit","discover","place","location","location-outline","location-information","add-location","delete-location","map-location","alarm-clock","timer","watch-1","watch","lock","unlock","key","service","mobile-phone","bicycle","truck","ship","basketball","football","soccer","baseball","wind-power","light-rain","lightning","heavy-rain","sunrise","sunrise-1","sunset","sunny","cloudy","partly-cloudy","cloudy-and-sunny","moon","moon-night","dish","dish-1","food","chicken","fork-spoon","knife-fork","burger","tableware","sugar","dessert","ice-cream","hot-water","water-cup","coffee-cup","cold-drink","goblet","goblet-full","goblet-square","goblet-square-full","refrigerator","grape","watermelon","cherry","apple","pear","orange","coffee","ice-tea","ice-drink","milk-tea","potato-strips","lollipop","ice-cream-square","ice-cream-round"]')},de89:function(e,t,a){"use strict";a("a2e1")},e625:function(e,t,a){"use strict";a("35c4")},ed08:function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"e",(function(){return l})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i})),a.d(t,"f",(function(){return c})),a.d(t,"d",(function(){return r}));a("53ca"),a("d9e2"),a("a630"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("b64b"),a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("6062"),a("3ca3"),a("466d"),a("5319"),a("88e6"),a("70cc"),a("eb03"),a("22e5"),a("c01e"),a("fa76"),a("8306"),a("159b"),a("ddb0"),a("c38a");function o(e,t,a){var o,l,n,i,c,r=function r(){var s=+new Date-i;s<t&&s>0?o=setTimeout(r,t-s):(o=null,a||(c=e.apply(n,l),o||(n=l=null)))};return function(){for(var l=arguments.length,s=new Array(l),d=0;d<l;d++)s[d]=arguments[d];n=this,i=+new Date;var u=a&&!o;return o||(o=setTimeout(r,t)),u&&(c=e.apply(n,s),n=s=null),c}}function l(e,t){for(var a=Object.create(null),o=e.split(","),l=0;l<o.length;l++)a[o[l]]=!0;return t?function(e){return a[e.toLowerCase()]}:function(e){return a[e]}}var n="export default ",i={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0},js:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"normal",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!0,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function c(e){return e.replace(/( |^)[a-z]/g,(function(e){return e.toUpperCase()}))}function r(e){return/^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(e)}}}]);