{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue", "mtime": 1754893563836}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyB1cGxvYWRTY2VuZUZpbGUsIGJhY2tncm91bmRGaWxlRGVsLCBmaWxlQmluZERlbCB9IGZyb20gJ0AvYXBpL3ZpZXcvc2NlbmVWaWV3Jw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTY2VuZUNvbmZpZ05vZGUnLA0KICBwcm9wczogew0KICAgIG5vZGU6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICByb290VHJlZTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgc2NlbmVUcmVlT3B0aW9uczogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgbGVmdFRyZWVJbmR1c3RyeUNvZGU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICdub2RlLnN0YXR1cycodmFsKSB7DQogICAgICBpZiAodmFsID09PSAnMCcpIHsNCiAgICAgICAgdGhpcy5maW5kQW5kT3BlblBhcmVudCh0aGlzLm5vZGUuaWQsIHRoaXMucm9vdFRyZWUpDQogICAgICB9DQogICAgfSwNCiAgICBub2RlOiB7DQogICAgICBoYW5kbGVyKG5ld05vZGUsIG9sZE5vZGUpIHsNCiAgICAgICAgaWYgKG5ld05vZGUgJiYgbmV3Tm9kZSAhPT0gb2xkTm9kZSkgew0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIC8vIOWIneWni+WMluiKgueCueaVsOaNrg0KICAgICAgICAgICAgdGhpcy5pbml0Tm9kZURhdGEoKQ0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDnoa7kv51pbnRyb2R1Y2VWaWRlb1Zv5a2Y5Zyo5LiU5pyJ5a6M5pW05pWw5o2u5pe25omN5aSE55CGDQogICAgICAgICAgICBpZiAobmV3Tm9kZS5pbnRyb2R1Y2VWaWRlb1ZvICYmIG5ld05vZGUuaW50cm9kdWNlVmlkZW9Wby5oYXNPd25Qcm9wZXJ0eSgnc3RhdHVzJykpIHsNCiAgICAgICAgICAgICAgLy8g5pWw5o2u5bey5a2Y5Zyo77yM5LiN6ZyA6KaB5Yid5aeL5YyW77yM55u05o6l5pu05paw5paH5Lu25YiX6KGoDQogICAgICAgICAgICAgIHRoaXMudXBkYXRlSW50cm9kdWNlVmlkZW9GaWxlTGlzdCgpDQogICAgICAgICAgICB9IGVsc2UgaWYgKCFuZXdOb2RlLmludHJvZHVjZVZpZGVvVm8pIHsNCiAgICAgICAgICAgICAgLy8g5pWw5o2u5LiN5a2Y5Zyo5pe25omN5Yid5aeL5YyWDQogICAgICAgICAgICAgIHRoaXMuaW5pdEludHJvZHVjZVZpZGVvKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC8vIOmHjeaWsOWIneWni+WMluaWh+S7tuWIl+ihqO+8jOa4hemZpOWPr+iDveeahOe7p+aJv+mXrumimA0KICAgICAgICAgICAgdGhpcy5pbml0RmlsZUxpc3RzKCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgaW1tZWRpYXRlOiB0cnVlLA0KICAgICAgZGVlcDogdHJ1ZQ0KICAgIH0sDQogICAgLy8g55uR5ZCsaW50cm9kdWNlVmlkZW9Wb+aVtOS4quWvueixoeeahOWPmOWMlg0KICAgICdub2RlLmludHJvZHVjZVZpZGVvVm8nOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgICAgICBpZiAobmV3VmFsICYmIG5ld1ZhbC5zdGF0dXMgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgIHRoaXMudXBkYXRlSW50cm9kdWNlVmlkZW9GaWxlTGlzdCgpDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUsDQogICAgICBkZWVwOiB0cnVlDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBmaWxlTGlzdHM6IHsNCiAgICAgICAgdHJhZGl0aW9uOiB7fSwNCiAgICAgICAgd2lzZG9tNWc6IHt9LA0KICAgICAgICBpbnRyb2R1Y2VWaWRlbzogW10NCiAgICAgIH0sDQogICAgICB1cGxvYWRNb2Rlczogew0KICAgICAgICB0cmFkaXRpb246IHt9LA0KICAgICAgICB3aXNkb201Zzoge30sDQogICAgICAgIGludHJvZHVjZVZpZGVvOiAndXBsb2FkJw0KICAgICAgfSwNCiAgICAgIC8vIOa3u+WKoOS7i+e7jeinhumikeaWh+S7tuWIl+ihqOe8k+WtmA0KICAgICAgaW50cm9kdWNlVmlkZW9GaWxlTGlzdDogW10sDQogICAgICAvLyDlm77niYfpooTop4gNCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHByZXZpZXdJbWFnZVVybDogJycsDQogICAgICAvLyDlnLrmma/nuqfogZTpgInmi6nlmajphY3nva4NCiAgICAgIHNjZW5lQ2FzY2FkZXJQcm9wczogew0KICAgICAgICBsYWJlbDogJ3NjZW5lTmFtZScsDQogICAgICAgIHZhbHVlOiAnaWQnLA0KICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywNCiAgICAgICAgZW1pdFBhdGg6IGZhbHNlLA0KICAgICAgICBjaGVja1N0cmljdGx5OiB0cnVlDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaW5pdEludHJvZHVjZVZpZGVvKCkNCiAgICB0aGlzLmluaXROb2RlRGF0YSgpDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0RGF0YSgpDQogICAgdGhpcy5pbml0RmlsZUxpc3RzKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXREYXRhKCkgew0KICAgICAgLy8g56Gu5L+dNUfmmbrmhafmqKHlnZfmnInpu5jorqTnu5PmnoTlubbmt7Hmi7fotJ3pgb/lhY3lvJXnlKjlhbHkuqsNCiAgICAgIGlmICghdGhpcy5ub2RlLndpc2RvbTVnKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLm5vZGUsICd3aXNkb201ZycsIHsNCiAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sS2V5OiAnJywNCiAgICAgICAgICBiYWNrZ3JvdW5kUmVzb3VyY2VzOiBbXSwNCiAgICAgICAgICBwYWluUG9pbnRzOiBbXQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5rex5ou36LSd546w5pyJ55qENUfmmbrmhafmlbDmja7vvIzpgb/lhY3lpJrkuKrlnLrmma/lhbHkuqvlvJXnlKgNCiAgICAgICAgY29uc3Qgd2lzZG9tNWdDb3B5ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLm5vZGUud2lzZG9tNWcpKQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAnd2lzZG9tNWcnLCB3aXNkb201Z0NvcHkpDQogICAgICAgIA0KICAgICAgICAvLyDkv67mraPlrZfmrrXmmKDlsIQNCiAgICAgICAgaWYgKHRoaXMubm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzKSB7DQogICAgICAgICAgdGhpcy5ub2RlLndpc2RvbTVnLmJhY2tncm91bmRSZXNvdXJjZXMuZm9yRWFjaChyZXNvdXJjZSA9PiB7DQogICAgICAgICAgICBpZiAocmVzb3VyY2UuYmFja2dyb3VuZEltZ0ZpbGVVcmwgJiYgIXJlc291cmNlLmJnSW1nKSB7DQogICAgICAgICAgICAgIHJlc291cmNlLmJnSW1nID0gcmVzb3VyY2UuYmFja2dyb3VuZEltZ0ZpbGVVcmwNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmIChyZXNvdXJjZS5iYWNrZ3JvdW5kRmlsZVVybCAmJiAhcmVzb3VyY2UuYmdGaWxlKSB7DQogICAgICAgICAgICAgIHJlc291cmNlLmJnRmlsZSA9IHJlc291cmNlLmJhY2tncm91bmRGaWxlVXJsDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAocmVzb3VyY2UudGFnICYmICFyZXNvdXJjZS5sYWJlbCkgew0KICAgICAgICAgICAgICByZXNvdXJjZS5sYWJlbCA9IHJlc291cmNlLnRhZw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5ZCM5qC35aSE55CG5Lyg57uf5qih5Z2XDQogICAgICBpZiAoIXRoaXMubm9kZS50cmFkaXRpb24pIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubm9kZSwgJ3RyYWRpdGlvbicsIHsNCiAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sS2V5OiAnJywNCiAgICAgICAgICBiYWNrZ3JvdW5kUmVzb3VyY2VzOiBbXSwNCiAgICAgICAgICBwYWluUG9pbnRzOiBbXQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5rex5ou36LSd5Lyg57uf5qih5Z2X5pWw5o2uDQogICAgICAgIGNvbnN0IHRyYWRpdGlvbkNvcHkgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMubm9kZS50cmFkaXRpb24pKQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAndHJhZGl0aW9uJywgdHJhZGl0aW9uQ29weSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWIneWni+WMluaWh+S7tuWIl+ihqA0KICAgIGluaXRGaWxlTGlzdHMoKSB7DQogICAgICAvLyDmuIXnqbrmiYDmnInmlofku7bliJfooajvvIzpgb/lhY3nu6fmib/pl67popgNCiAgICAgIHRoaXMuZmlsZUxpc3RzID0gew0KICAgICAgICB0cmFkaXRpb246IHt9LA0KICAgICAgICB3aXNkb201Zzoge30sDQogICAgICAgIGludHJvZHVjZVZpZGVvOiBbXQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDliJ3lp4vljJbkvKDnu5/mqKHlnZfnmoTmlofku7bliJfooagNCiAgICAgIGlmICh0aGlzLm5vZGUudHJhZGl0aW9uICYmIHRoaXMubm9kZS50cmFkaXRpb24uYmFja2dyb3VuZFJlc291cmNlcykgew0KICAgICAgICB0aGlzLm5vZGUudHJhZGl0aW9uLmJhY2tncm91bmRSZXNvdXJjZXMuZm9yRWFjaCgocmVzb3VyY2UsIGlkeCkgPT4gew0KICAgICAgICAgIC8vIOWPquacieW9k+i1hOa6kOehruWunuacieaWh+S7tuaXtuaJjeWIm+W7uuaWh+S7tuWIl+ihqA0KICAgICAgICAgIGlmIChyZXNvdXJjZS5iZ0ZpbGUgJiYgcmVzb3VyY2UuYmdGaWxlLnRyaW0oKSkgew0KICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSByZXNvdXJjZS5iZ0ZpbGUuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZmlsZUxpc3RzLnRyYWRpdGlvbiwgaWR4LCBbew0KICAgICAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICAgICAgdXJsOiByZXNvdXJjZS5iZ0ZpbGUsDQogICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKSArIGlkeA0KICAgICAgICAgICAgfV0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaYjuehruiuvue9ruS4uuepuuaVsOe7hA0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZmlsZUxpc3RzLnRyYWRpdGlvbiwgaWR4LCBbXSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWIneWni+WMljVH5pm65oWn5qih5Z2X55qE5paH5Lu25YiX6KGoDQogICAgICBpZiAodGhpcy5ub2RlLndpc2RvbTVnICYmIHRoaXMubm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzKSB7DQogICAgICAgIHRoaXMubm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzLmZvckVhY2goKHJlc291cmNlLCBpZHgpID0+IHsNCiAgICAgICAgICAvLyDlj6rmnInlvZPotYTmupDnoa7lrp7mnInmlofku7bml7bmiY3liJvlu7rmlofku7bliJfooagNCiAgICAgICAgICBpZiAocmVzb3VyY2UuYmdGaWxlICYmIHJlc291cmNlLmJnRmlsZS50cmltKCkpIHsNCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzb3VyY2UuYmdGaWxlLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZpbGVMaXN0cy53aXNkb201ZywgaWR4LCBbew0KICAgICAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICAgICAgdXJsOiByZXNvdXJjZS5iZ0ZpbGUsDQogICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKSArIGlkeCArIDEwMDANCiAgICAgICAgICAgIH1dKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDmmI7noa7orr7nva7kuLrnqbrmlbDnu4QNCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZpbGVMaXN0cy53aXNkb201ZywgaWR4LCBbXSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDliJ3lp4vljJbku4vnu43op4bpopHlr7nosaENCiAgICBpbml0SW50cm9kdWNlVmlkZW8oKSB7DQogICAgICBpZiAoIXRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLm5vZGUsICdpbnRyb2R1Y2VWaWRlb1ZvJywgew0KICAgICAgICAgIGlkOiAnJywNCiAgICAgICAgICB0eXBlOiAnJywNCiAgICAgICAgICB2aWV3SW5mb0lkOiAnJywNCiAgICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogJycsDQogICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6ICcnDQogICAgICAgIH0pDQogICAgICB9DQogICAgICAvLyDlrozlhajliKDpmaRzdGF0dXPnmoTph43mlrDorr7nva7vvIzkv53mjIHmjqXlj6Pov5Tlm57nmoTljp/lp4vlgLwNCiAgICAgIHRoaXMudXBkYXRlSW50cm9kdWNlVmlkZW9GaWxlTGlzdCgpDQogICAgfSwNCiAgICAvLyDnoa7kv53lhbbku5bmlbDmja7nu5PmnoTkuZ/mnInpu5jorqTlgLwNCiAgICBpbml0Tm9kZURhdGEoKSB7DQogICAgICAvLyDnoa7kv53ln7rnoYDlrZfmrrXmnInpu5jorqTlgLwgLSDlj6rlnKjnnJ/mraPmsqHmnInlgLzml7bmiY3orr7nva7pu5jorqTlgLwNCiAgICAgIGlmICh0aGlzLm5vZGUuaXNVbmZvbGQgPT09IHVuZGVmaW5lZCB8fCB0aGlzLm5vZGUuaXNVbmZvbGQgPT09IG51bGwgfHwgdGhpcy5ub2RlLmlzVW5mb2xkID09PSAnJykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAnaXNVbmZvbGQnLCAnMScpDQogICAgICB9DQogICAgICBpZiAodGhpcy5ub2RlLmRpc3BsYXlMb2NhdGlvbiA9PT0gdW5kZWZpbmVkIHx8IHRoaXMubm9kZS5kaXNwbGF5TG9jYXRpb24gPT09IG51bGwgfHwgdGhpcy5ub2RlLmRpc3BsYXlMb2NhdGlvbiA9PT0gJycpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubm9kZSwgJ2Rpc3BsYXlMb2NhdGlvbicsICcwJykNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLm5vZGUudHJlZUNsYXNzaWZpY2F0aW9uID09PSB1bmRlZmluZWQgfHwgdGhpcy5ub2RlLnRyZWVDbGFzc2lmaWNhdGlvbiA9PT0gbnVsbCB8fCB0aGlzLm5vZGUudHJlZUNsYXNzaWZpY2F0aW9uID09PSAnJykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAndHJlZUNsYXNzaWZpY2F0aW9uJywgJzMnKQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDnoa7kv53kvKDnu5/mqKHlnZfmnInpu5jorqTnu5PmnoQNCiAgICAgIGlmICghdGhpcy5ub2RlLnRyYWRpdGlvbikgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAndHJhZGl0aW9uJywgew0KICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxLZXk6ICcnLA0KICAgICAgICAgIGJhY2tncm91bmRSZXNvdXJjZXM6IFtdLA0KICAgICAgICAgIHBhaW5Qb2ludHM6IFtdDQogICAgICAgIH0pDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOehruS/nTVH5pm65oWn5qih5Z2X5pyJ6buY6K6k57uT5p6E5bm25L+u5q2j5a2X5q615pig5bCEDQogICAgICBpZiAoIXRoaXMubm9kZS53aXNkb201Zykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAnd2lzZG9tNWcnLCB7DQogICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgcGFub3JhbWljVmlld1htbEtleTogJycsDQogICAgICAgICAgYmFja2dyb3VuZFJlc291cmNlczogW10sDQogICAgICAgICAgcGFpblBvaW50czogW10NCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOS/ruatozVH5pm65oWn5qih5Z2X55qE5a2X5q615pig5bCEDQogICAgICAgIGlmICh0aGlzLm5vZGUud2lzZG9tNWcuYmFja2dyb3VuZFJlc291cmNlcykgew0KICAgICAgICAgIHRoaXMubm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzLmZvckVhY2gocmVzb3VyY2UgPT4gew0KICAgICAgICAgICAgLy8g56Gu5L+d5a2X5q615ZCN56ew5q2j56GuDQogICAgICAgICAgICBpZiAocmVzb3VyY2UuYmFja2dyb3VuZEltZ0ZpbGVVcmwgJiYgIXJlc291cmNlLmJnSW1nKSB7DQogICAgICAgICAgICAgIHJlc291cmNlLmJnSW1nID0gcmVzb3VyY2UuYmFja2dyb3VuZEltZ0ZpbGVVcmwNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmIChyZXNvdXJjZS5iYWNrZ3JvdW5kRmlsZVVybCAmJiAhcmVzb3VyY2UuYmdGaWxlKSB7DQogICAgICAgICAgICAgIHJlc291cmNlLmJnRmlsZSA9IHJlc291cmNlLmJhY2tncm91bmRGaWxlVXJsDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAocmVzb3VyY2UudGFnICYmICFyZXNvdXJjZS5sYWJlbCkgew0KICAgICAgICAgICAgICByZXNvdXJjZS5sYWJlbCA9IHJlc291cmNlLnRhZw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g56Gu5L+d5oiQ5pys6aKE5Lyw5pyJ6buY6K6k57uT5p6EDQogICAgICBpZiAoIXRoaXMubm9kZS5jb3N0RXN0aW1hdGUpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubm9kZSwgJ2Nvc3RFc3RpbWF0ZScsIHsNCiAgICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgICB0aXRsZTogJycsDQogICAgICAgICAgY29udGVudHM6IFtdDQogICAgICAgIH0pDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOehruS/neaIkOacrOmihOS8sOaciXN0YXR1c+Wtl+autQ0KICAgICAgaWYgKHRoaXMubm9kZS5jb3N0RXN0aW1hdGUgJiYgdGhpcy5ub2RlLmNvc3RFc3RpbWF0ZS5zdGF0dXMgPT09IHVuZGVmaW5lZCkgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLmNvc3RFc3RpbWF0ZSwgJ3N0YXR1cycsICcwJykNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOW8gOWFs+iBlOWKqA0KICAgIG9uU3RhdHVzQ2hhbmdlKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PT0gJzAnKSB7DQogICAgICAgIC8vIOW8gOWQr+aXtumAkuW9kuW8gOWQr+aJgOacieeItue6pw0KICAgICAgICBsZXQgcCA9IHRoaXMucGFyZW50DQogICAgICAgIHdoaWxlIChwKSB7DQogICAgICAgICAgaWYgKHAuc3RhdHVzICE9PSAnMCcpIHAuc3RhdHVzID0gJzAnDQogICAgICAgICAgcCA9IHAucGFyZW50DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWFs+mXreaXtumAkuW9kuWFs+mXreaJgOacieWtkOe6pw0KICAgICAgICBmdW5jdGlvbiBjbG9zZUNoaWxkcmVuKG5vZGUpIHsNCiAgICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICAgICAgbm9kZS5jaGlsZHJlbi5mb3JFYWNoKGNoaWxkID0+IHsNCiAgICAgICAgICAgICAgY2hpbGQuc3RhdHVzID0gJzEnDQogICAgICAgICAgICAgIGNsb3NlQ2hpbGRyZW4oY2hpbGQpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBjbG9zZUNoaWxkcmVuKHRoaXMubm9kZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOmAkuW9kuafpeaJvuW5tuW8gOWQr+aJgOacieeItue6pw0KICAgIGZpbmRBbmRPcGVuUGFyZW50KGlkLCB0cmVlKSB7DQogICAgICBmdW5jdGlvbiBoZWxwZXIobm9kZXMsIHBhcmVudCkgew0KICAgICAgICBmb3IgKGxldCBub2RlIG9mIG5vZGVzKSB7DQogICAgICAgICAgaWYgKG5vZGUuaWQgPT09IGlkKSB7DQogICAgICAgICAgICBpZiAocGFyZW50KSBwYXJlbnQuc3RhdHVzID0gJzAnDQogICAgICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICAgICAgaWYgKGhlbHBlcihub2RlLmNoaWxkcmVuLCBub2RlKSkgew0KICAgICAgICAgICAgICBpZiAocGFyZW50KSBwYXJlbnQuc3RhdHVzID0gJzAnDQogICAgICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgaGVscGVyKHRyZWUsIG51bGwpDQogICAgfSwNCiAgICAvLyDmlrDlop7og4zmma/otYTmupDnrqHnkIbmlrnms5UNCiAgICBhZGRCYWNrZ3JvdW5kUmVzb3VyY2UodHlwZSkgew0KICAgICAgaWYgKCF0aGlzLm5vZGVbdHlwZV0uYmFja2dyb3VuZFJlc291cmNlcykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlW3R5cGVdLCAnYmFja2dyb3VuZFJlc291cmNlcycsIFtdKQ0KICAgICAgfQ0KICAgICAgDQogICAgICBjb25zdCBuZXdJZHggPSB0aGlzLm5vZGVbdHlwZV0uYmFja2dyb3VuZFJlc291cmNlcy5sZW5ndGgNCiAgICAgIA0KICAgICAgLy8g5Yib5bu65a6M5YWo54us56uL55qE5paw6LWE5rqQ5a+56LGh77yM5LiN5YyF5ZCr6buY6K6k5Z2Q5qCH57uEDQogICAgICBjb25zdCBuZXdSZXNvdXJjZSA9IHsgDQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICB0YWc6ICcnLCANCiAgICAgICAgc3RhdHVzOiAnJywNCiAgICAgICAgdHlwZTogJycsDQogICAgICAgIHZpZXdJbmZvSWQ6ICcnLA0KICAgICAgICBiZ0ltZzogJycsIA0KICAgICAgICBiZ0ZpbGU6ICcnLA0KICAgICAgICBjb29yZGluYXRlczogW10gLy8g5pS55Li656m65pWw57uE77yM5LiN6buY6K6k5re75Yqg5Z2Q5qCH57uEDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOS9v+eUqOa3seaLt+i0neehruS/neWvueixoeWujOWFqOeLrOeriw0KICAgICAgY29uc3QgaW5kZXBlbmRlbnRSZXNvdXJjZSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkobmV3UmVzb3VyY2UpKQ0KICAgICAgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXMucHVzaChpbmRlcGVuZGVudFJlc291cmNlKQ0KICAgICAgDQogICAgICAvLyDliJ3lp4vljJblr7nlupTnmoTni6znq4vmlofku7bliJfooagNCiAgICAgIHRoaXMuJHNldCh0aGlzLmZpbGVMaXN0c1t0eXBlXSwgbmV3SWR4LCBbXSkNCiAgICAgIGNvbnNvbGUubG9nKCfmt7vliqDmlrDog4zmma/otYTmupA6JywgaW5kZXBlbmRlbnRSZXNvdXJjZSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOWIoOmZpOiDjOaZr+i1hOa6kA0KICAgIGFzeW5jIHJlbW92ZUJhY2tncm91bmRSZXNvdXJjZSh0eXBlLCBpZHgpIHsNCiAgICAgIGNvbnN0IHJlc291cmNlID0gdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbaWR4XQ0KICAgICAgDQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTmraTog4zmma/otYTmupDlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oYXN5bmMgKCkgPT4gew0KICAgICAgICAvLyDlpoLmnpzog4zmma/otYTmupDmnIlJRO+8jOiwg+eUqOWIoOmZpOaOpeWPow0KICAgICAgICBpZiAocmVzb3VyY2UuaWQpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5Yig6Zmk6IOM5pmv6LWE5rqQ77yM6K+356iN5YCZLi4uIikNCiAgICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGJhY2tncm91bmRGaWxlRGVsKHsgaWQ6IHJlc291cmNlLmlkIH0pDQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXMuc3BsaWNlKGlkeCwgMSkNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfliKDpmaTlpLHotKUnKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlpLHotKUnKQ0KICAgICAgICAgIH0gZmluYWxseSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmsqHmnIlJROeahOaWsOiDjOaZr+i1hOa6kO+8jOebtOaOpeS7juaVsOe7hOS4reenu+mZpA0KICAgICAgICAgIHRoaXMubm9kZVt0eXBlXS5iYWNrZ3JvdW5kUmVzb3VyY2VzLnNwbGljZShpZHgsIDEpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOa3u+WKoOWdkOagh+e7hA0KICAgIGFkZENvb3JkaW5hdGUodHlwZSwgcmVzb3VyY2VJZHgpIHsNCiAgICAgIGNvbnN0IHJlc291cmNlID0gdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbcmVzb3VyY2VJZHhdDQogICAgICBpZiAoIXJlc291cmNlLmNvb3JkaW5hdGVzKSB7DQogICAgICAgIHRoaXMuJHNldChyZXNvdXJjZSwgJ2Nvb3JkaW5hdGVzJywgW10pDQogICAgICB9DQogICAgICByZXNvdXJjZS5jb29yZGluYXRlcy5wdXNoKHsgDQogICAgICAgIGlkOiAwLA0KICAgICAgICBmaWxlSWQ6IDAsDQogICAgICAgIHg6ICcnLCANCiAgICAgICAgeTogJycsIA0KICAgICAgICB3aWRlOiAnJywgDQogICAgICAgIGhpZ2g6ICcnLCANCiAgICAgICAgc2NlbmVJZDogJycsIA0KICAgICAgICBzY2VuZUNvZGU6ICcnLA0KICAgICAgICB4bWxLZXk6ICcnDQogICAgICB9KQ0KICAgIH0sDQogICAgDQogICAgLy8g5Yig6Zmk5Z2Q5qCH57uEDQogICAgYXN5bmMgcmVtb3ZlQ29vcmRpbmF0ZSh0eXBlLCByZXNvdXJjZUlkeCwgY29vcmRJZHgpIHsNCiAgICAgIGNvbnN0IGNvb3JkID0gdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbcmVzb3VyY2VJZHhdLmNvb3JkaW5hdGVzW2Nvb3JkSWR4XQ0KICAgICAgDQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTmraTlnZDmoIfnu4TlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oYXN5bmMgKCkgPT4gew0KICAgICAgICAvLyDlpoLmnpzlnZDmoIfnu4TmnIlJRO+8jOiwg+eUqOWIoOmZpOaOpeWPow0KICAgICAgICBpZiAoY29vcmQuaWQpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5Yig6Zmk5Z2Q5qCH57uE77yM6K+356iN5YCZLi4uIikNCiAgICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZpbGVCaW5kRGVsKHsgaWQ6IGNvb3JkLmlkIH0pDQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbcmVzb3VyY2VJZHhdLmNvb3JkaW5hdGVzLnNwbGljZShjb29yZElkeCwgMSkNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfliKDpmaTlpLHotKUnKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlpLHotKUnKQ0KICAgICAgICAgIH0gZmluYWxseSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmsqHmnIlJROeahOaWsOWdkOagh+e7hO+8jOebtOaOpeS7juaVsOe7hOS4reenu+mZpA0KICAgICAgICAgIHRoaXMubm9kZVt0eXBlXS5iYWNrZ3JvdW5kUmVzb3VyY2VzW3Jlc291cmNlSWR4XS5jb29yZGluYXRlcy5zcGxpY2UoY29vcmRJZHgsIDEpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOWcuuaZr+e6p+iBlOmAieaLqeWZqOWPmOWMluWkhOeQhg0KICAgIGhhbmRsZVNjZW5lQ2FzY2FkZXJDaGFuZ2UodmFsLCB0eXBlLCByZXNvdXJjZUlkeCwgY29vcmRJZHgpIHsNCiAgICAgIGNvbnN0IHNjZW5lID0gdGhpcy5maW5kU2NlbmVCeUlkKHRoaXMuc2NlbmVUcmVlT3B0aW9ucywgdmFsKQ0KICAgICAgaWYgKHNjZW5lKSB7DQogICAgICAgIGNvbnN0IGNvb3JkID0gdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbcmVzb3VyY2VJZHhdLmNvb3JkaW5hdGVzW2Nvb3JkSWR4XQ0KICAgICAgICBjb29yZC5zY2VuZUNvZGUgPSBzY2VuZS5zY2VuZUNvZGUgfHwgJycNCiAgICAgICAgY29uc29sZS5sb2coJ+mAieaLqeeahOWcuuaZrzonLCBzY2VuZSwgJ+iuvue9rnNjZW5lQ29kZTonLCBjb29yZC5zY2VuZUNvZGUpDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDmoLnmja5JROafpeaJvuWcuuaZrw0KICAgIGZpbmRTY2VuZUJ5SWQodHJlZSwgaWQpIHsNCiAgICAgIGlmICghdHJlZSB8fCAhQXJyYXkuaXNBcnJheSh0cmVlKSkgcmV0dXJuIG51bGwNCiAgICAgIA0KICAgICAgZm9yIChjb25zdCBub2RlIG9mIHRyZWUpIHsNCiAgICAgICAgaWYgKG5vZGUuaWQgPT09IGlkKSB7DQogICAgICAgICAgcmV0dXJuIG5vZGUNCiAgICAgICAgfQ0KICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICAgIGNvbnN0IGZvdW5kID0gdGhpcy5maW5kU2NlbmVCeUlkKG5vZGUuY2hpbGRyZW4sIGlkKQ0KICAgICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBudWxsDQogICAgfSwNCiAgICANCiAgICAvLyDmoLzlvI/ljJblnZDmoIfmlbDmja7nlKjkuo7mj5DkuqQNCiAgICBmb3JtYXRDb29yZGluYXRlc0ZvclN1Ym1pdChjb29yZGluYXRlcykgew0KICAgICAgaWYgKCFjb29yZGluYXRlcyB8fCAhQXJyYXkuaXNBcnJheShjb29yZGluYXRlcykpIHsNCiAgICAgICAgcmV0dXJuIHsgY2xpY2tYOiAnJywgY2xpY2tZOiAnJyB9DQogICAgICB9DQogICAgICANCiAgICAgIGNvbnN0IHhWYWx1ZXMgPSBjb29yZGluYXRlcy5tYXAoY29vcmQgPT4gY29vcmQueCB8fCAnMCcpLmpvaW4oJywnKQ0KICAgICAgY29uc3QgeVZhbHVlcyA9IGNvb3JkaW5hdGVzLm1hcChjb29yZCA9PiBjb29yZC55IHx8ICcwJykuam9pbignLCcpDQogICAgICANCiAgICAgIHJldHVybiB7DQogICAgICAgIGNsaWNrWDogeFZhbHVlcywNCiAgICAgICAgY2xpY2tZOiB5VmFsdWVzDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDkv67mlLnnjrDmnInnmoTkuIrkvKDmlrnms5UNCiAgICBhc3luYyBiZWZvcmVVcGxvYWRTY2VuZUNvbmZpZ0ltZyhmaWxlLCBub2RlLCB0eXBlLCBhcnJheUtleU9yS2V5LCBpbmRleE9yVW5kZWZpbmVkLCBrZXlPclVuZGVmaW5lZCkgew0KICAgICAgaWYgKCFmaWxlLnR5cGUuc3RhcnRzV2l0aCgnaW1hZ2UvJykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5Lyg5Zu+54mH5paH5Lu277yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4iuS8oOWbvueJh++8jOivt+eojeWAmS4uLiIpDQogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdpbmR1c3RyeUNvZGUnLCB0aGlzLmxlZnRUcmVlSW5kdXN0cnlDb2RlKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ3NjZW5lQ29kZScsIG5vZGUuY29kZSkNCg0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCB1cGxvYWRTY2VuZUZpbGUoZm9ybURhdGEpDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCAmJiByZXMuZGF0YSkgew0KICAgICAgICAgIC8vIOWNleeLrOS4iuS8oOiDjOaZr+WbvueJh+mmluW4p+aXtu+8jOS9v+eUqCBmaWxlVXJsDQogICAgICAgICAgY29uc3QgaW1hZ2VVcmwgPSByZXMuZGF0YS5maWxlVXJsDQogICAgICAgICAgDQogICAgICAgICAgLy8g5Yik5pat5piv5ZCm5Li65pWw57uE5b2i5byP55qE5LiK5LygDQogICAgICAgICAgaWYgKHR5cGVvZiBpbmRleE9yVW5kZWZpbmVkID09PSAnbnVtYmVyJyAmJiBrZXlPclVuZGVmaW5lZCkgew0KICAgICAgICAgICAgLy8g5pWw57uE5b2i5byP77yabm9kZVt0eXBlXVthcnJheUtleV1baW5kZXhdW2tleV0NCiAgICAgICAgICAgIHRoaXMuJHNldChub2RlW3R5cGVdW2FycmF5S2V5T3JLZXldW2luZGV4T3JVbmRlZmluZWRdLCBrZXlPclVuZGVmaW5lZCwgaW1hZ2VVcmwpDQogICAgICAgICAgICBjb25zb2xlLmxvZygn5LiK5Lyg5oiQ5Yqf77yM6K6+572u5Zu+54mHVVJMOicsIGltYWdlVXJsKQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ+W9k+WJjXJlc291cmNlOicsIG5vZGVbdHlwZV1bYXJyYXlLZXlPcktleV1baW5kZXhPclVuZGVmaW5lZF0pDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWNleS4quWtl+auteW9ouW8j++8mm5vZGVbdHlwZV1ba2V5XQ0KICAgICAgICAgICAgdGhpcy4kc2V0KG5vZGVbdHlwZV0sIGFycmF5S2V5T3JLZXksIGltYWdlVXJsKQ0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip8nKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+S4iuS8oOWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S4iuS8oOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIA0KICAgIGFzeW5jIGJlZm9yZVVwbG9hZFNjZW5lQ29uZmlnRmlsZShmaWxlLCBub2RlLCB0eXBlLCBhcnJheUtleU9yS2V5LCBpbmRleE9yVW5kZWZpbmVkLCBrZXlPclVuZGVmaW5lZCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5LiK5Lyg5paH5Lu277yM6K+356iN5YCZLi4uIikNCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2luZHVzdHJ5Q29kZScsIHRoaXMubGVmdFRyZWVJbmR1c3RyeUNvZGUpDQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnc2NlbmVDb2RlJywgbm9kZS5jb2RlKQ0KDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZFNjZW5lRmlsZShmb3JtRGF0YSkNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgLy8g5Yik5pat5piv5ZCm5Li65pWw57uE5b2i5byP55qE5LiK5LygDQogICAgICAgICAgaWYgKHR5cGVvZiBpbmRleE9yVW5kZWZpbmVkID09PSAnbnVtYmVyJyAmJiBrZXlPclVuZGVmaW5lZCkgew0KICAgICAgICAgICAgLy8g5pWw57uE5b2i5byP5aSE55CGLi4uDQogICAgICAgICAgICB0aGlzLiRzZXQobm9kZVt0eXBlXVthcnJheUtleU9yS2V5XVtpbmRleE9yVW5kZWZpbmVkXSwga2V5T3JVbmRlZmluZWQsIHJlcy5kYXRhLmZpbGVVcmwpDQogICAgICAgICAgICANCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzLmRhdGEuZmlsZVVybC5zcGxpdCgnLycpLnBvcCgpDQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5maWxlTGlzdHNbdHlwZV0sIGluZGV4T3JVbmRlZmluZWQsIFt7DQogICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICB1cmw6IHJlcy5kYXRhLmZpbGVVcmwsDQogICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICAgICAgfV0pDQogICAgICAgICAgICANCiAgICAgICAgICAgIGlmIChmaWxlLnR5cGUgPT09ICd2aWRlby9tcDQnICYmIHJlcy5kYXRhLmltZ1VybCkgew0KICAgICAgICAgICAgICB0aGlzLiRzZXQobm9kZVt0eXBlXVthcnJheUtleU9yS2V5XVtpbmRleE9yVW5kZWZpbmVkXSwgJ2JnSW1nJywgcmVzLmRhdGEuaW1nVXJsKQ0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKn++8jOW3suiHquWKqOeUn+aIkOiDjOaZr+WbvueJh+mmluW4pycpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWNleS4quWtl+auteW9ouW8j++8mm5vZGVbdHlwZV1ba2V5XQ0KICAgICAgICAgICAgdGhpcy4kc2V0KG5vZGVbdHlwZV0sIGFycmF5S2V5T3JLZXksIHJlcy5kYXRhLmZpbGVVcmwpDQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOWmguaenOaYr+S7i+e7jeinhumikeS4iuS8oA0KICAgICAgICAgICAgaWYgKHR5cGUgPT09ICdpbnRyb2R1Y2VWaWRlb1ZvJyAmJiBhcnJheUtleU9yS2V5ID09PSAnYmFja2dyb3VuZEZpbGVVcmwnKSB7DQogICAgICAgICAgICAgIC8vIOabtOaWsOS7i+e7jeinhumikeaWh+S7tuWIl+ihqA0KICAgICAgICAgICAgICB0aGlzLnVwZGF0ZUludHJvZHVjZVZpZGVvRmlsZUxpc3QoKQ0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgaWYgKGZpbGUudHlwZSA9PT0gJ3ZpZGVvL21wNCcgJiYgcmVzLmRhdGEuaW1nVXJsKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kc2V0KG5vZGVbdHlwZV0sICdiYWNrZ3JvdW5kSW1nRmlsZVVybCcsIHJlcy5kYXRhLmltZ1VybCkNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKn++8jOW3suiHquWKqOeUn+aIkOS7i+e7jeinhumikemmluW4pycpDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip8nKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5LiK5Lyg5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIGFkZFBhaW5Qb2ludCh0eXBlKSB7DQogICAgICB0aGlzLm5vZGVbdHlwZV0ucGFpblBvaW50cyA9IHRoaXMubm9kZVt0eXBlXS5wYWluUG9pbnRzIHx8IFtdDQogICAgICB0aGlzLm5vZGVbdHlwZV0ucGFpblBvaW50cy5wdXNoKHsgdGl0bGU6ICcnLCBjb250ZW50czogWycnXSwgc2hvd1RpbWU6ICcnIH0pDQogICAgfSwNCiAgICByZW1vdmVQYWluUG9pbnQodHlwZSwgaWR4KSB7DQogICAgICB0aGlzLm5vZGVbdHlwZV0ucGFpblBvaW50cy5zcGxpY2UoaWR4LCAxKQ0KICAgIH0sDQogICAgYWRkUGFpbkNvbnRlbnQodHlwZSwgaWR4KSB7DQogICAgICB0aGlzLm5vZGVbdHlwZV0ucGFpblBvaW50c1tpZHhdLmNvbnRlbnRzLnB1c2goJycpDQogICAgfSwNCiAgICByZW1vdmVQYWluQ29udGVudCh0eXBlLCBpZHgsIGNpZHgpIHsNCiAgICAgIHRoaXMubm9kZVt0eXBlXS5wYWluUG9pbnRzW2lkeF0uY29udGVudHMuc3BsaWNlKGNpZHgsIDEpDQogICAgfSwNCiAgICBhZGRDb3N0Q29udGVudCgpIHsNCiAgICAgIHRoaXMubm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMucHVzaCgnJykNCiAgICB9LA0KICAgIHJlbW92ZUNvc3RDb250ZW50KGNpZHgpIHsNCiAgICAgIHRoaXMubm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMuc3BsaWNlKGNpZHgsIDEpDQogICAgfSwNCiAgICAvLyDojrflj5bog4zmma/mlofku7bliJfooagNCiAgICBnZXRCYWNrZ3JvdW5kRmlsZUxpc3Qobm9kZSwgdHlwZSwgaWR4KSB7DQogICAgICBjb25zdCByZXNvdXJjZSA9IG5vZGVbdHlwZV0uYmFja2dyb3VuZFJlc291cmNlc1tpZHhdDQogICAgICBpZiAocmVzb3VyY2UgJiYgcmVzb3VyY2UuYmdGaWxlKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzb3VyY2UuYmdGaWxlLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgcmV0dXJuIFt7DQogICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgdXJsOiByZXNvdXJjZS5iZ0ZpbGUsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpICsgaWR4DQogICAgICAgIH1dDQogICAgICB9DQogICAgICByZXR1cm4gW10NCiAgICB9LA0KICAgIC8vIOWkhOeQhuiDjOaZr+aWh+S7tuWIoOmZpA0KICAgIGhhbmRsZVJlbW92ZUJhY2tncm91bmRGaWxlKG5vZGUsIHR5cGUsIGlkeCkgew0KICAgICAgY29uc3QgcmVzb3VyY2UgPSBub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbaWR4XQ0KICAgICAgcmVzb3VyY2UuYmdGaWxlID0gJycNCiAgICAgIHJlc291cmNlLmJnSW1nID0gJycgLy8g5ZCM5pe25riF56m66IOM5pmv5Zu+54mH6aaW5binDQogICAgICB0aGlzLiRzZXQodGhpcy5maWxlTGlzdHNbdHlwZV0sIGlkeCwgW10pDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuW3suWIoOmZpCcpDQogICAgfSwNCiAgICAvLyDojrflj5bmlofku7bliJfooaggLSDnoa7kv53ov5Tlm57mraPnoa7nmoTmlofku7bliJfooagNCiAgICBnZXRGaWxlTGlzdCh0eXBlLCBpZHgpIHsNCiAgICAgIC8vIOajgOafpeWvueW6lOeahOi1hOa6kOaYr+WQpuecn+eahOacieaWh+S7tg0KICAgICAgY29uc3QgcmVzb3VyY2UgPSB0aGlzLm5vZGVbdHlwZV0gJiYgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXMgJiYgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbaWR4XQ0KICAgICAgDQogICAgICBpZiAoIXJlc291cmNlIHx8ICFyZXNvdXJjZS5iZ0ZpbGUgfHwgIXJlc291cmNlLmJnRmlsZS50cmltKCkpIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5paH5Lu277yM6L+U5Zue56m65pWw57uEDQogICAgICAgIHJldHVybiBbXQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlpoLmnpzmlofku7bliJfooajkuK3mnInmlbDmja7kuJTkuI7otYTmupDmlofku7bljLnphY3vvIzov5Tlm57mlofku7bliJfooagNCiAgICAgIGlmICh0aGlzLmZpbGVMaXN0c1t0eXBlXSAmJiB0aGlzLmZpbGVMaXN0c1t0eXBlXVtpZHhdICYmIHRoaXMuZmlsZUxpc3RzW3R5cGVdW2lkeF0ubGVuZ3RoID4gMCkgew0KICAgICAgICBjb25zdCBmaWxlTGlzdCA9IHRoaXMuZmlsZUxpc3RzW3R5cGVdW2lkeF0NCiAgICAgICAgLy8g6aqM6K+B5paH5Lu25YiX6KGo5Lit55qEVVJM5piv5ZCm5LiO6LWE5rqQ5Lit55qE5paH5Lu2VVJM5Yy56YWNDQogICAgICAgIGlmIChmaWxlTGlzdFswXS51cmwgPT09IHJlc291cmNlLmJnRmlsZSkgew0KICAgICAgICAgIHJldHVybiBmaWxlTGlzdA0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWmguaenOaWh+S7tuWIl+ihqOS4jeWMuemFjeaIluS4uuepuu+8jOS9hui1hOa6kOacieaWh+S7tu+8jOmHjeaWsOWIm+W7uuaWh+S7tuWIl+ihqA0KICAgICAgaWYgKHJlc291cmNlLmJnRmlsZSAmJiByZXNvdXJjZS5iZ0ZpbGUudHJpbSgpKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzb3VyY2UuYmdGaWxlLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgY29uc3QgbmV3RmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogcmVzb3VyY2UuYmdGaWxlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5maWxlTGlzdHNbdHlwZV0sIGlkeCwgbmV3RmlsZUxpc3QpDQogICAgICAgIHJldHVybiBuZXdGaWxlTGlzdA0KICAgICAgfQ0KICAgICAgDQogICAgICByZXR1cm4gW10NCiAgICB9LA0KICAgIGdldFVwbG9hZE1vZGUodHlwZSwgaWR4KSB7DQogICAgICBpZiAoIXRoaXMudXBsb2FkTW9kZXNbdHlwZV1baWR4XSkgew0KICAgICAgICAvLyDpu5jorqTmoLnmja7mmK/lkKblt7LmnInmlofku7ZVUkzmnaXliKTmlq3mqKHlvI8NCiAgICAgICAgY29uc3QgcmVzb3VyY2UgPSB0aGlzLm5vZGVbdHlwZV0uYmFja2dyb3VuZFJlc291cmNlc1tpZHhdDQogICAgICAgIGNvbnN0IGhhc0ZpbGUgPSByZXNvdXJjZSAmJiByZXNvdXJjZS5iZ0ZpbGUNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMudXBsb2FkTW9kZXNbdHlwZV0sIGlkeCwgaGFzRmlsZSA/ICd1cGxvYWQnIDogJ3VwbG9hZCcpDQogICAgICB9DQogICAgICByZXR1cm4gdGhpcy51cGxvYWRNb2Rlc1t0eXBlXVtpZHhdDQogICAgfSwNCiAgICBzZXRVcGxvYWRNb2RlKHR5cGUsIGlkeCwgdmFsdWUpIHsNCiAgICAgIGlmICh0eXBlb2YgaWR4ID09PSAnc3RyaW5nJykgew0KICAgICAgICAvLyDku4vnu43op4bpopHmqKHlvI/vvJp0eXBlPSdpbnRyb2R1Y2VWaWRlbycsIGlkeD0ndXBsb2FkJ+aIlid1cmwnDQogICAgICAgIHRoaXMuJHNldCh0aGlzLnVwbG9hZE1vZGVzLCB0eXBlLCBpZHgpDQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDog4zmma/otYTmupDmqKHlvI/vvJp0eXBlPSd0cmFkaXRpb24n5oiWJ3dpc2RvbTVnJywgaWR4PeaVsOWtl+e0ouW8lSwgdmFsdWU9J3VwbG9hZCfmiJYndXJsJw0KICAgICAgICB0aGlzLiRzZXQodGhpcy51cGxvYWRNb2Rlc1t0eXBlXSwgaWR4LCB2YWx1ZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVVybElucHV0KHR5cGUsIGlkeCwgdmFsdWUpIHsNCiAgICAgIC8vIOa4heepuuaWh+S7tuWIl+ihqA0KICAgICAgdGhpcy4kc2V0KHRoaXMuZmlsZUxpc3RzW3R5cGVdLCBpZHgsIFtdKQ0KICAgICAgDQogICAgICAvLyDlpoLmnpzovpPlhaXkuobpk77mjqXvvIzliJvlu7rlr7nlupTnmoTmlofku7bliJfooajpobkNCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZmlsZUxpc3RzW3R5cGVdLCBpZHgsIFt7DQogICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgdXJsOiB2YWx1ZSwNCiAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgfV0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmm7TmlrDku4vnu43op4bpopHmlofku7bliJfooagNCiAgICB1cGRhdGVJbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0KCkgew0KICAgICAgaWYgKHRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvICYmIHRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmJhY2tncm91bmRGaWxlVXJsKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gdGhpcy5ub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEZpbGVVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdGhpcy5ub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEZpbGVVcmwsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0SW50cm9kdWNlVmlkZW9GaWxlTGlzdCgpIHsNCiAgICAgIHJldHVybiB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QNCiAgICB9LA0KICAgIGhhbmRsZVJlbW92ZUludHJvZHVjZVZpZGVvKCkgew0KICAgICAgaWYgKHRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvKSB7DQogICAgICAgIHRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmJhY2tncm91bmRGaWxlVXJsID0gJycNCiAgICAgICAgdGhpcy5ub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEltZ0ZpbGVVcmwgPSAnJw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S7i+e7jeinhumikeW3suWIoOmZpCcpDQogICAgICAgIC8vIOWQjOaXtuabtOaWsOaWh+S7tuWIl+ihqA0KICAgICAgICB0aGlzLnVwZGF0ZUludHJvZHVjZVZpZGVvRmlsZUxpc3QoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5LuL57uN6KeG6aKR5byA5YWz5Y+Y5YyWDQogICAgb25JbnRyb2R1Y2VWaWRlb1N0YXR1c0NoYW5nZSh2YWwpIHsNCiAgICAgIC8vIOeugOWNleWkhOeQhu+8jOS4jemcgOimgeWkjeadgumAu+i+kQ0KICAgIH0sDQogICAgDQogICAgLy8g5Zu+54mH6aKE6KeIDQogICAgcHJldmlld0ltYWdlKHVybCkgew0KICAgICAgaWYgKHVybCkgew0KICAgICAgICB0aGlzLnByZXZpZXdJbWFnZVVybCA9IHVybA0KICAgICAgICB0aGlzLnByZXZpZXdWaXNpYmxlID0gdHJ1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5Yig6Zmk5LuL57uN6KeG6aKR5Zu+54mHDQogICAgZGVsZXRlSW50cm9kdWNlVmlkZW9JbWcoKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTmraTlm77niYflkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLm5vZGUuaW50cm9kdWNlVmlkZW9Wby5iYWNrZ3JvdW5kSW1nRmlsZVVybCA9ICcnDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Zu+54mH5bey5Yig6ZmkJykNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQ0KICAgIH0sDQogICAgDQogICAgLy8g5Yig6Zmk6IOM5pmv6LWE5rqQ5Zu+54mHDQogICAgZGVsZXRlQmFja2dyb3VuZEltZyh0eXBlLCBpZHgpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuWIoOmZpOatpOWbvueJh+WQl++8nycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMubm9kZVt0eXBlXS5iYWNrZ3JvdW5kUmVzb3VyY2VzW2lkeF0uYmdJbWcgPSAnJw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WbvueJh+W3suWIoOmZpCcpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIC8vIOmqjOivgVhNTCBLZXnovpPlhaXvvIjlj6rlhYHorrjoi7HmlofjgIHmlbDlrZflkozkuIvliJLnur/vvIkNCiAgICB2YWxpZGF0ZVhtbEtleSh2YWx1ZSwgdHlwZSkgew0KICAgICAgLy8g5Y+q5L+d55WZ6Iux5paH5a2X5q+N44CB5pWw5a2X5ZKM5LiL5YiS57q/DQogICAgICBjb25zdCBmaWx0ZXJlZFZhbHVlID0gdmFsdWUucmVwbGFjZSgvW15hLXpBLVowLTlfXS9nLCAnJykNCiAgICAgIHRoaXMubm9kZVt0eXBlXS5wYW5vcmFtaWNWaWV3WG1sS2V5ID0gZmlsdGVyZWRWYWx1ZQ0KICAgIH0sDQogICAgaGFuZGxlSW50cm9kdWNlVmlkZW9VcmxJbnB1dCh2YWx1ZSkgew0KICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW10NCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHZhbHVlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgICAgdGhpcy51cGRhdGVJbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0KCkNCiAgICB9LA0KICAgIC8vIOWkhOeQhuWdkOagh+aVsOWtl+i+k+WFpQ0KICAgIGhhbmRsZUNvb3JkTnVtYmVySW5wdXQodmFsLCBjb29yZCwgZmllbGQpIHsNCiAgICAgIC8vIOWmguaenHZhbOS4um51bGzmiJZ1bmRlZmluZWTvvIzorr7nva7kuLrnqbrlrZfnrKbkuLINCiAgICAgIGlmICh2YWwgPT09IG51bGwgfHwgdmFsID09PSB1bmRlZmluZWQpIHsNCiAgICAgICAgY29vcmRbZmllbGRdID0gJycNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvb3JkW2ZpZWxkXSA9IHZhbA0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDliKTmlq3lvZPliY3oioLngrnmmK/lkKbmnInlrZDoioLngrkNCiAgICBoYXNDaGlsZHJlbigpIHsNCiAgICAgIHJldHVybiB0aGlzLm5vZGUgJiYgdGhpcy5ub2RlLmNoaWxkcmVuICYmIHRoaXMubm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwDQogICAgfQ0KICB9LA0KICBjb21wb25lbnRzOiB7DQogICAgU2NlbmVDb25maWdOb2RlOiBudWxsIC8vIOmAkuW9kuazqOWGjO+8jOS4u+mhtemdomltcG9ydOaXtuihpeWFqA0KICB9DQp9DQo="}, {"version": 3, "sources": ["SceneConfigNode.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAol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file": "SceneConfigNode.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <el-card class=\"mini-block\" shadow=\"never\">\r\n    <div slot=\"header\">\r\n      <span>{{ node.name }}</span>\r\n      <el-switch v-model=\"node.status\" style=\"margin-left: 16px;\" :active-value=\"'0'\" :inactive-value=\"'1'\" @change=\"onStatusChange\" />\r\n    </div>\r\n    <div v-show=\"node.status === '0'\" class=\"sub-category-body\">\r\n      <!-- 主表单项 - 编码名称并排，坐标并排 -->\r\n      <el-form label-width=\"120px\">\r\n        <el-row :gutter=\"16\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"场景编码\">\r\n              <el-input v-model=\"node.code\" disabled type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"场景名称\">\r\n              <el-input v-model=\"node.name\" disabled type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"16\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"坐标X\" required>\r\n              <el-input v-model=\"node.x\" placeholder=\"请输入坐标X（百分比）\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"坐标Y\" required>\r\n              <el-input v-model=\"node.y\" placeholder=\"请输入坐标Y（百分比）\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"场景类型\" required>\r\n          <el-select v-model=\"node.type\" placeholder=\"请选择类型\">\r\n            <el-option label=\"默认\" value=\"1\" />\r\n            <el-option label=\"AI\" value=\"2\" />\r\n            <el-option label=\"三化\" value=\"3\" />\r\n            <el-option label=\"AI+三化\" value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <!-- 只有存在下级菜单时才显示 -->\r\n        <el-row :gutter=\"16\" v-if=\"hasChildren\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否展开下级\" required>\r\n              <el-radio-group v-model=\"node.isUnfold\">\r\n                <el-radio label=\"0\">展示</el-radio>\r\n                <el-radio label=\"1\">关闭</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"下级展示位置\" required>\r\n              <el-radio-group v-model=\"node.displayLocation\">\r\n                <el-radio label=\"0\">默认</el-radio>\r\n                <el-radio label=\"1\">右下</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"下级菜单分类\" required>\r\n              <el-radio-group v-model=\"node.treeClassification\">\r\n                <el-radio label=\"1\">传统</el-radio>\r\n                <el-radio label=\"2\">5G</el-radio>\r\n                <el-radio label=\"3\">全部</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 介绍视频和成本预估并排布局 -->\r\n      <el-row :gutter=\"20\">\r\n        <!-- 左侧：介绍视频模块 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"mini-block\" shadow=\"never\">\r\n            <div slot=\"header\">\r\n              <span>介绍视频</span>\r\n              <el-switch \r\n                v-model=\"node.introduceVideoVo.status\" \r\n                style=\"float:right;\"\r\n                :active-value=\"'0'\" \r\n                :inactive-value=\"'1'\" \r\n                @change=\"onIntroduceVideoStatusChange\" />\r\n            </div>\r\n            <div v-show=\"node.introduceVideoVo.status === '0'\">\r\n              <el-form label-width=\"120px\">\r\n                <el-form-item label=\"介绍视频首帧\">\r\n                  <el-upload\r\n                    class=\"upload image-upload\"\r\n                    action=\"#\"\r\n                    :show-file-list=\"false\"\r\n                    list-type=\"picture-card\"\r\n                    accept=\"image/*\"\r\n                    :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'introduceVideoVo', 'backgroundImgFileUrl', null, null)\"\r\n                    :http-request=\"() => {}\"\r\n                  >\r\n                    <div v-if=\"node.introduceVideoVo.backgroundImgFileUrl\" class=\"image-preview-container\">\r\n                      <img :src=\"node.introduceVideoVo.backgroundImgFileUrl\" class=\"upload-image\" />\r\n                      <div class=\"image-overlay\">\r\n                        <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(node.introduceVideoVo.backgroundImgFileUrl)\" title=\"预览\"></i>\r\n                        <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteIntroduceVideoImg\" title=\"删除\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <i v-else class=\"el-icon-plus\"></i>\r\n                  </el-upload>\r\n                </el-form-item>\r\n                <el-form-item label=\"介绍视频\">\r\n                  <div style=\"margin-bottom: 8px;\">\r\n                    <el-radio-group :value=\"uploadModes.introduceVideo || 'upload'\" @input=\"value => setUploadMode('introduceVideo', value)\" size=\"small\">\r\n                      <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                      <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                  \r\n                  <!-- 上传模式 -->\r\n                  <el-upload\r\n                    v-if=\"(uploadModes.introduceVideo || 'upload') === 'upload'\"\r\n                    action=\"#\"\r\n                    :show-file-list=\"true\"\r\n                    :file-list=\"getIntroduceVideoFileList()\"\r\n                    accept=\".mp4\"\r\n                    :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'introduceVideoVo', 'backgroundFileUrl', null, null)\"\r\n                    :http-request=\"() => {}\"\r\n                    :on-remove=\"handleRemoveIntroduceVideo\"\r\n                  >\r\n                    <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传mp4文件</div>\r\n                  </el-upload>\r\n                  \r\n                  <!-- 链接模式 -->\r\n                  <el-input\r\n                    v-else\r\n                    v-model=\"node.introduceVideoVo.backgroundFileUrl\"\r\n                    placeholder=\"请输入视频链接\"\r\n                    @input=\"handleIntroduceVideoUrlInput\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        \r\n        <!-- 右侧：成本预估模块 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"mini-block\" shadow=\"never\">\r\n            <div slot=\"header\">\r\n              <span>成本预估</span>\r\n              <el-switch v-model=\"node.costEstimate.status\" style=\"float:right;\" :active-value=\"'0'\" :inactive-value=\"'1'\" />\r\n            </div>\r\n            <div v-show=\"node.costEstimate.status === '0'\">\r\n              <el-form label-width=\"120px\">\r\n                <el-form-item label=\"大标题\" required>\r\n                  <el-input v-model=\"node.costEstimate.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"内容\" required>\r\n                  <div v-for=\"(content, cidx) in node.costEstimate.contents\" :key=\"'costEstimate-content-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                    <el-input v-model=\"node.costEstimate.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                    <el-button icon=\"el-icon-delete\" @click=\"removeCostContent(cidx)\" circle size=\"mini\" />\r\n                  </div>\r\n                  <el-button type=\"primary\" plain @click=\"addCostContent\">增加内容</el-button>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <!-- 传统小类 -->\r\n      <el-card class=\"mini-block\" shadow=\"never\">\r\n        <div slot=\"header\">传统</div>\r\n        <el-form label-width=\"120px\">\r\n          <el-form-item label=\"名称\" required>\r\n            <el-input v-model=\"node.tradition.name\" placeholder=\"请输入名称\" type=\"text\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"全景图唯一标识\" required>\r\n            <el-input \r\n              v-model=\"node.tradition.panoramicViewXmlKey\" \r\n              placeholder=\"请输入全景图唯一标识（仅英文）\" \r\n              type=\"text\"\r\n              @input=\"validateXmlKey($event, 'tradition')\"\r\n            />\r\n          </el-form-item>\r\n          \r\n          <!-- 传统模块背景资源 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">背景资源</div>\r\n            <div v-for=\"(resource, idx) in node.tradition.backgroundResources\" :key=\"idx\" class=\"background-resource-item\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">背景资源 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removeBackgroundResource('tradition', idx)\">删除</el-button>\r\n              </div>\r\n              <!-- <el-form-item label=\"标签\" required>\r\n                <el-input v-model=\"resource.label\" placeholder=\"请输入标签\" type=\"text\" />\r\n              </el-form-item> -->\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"坐标组\" required>\r\n                    <div v-for=\"(coord, coordIdx) in resource.coordinates\" :key=\"coordIdx\" class=\"coordinate-group\">\r\n                      <div class=\"coordinate-header\">\r\n                        <span>坐标组 {{ coordIdx + 1 }}</span>\r\n                        <el-button \r\n                          type=\"danger\" \r\n                          size=\"mini\" \r\n                          plain \r\n                          @click=\"removeCoordinate('tradition', idx, coordIdx)\"\r\n                        >\r\n                          删除\r\n                        </el-button>\r\n                      </div>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"X坐标\">\r\n                            <el-input v-model=\"coord.x\" placeholder=\"请输入X坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"Y坐标\">\r\n                            <el-input v-model=\"coord.y\" placeholder=\"请输入Y坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"绑定场景\">\r\n                            <el-cascader\r\n                              v-model=\"coord.sceneId\"\r\n                              :options=\"sceneTreeOptions\"\r\n                              :props=\"sceneCascaderProps\"\r\n                              filterable\r\n                              check-strictly\r\n                              placeholder=\"选择场景\"\r\n                              style=\"width: 100%;\"\r\n                              @change=\"val => handleSceneCascaderChange(val, 'tradition', idx, coordIdx)\"\r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"宽度\">\r\n                            <el-input-number \r\n                              :value=\"coord.wide\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'wide')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入宽度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"高度\">\r\n                            <el-input-number \r\n                              :value=\"coord.high\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'high')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入高度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"全景xml标签\">\r\n                            <el-input v-model=\"coord.xmlKey\" placeholder=\"请输入全景xml标签\" style=\"width: 100%\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                    <el-button type=\"primary\" size=\"mini\" plain @click=\"addCoordinate('tradition', idx)\">\r\n                      增加坐标组\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'tradition', 'backgroundResources', idx, 'bgImg')\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"resource.bgImg\" class=\"image-preview-container\">\r\n                        <img :src=\"resource.bgImg\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(resource.bgImg)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBackgroundImg('tradition', idx)\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\" required>\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.tradition[idx] || 'upload'\" @input=\"value => setUploadMode('tradition', idx, value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.tradition[idx] || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"getFileList('tradition', idx)\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'tradition', 'backgroundResources', idx, 'bgFile')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"(file, fileList) => handleRemoveBackgroundFile(node, 'tradition', idx)\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"resource.bgFile\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"value => handleUrlInput('tradition', idx, value)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addBackgroundResource('tradition')\">增加背景资源</el-button>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 传统模块痛点价值 - 统一输入框长度 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">痛点价值</div>\r\n            <div v-for=\"(point, idx) in node.tradition.painPoints\" :key=\"'tradition-' + idx\" class=\"pain-point-block\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">痛点价值 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removePainPoint('tradition', idx)\">删除</el-button>\r\n              </div>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"大标题\" required>\r\n                    <el-input v-model=\"point.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"展示时间\" required>\r\n                    <el-input-number v-model=\"point.showTime\" :min=\"0\" style=\"width: 100%\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-form-item label=\"内容\" required>\r\n                <el-row :gutter=\"16\">\r\n                  <el-col :span=\"16\">\r\n                    <div v-for=\"(content, cidx) in point.contents\" :key=\"'tradition-content-' + idx + '-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                      <el-input v-model=\"point.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                      <el-button icon=\"el-icon-delete\" @click=\"removePainContent('tradition', idx, cidx)\" circle size=\"mini\" />\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addPainContent('tradition', idx)\">增加内容</el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addPainPoint('tradition')\">增加痛点价值</el-button>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </el-card>\r\n      <!-- 5G智慧小类 -->\r\n      <el-card class=\"mini-block\" shadow=\"never\">\r\n        <div slot=\"header\">5G智慧</div>\r\n        <el-form label-width=\"120px\">\r\n          <el-form-item label=\"名称\" required>\r\n            <el-input v-model=\"node.wisdom5g.name\" placeholder=\"请输入名称\" type=\"text\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"全景图唯一标识\" required>\r\n            <el-input \r\n              v-model=\"node.wisdom5g.panoramicViewXmlKey\" \r\n              placeholder=\"请输入全景图唯一标识（仅英文）\" \r\n              type=\"text\"\r\n              @input=\"validateXmlKey($event, 'wisdom5g')\"\r\n            />\r\n          </el-form-item>\r\n          \r\n          <!-- 5G智慧模块背景资源 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">背景资源</div>\r\n            <div v-for=\"(resource, idx) in node.wisdom5g.backgroundResources\" :key=\"'wisdom5g-bg-' + idx\" class=\"background-resource-item\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">背景资源 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removeBackgroundResource('wisdom5g', idx)\">删除</el-button>\r\n              </div>\r\n              <!-- <el-form-item label=\"标签\" required>\r\n                <el-input v-model=\"resource.label\" placeholder=\"请输入标签\" type=\"text\" />\r\n              </el-form-item> -->\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"坐标组\" required>\r\n                    <div v-for=\"(coord, coordIdx) in resource.coordinates\" :key=\"coordIdx\" class=\"coordinate-group\">\r\n                      <div class=\"coordinate-header\">\r\n                        <span>坐标组 {{ coordIdx + 1 }}</span>\r\n                        <el-button \r\n                          type=\"danger\" \r\n                          size=\"mini\" \r\n                          plain \r\n                          @click=\"removeCoordinate('wisdom5g', idx, coordIdx)\"\r\n                        >\r\n                          删除\r\n                        </el-button>\r\n                      </div>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"X坐标\">\r\n                            <el-input v-model=\"coord.x\" placeholder=\"请输入X坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"Y坐标\">\r\n                            <el-input v-model=\"coord.y\" placeholder=\"请输入Y坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"绑定场景\">\r\n                            <el-cascader\r\n                              v-model=\"coord.sceneId\"\r\n                              :options=\"sceneTreeOptions\"\r\n                              :props=\"sceneCascaderProps\"\r\n                              filterable\r\n                              check-strictly\r\n                              placeholder=\"选择场景\"\r\n                              style=\"width: 100%;\"\r\n                              @change=\"val => handleSceneCascaderChange(val, 'wisdom5g', idx, coordIdx)\"\r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"宽度\">\r\n                            <el-input-number \r\n                              :value=\"coord.wide\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'wide')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入宽度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"高度\">\r\n                            <el-input-number \r\n                              :value=\"coord.high\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'high')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入高度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"全景xml标签\">\r\n                            <el-input v-model=\"coord.xmlKey\" placeholder=\"请输入全景xml标签\" style=\"width: 100%\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                    <el-button type=\"primary\" size=\"mini\" plain @click=\"addCoordinate('wisdom5g', idx)\">\r\n                      增加坐标组\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'wisdom5g', 'backgroundResources', idx, 'bgImg')\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"resource.bgImg\" class=\"image-preview-container\">\r\n                        <img :src=\"resource.bgImg\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(resource.bgImg)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBackgroundImg('wisdom5g', idx)\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\" required>\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.wisdom5g[idx] || 'upload'\" @input=\"value => setUploadMode('wisdom5g', idx, value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.wisdom5g[idx] || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"getFileList('wisdom5g', idx)\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'wisdom5g', 'backgroundResources', idx, 'bgFile')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"(file, fileList) => handleRemoveBackgroundFile(node, 'wisdom5g', idx)\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"resource.bgFile\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"value => handleUrlInput('wisdom5g', idx, value)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addBackgroundResource('wisdom5g')\">增加背景资源</el-button>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 5G智慧模块痛点价值 - 统一输入框长度 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">痛点价值</div>\r\n            <div v-for=\"(point, idx) in node.wisdom5g.painPoints\" :key=\"'wisdom5g-' + idx\" class=\"pain-point-block\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">痛点价值 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removePainPoint('wisdom5g', idx)\">删除</el-button>\r\n              </div>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"大标题\" required>\r\n                    <el-input v-model=\"point.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"展示时间\" required>\r\n                    <el-input-number v-model=\"point.showTime\" :min=\"0\" style=\"width: 100%\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-form-item label=\"内容\" required>\r\n                <el-row :gutter=\"16\">\r\n                  <el-col :span=\"16\">\r\n                    <div v-for=\"(content, cidx) in point.contents\" :key=\"'wisdom5g-content-' + idx + '-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                      <el-input v-model=\"point.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                      <el-button icon=\"el-icon-delete\" @click=\"removePainContent('wisdom5g', idx, cidx)\" circle size=\"mini\" />\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addPainContent('wisdom5g', idx)\">增加内容</el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addPainPoint('wisdom5g')\">增加痛点价值</el-button>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </el-card>\r\n    </div>\r\n    \r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"previewVisible\"\r\n      title=\"图片预览\"\r\n      width=\"60%\"\r\n      append-to-body\r\n    >\r\n      <div class=\"preview-container\">\r\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\r\n      </div>\r\n    </el-dialog>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport { uploadSceneFile, backgroundFileDel, fileBindDel } from '@/api/view/sceneView'\r\n\r\nexport default {\r\n  name: 'SceneConfigNode',\r\n  props: {\r\n    node: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    rootTree: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    sceneTreeOptions: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    leftTreeIndustryCode: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  watch: {\r\n    'node.status'(val) {\r\n      if (val === '0') {\r\n        this.findAndOpenParent(this.node.id, this.rootTree)\r\n      }\r\n    },\r\n    node: {\r\n      handler(newNode, oldNode) {\r\n        if (newNode && newNode !== oldNode) {\r\n          this.$nextTick(() => {\r\n            // 初始化节点数据\r\n            this.initNodeData()\r\n            \r\n            // 确保introduceVideoVo存在且有完整数据时才处理\r\n            if (newNode.introduceVideoVo && newNode.introduceVideoVo.hasOwnProperty('status')) {\r\n              // 数据已存在，不需要初始化，直接更新文件列表\r\n              this.updateIntroduceVideoFileList()\r\n            } else if (!newNode.introduceVideoVo) {\r\n              // 数据不存在时才初始化\r\n              this.initIntroduceVideo()\r\n            }\r\n            // 重新初始化文件列表，清除可能的继承问题\r\n            this.initFileLists()\r\n          })\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    // 监听introduceVideoVo整个对象的变化\r\n    'node.introduceVideoVo': {\r\n      handler(newVal) {\r\n        if (newVal && newVal.status !== undefined) {\r\n          this.updateIntroduceVideoFileList()\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      fileLists: {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: []\r\n      },\r\n      uploadModes: {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: 'upload'\r\n      },\r\n      // 添加介绍视频文件列表缓存\r\n      introduceVideoFileList: [],\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      // 场景级联选择器配置\r\n      sceneCascaderProps: {\r\n        label: 'sceneName',\r\n        value: 'id',\r\n        children: 'children',\r\n        emitPath: false,\r\n        checkStrictly: true\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.initIntroduceVideo()\r\n    this.initNodeData()\r\n  },\r\n  mounted() {\r\n    this.initData()\r\n    this.initFileLists()\r\n  },\r\n  methods: {\r\n    initData() {\r\n      // 确保5G智慧模块有默认结构并深拷贝避免引用共享\r\n      if (!this.node.wisdom5g) {\r\n        this.$set(this.node, 'wisdom5g', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 深拷贝现有的5G智慧数据，避免多个场景共享引用\r\n        const wisdom5gCopy = JSON.parse(JSON.stringify(this.node.wisdom5g))\r\n        this.$set(this.node, 'wisdom5g', wisdom5gCopy)\r\n        \r\n        // 修正字段映射\r\n        if (this.node.wisdom5g.backgroundResources) {\r\n          this.node.wisdom5g.backgroundResources.forEach(resource => {\r\n            if (resource.backgroundImgFileUrl && !resource.bgImg) {\r\n              resource.bgImg = resource.backgroundImgFileUrl\r\n            }\r\n            if (resource.backgroundFileUrl && !resource.bgFile) {\r\n              resource.bgFile = resource.backgroundFileUrl\r\n            }\r\n            if (resource.tag && !resource.label) {\r\n              resource.label = resource.tag\r\n            }\r\n          })\r\n        }\r\n      }\r\n      \r\n      // 同样处理传统模块\r\n      if (!this.node.tradition) {\r\n        this.$set(this.node, 'tradition', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 深拷贝传统模块数据\r\n        const traditionCopy = JSON.parse(JSON.stringify(this.node.tradition))\r\n        this.$set(this.node, 'tradition', traditionCopy)\r\n      }\r\n    },\r\n    // 初始化文件列表\r\n    initFileLists() {\r\n      // 清空所有文件列表，避免继承问题\r\n      this.fileLists = {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: []\r\n      }\r\n      \r\n      // 初始化传统模块的文件列表\r\n      if (this.node.tradition && this.node.tradition.backgroundResources) {\r\n        this.node.tradition.backgroundResources.forEach((resource, idx) => {\r\n          // 只有当资源确实有文件时才创建文件列表\r\n          if (resource.bgFile && resource.bgFile.trim()) {\r\n            const fileName = resource.bgFile.split('/').pop()\r\n            this.$set(this.fileLists.tradition, idx, [{\r\n              name: fileName,\r\n              url: resource.bgFile,\r\n              uid: Date.now() + idx\r\n            }])\r\n          } else {\r\n            // 明确设置为空数组\r\n            this.$set(this.fileLists.tradition, idx, [])\r\n          }\r\n        })\r\n      }\r\n      \r\n      // 初始化5G智慧模块的文件列表\r\n      if (this.node.wisdom5g && this.node.wisdom5g.backgroundResources) {\r\n        this.node.wisdom5g.backgroundResources.forEach((resource, idx) => {\r\n          // 只有当资源确实有文件时才创建文件列表\r\n          if (resource.bgFile && resource.bgFile.trim()) {\r\n            const fileName = resource.bgFile.split('/').pop()\r\n            this.$set(this.fileLists.wisdom5g, idx, [{\r\n              name: fileName,\r\n              url: resource.bgFile,\r\n              uid: Date.now() + idx + 1000\r\n            }])\r\n          } else {\r\n            // 明确设置为空数组\r\n            this.$set(this.fileLists.wisdom5g, idx, [])\r\n          }\r\n        })\r\n      }\r\n    },\r\n    // 初始化介绍视频对象\r\n    initIntroduceVideo() {\r\n      if (!this.node.introduceVideoVo) {\r\n        this.$set(this.node, 'introduceVideoVo', {\r\n          id: '',\r\n          type: '',\r\n          viewInfoId: '',\r\n          status: '0',\r\n          backgroundImgFileUrl: '',\r\n          backgroundFileUrl: ''\r\n        })\r\n      }\r\n      // 完全删除status的重新设置，保持接口返回的原始值\r\n      this.updateIntroduceVideoFileList()\r\n    },\r\n    // 确保其他数据结构也有默认值\r\n    initNodeData() {\r\n      // 确保基础字段有默认值 - 只在真正没有值时才设置默认值\r\n      if (this.node.isUnfold === undefined || this.node.isUnfold === null || this.node.isUnfold === '') {\r\n        this.$set(this.node, 'isUnfold', '1')\r\n      }\r\n      if (this.node.displayLocation === undefined || this.node.displayLocation === null || this.node.displayLocation === '') {\r\n        this.$set(this.node, 'displayLocation', '0')\r\n      }\r\n      if (this.node.treeClassification === undefined || this.node.treeClassification === null || this.node.treeClassification === '') {\r\n        this.$set(this.node, 'treeClassification', '3')\r\n      }\r\n      \r\n      // 确保传统模块有默认结构\r\n      if (!this.node.tradition) {\r\n        this.$set(this.node, 'tradition', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      }\r\n      \r\n      // 确保5G智慧模块有默认结构并修正字段映射\r\n      if (!this.node.wisdom5g) {\r\n        this.$set(this.node, 'wisdom5g', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 修正5G智慧模块的字段映射\r\n        if (this.node.wisdom5g.backgroundResources) {\r\n          this.node.wisdom5g.backgroundResources.forEach(resource => {\r\n            // 确保字段名称正确\r\n            if (resource.backgroundImgFileUrl && !resource.bgImg) {\r\n              resource.bgImg = resource.backgroundImgFileUrl\r\n            }\r\n            if (resource.backgroundFileUrl && !resource.bgFile) {\r\n              resource.bgFile = resource.backgroundFileUrl\r\n            }\r\n            if (resource.tag && !resource.label) {\r\n              resource.label = resource.tag\r\n            }\r\n          })\r\n        }\r\n      }\r\n      \r\n      // 确保成本预估有默认结构\r\n      if (!this.node.costEstimate) {\r\n        this.$set(this.node, 'costEstimate', {\r\n          status: '0',\r\n          title: '',\r\n          contents: []\r\n        })\r\n      }\r\n      \r\n      // 确保成本预估有status字段\r\n      if (this.node.costEstimate && this.node.costEstimate.status === undefined) {\r\n        this.$set(this.node.costEstimate, 'status', '0')\r\n      }\r\n    },\r\n    // 开关联动\r\n    onStatusChange(val) {\r\n      if (val === '0') {\r\n        // 开启时递归开启所有父级\r\n        let p = this.parent\r\n        while (p) {\r\n          if (p.status !== '0') p.status = '0'\r\n          p = p.parent\r\n        }\r\n      } else {\r\n        // 关闭时递归关闭所有子级\r\n        function closeChildren(node) {\r\n          if (node.children && node.children.length) {\r\n            node.children.forEach(child => {\r\n              child.status = '1'\r\n              closeChildren(child)\r\n            })\r\n          }\r\n        }\r\n        closeChildren(this.node)\r\n      }\r\n    },\r\n    // 递归查找并开启所有父级\r\n    findAndOpenParent(id, tree) {\r\n      function helper(nodes, parent) {\r\n        for (let node of nodes) {\r\n          if (node.id === id) {\r\n            if (parent) parent.status = '0'\r\n            return true\r\n          }\r\n          if (node.children && node.children.length) {\r\n            if (helper(node.children, node)) {\r\n              if (parent) parent.status = '0'\r\n              return true\r\n            }\r\n          }\r\n        }\r\n        return false\r\n      }\r\n      helper(tree, null)\r\n    },\r\n    // 新增背景资源管理方法\r\n    addBackgroundResource(type) {\r\n      if (!this.node[type].backgroundResources) {\r\n        this.$set(this.node[type], 'backgroundResources', [])\r\n      }\r\n      \r\n      const newIdx = this.node[type].backgroundResources.length\r\n      \r\n      // 创建完全独立的新资源对象，不包含默认坐标组\r\n      const newResource = { \r\n        id: null,\r\n        tag: '', \r\n        status: '',\r\n        type: '',\r\n        viewInfoId: '',\r\n        bgImg: '', \r\n        bgFile: '',\r\n        coordinates: [] // 改为空数组，不默认添加坐标组\r\n      }\r\n      \r\n      // 使用深拷贝确保对象完全独立\r\n      const independentResource = JSON.parse(JSON.stringify(newResource))\r\n      this.node[type].backgroundResources.push(independentResource)\r\n      \r\n      // 初始化对应的独立文件列表\r\n      this.$set(this.fileLists[type], newIdx, [])\r\n      console.log('添加新背景资源:', independentResource)\r\n    },\r\n    \r\n    // 删除背景资源\r\n    async removeBackgroundResource(type, idx) {\r\n      const resource = this.node[type].backgroundResources[idx]\r\n      \r\n      this.$confirm('确定删除此背景资源吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        // 如果背景资源有ID，调用删除接口\r\n        if (resource.id) {\r\n          try {\r\n            this.$modal.loading(\"正在删除背景资源，请稍候...\")\r\n            const res = await backgroundFileDel({ id: resource.id })\r\n            if (res.code === 0) {\r\n              this.node[type].backgroundResources.splice(idx, 1)\r\n              this.$message.success('删除成功')\r\n            } else {\r\n              this.$message.error(res.msg || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('删除失败')\r\n          } finally {\r\n            this.$modal.closeLoading()\r\n          }\r\n        } else {\r\n          // 没有ID的新背景资源，直接从数组中移除\r\n          this.node[type].backgroundResources.splice(idx, 1)\r\n          this.$message.success('删除成功')\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 添加坐标组\r\n    addCoordinate(type, resourceIdx) {\r\n      const resource = this.node[type].backgroundResources[resourceIdx]\r\n      if (!resource.coordinates) {\r\n        this.$set(resource, 'coordinates', [])\r\n      }\r\n      resource.coordinates.push({ \r\n        id: 0,\r\n        fileId: 0,\r\n        x: '', \r\n        y: '', \r\n        wide: '', \r\n        high: '', \r\n        sceneId: '', \r\n        sceneCode: '',\r\n        xmlKey: ''\r\n      })\r\n    },\r\n    \r\n    // 删除坐标组\r\n    async removeCoordinate(type, resourceIdx, coordIdx) {\r\n      const coord = this.node[type].backgroundResources[resourceIdx].coordinates[coordIdx]\r\n      \r\n      this.$confirm('确定删除此坐标组吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        // 如果坐标组有ID，调用删除接口\r\n        if (coord.id) {\r\n          try {\r\n            this.$modal.loading(\"正在删除坐标组，请稍候...\")\r\n            const res = await fileBindDel({ id: coord.id })\r\n            if (res.code === 0) {\r\n              this.node[type].backgroundResources[resourceIdx].coordinates.splice(coordIdx, 1)\r\n              this.$message.success('删除成功')\r\n            } else {\r\n              this.$message.error(res.msg || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('删除失败')\r\n          } finally {\r\n            this.$modal.closeLoading()\r\n          }\r\n        } else {\r\n          // 没有ID的新坐标组，直接从数组中移除\r\n          this.node[type].backgroundResources[resourceIdx].coordinates.splice(coordIdx, 1)\r\n          this.$message.success('删除成功')\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 场景级联选择器变化处理\r\n    handleSceneCascaderChange(val, type, resourceIdx, coordIdx) {\r\n      const scene = this.findSceneById(this.sceneTreeOptions, val)\r\n      if (scene) {\r\n        const coord = this.node[type].backgroundResources[resourceIdx].coordinates[coordIdx]\r\n        coord.sceneCode = scene.sceneCode || ''\r\n        console.log('选择的场景:', scene, '设置sceneCode:', coord.sceneCode)\r\n      }\r\n    },\r\n    \r\n    // 根据ID查找场景\r\n    findSceneById(tree, id) {\r\n      if (!tree || !Array.isArray(tree)) return null\r\n      \r\n      for (const node of tree) {\r\n        if (node.id === id) {\r\n          return node\r\n        }\r\n        if (node.children && node.children.length) {\r\n          const found = this.findSceneById(node.children, id)\r\n          if (found) return found\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    \r\n    // 格式化坐标数据用于提交\r\n    formatCoordinatesForSubmit(coordinates) {\r\n      if (!coordinates || !Array.isArray(coordinates)) {\r\n        return { clickX: '', clickY: '' }\r\n      }\r\n      \r\n      const xValues = coordinates.map(coord => coord.x || '0').join(',')\r\n      const yValues = coordinates.map(coord => coord.y || '0').join(',')\r\n      \r\n      return {\r\n        clickX: xValues,\r\n        clickY: yValues\r\n      }\r\n    },\r\n    \r\n    // 修改现有的上传方法\r\n    async beforeUploadSceneConfigImg(file, node, type, arrayKeyOrKey, indexOrUndefined, keyOrUndefined) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传图片，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.leftTreeIndustryCode)\r\n        formData.append('sceneCode', node.code)\r\n\r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 单独上传背景图片首帧时，使用 fileUrl\r\n          const imageUrl = res.data.fileUrl\r\n          \r\n          // 判断是否为数组形式的上传\r\n          if (typeof indexOrUndefined === 'number' && keyOrUndefined) {\r\n            // 数组形式：node[type][arrayKey][index][key]\r\n            this.$set(node[type][arrayKeyOrKey][indexOrUndefined], keyOrUndefined, imageUrl)\r\n            console.log('上传成功，设置图片URL:', imageUrl)\r\n            console.log('当前resource:', node[type][arrayKeyOrKey][indexOrUndefined])\r\n            this.$message.success('上传成功')\r\n          } else {\r\n            // 单个字段形式：node[type][key]\r\n            this.$set(node[type], arrayKeyOrKey, imageUrl)\r\n            this.$message.success('上传成功')\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('上传错误:', error)\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    \r\n    async beforeUploadSceneConfigFile(file, node, type, arrayKeyOrKey, indexOrUndefined, keyOrUndefined) {\r\n      try {\r\n        this.$modal.loading(\"正在上传文件，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.leftTreeIndustryCode)\r\n        formData.append('sceneCode', node.code)\r\n\r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 判断是否为数组形式的上传\r\n          if (typeof indexOrUndefined === 'number' && keyOrUndefined) {\r\n            // 数组形式处理...\r\n            this.$set(node[type][arrayKeyOrKey][indexOrUndefined], keyOrUndefined, res.data.fileUrl)\r\n            \r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            this.$set(this.fileLists[type], indexOrUndefined, [{\r\n              name: fileName,\r\n              url: res.data.fileUrl,\r\n              uid: Date.now()\r\n            }])\r\n            \r\n            if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n              this.$set(node[type][arrayKeyOrKey][indexOrUndefined], 'bgImg', res.data.imgUrl)\r\n              this.$message.success('上传成功，已自动生成背景图片首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          } else {\r\n            // 单个字段形式：node[type][key]\r\n            this.$set(node[type], arrayKeyOrKey, res.data.fileUrl)\r\n            \r\n            // 如果是介绍视频上传\r\n            if (type === 'introduceVideoVo' && arrayKeyOrKey === 'backgroundFileUrl') {\r\n              // 更新介绍视频文件列表\r\n              this.updateIntroduceVideoFileList()\r\n              \r\n              if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n                this.$set(node[type], 'backgroundImgFileUrl', res.data.imgUrl)\r\n                this.$message.success('上传成功，已自动生成介绍视频首帧')\r\n              } else {\r\n                this.$message.success('上传成功')\r\n              }\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    addPainPoint(type) {\r\n      this.node[type].painPoints = this.node[type].painPoints || []\r\n      this.node[type].painPoints.push({ title: '', contents: [''], showTime: '' })\r\n    },\r\n    removePainPoint(type, idx) {\r\n      this.node[type].painPoints.splice(idx, 1)\r\n    },\r\n    addPainContent(type, idx) {\r\n      this.node[type].painPoints[idx].contents.push('')\r\n    },\r\n    removePainContent(type, idx, cidx) {\r\n      this.node[type].painPoints[idx].contents.splice(cidx, 1)\r\n    },\r\n    addCostContent() {\r\n      this.node.costEstimate.contents.push('')\r\n    },\r\n    removeCostContent(cidx) {\r\n      this.node.costEstimate.contents.splice(cidx, 1)\r\n    },\r\n    // 获取背景文件列表\r\n    getBackgroundFileList(node, type, idx) {\r\n      const resource = node[type].backgroundResources[idx]\r\n      if (resource && resource.bgFile) {\r\n        const fileName = resource.bgFile.split('/').pop()\r\n        return [{\r\n          name: fileName,\r\n          url: resource.bgFile,\r\n          uid: Date.now() + idx\r\n        }]\r\n      }\r\n      return []\r\n    },\r\n    // 处理背景文件删除\r\n    handleRemoveBackgroundFile(node, type, idx) {\r\n      const resource = node[type].backgroundResources[idx]\r\n      resource.bgFile = ''\r\n      resource.bgImg = '' // 同时清空背景图片首帧\r\n      this.$set(this.fileLists[type], idx, [])\r\n      this.$message.success('文件已删除')\r\n    },\r\n    // 获取文件列表 - 确保返回正确的文件列表\r\n    getFileList(type, idx) {\r\n      // 检查对应的资源是否真的有文件\r\n      const resource = this.node[type] && this.node[type].backgroundResources && this.node[type].backgroundResources[idx]\r\n      \r\n      if (!resource || !resource.bgFile || !resource.bgFile.trim()) {\r\n        // 如果没有文件，返回空数组\r\n        return []\r\n      }\r\n      \r\n      // 如果文件列表中有数据且与资源文件匹配，返回文件列表\r\n      if (this.fileLists[type] && this.fileLists[type][idx] && this.fileLists[type][idx].length > 0) {\r\n        const fileList = this.fileLists[type][idx]\r\n        // 验证文件列表中的URL是否与资源中的文件URL匹配\r\n        if (fileList[0].url === resource.bgFile) {\r\n          return fileList\r\n        }\r\n      }\r\n      \r\n      // 如果文件列表不匹配或为空，但资源有文件，重新创建文件列表\r\n      if (resource.bgFile && resource.bgFile.trim()) {\r\n        const fileName = resource.bgFile.split('/').pop()\r\n        const newFileList = [{\r\n          name: fileName,\r\n          url: resource.bgFile,\r\n          uid: Date.now()\r\n        }]\r\n        this.$set(this.fileLists[type], idx, newFileList)\r\n        return newFileList\r\n      }\r\n      \r\n      return []\r\n    },\r\n    getUploadMode(type, idx) {\r\n      if (!this.uploadModes[type][idx]) {\r\n        // 默认根据是否已有文件URL来判断模式\r\n        const resource = this.node[type].backgroundResources[idx]\r\n        const hasFile = resource && resource.bgFile\r\n        this.$set(this.uploadModes[type], idx, hasFile ? 'upload' : 'upload')\r\n      }\r\n      return this.uploadModes[type][idx]\r\n    },\r\n    setUploadMode(type, idx, value) {\r\n      if (typeof idx === 'string') {\r\n        // 介绍视频模式：type='introduceVideo', idx='upload'或'url'\r\n        this.$set(this.uploadModes, type, idx)\r\n      } else {\r\n        // 背景资源模式：type='tradition'或'wisdom5g', idx=数字索引, value='upload'或'url'\r\n        this.$set(this.uploadModes[type], idx, value)\r\n      }\r\n    },\r\n    handleUrlInput(type, idx, value) {\r\n      // 清空文件列表\r\n      this.$set(this.fileLists[type], idx, [])\r\n      \r\n      // 如果输入了链接，创建对应的文件列表项\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.$set(this.fileLists[type], idx, [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }])\r\n      }\r\n    },\r\n    // 更新介绍视频文件列表\r\n    updateIntroduceVideoFileList() {\r\n      if (this.node.introduceVideoVo && this.node.introduceVideoVo.backgroundFileUrl) {\r\n        const fileName = this.node.introduceVideoVo.backgroundFileUrl.split('/').pop()\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: this.node.introduceVideoVo.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.introduceVideoFileList = []\r\n      }\r\n    },\r\n    getIntroduceVideoFileList() {\r\n      return this.introduceVideoFileList\r\n    },\r\n    handleRemoveIntroduceVideo() {\r\n      if (this.node.introduceVideoVo) {\r\n        this.node.introduceVideoVo.backgroundFileUrl = ''\r\n        this.node.introduceVideoVo.backgroundImgFileUrl = ''\r\n        this.$message.success('介绍视频已删除')\r\n        // 同时更新文件列表\r\n        this.updateIntroduceVideoFileList()\r\n      }\r\n    },\r\n    // 介绍视频开关变化\r\n    onIntroduceVideoStatusChange(val) {\r\n      // 简单处理，不需要复杂逻辑\r\n    },\r\n    \r\n    // 图片预览\r\n    previewImage(url) {\r\n      if (url) {\r\n        this.previewImageUrl = url\r\n        this.previewVisible = true\r\n      }\r\n    },\r\n    \r\n    // 删除介绍视频图片\r\n    deleteIntroduceVideoImg() {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.node.introduceVideoVo.backgroundImgFileUrl = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 删除背景资源图片\r\n    deleteBackgroundImg(type, idx) {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.node[type].backgroundResources[idx].bgImg = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    // 验证XML Key输入（只允许英文、数字和下划线）\r\n    validateXmlKey(value, type) {\r\n      // 只保留英文字母、数字和下划线\r\n      const filteredValue = value.replace(/[^a-zA-Z0-9_]/g, '')\r\n      this.node[type].panoramicViewXmlKey = filteredValue\r\n    },\r\n    handleIntroduceVideoUrlInput(value) {\r\n      this.introduceVideoFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n      this.updateIntroduceVideoFileList()\r\n    },\r\n    // 处理坐标数字输入\r\n    handleCoordNumberInput(val, coord, field) {\r\n      // 如果val为null或undefined，设置为空字符串\r\n      if (val === null || val === undefined) {\r\n        coord[field] = ''\r\n      } else {\r\n        coord[field] = val\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断当前节点是否有子节点\r\n    hasChildren() {\r\n      return this.node && this.node.children && this.node.children.length > 0\r\n    }\r\n  },\r\n  components: {\r\n    SceneConfigNode: null // 递归注册，主页面import时补全\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 限制上传图片的显示大小 */\r\n.image-upload .el-upload--picture-card {\r\n  width: 148px;\r\n  height: 148px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.upload-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 背景图片首帧图片大小控制 */\r\n.image-upload .el-upload-list__item-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 上传框也添加圆角 */\r\n.image-upload .el-upload--picture-card {\r\n  border-radius: 8px;\r\n}\r\n\r\n.mini-block {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.background-resource-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  background-color: #fafafa;\r\n  position: relative;\r\n}\r\n\r\n.resource-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.resource-title {\r\n  font-weight: bold;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.pain-point-block {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.pain-point-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 图片预览样式 */\r\n.preview-container {\r\n  text-align: center;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 70vh;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 图片悬停操作样式 */\r\n.image-preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  border-radius: 6px;\r\n}\r\n\r\n.image-preview-container:hover .image-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.preview-icon,\r\n.delete-icon {\r\n  color: white;\r\n  font-size: 20px;\r\n  margin: 0 10px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.preview-icon:hover,\r\n.delete-icon:hover {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.coordinate-group {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  margin-bottom: 12px;\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.coordinate-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: bold;\r\n  color: #606266;\r\n}\r\n\r\n/* 确保介绍视频和成本预估卡片高度一致 */\r\n.mini-block .el-card {\r\n  height: 100%;\r\n}\r\n\r\n.mini-block .el-card__body {\r\n  min-height: 300px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 介绍视频和成本预估并排时的高度统一 */\r\n.el-row .el-col .mini-block {\r\n  height: 100%;\r\n}\r\n\r\n.el-row .el-col .mini-block .el-card {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.el-row .el-col .mini-block .el-card__body {\r\n  flex: 1;\r\n  min-height: 300px;\r\n}\r\n</style> \r\n"]}]}