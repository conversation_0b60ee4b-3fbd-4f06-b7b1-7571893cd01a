<template>
  <div class="theme-selection-container">
    <div class="theme-header">
      <h2>选择主题风格</h2>
      <p>为您的行业场景选择合适的主题风格</p>
    </div>
    
    <div v-loading="loading && currentPage === 1" class="theme-grid" @scroll="handleScroll" ref="themeGrid">
      <div 
        v-for="theme in themeOptions" 
        :key="theme.themeId"
        class="theme-card"
        :class="{ 'selected': selectedTheme && selectedTheme.themeId === theme.themeId }"
        @click="selectTheme(theme)"
      >
        <div class="theme-preview">
          <img :src="theme.themeEffectImg" :alt="theme.themeName" />
          <div class="theme-overlay">
            <i class="el-icon-check" v-if="selectedTheme && selectedTheme.themeId === theme.themeId"></i>
            <i class="el-icon-zoom-in preview-icon" @click.stop="previewImage(theme.themeEffectImg)" title="预览大图"></i>
          </div>
        </div>
        <div class="theme-info">
          <h3>{{ theme.themeName }}</h3>
        </div>
      </div>
    </div>
    
    <!-- 加载更多提示 -->
    <div v-if="loadingMore" class="loading-more">
      <i class="el-icon-loading"></i>
      <span>加载中...</span>
    </div>
    
    <!-- 没有更多数据提示 -->
    <div v-if="!hasMore && themeOptions.length > 0" class="no-more">
      <span>没有更多数据了</span>
    </div>
    
    <div v-if="!loading && !themeOptions.length" class="empty-state">
      <i class="el-icon-picture-outline"></i>
      <p>暂无主题数据</p>
    </div>
    
    <div class="theme-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="confirmSelection" :disabled="!selectedTheme">确认选择</el-button>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      title="主题预览"
      width="60%"
      append-to-body
      @close="closePreview"
    >
      <div class="preview-container">
        <img :src="previewImageUrl" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getThemeList } from '@/api/view/sceneView'

export default {
  name: 'ThemeSelection',
  data() {
    return {
      selectedTheme: null,
      themeOptions: [],
      loading: false,
      loadingMore: false,
      currentPage: 1,
      pageSize: 20,
      hasMore: true,
      // 图片预览
      previewVisible: false,
      previewImageUrl: ''
    }
  },
  created() {
    this.loadThemeList()
  },
  methods: {
    // 加载主题列表
    async loadThemeList(isLoadMore = false) {
      if (isLoadMore) {
        this.loadingMore = true
      } else {
        this.loading = true
        this.currentPage = 1
        this.themeOptions = []
        this.hasMore = true
      }
      
      try {
        const res = await getThemeList({
          page: this.currentPage,
          limit: this.pageSize
        })
        
        if (res.code === 0 && Array.isArray(res.data)) {
          if (isLoadMore) {
            this.themeOptions = [...this.themeOptions, ...res.data]
          } else {
            this.themeOptions = res.data
          }
          
          // 判断是否还有更多数据
          this.hasMore = res.data.length === this.pageSize
        } else {
          this.$message.error(res.msg || '获取主题列表失败')
        }
      } catch (error) {
        this.$message.error('获取主题列表失败')
      } finally {
        this.loading = false
        this.loadingMore = false
      }
    },
    
    // 滚动事件处理
    handleScroll(event) {
      const { scrollTop, scrollHeight, clientHeight } = event.target
      
      // 距离底部50px时触发加载
      if (scrollTop + clientHeight >= scrollHeight - 50) {
        this.loadMore()
      }
    },
    
    // 加载更多
    async loadMore() {
      if (this.loadingMore || !this.hasMore) {
        return
      }
      
      this.currentPage++
      await this.loadThemeList(true)
    },
    
    // 获取主题颜色（可以根据主题ID或名称设置不同颜色）
    getThemeColor(theme) {
      const colors = ['#409EFF', '#13ce66', '#f56c6c', '#909399', '#e6a23c']
      const index = parseInt(theme.themeId) % colors.length
      return colors[index]
    },
    
    selectTheme(theme) {
      this.selectedTheme = theme
    },
    
    confirmSelection() {
      if (!this.selectedTheme) {
        this.$message.warning('请先选择一个主题')
        return
      }
      
      this.$emit('confirm', this.selectedTheme)
    },
    
    // 图片预览
    previewImage(url) {
      if (url) {
        this.previewImageUrl = url
        this.previewVisible = true
      }
    },
    
    // 关闭预览
    closePreview() {
      this.previewVisible = false
      this.previewImageUrl = ''
    }
  }
}
</script>

<style scoped>
.theme-selection-container {
  padding: 10px 20px 20px 20px;
  height: 600px;
  display: flex;
  flex-direction: column;
}

.theme-header {
  margin-bottom: 20px;
}

.theme-header h2 {
  font-size: 24px;
  color: #303133;
  margin: 0 0 8px 0;
}

.theme-header p {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 15px;
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

.theme-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  height: fit-content;
}

.theme-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.theme-card.selected {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);
}

.theme-preview {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.theme-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.theme-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(128, 128, 128, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.theme-card:hover .theme-overlay {
  opacity: 1;
}

.theme-card.selected .theme-overlay {
  opacity: 1;
}

.theme-overlay i {
  font-size: 24px;
  color: white;
  margin: 0 5px;
  cursor: pointer;
  transition: transform 0.2s;
}

.theme-overlay i:hover {
  transform: scale(1.2);
}

.theme-info {
  padding: 10px;
  text-align: left;
  height: 60px;
  display: flex;
  flex-direction: column;
}

.theme-info h3 {
  font-size: 14px;
  color: #303133;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.theme-info p {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin: 0;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.loading-more i {
  margin-right: 8px;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 20px;
  color: #c0c4cc;
  font-size: 14px;
}

.theme-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  flex: 1;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 自定义滚动条样式 */
.theme-grid::-webkit-scrollbar {
  width: 6px;
}

.theme-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.theme-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.theme-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}
</style>























