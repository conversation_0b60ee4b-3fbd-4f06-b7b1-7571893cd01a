<template>
  <div class="network-plan-config">
    <!-- 视频讲解模块 -->
    <el-card class="video-explanation-card" shadow="never">
      <div slot="header" class="video-header-row">
        <span>视频讲解</span>
        <el-switch 
          v-model="videoExplanation.status" 
          :active-value="'0'" 
          :inactive-value="'1'" 
          style="float:right;" 
          @change="handleVideoExplanationStatusChange"
        />
      </div>
      <div v-show="videoExplanation.status === '0'">
        <el-form label-width="120px" size="small">
          <el-form-item label="讲解视频">
            <div style="margin-bottom: 8px;">
              <el-radio-group v-model="videoExplanationUploadMode" @input="handleVideoExplanationModeChange" size="small">
                <el-radio-button label="upload">上传文件</el-radio-button>
                <el-radio-button label="url">填写链接</el-radio-button>
              </el-radio-group>
            </div>
            
            <!-- 上传模式 -->
            <el-upload
              v-if="videoExplanationUploadMode === 'upload'"
              :key="`video-explanation-${videoExplanation.backgroundFileUrl ? 'has' : 'empty'}`"
              action="#"
              :show-file-list="true"
              :file-list="videoExplanationFileList"
              accept=".mp4"
              :before-upload="beforeUploadExplanationVideo"
              :http-request="() => {}"
              :on-remove="handleRemoveVideoExplanationFile"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传mp4文件</div>
            </el-upload>
            
            <!-- 链接模式 -->
            <el-input
              v-else
              :value="videoExplanation.backgroundFileUrl"
              placeholder="请输入视频链接"
              @input="handleVideoExplanationUrlInput"
            />
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 网络方案配置 -->
    <div class="config-header" style="margin-top: 20px;">
      <span>网络方案配置</span>
      <el-button type="primary" size="small" @click="addNetworkPlan">
        <i class="el-icon-plus"></i> 添加方案
      </el-button>
    </div>
    
    <div class="plan-list">
      <el-row :gutter="20">
        <el-col 
          :span="12" 
          v-for="(plan, index) in networkPlans" 
          :key="index"
        >
          <el-card class="plan-item" shadow="hover">
            <div slot="header" class="plan-header">
              <span>方案 {{ index + 1 }}</span>
              <el-button 
                type="danger" 
                size="mini" 
                icon="el-icon-delete" 
                circle 
                @click="removePlan(index)"
              />
            </div>
            
            <el-form :model="plan" label-width="80px" size="small">
              <el-form-item label="名称" required>
                <el-input 
                  v-model="plan.tag" 
                  placeholder="请输入方案名称"
                  @input="debouncedEmitChange"
                />
              </el-form-item>
              
              <!-- <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="X坐标" required>
                    <el-input 
                      v-model="plan.x" 
                      placeholder="请输入X坐标" 
                      @input="validateCoordinate($event, 'x', index)"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="Y坐标" required>
                    <el-input 
                      v-model="plan.y" 
                      placeholder="请输入Y坐标"
                      @input="validateCoordinate($event, 'y', index)"
                    />
                  </el-form-item>
                </el-col>
              </el-row> -->
              
              <!-- <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="宽度" required>
                    <el-input-number 
                      v-model="plan.width" 
                      :min="0" 
                      :max="9999" 
                      placeholder="宽度"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="高度" required>
                    <el-input-number 
                      v-model="plan.height" 
                      :min="0" 
                      :max="9999" 
                      placeholder="高度"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row> -->
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="图片">
                    <el-upload
                      :key="`image-${index}-${plan.id || 'new'}`"
                      class="image-upload"
                      action="#"
                      :show-file-list="false"
                      list-type="picture-card"
                      accept="image/*"
                      :before-upload="file => beforeUploadImage(file, index)"
                      :http-request="() => {}"
                    >
                      <div v-if="plan.imageUrl" class="image-preview-container">
                        <img :src="plan.imageUrl" class="upload-image" />
                        <div class="image-overlay">
                          <i class="el-icon-zoom-in preview-icon" @click.stop="previewImage(plan.imageUrl)" title="预览"></i>
                          <i class="el-icon-delete delete-icon" @click.stop="deletePlanImage(index)" title="删除"></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus"></i>
                    </el-upload>
                    <div class="upload-tip">支持jpg/png，最大20MB</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="视频文件">
                    <div style="margin-bottom: 8px;">
                      <el-radio-group :value="getUploadMode('video', index)" @input="value => setUploadMode('video', index, value)" size="small">
                        <el-radio-button label="upload">上传文件</el-radio-button>
                        <el-radio-button label="url">填写链接</el-radio-button>
                      </el-radio-group>
                    </div>
                    
                    <!-- 上传模式 -->
                    <el-upload
                      v-if="getUploadMode('video', index) === 'upload'"
                      :key="`video-${index}-${plan.id || 'new'}-${plan.videoUrl ? 'has' : 'empty'}`"
                      action="#"
                      :show-file-list="true"
                      :file-list="plan.videoFileList"
                      accept="video/*"
                      :before-upload="file => beforeUploadVideo(file, index)"
                      :http-request="() => {}"
                      :on-remove="file => handleRemoveVideo(file, index)"
                    >
                      <el-button size="small" type="primary">选择视频</el-button>
                      <div slot="tip" class="el-upload__tip">支持mp4等格式，最大200MB</div>
                    </el-upload>
                    
                    <!-- 链接模式 -->
                    <el-input
                      v-else
                      :value="plan.videoUrl"
                      placeholder="请输入视频链接"
                      @input="value => handleVideoUrlInput(value, index)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
      
      <div v-if="!networkPlans.length" class="empty-state">
        <i class="el-icon-document-add"></i>
        <p>暂无网络方案，点击上方按钮添加</p>
      </div>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      title="图片预览"
      width="60%"
      append-to-body
    >
      <div class="preview-container">
        <img :src="previewImageUrl" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { uploadSceneFile, sceneFileInfoDel } from '@/api/view/sceneView'

export default {
  name: 'NetworkPlanConfig',
  props: {
    value: {
      type: Object,
      default: () => ({
        networkVideoList: [],
        videoExplanationVo: {
          status: '0',
          backgroundFileUrl: '',
          videoSegmentedVoList: []
        }
      })
    },
    sceneTreeOptions: {
      type: Array,
      default: () => []
    },
    leftTreeIndustryCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      networkPlans: [],
      videoExplanation: {
        status: '0',
        backgroundFileUrl: '',
        videoSegmentedVoList: []
      },
      videoExplanationFileList: [],
      sceneCascaderProps: {
        label: 'sceneName',
        value: 'id',
        children: 'children',
        emitPath: false
      },
      isUpdating: false,
      emitTimer: null, // 添加防抖定时器
      videoUploadModes: {}, // 用于存储每个方案的视频上传模式
      videoExplanationUploadMode: 'upload', // 视频讲解上传模式
      // 图片预览
      previewVisible: false,
      previewImageUrl: ''
    }
  },
  computed: {
    videoSegmentedList() {
      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {
        return [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]
      }
      return this.videoExplanation.videoSegmentedVoList
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.isUpdating) return
        
        if (newVal) {
          // 处理网络视频列表 - 使用深拷贝避免引用问题
          const newNetworkPlans = (newVal.networkVideoList || []).map(item => ({
            id: item.id || null,
            tag: item.tag || '',
            x: item.clickX || '',
            y: item.clickY || '',
            width: item.wide || 0,
            height: item.high || 0,
            imageUrl: item.backgroundImgFileUrl || '',
            videoUrl: item.backgroundFileUrl || '',
            videoFileList: item.backgroundFileUrl ? [{
              name: item.backgroundFileUrl.split('/').pop(),
              url: item.backgroundFileUrl,
              uid: Date.now()
            }] : []
          }))
          
          // 只有在数据真正变化时才更新
          if (JSON.stringify(this.networkPlans) !== JSON.stringify(newNetworkPlans)) {
            this.networkPlans = newNetworkPlans
          }
          
          // 处理视频讲解
          const newVideoExplanation = {
            status: newVal.videoExplanationVo?.status || '0',
            backgroundFileUrl: newVal.videoExplanationVo?.backgroundFileUrl || '',
            videoSegmentedVoList: newVal.videoExplanationVo?.videoSegmentedVoList || []
          }
          
          if (JSON.stringify(this.videoExplanation) !== JSON.stringify(newVideoExplanation)) {
            this.videoExplanation = newVideoExplanation
            this.updateVideoExplanationFileList()
          }
        }
      },
      immediate: true,
      deep: false // 改为浅层监听
    }
  },
  methods: {
    emitChange() {
      if (this.isUpdating) return
      
      // 清除之前的定时器
      if (this.emitTimer) {
        clearTimeout(this.emitTimer)
      }
      
      // 使用防抖，避免频繁触发
      this.emitTimer = setTimeout(() => {
        this.isUpdating = true
        
        const data = {
          networkVideoList: this.networkPlans.map(plan => ({
            id: plan.id || null,
            tag: plan.tag || null,
            clickX: plan.x || null,
            clickY: plan.y || null,
            wide: plan.width || null,
            high: plan.height || null,
            backgroundImgFileUrl: plan.imageUrl || null,
            backgroundFileUrl: plan.videoUrl || null
          })),
          videoExplanationVo: {
            status: this.videoExplanation.status,
            backgroundFileUrl: this.videoExplanation.backgroundFileUrl || null,
            videoSegmentedVoList: this.videoExplanation.videoSegmentedVoList.length ? this.videoExplanation.videoSegmentedVoList : null
          }
        }
        
        this.$emit('input', data)
        
        // 延迟重置标志
        this.$nextTick(() => {
          setTimeout(() => {
            this.isUpdating = false
          }, 50)
        })
      }, 200) // 减少防抖时间
    },
    
    addNetworkPlan() {
      const newPlan = {
        tag: '',
        x: '',
        y: '',
        width: 0,
        height: 0,
        imageUrl: '',
        videoUrl: '',
        videoFileList: []
      }
      this.networkPlans.push(newPlan)
      this.emitChange()
    },
    
    async removePlan(index) {
      const plan = this.networkPlans[index]
      
      this.$confirm('确定删除此网络方案吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // 如果方案有ID，调用删除接口
        if (plan.id) {
          try {
            this.$modal.loading("正在删除方案，请稍候...")
            const res = await sceneFileInfoDel({ id: plan.id })
            if (res.code === 0) {
              this.networkPlans.splice(index, 1)
              this.emitChange()
              this.$message.success('删除成功')
            } else {
              this.$message.error(res.msg || '删除失败')
            }
          } catch (error) {
            this.$message.error('删除失败')
          } finally {
            this.$modal.closeLoading()
          }
        } else {
          // 没有ID的新方案，直接从数组中移除
          this.networkPlans.splice(index, 1)
          this.emitChange()
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },
    
    validateCoordinate(value, type, index) {
      const regex = /^-?\d*\.?\d*$/
      if (!regex.test(value)) {
        // 直接修改，不触发响应式更新
        const plan = this.networkPlans[index]
        plan[type] = value.replace(/[^-\d.]/g, '')
      }
      // 延迟触发更新
      this.debouncedEmitChange()
    },
    
    async beforeUploadImage(file, index) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件！')
        return false
      }
      
      if (file.size > 20 * 1024 * 1024) {
        this.$message.error('图片大小不能超过20MB！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传图片，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.leftTreeIndustryCode)

        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          // 直接修改对象属性，避免触发数组重新渲染
          const plan = this.networkPlans[index]
          plan.imageUrl = res.data.fileUrl
          
          // 使用防抖延迟触发更新
          this.debouncedEmitChange()
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    
    async beforeUploadVideo(file, index) {
      if (!file.type.startsWith('video/')) {
        this.$message.error('只能上传视频文件！')
        return false
      }
      
      if (file.size > 200 * 1024 * 1024) {
        this.$message.error('视频大小不能超过200MB！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传视频，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.leftTreeIndustryCode)

        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          const plan = this.networkPlans[index]
          const fileName = res.data.fileUrl.split('/').pop()
          
          // 直接修改对象属性
          plan.videoUrl = res.data.fileUrl
          plan.videoFileList = [{
            name: fileName,
            url: res.data.fileUrl,
            uid: Date.now()
          }]
          
          this.debouncedEmitChange()
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    
    handleRemoveVideo(file, index) {
      // 批量更新，避免多次重新渲染
      this.$set(this.networkPlans[index], 'videoUrl', '')
      this.$set(this.networkPlans[index], 'videoFileList', [])
      
      this.$nextTick(() => {
        this.emitChange()
      })
      this.$message.success('视频已删除')
    },
    
    async beforeUploadExplanationVideo(file) {
      if (!file.type.startsWith('video/') && !file.name.endsWith('.mp4')) {
        this.$message.error('只能上传MP4视频文件！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传视频，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.leftTreeIndustryCode)
        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          // 直接修改对象属性，避免触发响应式更新
          this.videoExplanation.backgroundFileUrl = res.data.fileUrl
          
          const fileName = res.data.fileUrl.split('/').pop()
          // 直接赋值文件列表
          this.videoExplanationFileList = [{
            name: fileName,
            url: res.data.fileUrl,
            uid: Date.now()
          }]
          
          this.debouncedEmitChange()
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    
    handleRemoveVideoExplanationFile() {
      // 批量更新，避免多次重新渲染
      this.videoExplanation.backgroundFileUrl = ''
      this.videoExplanationFileList = []
      
      this.$nextTick(() => {
        this.debouncedEmitChange()
      })
      this.$message.success('讲解视频已删除')
    },
    
    updateVideoExplanationFileList() {
      if (this.videoExplanation.backgroundFileUrl) {
        const fileName = this.videoExplanation.backgroundFileUrl.split('/').pop()
        this.videoExplanationFileList = [{
          name: fileName,
          url: this.videoExplanation.backgroundFileUrl,
          uid: Date.now()
        }]
      } else {
        this.videoExplanationFileList = []
      }
    },
    
    addVideoSegment() {
      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {
        this.videoExplanation.videoSegmentedVoList = [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]
      }
      this.videoExplanation.videoSegmentedVoList.push({ time: '', sceneId: '', sceneName: '', sceneCode: '' })
      this.emitChange()
    },
    
    removeVideoSegment(idx) {
      this.videoExplanation.videoSegmentedVoList.splice(idx, 1)
      this.emitChange()
    },
    
    handleTimeChange(val, idx) {
      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {
        this.videoExplanation.videoSegmentedVoList = [{ time: 0, sceneId: '', sceneName: '', sceneCode: '' }]
      }
      if (this.videoExplanation.videoSegmentedVoList[idx]) {
        this.videoExplanation.videoSegmentedVoList[idx].time = val
        this.emitChange()
      }
    },
    
    handleSceneCascaderChange(val, idx) {
      const findScene = (tree, id) => {
        for (const node of tree) {
          if (node.id === id) return node
          if (node.children && node.children.length) {
            const found = findScene(node.children, id)
            if (found) return found
          }
        }
        return null
      }
      const node = findScene(this.sceneTreeOptions, val)
      if (node) {
        this.videoExplanation.videoSegmentedVoList[idx].sceneName = node.sceneName
        this.videoExplanation.videoSegmentedVoList[idx].sceneCode = node.sceneCode
        this.emitChange()
      }
    },
    
    handleVideoExplanationStatusChange() {
      this.emitChange()
    },
    // 添加防抖方法
    debouncedEmitChange() {
      if (this.emitTimer) {
        clearTimeout(this.emitTimer)
      }
      this.emitTimer = setTimeout(() => {
        this.emitChange()
      }, 500) // 增加防抖时间
    },
    // 图片预览
    previewImage(url) {
      if (url) {
        this.previewImageUrl = url
        this.previewVisible = true
      }
    },
    
    // 删除网络方案图片
    deletePlanImage(index) {
      this.$confirm('确定删除此图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.networkPlans[index].imageUrl = ''
        this.debouncedEmitChange()
        this.$message.success('图片已删除')
      }).catch(() => {})
    },
    // 获取上传模式
    getUploadMode(type, index) {
      if (!this.videoUploadModes[index]) {
        this.$set(this.videoUploadModes, index, 'upload')
      }
      return this.videoUploadModes[index]
    },
    // 设置上传模式
    setUploadMode(type, index, mode) {
      this.$set(this.videoUploadModes, index, mode)
    },
    // 视频链接输入处理
    handleVideoUrlInput(value, index) {
      const plan = this.networkPlans[index]
      plan.videoUrl = value
      plan.videoFileList = []
      
      if (value) {
        const fileName = value.split('/').pop() || '外部链接文件'
        plan.videoFileList = [{
          name: fileName,
          url: value,
          uid: Date.now()
        }]
      }
      
      this.debouncedEmitChange()
    },
    // 视频讲解模式切换处理
    handleVideoExplanationModeChange(mode) {
      this.videoExplanationUploadMode = mode
    },
    // 视频讲解链接输入处理
    handleVideoExplanationUrlInput(value) {
      this.videoExplanation.backgroundFileUrl = value
      this.videoExplanationFileList = []
      
      if (value) {
        const fileName = value.split('/').pop() || '外部链接文件'
        this.videoExplanationFileList = [{
          name: fileName,
          url: value,
          uid: Date.now()
        }]
      }
      
      this.debouncedEmitChange()
    }
  },
  beforeDestroy() {
    // 组件销毁时清除定时器
    if (this.emitTimer) {
      clearTimeout(this.emitTimer)
    }
  }
}
</script>

<style scoped>
.network-plan-config {
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

.plan-list {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

.plan-item {
  margin-bottom: 20px;
  height: auto;
  width: 100%;
  box-sizing: border-box;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-upload .el-upload--picture-card {
  width: 80px;
  height: 80px;
  border-radius: 6px;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 滚动条样式 */
.plan-list::-webkit-scrollbar {
  width: 6px;
}

.plan-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.plan-list::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.plan-list::-webkit-scrollbar-thumb:hover {
  background: #a6a9ad;
}

/* 确保所有元素不超出容器宽度 */
.el-row {
  width: 100%;
  margin: 0 !important;
}

.el-col {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

/* 确保表单元素不超出宽度 */
.el-form-item {
  margin-bottom: 18px;
}

.el-input, .el-input-number {
  width: 100% !important;
}

.video-explanation-card {
  margin-bottom: 20px;
}

.video-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 图片预览样式 */
.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

/* 图片悬停操作样式 */
.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 6px;
}

.image-preview-container:hover .image-overlay {
  opacity: 1;
}

.preview-icon,
.delete-icon {
  color: white;
  font-size: 20px;
  margin: 0 10px;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-icon:hover,
.delete-icon:hover {
  transform: scale(1.2);
}
</style>





























