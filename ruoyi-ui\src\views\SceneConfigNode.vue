<template>
  <el-card class="mini-block" shadow="never">
    <div slot="header">
      <span>{{ node.name }}</span>
      <el-switch v-model="node.status" style="margin-left: 16px;" :active-value="'0'" :inactive-value="'1'" @change="onStatusChange" />
    </div>
    <div v-show="node.status === '0'" class="sub-category-body">
      <!-- 主表单项 - 编码名称并排，坐标并排 -->
      <el-form label-width="120px">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="场景编码">
              <el-input v-model="node.code" disabled type="text" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="场景名称">
              <el-input v-model="node.name" disabled type="text" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="坐标X" required>
              <el-input v-model="node.x" placeholder="请输入坐标X（百分比）" type="text" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="坐标Y" required>
              <el-input v-model="node.y" placeholder="请输入坐标Y（百分比）" type="text" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="场景类型" required>
          <el-select v-model="node.type" placeholder="请选择类型">
            <el-option label="默认" value="1" />
            <el-option label="AI" value="2" />
            <el-option label="三化" value="3" />
            <el-option label="AI+三化" value="4" />
          </el-select>
        </el-form-item>
        
        <!-- 只有存在下级菜单时才显示 -->
        <el-row :gutter="16" v-if="hasChildren">
          <el-col :span="12">
            <el-form-item label="是否展开下级" required>
              <el-radio-group v-model="node.isUnfold">
                <el-radio label="0">展示</el-radio>
                <el-radio label="1">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下级展示位置" required>
              <el-radio-group v-model="node.displayLocation">
                <el-radio label="0">默认</el-radio>
                <el-radio label="1">右下</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下级菜单分类" required>
              <el-radio-group v-model="node.treeClassification">
                <el-radio label="1">传统</el-radio>
                <el-radio label="2">5G</el-radio>
                <el-radio label="3">全部</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 介绍视频和成本预估并排布局 -->
      <el-row :gutter="20">
        <!-- 左侧：介绍视频模块 -->
        <el-col :span="12">
          <el-card class="mini-block" shadow="never">
            <div slot="header">
              <span>介绍视频</span>
              <el-switch 
                v-model="node.introduceVideoVo.status" 
                style="float:right;"
                :active-value="'0'" 
                :inactive-value="'1'" 
                @change="onIntroduceVideoStatusChange" />
            </div>
            <div v-show="node.introduceVideoVo.status === '0'">
              <el-form label-width="120px">
                <el-form-item label="介绍视频首帧">
                  <el-upload
                    class="upload image-upload"
                    action="#"
                    :show-file-list="false"
                    list-type="picture-card"
                    accept="image/*"
                    :before-upload="file => beforeUploadSceneConfigImg(file, node, 'introduceVideoVo', 'backgroundImgFileUrl', null, null)"
                    :http-request="() => {}"
                  >
                    <div v-if="node.introduceVideoVo.backgroundImgFileUrl" class="image-preview-container">
                      <img :src="node.introduceVideoVo.backgroundImgFileUrl" class="upload-image" />
                      <div class="image-overlay">
                        <i class="el-icon-zoom-in preview-icon" @click.stop="previewImage(node.introduceVideoVo.backgroundImgFileUrl)" title="预览"></i>
                        <i class="el-icon-delete delete-icon" @click.stop="deleteIntroduceVideoImg" title="删除"></i>
                      </div>
                    </div>
                    <i v-else class="el-icon-plus"></i>
                  </el-upload>
                </el-form-item>
                <el-form-item label="介绍视频">
                  <div style="margin-bottom: 8px;">
                    <el-radio-group :value="uploadModes.introduceVideo || 'upload'" @input="value => setUploadMode('introduceVideo', value)" size="small">
                      <el-radio-button label="upload">上传文件</el-radio-button>
                      <el-radio-button label="url">填写链接</el-radio-button>
                    </el-radio-group>
                  </div>
                  
                  <!-- 上传模式 -->
                  <el-upload
                    v-if="(uploadModes.introduceVideo || 'upload') === 'upload'"
                    action="#"
                    :show-file-list="true"
                    :file-list="getIntroduceVideoFileList()"
                    accept=".mp4"
                    :before-upload="file => beforeUploadSceneConfigFile(file, node, 'introduceVideoVo', 'backgroundFileUrl', null, null)"
                    :http-request="() => {}"
                    :on-remove="handleRemoveIntroduceVideo"
                  >
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传mp4文件</div>
                  </el-upload>
                  
                  <!-- 链接模式 -->
                  <el-input
                    v-else
                    v-model="node.introduceVideoVo.backgroundFileUrl"
                    placeholder="请输入视频链接"
                    @input="handleIntroduceVideoUrlInput"
                  />
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>
        
        <!-- 右侧：成本预估模块 -->
        <el-col :span="12">
          <el-card class="mini-block" shadow="never">
            <div slot="header">
              <span>成本预估</span>
              <el-switch v-model="node.costEstimate.status" style="float:right;" :active-value="'0'" :inactive-value="'1'" />
            </div>
            <div v-show="node.costEstimate.status === '0'">
              <el-form label-width="120px">
                <el-form-item label="大标题" required>
                  <el-input v-model="node.costEstimate.title" placeholder="请输入大标题" type="text" />
                </el-form-item>
                <el-form-item label="内容" required>
                  <div v-for="(content, cidx) in node.costEstimate.contents" :key="'costEstimate-content-' + cidx" style="display:flex;align-items:center;margin-bottom:8px;">
                    <el-input v-model="node.costEstimate.contents[cidx]" placeholder="请输入内容" style="width:calc(100% - 40px);margin-right:8px;" type="text" />
                    <el-button icon="el-icon-delete" @click="removeCostContent(cidx)" circle size="mini" />
                  </div>
                  <el-button type="primary" plain @click="addCostContent">增加内容</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 传统小类 -->
      <el-card class="mini-block" shadow="never">
        <div slot="header">传统</div>
        <el-form label-width="120px">
          <el-form-item label="名称" required>
            <el-input v-model="node.tradition.name" placeholder="请输入名称" type="text" />
          </el-form-item>
          <el-form-item label="全景图唯一标识" required>
            <el-input 
              v-model="node.tradition.panoramicViewXmlKey" 
              placeholder="请输入全景图唯一标识（仅英文）" 
              type="text"
              @input="validateXmlKey($event, 'tradition')"
            />
          </el-form-item>
          
          <!-- 传统模块背景资源 -->
          <div class="mini-block" style="margin-bottom:0;">
            <div style="font-weight:bold;margin-bottom:8px;">背景资源</div>
            <div v-for="(resource, idx) in node.tradition.backgroundResources" :key="idx" class="background-resource-item">
              <div class="resource-header">
                <span class="resource-title">背景资源 {{ idx + 1 }}</span>
                <el-button type="danger" size="mini" plain @click="removeBackgroundResource('tradition', idx)">删除</el-button>
              </div>
              <!-- <el-form-item label="标签" required>
                <el-input v-model="resource.label" placeholder="请输入标签" type="text" />
              </el-form-item> -->
              <el-row :gutter="16">
                <el-col :span="24">
                  <el-form-item label="坐标组" required>
                    <div v-for="(coord, coordIdx) in resource.coordinates" :key="coordIdx" class="coordinate-group">
                      <div class="coordinate-header">
                        <span>坐标组 {{ coordIdx + 1 }}</span>
                        <el-button 
                          type="danger" 
                          size="mini" 
                          plain 
                          @click="removeCoordinate('tradition', idx, coordIdx)"
                        >
                          删除
                        </el-button>
                      </div>
                      <el-row :gutter="16">
                        <el-col :span="8">
                          <el-form-item label="X坐标">
                            <el-input v-model="coord.x" placeholder="请输入X坐标" type="text" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="Y坐标">
                            <el-input v-model="coord.y" placeholder="请输入Y坐标" type="text" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="绑定场景">
                            <el-cascader
                              v-model="coord.sceneId"
                              :options="sceneTreeOptions"
                              :props="sceneCascaderProps"
                              filterable
                              check-strictly
                              placeholder="选择场景"
                              style="width: 100%;"
                              @change="val => handleSceneCascaderChange(val, 'tradition', idx, coordIdx)"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row :gutter="16">
                        <el-col :span="10">
                          <el-form-item label="宽度">
                            <el-input-number 
                              :value="coord.wide" 
                              @input="val => handleCoordNumberInput(val, coord, 'wide')"
                              :min="0" 
                              :max="999" 
                              placeholder="请输入宽度" 
                              style="width: 100%" 
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="10">
                          <el-form-item label="高度">
                            <el-input-number 
                              :value="coord.high" 
                              @input="val => handleCoordNumberInput(val, coord, 'high')"
                              :min="0" 
                              :max="999" 
                              placeholder="请输入高度" 
                              style="width: 100%" 
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="10">
                          <el-form-item label="全景xml标签">
                            <el-input v-model="coord.xmlKey" placeholder="请输入全景xml标签" style="width: 100%" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                    <el-button type="primary" size="mini" plain @click="addCoordinate('tradition', idx)">
                      增加坐标组
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="背景图片首帧">
                    <el-upload
                      class="upload image-upload"
                      action="#"
                      :show-file-list="false"
                      list-type="picture-card"
                      accept="image/*"
                      :before-upload="file => beforeUploadSceneConfigImg(file, node, 'tradition', 'backgroundResources', idx, 'bgImg')"
                      :http-request="() => {}"
                    >
                      <div v-if="resource.bgImg" class="image-preview-container">
                        <img :src="resource.bgImg" class="upload-image" />
                        <div class="image-overlay">
                          <i class="el-icon-zoom-in preview-icon" @click.stop="previewImage(resource.bgImg)" title="预览"></i>
                          <i class="el-icon-delete delete-icon" @click.stop="deleteBackgroundImg('tradition', idx)" title="删除"></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="背景文件" required>
                    <div style="margin-bottom: 8px;">
                      <el-radio-group v-model="uploadModes.tradition[idx] || 'upload'" @input="value => setUploadMode('tradition', idx, value)" size="small">
                        <el-radio-button label="upload">上传文件</el-radio-button>
                        <el-radio-button label="url">填写链接</el-radio-button>
                      </el-radio-group>
                    </div>
                    
                    <!-- 上传模式 -->
                    <el-upload
                      v-if="(uploadModes.tradition[idx] || 'upload') === 'upload'"
                      action="#"
                      :show-file-list="true"
                      :file-list="getFileList('tradition', idx)"
                      :before-upload="file => beforeUploadSceneConfigFile(file, node, 'tradition', 'backgroundResources', idx, 'bgFile')"
                      :http-request="() => {}"
                      :on-remove="(file, fileList) => handleRemoveBackgroundFile(node, 'tradition', idx)"
                    >
                      <el-button size="small" type="primary">点击上传</el-button>
                    </el-upload>
                    
                    <!-- 链接模式 -->
                    <el-input
                      v-else
                      v-model="resource.bgFile"
                      placeholder="请输入文件链接"
                      @input="value => handleUrlInput('tradition', idx, value)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <el-form-item>
              <el-button type="primary" plain @click="addBackgroundResource('tradition')">增加背景资源</el-button>
            </el-form-item>
          </div>
          
          <!-- 传统模块痛点价值 - 统一输入框长度 -->
          <div class="mini-block" style="margin-bottom:0;">
            <div style="font-weight:bold;margin-bottom:8px;">痛点价值</div>
            <div v-for="(point, idx) in node.tradition.painPoints" :key="'tradition-' + idx" class="pain-point-block">
              <div class="resource-header">
                <span class="resource-title">痛点价值 {{ idx + 1 }}</span>
                <el-button type="danger" size="mini" plain @click="removePainPoint('tradition', idx)">删除</el-button>
              </div>
              <el-row :gutter="16">
                <el-col :span="16">
                  <el-form-item label="大标题" required>
                    <el-input v-model="point.title" placeholder="请输入大标题" type="text" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="展示时间" required>
                    <el-input-number v-model="point.showTime" :min="0" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="内容" required>
                <el-row :gutter="16">
                  <el-col :span="16">
                    <div v-for="(content, cidx) in point.contents" :key="'tradition-content-' + idx + '-' + cidx" style="display:flex;align-items:center;margin-bottom:8px;">
                      <el-input v-model="point.contents[cidx]" placeholder="请输入内容" style="width:calc(100% - 40px);margin-right:8px;" type="text" />
                      <el-button icon="el-icon-delete" @click="removePainContent('tradition', idx, cidx)" circle size="mini" />
                    </div>
                    <el-button type="primary" plain @click="addPainContent('tradition', idx)">增加内容</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>
            <el-form-item>
              <el-button type="primary" plain @click="addPainPoint('tradition')">增加痛点价值</el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-card>
      <!-- 5G智慧小类 -->
      <el-card class="mini-block" shadow="never">
        <div slot="header">5G智慧</div>
        <el-form label-width="120px">
          <el-form-item label="名称" required>
            <el-input v-model="node.wisdom5g.name" placeholder="请输入名称" type="text" />
          </el-form-item>
          <el-form-item label="全景图唯一标识" required>
            <el-input 
              v-model="node.wisdom5g.panoramicViewXmlKey" 
              placeholder="请输入全景图唯一标识（仅英文）" 
              type="text"
              @input="validateXmlKey($event, 'wisdom5g')"
            />
          </el-form-item>
          
          <!-- 5G智慧模块背景资源 -->
          <div class="mini-block" style="margin-bottom:0;">
            <div style="font-weight:bold;margin-bottom:8px;">背景资源</div>
            <div v-for="(resource, idx) in node.wisdom5g.backgroundResources" :key="'wisdom5g-bg-' + idx" class="background-resource-item">
              <div class="resource-header">
                <span class="resource-title">背景资源 {{ idx + 1 }}</span>
                <el-button type="danger" size="mini" plain @click="removeBackgroundResource('wisdom5g', idx)">删除</el-button>
              </div>
              <!-- <el-form-item label="标签" required>
                <el-input v-model="resource.label" placeholder="请输入标签" type="text" />
              </el-form-item> -->
              <el-row :gutter="16">
                <el-col :span="24">
                  <el-form-item label="坐标组" required>
                    <div v-for="(coord, coordIdx) in resource.coordinates" :key="coordIdx" class="coordinate-group">
                      <div class="coordinate-header">
                        <span>坐标组 {{ coordIdx + 1 }}</span>
                        <el-button 
                          type="danger" 
                          size="mini" 
                          plain 
                          @click="removeCoordinate('wisdom5g', idx, coordIdx)"
                        >
                          删除
                        </el-button>
                      </div>
                      <el-row :gutter="16">
                        <el-col :span="8">
                          <el-form-item label="X坐标">
                            <el-input v-model="coord.x" placeholder="请输入X坐标" type="text" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="Y坐标">
                            <el-input v-model="coord.y" placeholder="请输入Y坐标" type="text" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="绑定场景">
                            <el-cascader
                              v-model="coord.sceneId"
                              :options="sceneTreeOptions"
                              :props="sceneCascaderProps"
                              filterable
                              check-strictly
                              placeholder="选择场景"
                              style="width: 100%;"
                              @change="val => handleSceneCascaderChange(val, 'wisdom5g', idx, coordIdx)"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row :gutter="16">
                        <el-col :span="10">
                          <el-form-item label="宽度">
                            <el-input-number 
                              :value="coord.wide" 
                              @input="val => handleCoordNumberInput(val, coord, 'wide')"
                              :min="0" 
                              :max="999" 
                              placeholder="请输入宽度" 
                              style="width: 100%" 
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="10">
                          <el-form-item label="高度">
                            <el-input-number 
                              :value="coord.high" 
                              @input="val => handleCoordNumberInput(val, coord, 'high')"
                              :min="0" 
                              :max="999" 
                              placeholder="请输入高度" 
                              style="width: 100%" 
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="10">
                          <el-form-item label="全景xml标签">
                            <el-input v-model="coord.xmlKey" placeholder="请输入全景xml标签" style="width: 100%" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                    <el-button type="primary" size="mini" plain @click="addCoordinate('wisdom5g', idx)">
                      增加坐标组
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="背景图片首帧">
                    <el-upload
                      class="upload image-upload"
                      action="#"
                      :show-file-list="false"
                      list-type="picture-card"
                      accept="image/*"
                      :before-upload="file => beforeUploadSceneConfigImg(file, node, 'wisdom5g', 'backgroundResources', idx, 'bgImg')"
                      :http-request="() => {}"
                    >
                      <div v-if="resource.bgImg" class="image-preview-container">
                        <img :src="resource.bgImg" class="upload-image" />
                        <div class="image-overlay">
                          <i class="el-icon-zoom-in preview-icon" @click.stop="previewImage(resource.bgImg)" title="预览"></i>
                          <i class="el-icon-delete delete-icon" @click.stop="deleteBackgroundImg('wisdom5g', idx)" title="删除"></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="背景文件" required>
                    <div style="margin-bottom: 8px;">
                      <el-radio-group v-model="uploadModes.wisdom5g[idx] || 'upload'" @input="value => setUploadMode('wisdom5g', idx, value)" size="small">
                        <el-radio-button label="upload">上传文件</el-radio-button>
                        <el-radio-button label="url">填写链接</el-radio-button>
                      </el-radio-group>
                    </div>
                    
                    <!-- 上传模式 -->
                    <el-upload
                      v-if="(uploadModes.wisdom5g[idx] || 'upload') === 'upload'"
                      action="#"
                      :show-file-list="true"
                      :file-list="getFileList('wisdom5g', idx)"
                      :before-upload="file => beforeUploadSceneConfigFile(file, node, 'wisdom5g', 'backgroundResources', idx, 'bgFile')"
                      :http-request="() => {}"
                      :on-remove="(file, fileList) => handleRemoveBackgroundFile(node, 'wisdom5g', idx)"
                    >
                      <el-button size="small" type="primary">点击上传</el-button>
                    </el-upload>
                    
                    <!-- 链接模式 -->
                    <el-input
                      v-else
                      v-model="resource.bgFile"
                      placeholder="请输入文件链接"
                      @input="value => handleUrlInput('wisdom5g', idx, value)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <el-form-item>
              <el-button type="primary" plain @click="addBackgroundResource('wisdom5g')">增加背景资源</el-button>
            </el-form-item>
          </div>
          
          <!-- 5G智慧模块痛点价值 - 统一输入框长度 -->
          <div class="mini-block" style="margin-bottom:0;">
            <div style="font-weight:bold;margin-bottom:8px;">痛点价值</div>
            <div v-for="(point, idx) in node.wisdom5g.painPoints" :key="'wisdom5g-' + idx" class="pain-point-block">
              <div class="resource-header">
                <span class="resource-title">痛点价值 {{ idx + 1 }}</span>
                <el-button type="danger" size="mini" plain @click="removePainPoint('wisdom5g', idx)">删除</el-button>
              </div>
              <el-row :gutter="16">
                <el-col :span="16">
                  <el-form-item label="大标题" required>
                    <el-input v-model="point.title" placeholder="请输入大标题" type="text" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="展示时间" required>
                    <el-input-number v-model="point.showTime" :min="0" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="内容" required>
                <el-row :gutter="16">
                  <el-col :span="16">
                    <div v-for="(content, cidx) in point.contents" :key="'wisdom5g-content-' + idx + '-' + cidx" style="display:flex;align-items:center;margin-bottom:8px;">
                      <el-input v-model="point.contents[cidx]" placeholder="请输入内容" style="width:calc(100% - 40px);margin-right:8px;" type="text" />
                      <el-button icon="el-icon-delete" @click="removePainContent('wisdom5g', idx, cidx)" circle size="mini" />
                    </div>
                    <el-button type="primary" plain @click="addPainContent('wisdom5g', idx)">增加内容</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>
            <el-form-item>
              <el-button type="primary" plain @click="addPainPoint('wisdom5g')">增加痛点价值</el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-card>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      title="图片预览"
      width="60%"
      append-to-body
    >
      <div class="preview-container">
        <img :src="previewImageUrl" class="preview-image" />
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import { uploadSceneFile, backgroundFileDel, fileBindDel } from '@/api/view/sceneView'

export default {
  name: 'SceneConfigNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    rootTree: {
      type: Array,
      default: () => []
    },
    sceneTreeOptions: {
      type: Array,
      default: () => []
    },
    leftTreeIndustryCode: {
      type: String,
      default: ''
    }
  },
  watch: {
    'node.status'(val) {
      if (val === '0') {
        this.findAndOpenParent(this.node.id, this.rootTree)
      }
    },
    node: {
      handler(newNode, oldNode) {
        if (newNode && newNode !== oldNode) {
          this.$nextTick(() => {
            // 初始化节点数据
            this.initNodeData()
            
            // 确保introduceVideoVo存在且有完整数据时才处理
            if (newNode.introduceVideoVo && newNode.introduceVideoVo.hasOwnProperty('status')) {
              // 数据已存在，不需要初始化，直接更新文件列表
              this.updateIntroduceVideoFileList()
            } else if (!newNode.introduceVideoVo) {
              // 数据不存在时才初始化
              this.initIntroduceVideo()
            }
            // 重新初始化文件列表，清除可能的继承问题
            this.initFileLists()
          })
        }
      },
      immediate: true,
      deep: true
    },
    // 监听introduceVideoVo整个对象的变化
    'node.introduceVideoVo': {
      handler(newVal) {
        if (newVal && newVal.status !== undefined) {
          this.updateIntroduceVideoFileList()
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      fileLists: {
        tradition: {},
        wisdom5g: {},
        introduceVideo: []
      },
      uploadModes: {
        tradition: {},
        wisdom5g: {},
        introduceVideo: 'upload'
      },
      // 添加介绍视频文件列表缓存
      introduceVideoFileList: [],
      // 图片预览
      previewVisible: false,
      previewImageUrl: '',
      // 场景级联选择器配置
      sceneCascaderProps: {
        label: 'sceneName',
        value: 'id',
        children: 'children',
        emitPath: false,
        checkStrictly: true
      }
    }
  },
  created() {
    this.initIntroduceVideo()
    this.initNodeData()
  },
  mounted() {
    this.initData()
    this.initFileLists()
  },
  methods: {
    initData() {
      // 确保5G智慧模块有默认结构并深拷贝避免引用共享
      if (!this.node.wisdom5g) {
        this.$set(this.node, 'wisdom5g', {
          name: '',
          panoramicViewXmlKey: '',
          backgroundResources: [],
          painPoints: []
        })
      } else {
        // 深拷贝现有的5G智慧数据，避免多个场景共享引用
        const wisdom5gCopy = JSON.parse(JSON.stringify(this.node.wisdom5g))
        this.$set(this.node, 'wisdom5g', wisdom5gCopy)
        
        // 修正字段映射
        if (this.node.wisdom5g.backgroundResources) {
          this.node.wisdom5g.backgroundResources.forEach(resource => {
            if (resource.backgroundImgFileUrl && (!resource.bgImg || resource.bgImg === '')) {
              resource.bgImg = resource.backgroundImgFileUrl
            }
            if (resource.backgroundFileUrl && (!resource.bgFile || resource.bgFile === '')) {
              resource.bgFile = resource.backgroundFileUrl
            }
            if (resource.tag && (!resource.label || resource.label === '')) {
              resource.label = resource.tag
            }
          })
        }
      }
      
      // 同样处理传统模块
      if (!this.node.tradition) {
        this.$set(this.node, 'tradition', {
          name: '',
          panoramicViewXmlKey: '',
          backgroundResources: [],
          painPoints: []
        })
      } else {
        // 深拷贝传统模块数据
        const traditionCopy = JSON.parse(JSON.stringify(this.node.tradition))
        this.$set(this.node, 'tradition', traditionCopy)
      }
    },
    // 初始化文件列表
    initFileLists() {
      // 清空所有文件列表，避免继承问题
      this.fileLists = {
        tradition: {},
        wisdom5g: {},
        introduceVideo: []
      }
      
      // 初始化传统模块的文件列表
      if (this.node.tradition && this.node.tradition.backgroundResources) {
        this.node.tradition.backgroundResources.forEach((resource, idx) => {
          // 只有当资源确实有文件时才创建文件列表
          if (resource.bgFile && resource.bgFile.trim()) {
            const fileName = resource.bgFile.split('/').pop()
            this.$set(this.fileLists.tradition, idx, [{
              name: fileName,
              url: resource.bgFile,
              uid: Date.now() + idx
            }])
          } else {
            // 明确设置为空数组
            this.$set(this.fileLists.tradition, idx, [])
          }
        })
      }
      
      // 初始化5G智慧模块的文件列表
      if (this.node.wisdom5g && this.node.wisdom5g.backgroundResources) {
        this.node.wisdom5g.backgroundResources.forEach((resource, idx) => {
          // 只有当资源确实有文件时才创建文件列表
          if (resource.bgFile && resource.bgFile.trim()) {
            const fileName = resource.bgFile.split('/').pop()
            this.$set(this.fileLists.wisdom5g, idx, [{
              name: fileName,
              url: resource.bgFile,
              uid: Date.now() + idx + 1000
            }])
          } else {
            // 明确设置为空数组
            this.$set(this.fileLists.wisdom5g, idx, [])
          }
        })
      }
    },
    // 初始化介绍视频对象
    initIntroduceVideo() {
      if (!this.node.introduceVideoVo) {
        this.$set(this.node, 'introduceVideoVo', {
          id: '',
          type: '',
          viewInfoId: '',
          status: '0',
          backgroundImgFileUrl: '',
          backgroundFileUrl: ''
        })
      }
      // 完全删除status的重新设置，保持接口返回的原始值
      this.updateIntroduceVideoFileList()
    },
    // 确保其他数据结构也有默认值
    initNodeData() {
      // 确保基础字段有默认值 - 只在真正没有值时才设置默认值
      if (this.node.isUnfold === undefined || this.node.isUnfold === null || this.node.isUnfold === '') {
        this.$set(this.node, 'isUnfold', '1')
      }
      if (this.node.displayLocation === undefined || this.node.displayLocation === null || this.node.displayLocation === '') {
        this.$set(this.node, 'displayLocation', '0')
      }
      if (this.node.treeClassification === undefined || this.node.treeClassification === null || this.node.treeClassification === '') {
        this.$set(this.node, 'treeClassification', '3')
      }
      
      // 确保传统模块有默认结构
      if (!this.node.tradition) {
        this.$set(this.node, 'tradition', {
          name: '',
          panoramicViewXmlKey: '',
          backgroundResources: [],
          painPoints: []
        })
      }
      
      // 确保5G智慧模块有默认结构并修正字段映射
      if (!this.node.wisdom5g) {
        this.$set(this.node, 'wisdom5g', {
          name: '',
          panoramicViewXmlKey: '',
          backgroundResources: [],
          painPoints: []
        })
      } else {
        // 修正5G智慧模块的字段映射
        if (this.node.wisdom5g.backgroundResources) {
          this.node.wisdom5g.backgroundResources.forEach(resource => {
            // 确保字段名称正确，但不覆盖已存在的值
            if (resource.backgroundImgFileUrl && (!resource.bgImg || resource.bgImg === '')) {
              resource.bgImg = resource.backgroundImgFileUrl
            }
            if (resource.backgroundFileUrl && (!resource.bgFile || resource.bgFile === '')) {
              resource.bgFile = resource.backgroundFileUrl
            }
            if (resource.tag && (!resource.label || resource.label === '')) {
              resource.label = resource.tag
            }
          })
        }
      }
      
      // 确保成本预估有默认结构
      if (!this.node.costEstimate) {
        this.$set(this.node, 'costEstimate', {
          status: '0',
          title: '',
          contents: []
        })
      }
      
      // 确保成本预估有status字段
      if (this.node.costEstimate && this.node.costEstimate.status === undefined) {
        this.$set(this.node.costEstimate, 'status', '0')
      }
    },
    // 开关联动
    onStatusChange(val) {
      if (val === '0') {
        // 开启时递归开启所有父级
        let p = this.parent
        while (p) {
          if (p.status !== '0') p.status = '0'
          p = p.parent
        }
      } else {
        // 关闭时递归关闭所有子级
        function closeChildren(node) {
          if (node.children && node.children.length) {
            node.children.forEach(child => {
              child.status = '1'
              closeChildren(child)
            })
          }
        }
        closeChildren(this.node)
      }
    },
    // 递归查找并开启所有父级
    findAndOpenParent(id, tree) {
      function helper(nodes, parent) {
        for (let node of nodes) {
          if (node.id === id) {
            if (parent) parent.status = '0'
            return true
          }
          if (node.children && node.children.length) {
            if (helper(node.children, node)) {
              if (parent) parent.status = '0'
              return true
            }
          }
        }
        return false
      }
      helper(tree, null)
    },
    // 新增背景资源管理方法
    addBackgroundResource(type) {
      if (!this.node[type].backgroundResources) {
        this.$set(this.node[type], 'backgroundResources', [])
      }
      
      const newIdx = this.node[type].backgroundResources.length
      
      // 创建完全独立的新资源对象，不包含默认坐标组
      const newResource = { 
        id: null,
        tag: '', 
        status: '',
        type: '',
        viewInfoId: '',
        bgImg: '', 
        bgFile: '',
        coordinates: [] // 改为空数组，不默认添加坐标组
      }
      
      // 使用深拷贝确保对象完全独立
      const independentResource = JSON.parse(JSON.stringify(newResource))
      this.node[type].backgroundResources.push(independentResource)
      
      // 初始化对应的独立文件列表
      this.$set(this.fileLists[type], newIdx, [])
      console.log('添加新背景资源:', independentResource)
    },
    
    // 删除背景资源
    async removeBackgroundResource(type, idx) {
      const resource = this.node[type].backgroundResources[idx]
      
      this.$confirm('确定删除此背景资源吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // 如果背景资源有ID，调用删除接口
        if (resource.id) {
          try {
            this.$modal.loading("正在删除背景资源，请稍候...")
            const res = await backgroundFileDel({ id: resource.id })
            if (res.code === 0) {
              this.node[type].backgroundResources.splice(idx, 1)
              this.$message.success('删除成功')
            } else {
              this.$message.error(res.msg || '删除失败')
            }
          } catch (error) {
            this.$message.error('删除失败')
          } finally {
            this.$modal.closeLoading()
          }
        } else {
          // 没有ID的新背景资源，直接从数组中移除
          this.node[type].backgroundResources.splice(idx, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },
    
    // 添加坐标组
    addCoordinate(type, resourceIdx) {
      const resource = this.node[type].backgroundResources[resourceIdx]
      if (!resource.coordinates) {
        this.$set(resource, 'coordinates', [])
      }
      resource.coordinates.push({ 
        id: 0,
        fileId: 0,
        x: '', 
        y: '', 
        wide: '', 
        high: '', 
        sceneId: '', 
        sceneCode: '',
        xmlKey: ''
      })
    },
    
    // 删除坐标组
    async removeCoordinate(type, resourceIdx, coordIdx) {
      const coord = this.node[type].backgroundResources[resourceIdx].coordinates[coordIdx]
      
      this.$confirm('确定删除此坐标组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // 如果坐标组有ID，调用删除接口
        if (coord.id) {
          try {
            this.$modal.loading("正在删除坐标组，请稍候...")
            const res = await fileBindDel({ id: coord.id })
            if (res.code === 0) {
              this.node[type].backgroundResources[resourceIdx].coordinates.splice(coordIdx, 1)
              this.$message.success('删除成功')
            } else {
              this.$message.error(res.msg || '删除失败')
            }
          } catch (error) {
            this.$message.error('删除失败')
          } finally {
            this.$modal.closeLoading()
          }
        } else {
          // 没有ID的新坐标组，直接从数组中移除
          this.node[type].backgroundResources[resourceIdx].coordinates.splice(coordIdx, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },
    
    // 场景级联选择器变化处理
    handleSceneCascaderChange(val, type, resourceIdx, coordIdx) {
      const scene = this.findSceneById(this.sceneTreeOptions, val)
      if (scene) {
        const coord = this.node[type].backgroundResources[resourceIdx].coordinates[coordIdx]
        coord.sceneCode = scene.sceneCode || ''
        console.log('选择的场景:', scene, '设置sceneCode:', coord.sceneCode)
      }
    },
    
    // 根据ID查找场景
    findSceneById(tree, id) {
      if (!tree || !Array.isArray(tree)) return null
      
      for (const node of tree) {
        if (node.id === id) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findSceneById(node.children, id)
          if (found) return found
        }
      }
      return null
    },
    
    // 格式化坐标数据用于提交
    formatCoordinatesForSubmit(coordinates) {
      if (!coordinates || !Array.isArray(coordinates)) {
        return { clickX: '', clickY: '' }
      }
      
      const xValues = coordinates.map(coord => coord.x || '0').join(',')
      const yValues = coordinates.map(coord => coord.y || '0').join(',')
      
      return {
        clickX: xValues,
        clickY: yValues
      }
    },
    
    // 修改现有的上传方法
    async beforeUploadSceneConfigImg(file, node, type, arrayKeyOrKey, indexOrUndefined, keyOrUndefined) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传图片，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.leftTreeIndustryCode)
        formData.append('sceneCode', node.code)

        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          // 单独上传背景图片首帧时，使用 fileUrl
          const imageUrl = res.data.fileUrl
          
          // 判断是否为数组形式的上传
          if (typeof indexOrUndefined === 'number' && keyOrUndefined) {
            // 数组形式：node[type][arrayKey][index][key]
            this.$set(this.node[type][arrayKeyOrKey][indexOrUndefined], keyOrUndefined, imageUrl)
            console.log('上传成功，设置图片URL:', imageUrl)
            console.log('当前resource:', this.node[type][arrayKeyOrKey][indexOrUndefined])

            // 强制更新视图
            this.$forceUpdate()
            this.$message.success('上传成功')
          } else {
            // 单个字段形式：node[type][key]
            this.$set(this.node[type], arrayKeyOrKey, imageUrl)
            this.$message.success('上传成功')
          }
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传错误:', error)
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    
    async beforeUploadSceneConfigFile(file, node, type, arrayKeyOrKey, indexOrUndefined, keyOrUndefined) {
      try {
        this.$modal.loading("正在上传文件，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.leftTreeIndustryCode)
        formData.append('sceneCode', node.code)

        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          // 判断是否为数组形式的上传
          if (typeof indexOrUndefined === 'number' && keyOrUndefined) {
            // 数组形式处理...
            this.$set(node[type][arrayKeyOrKey][indexOrUndefined], keyOrUndefined, res.data.fileUrl)
            
            const fileName = res.data.fileUrl.split('/').pop()
            this.$set(this.fileLists[type], indexOrUndefined, [{
              name: fileName,
              url: res.data.fileUrl,
              uid: Date.now()
            }])
            
            if (file.type === 'video/mp4' && res.data.imgUrl) {
              this.$set(node[type][arrayKeyOrKey][indexOrUndefined], 'bgImg', res.data.imgUrl)
              this.$message.success('上传成功，已自动生成背景图片首帧')
            } else {
              this.$message.success('上传成功')
            }
          } else {
            // 单个字段形式：node[type][key]
            this.$set(node[type], arrayKeyOrKey, res.data.fileUrl)
            
            // 如果是介绍视频上传
            if (type === 'introduceVideoVo' && arrayKeyOrKey === 'backgroundFileUrl') {
              // 更新介绍视频文件列表
              this.updateIntroduceVideoFileList()
              
              if (file.type === 'video/mp4' && res.data.imgUrl) {
                this.$set(node[type], 'backgroundImgFileUrl', res.data.imgUrl)
                this.$message.success('上传成功，已自动生成介绍视频首帧')
              } else {
                this.$message.success('上传成功')
              }
            } else {
              this.$message.success('上传成功')
            }
          }
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    addPainPoint(type) {
      this.node[type].painPoints = this.node[type].painPoints || []
      this.node[type].painPoints.push({ title: '', contents: [''], showTime: '' })
    },
    removePainPoint(type, idx) {
      this.node[type].painPoints.splice(idx, 1)
    },
    addPainContent(type, idx) {
      this.node[type].painPoints[idx].contents.push('')
    },
    removePainContent(type, idx, cidx) {
      this.node[type].painPoints[idx].contents.splice(cidx, 1)
    },
    addCostContent() {
      this.node.costEstimate.contents.push('')
    },
    removeCostContent(cidx) {
      this.node.costEstimate.contents.splice(cidx, 1)
    },
    // 获取背景文件列表
    getBackgroundFileList(node, type, idx) {
      const resource = node[type].backgroundResources[idx]
      if (resource && resource.bgFile) {
        const fileName = resource.bgFile.split('/').pop()
        return [{
          name: fileName,
          url: resource.bgFile,
          uid: Date.now() + idx
        }]
      }
      return []
    },
    // 处理背景文件删除
    handleRemoveBackgroundFile(node, type, idx) {
      const resource = node[type].backgroundResources[idx]
      resource.bgFile = ''
      resource.bgImg = '' // 同时清空背景图片首帧
      this.$set(this.fileLists[type], idx, [])
      this.$message.success('文件已删除')
    },
    // 获取文件列表 - 确保返回正确的文件列表
    getFileList(type, idx) {
      // 检查对应的资源是否真的有文件
      const resource = this.node[type] && this.node[type].backgroundResources && this.node[type].backgroundResources[idx]
      
      if (!resource || !resource.bgFile || !resource.bgFile.trim()) {
        // 如果没有文件，返回空数组
        return []
      }
      
      // 如果文件列表中有数据且与资源文件匹配，返回文件列表
      if (this.fileLists[type] && this.fileLists[type][idx] && this.fileLists[type][idx].length > 0) {
        const fileList = this.fileLists[type][idx]
        // 验证文件列表中的URL是否与资源中的文件URL匹配
        if (fileList[0].url === resource.bgFile) {
          return fileList
        }
      }
      
      // 如果文件列表不匹配或为空，但资源有文件，重新创建文件列表
      if (resource.bgFile && resource.bgFile.trim()) {
        const fileName = resource.bgFile.split('/').pop()
        const newFileList = [{
          name: fileName,
          url: resource.bgFile,
          uid: Date.now()
        }]
        this.$set(this.fileLists[type], idx, newFileList)
        return newFileList
      }
      
      return []
    },
    getUploadMode(type, idx) {
      if (!this.uploadModes[type][idx]) {
        // 默认根据是否已有文件URL来判断模式
        const resource = this.node[type].backgroundResources[idx]
        const hasFile = resource && resource.bgFile
        this.$set(this.uploadModes[type], idx, hasFile ? 'upload' : 'upload')
      }
      return this.uploadModes[type][idx]
    },
    setUploadMode(type, idx, value) {
      if (typeof idx === 'string') {
        // 介绍视频模式：type='introduceVideo', idx='upload'或'url'
        this.$set(this.uploadModes, type, idx)
      } else {
        // 背景资源模式：type='tradition'或'wisdom5g', idx=数字索引, value='upload'或'url'
        this.$set(this.uploadModes[type], idx, value)
      }
    },
    handleUrlInput(type, idx, value) {
      // 清空文件列表
      this.$set(this.fileLists[type], idx, [])
      
      // 如果输入了链接，创建对应的文件列表项
      if (value) {
        const fileName = value.split('/').pop() || '外部链接文件'
        this.$set(this.fileLists[type], idx, [{
          name: fileName,
          url: value,
          uid: Date.now()
        }])
      }
    },
    // 更新介绍视频文件列表
    updateIntroduceVideoFileList() {
      if (this.node.introduceVideoVo && this.node.introduceVideoVo.backgroundFileUrl) {
        const fileName = this.node.introduceVideoVo.backgroundFileUrl.split('/').pop()
        this.introduceVideoFileList = [{
          name: fileName,
          url: this.node.introduceVideoVo.backgroundFileUrl,
          uid: Date.now()
        }]
      } else {
        this.introduceVideoFileList = []
      }
    },
    getIntroduceVideoFileList() {
      return this.introduceVideoFileList
    },
    handleRemoveIntroduceVideo() {
      if (this.node.introduceVideoVo) {
        this.node.introduceVideoVo.backgroundFileUrl = ''
        this.node.introduceVideoVo.backgroundImgFileUrl = ''
        this.$message.success('介绍视频已删除')
        // 同时更新文件列表
        this.updateIntroduceVideoFileList()
      }
    },
    // 介绍视频开关变化
    onIntroduceVideoStatusChange(val) {
      // 简单处理，不需要复杂逻辑
    },
    
    // 图片预览
    previewImage(url) {
      if (url) {
        this.previewImageUrl = url
        this.previewVisible = true
      }
    },
    
    // 删除介绍视频图片
    deleteIntroduceVideoImg() {
      this.$confirm('确定删除此图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.node.introduceVideoVo.backgroundImgFileUrl = ''
        this.$message.success('图片已删除')
      }).catch(() => {})
    },
    
    // 删除背景资源图片
    deleteBackgroundImg(type, idx) {
      this.$confirm('确定删除此图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$set(this.node[type].backgroundResources[idx], 'bgImg', '')
        // 强制更新视图
        this.$forceUpdate()
        this.$message.success('图片已删除')
      }).catch(() => {})
    },
    // 验证XML Key输入（只允许英文、数字和下划线）
    validateXmlKey(value, type) {
      // 只保留英文字母、数字和下划线
      const filteredValue = value.replace(/[^a-zA-Z0-9_]/g, '')
      this.node[type].panoramicViewXmlKey = filteredValue
    },
    handleIntroduceVideoUrlInput(value) {
      this.introduceVideoFileList = []
      if (value) {
        const fileName = value.split('/').pop() || '外部链接文件'
        this.introduceVideoFileList = [{
          name: fileName,
          url: value,
          uid: Date.now()
        }]
      }
      this.updateIntroduceVideoFileList()
    },
    // 处理坐标数字输入
    handleCoordNumberInput(val, coord, field) {
      // 如果val为null或undefined，设置为空字符串
      if (val === null || val === undefined) {
        coord[field] = ''
      } else {
        coord[field] = val
      }
    }
  },
  computed: {
    // 判断当前节点是否有子节点
    hasChildren() {
      return this.node && this.node.children && this.node.children.length > 0
    }
  },
  components: {
    SceneConfigNode: null // 递归注册，主页面import时补全
  }
}
</script>

<style scoped>
/* 限制上传图片的显示大小 */
.image-upload .el-upload--picture-card {
  width: 148px;
  height: 148px;
  border-radius: 8px;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 6px;
}

/* 背景图片首帧图片大小控制 */
.image-upload .el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

/* 上传框也添加圆角 */
.image-upload .el-upload--picture-card {
  border-radius: 8px;
}

.mini-block {
  margin-bottom: 15px;
}

.background-resource-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
  position: relative;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.resource-title {
  font-weight: bold;
  color: #303133;
  font-size: 14px;
}

.pain-point-block {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.pain-point-block:last-child {
  margin-bottom: 0;
}

/* 图片预览样式 */
.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

/* 图片悬停操作样式 */
.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 6px;
}

.image-preview-container:hover .image-overlay {
  opacity: 1;
}

.preview-icon,
.delete-icon {
  color: white;
  font-size: 20px;
  margin: 0 10px;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-icon:hover,
.delete-icon:hover {
  transform: scale(1.2);
}

.coordinate-group {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  background-color: #fafbfc;
}

.coordinate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: bold;
  color: #606266;
}

/* 确保介绍视频和成本预估卡片高度一致 */
.mini-block .el-card {
  height: 100%;
}

.mini-block .el-card__body {
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

/* 介绍视频和成本预估并排时的高度统一 */
.el-row .el-col .mini-block {
  height: 100%;
}

.el-row .el-col .mini-block .el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.el-row .el-col .mini-block .el-card__body {
  flex: 1;
  min-height: 300px;
}
</style> 
