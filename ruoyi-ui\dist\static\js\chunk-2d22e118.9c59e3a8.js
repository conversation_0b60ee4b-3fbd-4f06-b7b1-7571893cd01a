(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22e118"],{f9d3:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[n("el-form-item",{attrs:{label:"凭证类型",prop:"type"}},[n("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"凭证类型",clearable:""},model:{value:e.queryParams.type,callback:function(t){e.$set(e.queryParams,"type",t)},expression:"queryParams.type"}},e._l(e.dict.type.token_type,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",{attrs:{label:"使用情况",prop:"status"}},[n("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"凭证类型",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.token_status,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:token:add"],expression:"['baoneng:token:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:token:upd"],expression:"['baoneng:token:upd']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tokenList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),n("el-table-column",{attrs:{label:"缴费凭证",align:"center",prop:"token"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.token?n("el-tooltip",{attrs:{content:t.row.token,"raw-content":"",placement:"top-start"}},[t.row.token&&t.row.token.length<=15?n("span",[e._v(" "+e._s(t.row.token)+" ")]):e._e(),t.row.token&&t.row.token.length>15?n("span",[e._v(" "+e._s(t.row.token.substr(0,15)+"...")+" ")]):e._e()]):null==t.row.address?n("span",[e._v(" NA ")]):e._e()]}}])}),n("el-table-column",{attrs:{label:"凭证类型",align:"center",prop:"typeDesc"}}),n("el-table-column",{attrs:{label:"归属人微信",align:"center",prop:"tkAffiliation"}}),n("el-table-column",{attrs:{label:"使用状态",align:"center",prop:"statusDesc"}}),n("el-table-column",{attrs:{label:"获取时间",align:"center",prop:"captureTime"}}),n("el-table-column",{attrs:{label:"过期时间",align:"center",prop:"expirationTime"}}),n("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:token:detail"],expression:"['baoneng:token:detail']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.showTokenDetail(t.row)}}},[e._v("使用详情 ")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:token:upd"],expression:"['baoneng:token:upd']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:token:del"],expression:"['baoneng:token:del']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.page,limit:e.queryParams.limit},on:{"update:page":function(t){return e.$set(e.queryParams,"page",t)},"update:limit":function(t){return e.$set(e.queryParams,"limit",t)},pagination:e.getList}}),n("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"缴费凭证",prop:"token"}},[n("el-input",{attrs:{placeholder:"请输入缴费凭证"},model:{value:e.form.token,callback:function(t){e.$set(e.form,"token",t)},expression:"form.token"}})],1),n("el-form-item",{attrs:{label:"凭证类型",prop:"type"}},[n("el-select",{attrs:{placeholder:"请选择凭证类型"},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},e._l(e.dict.type.token_type,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",{attrs:{label:"微信",prop:"tkAffiliation"}},[n("el-input",{attrs:{placeholder:"请输入微信号"},model:{value:e.form.tkAffiliation,callback:function(t){e.$set(e.form,"tkAffiliation",t)},expression:"form.tkAffiliation"}})],1),n("el-form-item",{attrs:{label:"备注",prop:"remark"}},[n("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],l=(n("d81d"),n("d3b7"),n("25f0"),n("b775"));function r(e){return Object(l["a"])({url:"baoneng/token/list",method:"get",params:e})}function i(e){return Object(l["a"])({url:"/baoneng/token/add",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/baoneng/token/upd",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/baoneng/token/detail",method:"get",params:e})}function c(e){return Object(l["a"])({url:"/baoneng/token/del",method:"post",data:e})}var m={name:"token",dicts:["token_type","token_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,tokenList:[],title:"",deptInfoList:[],open:!1,queryParams:{page:1,limit:10,tokenType:null,tokenStatus:null},form:{token:null,type:null,tkAffiliation:null,remark:null},rules:{token:[{required:!0,message:"缴费凭证不能为空",trigger:"blur"}],type:[{required:!0,message:"凭证类型不能为空",trigger:"blur"}],tkAffiliation:[{required:!0,message:"凭证所属客户不能为空",trigger:"blur"}]}}},mounted:function(){},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,r(this.queryParams).then((function(t){e.tokenList=t.data.records,e.total=t.data.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,carNumber:null,carAffiliation:null,clientAffiliation:null},this.resetForm("form")},handleQuery:function(){this.queryParams.page=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),console.log(this.ids),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加凭证"},handleUpdate:function(e){var t=this;this.reset();var n=e.id||this.ids[0];u({tokenId:n}).then((function(e){t.form=e.data,t.form.type=e.data.type.toString(),t.open=!0,t.title="修改凭证"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?s(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):i(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,n=e.id||this.ids;this.$modal.confirm("是否确认删除数据项？").then((function(){return c({tokenId:n})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},p=m,d=n("2877"),f=Object(d["a"])(p,a,o,!1,null,null,null);t["default"]=f.exports}}]);