{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754893070949}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\babel.config.js", "mtime": 1753326339083}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1743599728056}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}