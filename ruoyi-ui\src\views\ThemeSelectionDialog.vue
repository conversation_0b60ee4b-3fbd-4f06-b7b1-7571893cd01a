<template>
  <div>
    <!-- 主题选择器 -->
    <div class="theme-selector">
      <div v-if="selectedTheme" class="selected-theme-preview">
        <img :src="selectedTheme.themeEffectImg" :alt="selectedTheme.themeName" class="theme-preview-img" />
        <div class="theme-preview-info">
          <div class="theme-name">{{ selectedTheme.themeName }}</div>
        </div>
      </div>
      <el-button v-else type="primary" plain @click="openDialog">选择主题</el-button>
      <el-button v-if="selectedTheme" size="small" @click="openDialog">更换主题</el-button>
    </div>

    <!-- 主题选择弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="closeDialog"
      :show-close="true"
    >
      <theme-selection 
        @confirm="onThemeConfirm"
        @cancel="closeDialog"
      />
    </el-dialog>
  </div>
</template>

<script>
import ThemeSelection from './ThemeSelection.vue'

export default {
  name: 'ThemeSelectionDialog',
  components: {
    ThemeSelection
  },
  props: {
    value: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectedTheme: this.value
    }
  },
  watch: {
    value(newVal) {
      this.selectedTheme = newVal
    },
    selectedTheme(newVal) {
      this.$emit('input', newVal)
    }
  },
  methods: {
    // 打开主题选择弹窗
    openDialog() {
      this.dialogVisible = true
    },
    
    // 关闭主题选择弹窗
    closeDialog() {
      this.dialogVisible = false
    },
    
    // 主题确认回调
    onThemeConfirm(theme) {
      this.selectedTheme = theme
      this.dialogVisible = false
      
      this.$emit('change', theme)
      this.$message.success(`已选择${theme.themeName}主题`)
    }
  }
}
</script>

<style scoped>
/* 主题选择器样式 */
.theme-selector {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.selected-theme-preview {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #f9f9f9;
}

.theme-preview-img {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 12px;
}

.theme-preview-info {
  flex: 1;
}

.theme-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.theme-desc {
  font-size: 12px;
  color: #909399;
}
</style>


