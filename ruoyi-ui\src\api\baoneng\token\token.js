import request from '@/utils/request'

export function tokenList(query){

  return request({
    url: 'baoneng/token/list',
    method: 'get',
    params: query
  })

}

export function addToken(data){

  return request({
    url: '/baoneng/token/add',
    method: 'post',
    data: data
  })

}


export function updToken(data){

  return request({
    url: '/baoneng/token/upd',
    method: 'post',
    data: data
  })

}


export function getTokenDetail(query){

  return request({
    url: '/baoneng/token/detail',
    method: 'get',
    params: query
  })

}

export function delToken(data){

  return request({
    url: '/baoneng/token/del',
    method: 'post',
    data: data
  })

}
