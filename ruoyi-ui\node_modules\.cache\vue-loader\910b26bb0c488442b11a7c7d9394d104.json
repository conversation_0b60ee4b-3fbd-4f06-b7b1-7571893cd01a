{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=template&id=a83bd3b0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754893587771}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1743599730124}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}