{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=template&id=a83bd3b0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754894183874}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1743599730124}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9InBhZ2UtY29udGFpbmVyIj4KICA8IS0tIOKchSDlt6bkvqfoj5zljZXljLrln58gLS0+CiAgPGRpdiBjbGFzcz0ibWVudS1wYW5lbCI+CiAgICA8IS0tIOaQnOe0ouahhiAtLT4KICAgIDxkaXYgY2xhc3M9Im1lbnUtc2VhcmNoIj4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbD0ic2VhcmNoS2V5d29yZCIKICAgICAgICBwbGFjZWhvbGRlcj0i5pCc57Si6I+c5Y2VLi4uIgogICAgICAgIHByZWZpeC1pY29uPSJlbC1pY29uLXNlYXJjaCIKICAgICAgICBjbGVhcmFibGUKICAgICAgICBAaW5wdXQ9ImhhbmRsZVNlYXJjaCIKICAgICAgLz4KICAgIDwvZGl2PgogICAgCiAgICA8ZWwtdHJlZQogICAgICByZWY9Im1lbnVUcmVlIgogICAgICA6ZGF0YT0iZmlsdGVyZWRNZW51RGF0YSIKICAgICAgOnByb3BzPSJ7IGxhYmVsOiAnbmFtZScsIGNoaWxkcmVuOiAnY2hpbGRyZW4nIH0iCiAgICAgIG5vZGUta2V5PSJpZCIKICAgICAgOmN1cnJlbnQtbm9kZS1rZXk9ImFjdGl2ZU1lbnUiCiAgICAgIEBub2RlLWNsaWNrPSJoYW5kbGVUcmVlTm9kZUNsaWNrIgogICAgICBoaWdobGlnaHQtY3VycmVudAogICAgICA6ZXhwYW5kLW9uLWNsaWNrLW5vZGU9ImZhbHNlIgogICAgICA6ZGVmYXVsdC1leHBhbmRlZC1rZXlzPSJtZW51RGF0YS5tYXAoaXRlbSA9PiBpdGVtLmlkKSIKICAgICAgY2xhc3M9Im1lbnUtdHJlZSIKICAgID4KICAgICAgPHNwYW4gY2xhc3M9ImN1c3RvbS10cmVlLW5vZGUiIHNsb3Qtc2NvcGU9Insgbm9kZSwgZGF0YSB9Ij4KICAgICAgICA8c3BhbiB2LWh0bWw9ImhpZ2hsaWdodFRleHQoZGF0YS5uYW1lKSI+PC9zcGFuPgogICAgICA8L3NwYW4+CiAgICA8L2VsLXRyZWU+CiAgPC9kaXY+CgogIDwhLS0g4pyFIOWPs+S+p+WGheWuueWMuuWfnyAtLT4KICA8ZGl2IGNsYXNzPSJjb250ZW50LXBhbmVsIiA6Y2xhc3M9InsgJ2xvYWRpbmctbm8tc2Nyb2xsJzogc3dpdGNoaW5nSW5kdXN0cnkgfSIgdi1sb2FkaW5nPSJzd2l0Y2hpbmdJbmR1c3RyeSIgZWxlbWVudC1sb2FkaW5nLXRleHQ9Iuato+WcqOWIh+aNouihjOS4mi4uLiI+CiAgICA8ZWwtZm9ybSA6bW9kZWw9ImZvcm0iIHJlZj0ic2NlbmVGb3JtIiBsYWJlbC13aWR0aD0iMTIwcHgiPgogICAgICA8ZWwtcm93IDpndXR0ZXI9IjIwIj4KICAgICAgICA8IS0tIOW3puS+p++8muWfuuacrOS/oeaBryAtLT4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtY2FyZCBjbGFzcz0ibWluaS1ibG9jayIgc2hhZG93PSJuZXZlciI+CiAgICAgICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIj7ln7rmnKzkv6Hmga88L2Rpdj4KICAgICAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIyMCI+CiAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Li75qCH6aKYIiByZXF1aXJlZD4KICAgICAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0ubWFpblRpdGxlIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5Li75qCH6aKYIiAvPgogICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Ymv5qCH6aKYIiByZXF1aXJlZD4KICAgICAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0uc3ViVGl0bGUiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlia/moIfpopgiIC8+CiAgICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMjAiPgogICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuiDjOaZr+WbvueJh+mmluW4pyI+CiAgICAgICAgICAgICAgICAgIDxlbC11cGxvYWQKICAgICAgICAgICAgICAgICAgICBjbGFzcz0idXBsb2FkIGltYWdlLXVwbG9hZCIKICAgICAgICAgICAgICAgICAgICBhY3Rpb249IiMiCiAgICAgICAgICAgICAgICAgICAgOnNob3ctZmlsZS1saXN0PSJmYWxzZSIKICAgICAgICAgICAgICAgICAgICBsaXN0LXR5cGU9InBpY3R1cmUtY2FyZCIKICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9ImltYWdlLyoiCiAgICAgICAgICAgICAgICAgICAgOmJlZm9yZS11cGxvYWQ9ImJlZm9yZVVwbG9hZEludHJvZHVjZUltZyIKICAgICAgICAgICAgICAgICAgICA6aHR0cC1yZXF1ZXN0PSIoKSA9PiB7fSIKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgIDxkaXYgdi1pZj0iZm9ybS5iZ0ltZ1VybCIgY2xhc3M9ImltYWdlLXByZXZpZXctY29udGFpbmVyIj4KICAgICAgICAgICAgICAgICAgICAgIDxpbWcgOnNyYz0iZm9ybS5iZ0ltZ1VybCIgY2xhc3M9InVwbG9hZC1pbWFnZSIgLz4KICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImltYWdlLW92ZXJsYXkiPgogICAgICAgICAgICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi16b29tLWluIHByZXZpZXctaWNvbiIgQGNsaWNrLnN0b3A9InByZXZpZXdJbWFnZShmb3JtLmJnSW1nVXJsKSIgdGl0bGU9IumihOiniCI+PC9pPgogICAgICAgICAgICAgICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1kZWxldGUgZGVsZXRlLWljb24iIEBjbGljay5zdG9wPSJkZWxldGVCZ0ltYWdlIiB0aXRsZT0i5Yig6ZmkIj48L2k+CiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICA8aSB2LWVsc2UgY2xhc3M9ImVsLWljb24tcGx1cyI+PC9pPgogICAgICAgICAgICAgICAgICA8L2VsLXVwbG9hZD4KICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS4u+mimOmAieaLqSI+CiAgICAgICAgICAgICAgICAgIDx0aGVtZS1zZWxlY3Rpb24tZGlhbG9nIAogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9InNlbGVjdGVkVGhlbWUiCiAgICAgICAgICAgICAgICAgICAgQGNoYW5nZT0ib25UaGVtZUNoYW5nZSIKICAgICAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICA8L2VsLXJvdz4KICAgICAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIyMCI+CiAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6IOM5pmv5paH5Lu2Ij4KICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0ibWFyZ2luLWJvdHRvbTogOHB4OyI+CiAgICAgICAgICAgICAgICAgICAgPGVsLXJhZGlvLWdyb3VwIHYtbW9kZWw9InVwbG9hZE1vZGVzLmJnRmlsZSB8fCAndXBsb2FkJyIgQGlucHV0PSJ2YWx1ZSA9PiBzZXRVcGxvYWRNb2RlKCdiZ0ZpbGUnLCB2YWx1ZSkiIHNpemU9InNtYWxsIj4KICAgICAgICAgICAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gbGFiZWw9InVwbG9hZCI+5LiK5Lyg5paH5Lu2PC9lbC1yYWRpby1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSJ1cmwiPuWhq+WGmemTvuaOpTwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgICAgICAgICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgPCEtLSDkuIrkvKDmqKHlvI8gLS0+CiAgICAgICAgICAgICAgICAgIDxlbC11cGxvYWQKICAgICAgICAgICAgICAgICAgICB2LWlmPSIodXBsb2FkTW9kZXMuYmdGaWxlIHx8ICd1cGxvYWQnKSA9PT0gJ3VwbG9hZCciCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9InVwbG9hZCIKICAgICAgICAgICAgICAgICAgICBhY3Rpb249IiMiCiAgICAgICAgICAgICAgICAgICAgOnNob3ctZmlsZS1saXN0PSJ0cnVlIgogICAgICAgICAgICAgICAgICAgIDpmaWxlLWxpc3Q9ImJnRmlsZUxpc3QiCiAgICAgICAgICAgICAgICAgICAgOmJlZm9yZS11cGxvYWQ9ImZpbGUgPT4gYmVmb3JlVXBsb2FkSW50cm9kdWNlVmlkZW8oZmlsZSkiCiAgICAgICAgICAgICAgICAgICAgOmh0dHAtcmVxdWVzdD0iKCkgPT4ge30iCiAgICAgICAgICAgICAgICAgICAgOm9uLXJlbW92ZT0iaGFuZGxlUmVtb3ZlQmdGaWxlIgogICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5Ij7kuIrkvKDog4zmma/mlofku7Y8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICAgICAgPC9lbC11cGxvYWQ+CiAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICA8IS0tIOmTvuaOpeaooeW8jyAtLT4KICAgICAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICAgICAgdi1lbHNlCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5iZ0ZpbGVVcmwiCiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaWh+S7tumTvuaOpSIKICAgICAgICAgICAgICAgICAgICBAaW5wdXQ9ImhhbmRsZUJnRmlsZVVybElucHV0IgogICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0iWE1M5paH5Lu2Ij4KICAgICAgICAgICAgICAgICAgPGVsLXVwbG9hZAogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJ1cGxvYWQiCiAgICAgICAgICAgICAgICAgICAgYWN0aW9uPSIjIgogICAgICAgICAgICAgICAgICAgIDpzaG93LWZpbGUtbGlzdD0idHJ1ZSIKICAgICAgICAgICAgICAgICAgICA6ZmlsZS1saXN0PSJ4bWxGaWxlTGlzdCIKICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9Ii54bWwiCiAgICAgICAgICAgICAgICAgICAgOmJlZm9yZS11cGxvYWQ9ImJlZm9yZVVwbG9hZFhtbEZpbGUiCiAgICAgICAgICAgICAgICAgICAgOmh0dHAtcmVxdWVzdD0iKCkgPT4ge30iCiAgICAgICAgICAgICAgICAgICAgOm9uLXJlbW92ZT0iaGFuZGxlUmVtb3ZlWG1sRmlsZSIKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSI+5LiK5LygWE1M5paH5Lu2PC9lbC1idXR0b24+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBzbG90PSJ0aXAiIGNsYXNzPSJlbC11cGxvYWRfX3RpcCI+5Y+q6IO95LiK5LygeG1s5paH5Lu277yM5LiU5LiN6LaF6L+HNTBNQjwvZGl2PgogICAgICAgICAgICAgICAgICA8L2VsLXVwbG9hZD4KICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICA8L2VsLXJvdz4KICAgICAgICAgIDwvZWwtY2FyZD4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICAKICAgICAgICA8IS0tIOWPs+S+p++8muinhumikeiusuinoyAtLT4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtY2FyZCBjbGFzcz0ibWluaS1ibG9jayB2aWRlby1jYXJkIiBzaGFkb3c9Im5ldmVyIj4KICAgICAgICAgICAgPGRpdiBzbG90PSJoZWFkZXIiIGNsYXNzPSJ2aWRlby1oZWFkZXItcm93Ij4KICAgICAgICAgICAgICA8c3Bhbj7op4bpopHorrLop6M8L3NwYW4+CiAgICAgICAgICAgICAgPGVsLXN3aXRjaCB2LW1vZGVsPSJ2aWRlb0V4cGxhbmF0aW9uLnN0YXR1cyIgOmFjdGl2ZS12YWx1ZT0iJzAnIiA6aW5hY3RpdmUtdmFsdWU9IicxJyIgc3R5bGU9ImZsb2F0OnJpZ2h0OyIgLz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgdi1zaG93PSJ2aWRlb0V4cGxhbmF0aW9uLnN0YXR1cyA9PT0gJzAnIj4KICAgICAgICAgICAgPGVsLWNvbGxhcHNlLXRyYW5zaXRpb24+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0idmlkZW8tY2FyZC15dSI+CiAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLorrLop6Pop4bpopEiPgogICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJtYXJnaW4tYm90dG9tOiA4cHg7Ij4KICAgICAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0idXBsb2FkTW9kZXMudmlkZW9FeHBsYW5hdGlvbiB8fCAndXBsb2FkJyIgQGlucHV0PSJ2YWx1ZSA9PiBzZXRVcGxvYWRNb2RlKCd2aWRlb0V4cGxhbmF0aW9uJywgdmFsdWUpIiBzaXplPSJzbWFsbCI+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSJ1cGxvYWQiPuS4iuS8oOaWh+S7tjwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgICAgICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbiBsYWJlbD0idXJsIj7loavlhpnpk77mjqU8L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgICAgICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgIDwhLS0g5LiK5Lyg5qih5byPIC0tPgogICAgICAgICAgICAgICAgICA8ZWwtdXBsb2FkCiAgICAgICAgICAgICAgICAgICAgdi1pZj0iKHVwbG9hZE1vZGVzLnZpZGVvRXhwbGFuYXRpb24gfHwgJ3VwbG9hZCcpID09PSAndXBsb2FkJyIKICAgICAgICAgICAgICAgICAgICBhY3Rpb249IiMiCiAgICAgICAgICAgICAgICAgICAgOnNob3ctZmlsZS1saXN0PSJ0cnVlIgogICAgICAgICAgICAgICAgICAgIDpmaWxlLWxpc3Q9InZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCIKICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9Ii5tcDQiCiAgICAgICAgICAgICAgICAgICAgOmJlZm9yZS11cGxvYWQ9ImZpbGUgPT4gYmVmb3JlVXBsb2FkSW50cm9kdWNlVmlkZW8oZmlsZSwgJ3ZpZGVvRXhwbGFuYXRpb24nLCAnYmFja2dyb3VuZEZpbGVVcmwnKSIKICAgICAgICAgICAgICAgICAgICA6aHR0cC1yZXF1ZXN0PSIoKSA9PiB7fSIKICAgICAgICAgICAgICAgICAgICA6b24tcmVtb3ZlPSJoYW5kbGVSZW1vdmVWaWRlb0V4cGxhbmF0aW9uRmlsZSIKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ic21hbGwiIHR5cGU9InByaW1hcnkiPueCueWHu+S4iuS8oDwvZWwtYnV0dG9uPgogICAgICAgICAgICAgICAgICAgIDxkaXYgc2xvdD0idGlwIiBjbGFzcz0iZWwtdXBsb2FkX190aXAiPuWPquiDveS4iuS8oG1wNOaWh+S7tjwvZGl2PgogICAgICAgICAgICAgICAgICA8L2VsLXVwbG9hZD4KICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgIDwhLS0g6ZO+5o6l5qih5byPIC0tPgogICAgICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgICAgICB2LWVsc2UKICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJ2aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsIgogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXop4bpopHpk77mjqUiCiAgICAgICAgICAgICAgICAgICAgQGlucHV0PSJoYW5kbGVWaWRlb0V4cGxhbmF0aW9uVXJsSW5wdXQiCiAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuinhumikeWIhuauteivtOaYjiI+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InNlZ21lbnQtc2Nyb2xsIj4KICAgICAgICAgICAgICAgICAgICA8ZGl2IHYtZm9yPSIoc2VnLCBpZHgpIGluIHZpZGVvU2VnbWVudGVkTGlzdCIgOmtleT0iaWR4IiBzdHlsZT0iZGlzcGxheTpmbGV4O2FsaWduLWl0ZW1zOmNlbnRlcjttYXJnaW4tYm90dG9tOjhweDsiPgogICAgICAgICAgICAgICAgICAgICAgPGVsLWlucHV0LW51bWJlcgogICAgICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJzZWcudGltZSIKICAgICAgICAgICAgICAgICAgICAgICAgOm1pbj0iMCIKICAgICAgICAgICAgICAgICAgICAgICAgOm1heD0iOTk5OTk5IgogICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i5pe26Ze0IgogICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEyMHB4OyBtYXJnaW4tcmlnaHQ6IDEwcHg7IgogICAgICAgICAgICAgICAgICAgICAgICBAY2hhbmdlPSJ2YWwgPT4gaGFuZGxlVGltZUNoYW5nZSh2YWwsIGlkeCkiCiAgICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9Im1hcmdpbi1yaWdodDogMTBweDsgY29sb3I6ICM2MDYyNjY7Ij7np5I8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtY2FzY2FkZXIKICAgICAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ic2VnLnNjZW5lSWQiCiAgICAgICAgICAgICAgICAgICAgICAgIDpvcHRpb25zPSJzY2VuZVRyZWVPcHRpb25zIgogICAgICAgICAgICAgICAgICAgICAgICA6cHJvcHM9InNjZW5lQ2FzY2FkZXJQcm9wcyIKICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgICAgICAgICAgICAgICBjaGVjay1zdHJpY3RseQogICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i5omA5bGe5Zy65pmvIgogICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDIwMHB4OyBtYXJnaW4tcmlnaHQ6IDEwcHg7IgogICAgICAgICAgICAgICAgICAgICAgICBAY2hhbmdlPSJ2YWwgPT4gaGFuZGxlU2NlbmVDYXNjYWRlckNoYW5nZSh2YWwsIGlkeCkiCiAgICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJkYW5nZXIiIGljb249ImVsLWljb24tZGVsZXRlIiBjaXJjbGUgQGNsaWNrPSJyZW1vdmVWaWRlb1NlZ21lbnQoaWR4KSIgLz4KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgcGxhaW4gQGNsaWNrPSJhZGRWaWRlb1NlZ21lbnQiIHN0eWxlPSJtYXJnaW4tdG9wOiA4cHg7Ij7lop7liqDliIbmrrU8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2VsLWNvbGxhcHNlLXRyYW5zaXRpb24+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9lbC1jYXJkPgogICAgICAgIDwvZWwtY29sPgogICAgICA8L2VsLXJvdz4KICAgIDwvZWwtZm9ybT4KCiAgICA8IS0tIOKchSDliIbnsbvljLrln5/vvIjmlK/mjIHmipjlj6DvvIkgLS0+CiAgICA8ZGl2CiAgICAgIHYtZm9yPSIoY2F0ZWdvcnksIGluZGV4KSBpbiBjYXRlZ29yaWVzIgogICAgICA6a2V5PSJjYXRlZ29yeS5rZXkiCiAgICAgIGNsYXNzPSJjYXRlZ29yeS1ibG9jayIKICAgID4KICAgICAgPGRpdiBjbGFzcz0iY2F0ZWdvcnktaGVhZGVyIj4KICAgICAgICA8ZGl2IGNsYXNzPSJjYXRlZ29yeS10aXRsZSIgQGRibGNsaWNrPSJzdGFydEVkaXRUaXRsZShpbmRleCkiPgogICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgIHYtaWY9ImNhdGVnb3J5LmVkaXRpbmciCiAgICAgICAgICAgIHYtbW9kZWw9ImNhdGVnb3J5LmVkaXRpbmdOYW1lIgogICAgICAgICAgICBAYmx1cj0iZmluaXNoRWRpdFRpdGxlKGluZGV4KSIKICAgICAgICAgICAgQGtleXVwLmVudGVyPSJmaW5pc2hFZGl0VGl0bGUoaW5kZXgpIgogICAgICAgICAgICBAa2V5dXAuZXNjPSJjYW5jZWxFZGl0VGl0bGUoaW5kZXgpIgogICAgICAgICAgICA6ZGF0YS1lZGl0LWluZGV4PSJpbmRleCIKICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMjAwcHg7IgogICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5qCH6aKYIgogICAgICAgICAgLz4KICAgICAgICAgIDxzcGFuIHYtZWxzZT57eyBjYXRlZ29yeS5uYW1lIH19PC9zcGFuPgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC1zd2l0Y2gKICAgICAgICAgIHYtbW9kZWw9ImNhdGVnb3J5LmVuYWJsZWQiCiAgICAgICAgICBhY3RpdmUtY29sb3I9IiMxM2NlNjYiCiAgICAgICAgICBpbmFjdGl2ZS1jb2xvcj0iI2NjYyIKICAgICAgICAvPgogICAgICA8L2Rpdj4KCiAgICAgIDxkaXYgdi1zaG93PSJjYXRlZ29yeS5lbmFibGVkIiBjbGFzcz0iY2F0ZWdvcnktYm9keSI+CiAgICAgICAgPGRpdiB2LWlmPSJjYXRlZ29yeS5rZXkgPT09ICdkZWZhdWx0X3NjZW5lJyI+CiAgICAgICAgICA8ZWwtZm9ybSBsYWJlbC13aWR0aD0iMTIwcHgiPgogICAgICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjIwIj4KICAgICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgICAgICA8IS0tIOWcuuaZr+mFjee9ruWIhuWdlyAtLT4KICAgICAgICAgICAgICAgIDxlbC1jYXJkIGNsYXNzPSJtaW5pLWJsb2NrIHNjZW5lLWNvbmZpZy1jb250YWluZXIiIHNoYWRvdz0ibmV2ZXIiIHN0eWxlPSJtYXJnaW4tdG9wOiAyMHB4OyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIj7lnLrmma/phY3nva48L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjIwIj4KICAgICAgICAgICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjYiPgogICAgICAgICAgICAgICAgICAgICAgICA8ZWwtdHJlZQogICAgICAgICAgICAgICAgICAgICAgICAgIHJlZj0ic2NlbmVUcmVlIgogICAgICAgICAgICAgICAgICAgICAgICAgIDpkYXRhPSJzY2VuZUNvbmZpZ1RyZWUiCiAgICAgICAgICAgICAgICAgICAgICAgICAgbm9kZS1rZXk9ImlkIgogICAgICAgICAgICAgICAgICAgICAgICAgIDpwcm9wcz0ieyBsYWJlbDogJ25hbWUnLCBjaGlsZHJlbjogJ2NoaWxkcmVuJyB9IgogICAgICAgICAgICAgICAgICAgICAgICAgIEBub2RlLWNsaWNrPSJoYW5kbGVTY2VuZU5vZGVDbGljayIKICAgICAgICAgICAgICAgICAgICAgICAgICBoaWdobGlnaHQtY3VycmVudAogICAgICAgICAgICAgICAgICAgICAgICAgIDpleHBhbmQtb24tY2xpY2stbm9kZT0iZmFsc2UiCiAgICAgICAgICAgICAgICAgICAgICAgICAgOmRlZmF1bHQtZXhwYW5kZWQta2V5cz0idHJlZUV4cGFuZGVkS2V5cy5sZW5ndGggPiAwID8gdHJlZUV4cGFuZGVkS2V5cyA6IChzY2VuZUNvbmZpZ1RyZWUubGVuZ3RoID8gW3NjZW5lQ29uZmlnVHJlZVswXS5pZF0gOiBbXSkiCiAgICAgICAgICAgICAgICAgICAgICAgICAgOmN1cnJlbnQtbm9kZS1rZXk9InNlbGVjdGVkTm9kZSA/IHNlbGVjdGVkTm9kZS5pZCA6IG51bGwiCiAgICAgICAgICAgICAgICAgICAgICAgICAgOnNvcnQ9ImZhbHNlIgogICAgICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxOCI+CiAgICAgICAgICAgICAgICAgICAgICAgIDxTY2VuZUNvbmZpZ05vZGUgCiAgICAgICAgICAgICAgICAgICAgICAgICAgdi1pZj0ic2VsZWN0ZWROb2RlIiAKICAgICAgICAgICAgICAgICAgICAgICAgICA6bm9kZT0ic2VsZWN0ZWROb2RlIiAKICAgICAgICAgICAgICAgICAgICAgICAgICA6cm9vdC10cmVlPSJzY2VuZUNvbmZpZ1RyZWUiCiAgICAgICAgICAgICAgICAgICAgICAgICAgOnNjZW5lLXRyZWUtb3B0aW9ucz0ic2NlbmVUcmVlT3B0aW9ucyIKICAgICAgICAgICAgICAgICAgICAgICAgICA6bGVmdC10cmVlLWluZHVzdHJ5LWNvZGU9ImluZHVzdHJ5Q29kZSIKICAgICAgICAgICAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICAgICAgICAgIDwvZWwtcm93PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZWwtY2FyZD4KICAgICAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgICA8L2VsLWZvcm0+CiAgICAgICAgPC9kaXY+CgogICAgICAgIDwhLS0g5YW25LuW5YiG57G755qE5Y2g5L2N5YaF5a65IC0tPgogICAgICAgIDxkaXYgdi1lbHNlLWlmPSJjYXRlZ29yeS5rZXkgPT09ICdkZWZhdWx0X3BsYW4nIj4KICAgICAgICAgIDxuZXR3b3JrLXBsYW4tY29uZmlnIHYtbW9kZWw9Im5ldHdvcmtQbGFuRGF0YSIgOmxlZnQtdHJlZS1pbmR1c3RyeS1jb2RlPSJpbmR1c3RyeUNvZGUiIC8+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGRpdiB2LWVsc2UtaWY9ImNhdGVnb3J5LmtleSA9PT0gJ2RlZmF1bHRfdmFsdWUnIj4KICAgICAgICAgIDxidXNpbmVzcy12YWx1ZS1jb25maWcgdi1tb2RlbD0iYnVzaW5lc3NWYWx1ZURhdGEiIDpsZWZ0LXRyZWUtaW5kdXN0cnktY29kZT0iaW5kdXN0cnlDb2RlIiAvPgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgdi1lbHNlLWlmPSJjYXRlZ29yeS5rZXkgPT09ICdkZWZhdWx0X3ZyJyI+CiAgICAgICAgICA8dnItc2NlbmUtY29uZmlnIHYtbW9kZWw9InZyU2NlbmVEYXRhIiAvPgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgdi1lbHNlPgogICAgICAgICAgPHA+6L+Z6YeM5pivIDxzdHJvbmc+e3sgY2F0ZWdvcnkubmFtZSB9fTwvc3Ryb25nPiDliIbnsbvnmoTlhoXlrrnljLrln5/jgII8L3A+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJzdWJtaXQtZm9vdGVyIj4KICAgIDxkaXYgY2xhc3M9ImZvcm0tYWN0aW9ucyI+CiAgICAgIDwhLS0g5ZCM5q2l5paH5Lu25oyJ6ZKu5YqgIHRvb2x0aXAgLS0+CiAgICAgIDxlbC10b29sdGlwIAogICAgICAgIGVmZmVjdD0iZGFyayIgCiAgICAgICAgY29udGVudD0i6ZO+5o6l5aGr5YaZ5a6M5oiQ5ZCO77yM54K55Ye744CQ5o+Q5Lqk44CR5ZCO5YaN54K55Ye744CQ5ZCM5q2l44CR5oyJ6ZKuIiAKICAgICAgICBwbGFjZW1lbnQ9InRvcCIKICAgICAgPgogICAgICAgIDxlbC1idXR0b24gCiAgICAgICAgICB0eXBlPSJzdWNjZXNzIiAKICAgICAgICAgIEBjbGljaz0iaGFuZGxlU3luY2hyb25pemVGaWxlIiAKICAgICAgICAgIDpkaXNhYmxlZD0iIWZvcm0uc2NlbmVWaWV3Q29uZmlnSWQiCiAgICAgICAgICA6bG9hZGluZz0ic3luY2hyb25pemluZyIKICAgICAgICA+CiAgICAgICAgICB7eyBzeW5jaHJvbml6aW5nID8gJ+WQjOatpeS4rS4uLicgOiAn5ZCM5q2l5paH5Lu2JyB9fQogICAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8L2VsLXRvb2x0aXA+CgogICAgICA8ZWwtYnV0dG9uIAogICAgICAgIHR5cGU9InByaW1hcnkiIAogICAgICAgIEBjbGljaz0iaGFuZGxlU3VibWl0IiAKICAgICAgICA6bG9hZGluZz0ic3VibWl0dGluZyIgCiAgICAgICAgc3R5bGU9Im1hcmdpbi1sZWZ0OiAzMHB4OyIKICAgICAgPgogICAgICAgIHt7IHN1Ym1pdHRpbmcgPyAn5o+Q5Lqk5LitLi4uJyA6ICfmj5DkuqQnIH19CiAgICAgIDwvZWwtYnV0dG9uPgogICAgPC9kaXY+CiAgPC9kaXY+CgogIDwvZGl2PgogIAogIDwhLS0g5Zu+54mH6aKE6KeI5a+56K+d5qGGIC0tPgogIDxlbC1kaWFsb2cKICAgIDp2aXNpYmxlLnN5bmM9InByZXZpZXdWaXNpYmxlIgogICAgdGl0bGU9IuWbvueJh+mihOiniCIKICAgIHdpZHRoPSI2MCUiCiAgICBhcHBlbmQtdG8tYm9keQogICAgQGNsb3NlPSJjbG9zZVByZXZpZXciCiAgPgogICAgPGRpdiBjbGFzcz0icHJldmlldy1jb250YWluZXIiPgogICAgICA8aW1nIDpzcmM9InByZXZpZXdJbWFnZVVybCIgY2xhc3M9InByZXZpZXctaW1hZ2UiIC8+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KPC9kaXY+Cg=="}, null]}