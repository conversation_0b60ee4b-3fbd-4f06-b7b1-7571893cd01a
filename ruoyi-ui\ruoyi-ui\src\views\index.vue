methods: {
  async loadIndustryMenu() {
    const res = await getIndustryList()
    if (res.code === 0 && Array.isArray(res.data)) {
      // 转换数据结构为树形菜单
      this.menuData = res.data.map(plate => ({
        id: `plate_${plate.plateKey}`,
        name: plate.plateName,
        type: 'plate',
        children: plate.industryTreeListVos ? plate.industryTreeListVos.map(industry => ({
          id: industry.id,
          name: industry.industryName,
          industryCode: industry.industryCode,
          plate: industry.plate,
          type: 'industry'
        })) : []
      }))
      
      // 创建扁平化的行业数据，用于业务逻辑
      this.flatMenuData = []
      res.data.forEach(plate => {
        if (plate.industryTreeListVos) {
          this.flatMenuData.push(...plate.industryTreeListVos)
        }
      })
      
      // 默认选中第一个行业
      if (this.flatMenuData.length) {
        this.activeMenu = String(this.flatMenuData[0].id)
        await this.handleSelect(this.activeMenu)
      }
    }
  },
  
  handleTreeNodeClick(data) {
    // 只有点击行业节点才处理
    if (data.type === 'industry') {
      this.handleSelect(String(data.id))
    }
  },
  
  handleSubmit() {
    // 从扁平化菜单数据中获取当前行业的 industryCode
    const currentIndustry = this.flatMenuData.find(item => String(item.id) === this.activeMenu)
    const industryCode = currentIndustry ? currentIndustry.industryCode : null
    
    // 构建提交数据
    const submitData = {
      industryId: this.activeMenu,
      industryCode: industryCode, // 新增参数
      sceneViewConfigId: this.form.sceneViewConfigId || null,
      mainTitle: this.form.mainTitle || null,
      subTitle: this.form.subTitle || null,
      themeId: this.selectedTheme ? this.selectedTheme.themeId : null,
      backgroundImgFileUrl: this.form.bgImgUrl || null,
      backgroundFileUrl: this.form.bgFileUrl || null,
      panoramicViewXmlUrl: this.form.panoramicViewXmlUrl || null,
      networkSolutionInfoVo: {
        networkVideoList: (this.networkPlanDataMap[this.activeMenu]?.networkVideoList && Array.isArray(this.networkPlanDataMap[this.activeMenu].networkVideoList)) ? 
          this.networkPlanDataMap[this.activeMenu].networkVideoList.map(plan => ({
            id: plan.id || null,
            type: 4,
            tag: plan.tag || null,
            clickX: plan.clickX || null,
            clickY: plan.clickY || null,
            wide: plan.wide || null,
            high: plan.high || null,
            backgroundImgFileUrl: plan.backgroundImgFileUrl || null,
            backgroundFileUrl: plan.backgroundFileUrl || null,
            status: null,
            viewInfoId: null
          })) : [],
        videoExplanationVo: {
          status: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.status || '0',
          backgroundFileUrl: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.backgroundFileUrl || null,
          videoSegmentedVoList: (this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.videoSegmentedVoList?.length) ? 
            this.networkPlanDataMap[this.activeMenu].videoExplanationVo.videoSegmentedVoList.map(seg => ({
              time: seg.time || null,
              sceneCode: seg.sceneCode || null,
              sceneName: seg.sceneName || null
            })) : null
        }
      },
      sceneDefaultConfigVoList: this.categories.map(cat => {
        const baseConfig = {
          id: cat.id || null,
          industryId: this.activeMenu || null,
          name: cat.name,
          keyName: cat.key,
          keyValue: cat.enabled ? '0' : '1',
          remark: cat.remark || cat.name,
          defaultStatus: '0'
        }
      
        if (cat.key === 'default_scene') {
          baseConfig.industrySceneInfoVo = {
            videoExplanationVo: {
              status: this.videoExplanation.status,
              backgroundFileUrl: this.videoExplanation.backgroundFileUrl || null,
              videoSegmentedVoList: this.videoExplanation.videoSegmentedVoList.length ? this.videoExplanation.videoSegmentedVoList : null
            },
            sceneListVo: this.convertSceneTreeToApi(this.sceneConfigTree)
          }
        } else {
          baseConfig.industrySceneInfoVo = null
        }
        
        return baseConfig
      }),
      commercialValueDTO: (this.businessValueDataMap[this.activeMenu] && Array.isArray(this.businessValueDataMap[this.activeMenu])) ? 
        this.businessValueDataMap[this.activeMenu].map(value => ({
          id: value.id || null,
          viewInfoId: value.viewInfoId || null,
          type: 5,
          tag: value.tag || null,
          backgroundImgFileUrl: value.backgroundImgFileUrl || null,
          backgroundFileUrl: value.backgroundFileUrl || null
        })) : [],
      vrInfoDtoList: (this.vrSceneDataMap[this.activeMenu] && Array.isArray(this.vrSceneDataMap[this.activeMenu])) ? 
        this.vrSceneDataMap[this.activeMenu].map(vr => ({
          id: vr.id || null,
          industryId: vr.industryId || this.activeMenu,
          type: vr.type || 6,
          viewInfoId: vr.viewInfoId || null,
          name: vr.name || '',
          address: vr.address || ''
        })) : [],
    }
    
    sceneViewUpd(submitData).then(response => {
      this.$modal.msgSuccess("修改成功");
      
      // 重新加载数据
      this.handleSelect(this.activeMenu)
    })
    
    console.log('提交内容:', submitData)
    this.$message.success('已打印到控制台，可对接保存接口')
  }
}