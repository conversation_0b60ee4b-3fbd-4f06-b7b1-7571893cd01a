(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d67a0"],{"735c":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"车牌号",prop:"carNumber"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.carNumber,callback:function(t){e.$set(e.queryParams,"carNumber",t)},expression:"queryParams.carNumber"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:car:add"],expression:"['baoneng:car:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:car:upd"],expression:"['baoneng:car:upd']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.carList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"carNumber"}}),a("el-table-column",{attrs:{label:"车主微信号",align:"center",prop:"carAffiliation"}}),a("el-table-column",{attrs:{label:"客户归属",align:"center",prop:"clientAffiliation"}}),a("el-table-column",{attrs:{label:"添加时间",align:"center",prop:"createTime"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:car:upd"],expression:"['baoneng:car:upd']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleEditDevice(t.row)}}},[e._v("详情")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:car:upd"],expression:"['baoneng:car:upd']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["baoneng:car:del"],expression:"['baoneng:car:del']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.page,limit:e.queryParams.limit},on:{"update:page":function(t){return e.$set(e.queryParams,"page",t)},"update:limit":function(t){return e.$set(e.queryParams,"limit",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"车牌号",prop:"carNumber"}},[a("el-input",{attrs:{placeholder:"请输入车牌号"},model:{value:e.form.carNumber,callback:function(t){e.$set(e.form,"carNumber",t)},expression:"form.carNumber"}})],1),a("el-form-item",{attrs:{label:"车主微信",prop:"carAffiliation"}},[a("el-input",{attrs:{placeholder:"请输入车主微信号"},model:{value:e.form.carAffiliation,callback:function(t){e.$set(e.form,"carAffiliation",t)},expression:"form.carAffiliation"}})],1),a("el-form-item",{attrs:{label:"客户归属",prop:"clientAffiliation"}},[a("el-select",{attrs:{placeholder:"请选择客户归属"},model:{value:e.form.clientAffiliation,callback:function(t){e.$set(e.form,"clientAffiliation",t)},expression:"form.clientAffiliation"}},e._l(e.userList,(function(e){return a("el-option",{key:e.userId,attrs:{label:e.nickName,value:e.nickName}})})),1)],1),a("el-form-item",{attrs:{label:"卡类型",prop:"carType"}},[a("el-select",{attrs:{placeholder:"请选择卡类型"},model:{value:e.form.carType,callback:function(t){e.$set(e.form,"carType",t)},expression:"form.carType"}},e._l(e.dict.type.car_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"办理日期",prop:"transactTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择安装时间"},model:{value:e.form.transactTime,callback:function(t){e.$set(e.form,"transactTime",t)},expression:"form.transactTime"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],n=a("5530"),l=(a("d81d"),a("14d9"),a("b775"));function o(e){return Object(l["a"])({url:"/baoneng/car/list",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/baoneng/car/add",method:"post",data:e})}function c(e){return console.log(e),Object(l["a"])({url:"/baoneng/car/detail",method:"get",params:e})}function u(e){return Object(l["a"])({url:"/baoneng/car/upd",method:"post",data:e})}function m(e){return Object(l["a"])({url:"/baoneng/car/del",method:"post",data:e})}function d(){return Object(l["a"])({url:"/system/users/list",method:"get"})}var p={name:"Device",dicts:["car_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,carList:[],title:"",userList:[],deptInfoList:[],open:!1,queryParams:{page:1,limit:10,id:null},form:{},rules:{carNumber:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],carAffiliation:[{required:!0,message:"车主微信号不能为空",trigger:"blur"}],clientAffiliation:[{required:!0,message:"客户归属不能为空",trigger:"blur"}],carType:[{required:!0,message:"卡类型不能为空",trigger:"blur"}],transactTime:[{required:!0,message:"办理时间不能为空",trigger:"blur"}]}}},mounted:function(){var e=this;d().then((function(t){e.userList=t.data}))},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.carList=t.data.records,e.total=t.data.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,carNumber:null,carAffiliation:null,clientAffiliation:null},this.resetForm("form")},handleQuery:function(){this.queryParams.page=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),console.log(this.ids),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加设备"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids[0];c({carId:a}).then((function(e){t.form=e.data,t.open=!0,t.title="修改车辆"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?u(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):s(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除车牌号为"'+e.carNumber+'"的数据项？').then((function(){return m({carId:a})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("base/device/export",Object(n["a"])({},this.queryParams),"device_".concat((new Date).getTime(),".xlsx"))},handleEditDevice:function(e){var t=0;0!=e&&(t=e.id||this.ids),this.$router.push({path:"/base/device-detail",query:{id:t}})}}},f=p,h=a("2877"),b=Object(h["a"])(f,r,i,!1,null,null,null);t["default"]=b.exports}}]);