import request from '@/utils/request'

export function carList(query) {

  return request({
    url: '/baoneng/car/list',
    method: 'get',
    params: query
  })

}

export function addCar(data){

  return request({
    url: '/baoneng/car/add',
    method: 'post',
    data: data
  })

}

export function getCarDetail(query){
  console.log(query)
  return request({
    url: '/baoneng/car/detail',
    method: 'get',
    params: query
  })

}

export function updCar(data){

  return request({
    url: '/baoneng/car/upd',
    method: 'post',
    data: data
  })

}

export function delCar(data){

  return request({
    url: '/baoneng/car/del',
    method: 'post',
    data: data
  })

}
