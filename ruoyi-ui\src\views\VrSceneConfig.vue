<template>
  <div class="vr-scene-config">
    <!-- VR看现场配置 -->
    <div class="config-header">
      <span>VR看现场配置</span>
      <el-button type="primary" size="small" @click="addVr">
        <i class="el-icon-plus"></i> 添加VR场景
      </el-button>
    </div>
    
    <div class="vr-list">
      <div class="vr-scroll-container">
        <el-row :gutter="20">
          <el-col 
            :span="8" 
            v-for="(vr, index) in vrList" 
            :key="index"
          >
            <el-card class="vr-item" shadow="hover">
              <div slot="header" class="vr-header">
                <span>VR场景 {{ index + 1 }}</span>
                <el-button 
                  type="danger" 
                  size="mini" 
                  icon="el-icon-delete" 
                  circle 
                  @click="removeVr(index)"
                />
              </div>
              <el-form label-width="60px" size="small">
                <el-form-item label="名称" required>
                  <el-input 
                    v-model="vr.name" 
                    placeholder="请输入VR场景名称" 
                    @input="emitChange"
                  />
                </el-form-item>
                <el-form-item label="地址" required>
                  <el-input 
                    v-model="vr.address" 
                    placeholder="请输入VR场景地址（http://或https://开头）" 
                    @input="validateAddress(vr, index)"
                    @blur="validateAddressOnBlur(vr, index)"
                    @paste="handlePaste(vr, index, $event)"
                  />
                  <div v-if="vr.addressError" class="error-text">
                    {{ vr.addressError }}
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <div v-if="!vrList.length" class="empty-state">
        <i class="el-icon-document-add"></i>
        <p>暂无VR场景，点击上方按钮添加</p>
      </div>
    </div>
  </div>
</template>

<script>
import { sceneFileInfoDel } from '@/api/view/sceneView'
export default {
  name: 'VrSceneConfig',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      vrList: []
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.vrList = Array.isArray(newVal) ? 
          JSON.parse(JSON.stringify(newVal)) : []
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addVr() {
      this.vrList.push({ 
        id: null,
        industryId: null,
        type: null,
        viewInfoId: null,
        name: '', 
        address: '', 
        addressError: '' 
      })
      this.emitChange()
    },
    async removeVr(index) {
      const vr = this.vrList[index]
      
      this.$confirm('确定删除此VR场景吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // 如果VR场景有ID，调用删除接口
        if (vr.id) {
          try {
            this.$modal.loading("正在删除VR场景，请稍候...")
            const res = await sceneFileInfoDel({ id: vr.id })
            if (res.code === 0) {
              this.vrList.splice(index, 1)
              this.emitChange()
              this.$message.success('删除成功')
            } else {
              this.$message.error(res.msg || '删除失败')
            }
          } catch (error) {
            this.$message.error('删除失败')
          } finally {
            this.$modal.closeLoading()
          }
        } else {
          // 没有ID的新VR场景，直接从数组中移除
          this.vrList.splice(index, 1)
          this.emitChange()
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },
    emitChange() {
      this.$emit('input', this.vrList)
    },
    validateAddress(vr, index) {
      // 实时清除错误信息，但不进行格式验证（避免输入过程中频繁提示）
      this.$set(vr, 'addressError', '')
      this.emitChange()
    },
    
    validateAddressOnBlur(vr, index) {
      if (!vr.address) {
        this.$set(vr, 'addressError', '请输入VR场景地址')
        return
      }
      
      // 更严格的URL格式验证
      const urlPattern = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
      
      if (!urlPattern.test(vr.address)) {
        this.$set(vr, 'addressError', '请输入有效的链接地址（必须以http://或https://开头）')
      } else {
        this.$set(vr, 'addressError', '')
      }
    },
    handlePaste(vr, index, event) {
      // 获取粘贴的内容
      const pasteData = event.clipboardData.getData('text')
      
      // 简单验证粘贴的内容是否像链接
      if (pasteData && !pasteData.startsWith('http://') && !pasteData.startsWith('https://')) {
        event.preventDefault()
        this.$message.warning('请粘贴有效的链接地址（以http://或https://开头）')
        return false
      }
    }
  }
}
</script>

<style scoped>
.vr-scene-config {
  padding: 20px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

.vr-list {
  min-height: 200px;
}

.vr-scroll-container {
  max-height: 480px; /* 限制高度，大约可显示2行（6个卡片） */
  overflow-y: auto;
  overflow-x: hidden;
}

.vr-item {
  margin-bottom: 20px;
  height: 200px; /* 固定卡片高度 */
}

.vr-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.el-form-item {
  margin-bottom: 15px;
}

.el-form-item:last-child {
  margin-bottom: 0;
}

/* 滚动条样式 */
.vr-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.vr-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.vr-scroll-container::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.vr-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a6a9ad;
}
</style>














