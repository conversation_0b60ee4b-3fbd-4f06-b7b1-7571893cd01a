{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754893070949}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1743599728056}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRJbmR1c3RyeUxpc3QsIGdldFNjZW5lVHJlZUxpc3QgfSBmcm9tICdAL2FwaS92aWV3L2luZHVzdHJ5Jw0KaW1wb3J0IFNjZW5lQ29uZmlnTm9kZSBmcm9tICcuL1NjZW5lQ29uZmlnTm9kZS52dWUnDQppbXBvcnQgeyBnZXRTY2VuZVZpZXdDb25maWcsIHNjZW5lVmlld1VwZCwgdXBsb2FkU2NlbmVGaWxlLCBzeW5jaHJvbml6YXRpb25GaWxlIH0gZnJvbSAnQC9hcGkvdmlldy9zY2VuZVZpZXcnDQppbXBvcnQgTmV0d29ya1BsYW5Db25maWcgZnJvbSAnLi9OZXR3b3JrUGxhbkNvbmZpZy52dWUnDQppbXBvcnQgQnVzaW5lc3NWYWx1ZUNvbmZpZyBmcm9tICcuL0J1c2luZXNzVmFsdWVDb25maWcudnVlJw0KaW1wb3J0IFZyU2NlbmVDb25maWcgZnJvbSAnLi9WclNjZW5lQ29uZmlnLnZ1ZScNCmltcG9ydCBUaGVtZVNlbGVjdGlvbkRpYWxvZyBmcm9tICcuL1RoZW1lU2VsZWN0aW9uRGlhbG9nLnZ1ZScNCmltcG9ydCBheGlvcyBmcm9tICdheGlvcycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnSW5kdXN0cnlTY2VuZVBhZ2UnLA0KICBjb21wb25lbnRzOiB7DQogICAgU2NlbmVDb25maWdOb2RlLA0KICAgIE5ldHdvcmtQbGFuQ29uZmlnLA0KICAgIEJ1c2luZXNzVmFsdWVDb25maWcsDQogICAgVnJTY2VuZUNvbmZpZywNCiAgICBUaGVtZVNlbGVjdGlvbkRpYWxvZw0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBtZW51RGF0YTogW10sIC8vIOWOn+Wni+iPnOWNleaVsOaNrg0KICAgICAgZmxhdE1lbnVEYXRhOiBbXSwgLy8g5omB5bmz5YyW55qE6KGM5Lia5pWw5o2u77yM55So5LqO5pCc57Si5ZKM5Lia5Yqh6YC76L6RDQogICAgICBhY3RpdmVNZW51OiAnJywNCiAgICAgIGluZHVzdHJ5Q29kZTogJycsDQogICAgICBzZWxlY3RlZFRoZW1lOiBudWxsLCAvLyDlvZPliY3pgInmi6nnmoTkuLvpopgNCiAgICAgIGZvcm06IHsNCiAgICAgICAgbWFpblRpdGxlOiAnJywNCiAgICAgICAgc3ViVGl0bGU6ICcnLA0KICAgICAgICBiZ0ltZ1VybDogJycsDQogICAgICAgIGJnRmlsZVVybDogJycsDQogICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxVcmw6ICcnDQogICAgICB9LA0KICAgICAgc2NlbmVDb25maWdUcmVlOiBbXSwNCiAgICAgIHNlbGVjdGVkTm9kZTogbnVsbCwNCiAgICAgIGxvYWRpbmc6IGZhbHNlLCAvLyDpobXpnaLliqDovb3nirbmgIENCiAgICAgIHN3aXRjaGluZ0luZHVzdHJ5OiBmYWxzZSwgLy8g5paw5aKe77ya5YiH5o2i6KGM5Lia55qEbG9hZGluZ+eKtuaAgQ0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgaW50cm9kdWNlVmlkZW9JbWdVcmw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35LiK5Lyg5LuL57uN6KeG6aKR6aaW5binJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICBpbnRyb2R1Y2VWaWRlb0ZpbGVVcmw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35LiK5Lyg5LuL57uN6KeG6aKRJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICB2aWRlb0V4cGxhbmF0aW9uRmlsZVVybDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fkuIrkvKDorrLop6Pop4bpopEnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICB1cGxvYWRpbmdUeXBlOiAnJywNCiAgICAgIHVwbG9hZGluZ0tleTogJycsDQogICAgICBjYXRlZ29yaWVzOiBbXSwgLy8g5pS55Li656m65pWw57uE77yM5LuO5ZCO56uv5Yqo5oCB6I635Y+WDQogICAgICBpbnRyb2R1Y2VWaWRlbzogew0KICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6ICcnLA0KICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycNCiAgICAgIH0sDQogICAgICB2aWRlb0V4cGxhbmF0aW9uOiB7DQogICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycsDQogICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHNjZW5lVHJlZU9wdGlvbnM6IFtdLA0KICAgICAgc2NlbmVDYXNjYWRlclByb3BzOiB7DQogICAgICAgIGxhYmVsOiAnc2NlbmVOYW1lJywNCiAgICAgICAgdmFsdWU6ICdpZCcsDQogICAgICAgIGNoaWxkcmVuOiAnY2hpbGRyZW4nLA0KICAgICAgICBlbWl0UGF0aDogZmFsc2UsDQogICAgICAgIGNoZWNrU3RyaWN0bHk6IHRydWUsDQogICAgICAgIGRpc2FibGVkOiAoZGF0YSkgPT4gew0KICAgICAgICAgIC8vIOWFgeiuuOaJgOacieiKgueCueWPr+mAie+8jOWPquimgeayoeacieiiq+WFtuS7luWIhuautemAieS4rQ0KICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSB0aGlzLnZpZGVvRXhwbGFuYXRpb24gJiYgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0DQogICAgICAgICAgICA/IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5zb21lKHNlZyA9PiBzZWcuc2NlbmVJZCA9PT0gZGF0YS5pZCkNCiAgICAgICAgICAgIDogZmFsc2UNCiAgICAgICAgICByZXR1cm4gaXNTZWxlY3RlZA0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgYmdGaWxlTGlzdDogW10sIC8vIOiDjOaZr+aWh+S7tuWIl+ihqA0KICAgICAgdmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0OiBbXSwgLy8g6K6y6Kej6KeG6aKR5paH5Lu25YiX6KGoDQogICAgICB4bWxGaWxlTGlzdDogW10sIC8vIFhNTOaWh+S7tuWIl+ihqA0KICAgICAgbmV0d29ya1BsYW5EYXRhTWFwOiB7fSwgLy8g5pS55Li65a+56LGh77yM5oyJ6I+c5Y2VSUTlrZjlgqgNCiAgICAgIGJ1c2luZXNzVmFsdWVEYXRhTWFwOiB7fSwgLy8g5ZWG5Lia5Lu35YC85pWw5o2u5pig5bCEDQogICAgICB2clNjZW5lRGF0YU1hcDoge30sIC8vIFZS55yL546w5Zy65pWw5o2u5pig5bCEDQogICAgICAvLyDlm77niYfpooTop4gNCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHByZXZpZXdJbWFnZVVybDogJycsDQogICAgICBzZWFyY2hLZXl3b3JkOiAnJywNCiAgICAgIHRyZWVFeHBhbmRlZEtleXM6IFtdLCAvLyDmlrDlop7vvJrkv53lrZjmoJHnmoTlsZXlvIDnirbmgIENCiAgICAgIHVwbG9hZE1vZGVzOiB7DQogICAgICAgIGJnRmlsZTogJ3VwbG9hZCcsDQogICAgICAgIHZpZGVvRXhwbGFuYXRpb246ICd1cGxvYWQnLA0KICAgICAgICBpbnRyb2R1Y2VWaWRlbzogJ3VwbG9hZCcNCiAgICAgIH0sDQogICAgICBzeW5jaHJvbml6aW5nOiBmYWxzZSwNCiAgICAgIHN1Ym1pdHRpbmc6IGZhbHNlIC8vIOa3u+WKoOi/meS4quWxnuaApw0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICB2aWRlb1NlZ21lbnRlZExpc3QoKSB7DQogICAgICAvLyDlpoLmnpzmsqHmnInmlbDmja7vvIzpu5jorqTov5Tlm57kuIDooYznqbrmlbDmja4NCiAgICAgIGlmICghdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0IHx8IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIFt7IHRpbWU6ICcnLCBzY2VuZUlkOiAnJywgc2NlbmVOYW1lOiAnJywgc2NlbmVDb2RlOiAnJyB9XQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdA0KICAgIH0sDQogICAgbmV0d29ya1BsYW5EYXRhOiB7DQogICAgICBnZXQoKSB7DQogICAgICAgIGNvbnN0IG1lbnVEYXRhID0gdGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XQ0KICAgICAgICBpZiAoIW1lbnVEYXRhKSB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIG5ldHdvcmtWaWRlb0xpc3Q6IFtdLA0KICAgICAgICAgICAgdmlkZW9FeHBsYW5hdGlvblZvOiB7DQogICAgICAgICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycsDQogICAgICAgICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiBbXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gbWVudURhdGENCiAgICAgIH0sDQogICAgICBzZXQodmFsdWUpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubmV0d29ya1BsYW5EYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIHZhbHVlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYnVzaW5lc3NWYWx1ZURhdGE6IHsNCiAgICAgIGdldCgpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuYnVzaW5lc3NWYWx1ZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XSB8fCBbXQ0KICAgICAgfSwNCiAgICAgIHNldCh2YWx1ZSkgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5idXNpbmVzc1ZhbHVlRGF0YU1hcCwgdGhpcy5hY3RpdmVNZW51LCB2YWx1ZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIHZyU2NlbmVEYXRhOiB7DQogICAgICBnZXQoKSB7DQogICAgICAgIHJldHVybiB0aGlzLnZyU2NlbmVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0gfHwgW10NCiAgICAgIH0sDQogICAgICBzZXQodmFsKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLnZyU2NlbmVEYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIHZhbCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGZpbHRlcmVkTWVudURhdGEoKSB7DQogICAgICBpZiAoIXRoaXMuc2VhcmNoS2V5d29yZCkgew0KICAgICAgICByZXR1cm4gdGhpcy5tZW51RGF0YQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDpgJLlvZLov4fmu6TmoJHlvaLmlbDmja4NCiAgICAgIGNvbnN0IGZpbHRlclRyZWUgPSAobm9kZXMpID0+IHsNCiAgICAgICAgcmV0dXJuIG5vZGVzLm1hcChub2RlID0+IHsNCiAgICAgICAgICBjb25zdCBmaWx0ZXJlZENoaWxkcmVuID0gbm9kZS5jaGlsZHJlbiA/IGZpbHRlclRyZWUobm9kZS5jaGlsZHJlbikgOiBbXQ0KICAgICAgICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBub2RlLm5hbWUgJiYgbm9kZS5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXModGhpcy5zZWFyY2hLZXl3b3JkLnRvTG93ZXJDYXNlKCkpDQogICAgICAgICAgDQogICAgICAgICAgaWYgKG1hdGNoZXNTZWFyY2ggfHwgZmlsdGVyZWRDaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAuLi5ub2RlLA0KICAgICAgICAgICAgICBjaGlsZHJlbjogZmlsdGVyZWRDaGlsZHJlbg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gbnVsbA0KICAgICAgICB9KS5maWx0ZXIoQm9vbGVhbikNCiAgICAgIH0NCiAgICAgIA0KICAgICAgcmV0dXJuIGZpbHRlclRyZWUodGhpcy5tZW51RGF0YSkNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8g5LuOVVJM6I635Y+WdG9rZW7lubborr7nva4NCiAgICB0aGlzLmluaXRUb2tlbkZyb21VcmwoKQ0KICAgIHRoaXMubG9hZEluZHVzdHJ5TWVudSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDku45VUkzojrflj5Z0b2tlbuW5tuiuvue9rg0KICAgIGluaXRUb2tlbkZyb21VcmwoKSB7DQogICAgICBjb25zdCB1cmxQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHdpbmRvdy5sb2NhdGlvbi5zZWFyY2gpDQogIGNvbnN0IHRva2VuID0gdXJsUGFyYW1zLmdldCgndG9rZW4nKQ0KICANCiAgaWYgKHRva2VuKSB7DQogICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2V4dGVybmFsLXRva2VuJywgdG9rZW4pDQogICAgYXhpb3MuZGVmYXVsdHMuaGVhZGVycy5jb21tb25bJ0F1dGhvcml6YXRpb24nXSA9IGBCZWFyZXIgJHt0b2tlbn1gDQogICAgY29uc29sZS5sb2coJ+S7jlVSTOiOt+WPluWIsHRva2VuOicsIHRva2VuKQ0KICB9IGVsc2Ugew0KICAgIGNvbnN0IHN0b3JlZFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2V4dGVybmFsLXRva2VuJykNCiAgICBpZiAoc3RvcmVkVG9rZW4pIHsNCiAgICAgIGF4aW9zLmRlZmF1bHRzLmhlYWRlcnMuY29tbW9uWydBdXRob3JpemF0aW9uJ10gPSBgQmVhcmVyICR7c3RvcmVkVG9rZW59YA0KICAgIH0NCiAgfQ0KICAgIH0sDQogICAgYXN5bmMgbG9hZEluZHVzdHJ5TWVudSgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldEluZHVzdHJ5TGlzdCgpDQogICAgICBpZiAocmVzLmNvZGUgPT09IDAgJiYgQXJyYXkuaXNBcnJheShyZXMuZGF0YSkpIHsNCiAgICAgICAgLy8g6L2s5o2i5pWw5o2u57uT5p6E5Li65qCR5b2i6I+c5Y2VDQogICAgICAgIHRoaXMubWVudURhdGEgPSByZXMuZGF0YS5tYXAocGxhdGUgPT4gKHsNCiAgICAgICAgICBpZDogYHBsYXRlXyR7cGxhdGUucGxhdGVLZXl9YCwNCiAgICAgICAgICBuYW1lOiBwbGF0ZS5wbGF0ZU5hbWUsDQogICAgICAgICAgdHlwZTogJ3BsYXRlJywNCiAgICAgICAgICBjaGlsZHJlbjogcGxhdGUuaW5kdXN0cnlUcmVlTGlzdFZvcyA/IHBsYXRlLmluZHVzdHJ5VHJlZUxpc3RWb3MubWFwKGluZHVzdHJ5ID0+ICh7DQogICAgICAgICAgICBpZDogaW5kdXN0cnkuaWQsDQogICAgICAgICAgICBuYW1lOiBpbmR1c3RyeS5pbmR1c3RyeU5hbWUsDQogICAgICAgICAgICBpbmR1c3RyeUNvZGU6IGluZHVzdHJ5LmluZHVzdHJ5Q29kZSwNCiAgICAgICAgICAgIHBsYXRlOiBpbmR1c3RyeS5wbGF0ZSwNCiAgICAgICAgICAgIHR5cGU6ICdpbmR1c3RyeScNCiAgICAgICAgICB9KSkgOiBbXQ0KICAgICAgICB9KSkNCiAgICAgICAgDQogICAgICAgIC8vIOWIm+W7uuaJgeW5s+WMlueahOihjOS4muaVsOaNru+8jOeUqOS6juS4muWKoemAu+i+kQ0KICAgICAgICB0aGlzLmZsYXRNZW51RGF0YSA9IFtdDQogICAgICAgIHJlcy5kYXRhLmZvckVhY2gocGxhdGUgPT4gew0KICAgICAgICAgIGlmIChwbGF0ZS5pbmR1c3RyeVRyZWVMaXN0Vm9zKSB7DQogICAgICAgICAgICB0aGlzLmZsYXRNZW51RGF0YS5wdXNoKC4uLnBsYXRlLmluZHVzdHJ5VHJlZUxpc3RWb3MpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICANCiAgICAgICAgLy8g6buY6K6k6YCJ5Lit56ys5LiA5Liq6KGM5LiaDQogICAgICAgIGlmICh0aGlzLmZsYXRNZW51RGF0YS5sZW5ndGgpIHsNCiAgICAgICAgICB0aGlzLmFjdGl2ZU1lbnUgPSBTdHJpbmcodGhpcy5mbGF0TWVudURhdGFbMF0uaWQpDQogICAgICAgICAgdGhpcy5pbmR1c3RyeUNvZGUgPSB0aGlzLmZsYXRNZW51RGF0YVswXS5pbmR1c3RyeUNvZGUNCiAgICAgICAgICAvLyDnrYnlvoVET03mm7TmlrDlkI7orr7nva7moJHnu4Tku7bnmoTlvZPliY3oioLngrkNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAvLyDnoa7kv53moJHnu4Tku7blt7LmuLLmn5Plubborr7nva7lvZPliY3pgInkuK3oioLngrkNCiAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLm1lbnVUcmVlICYmIHRoaXMuJHJlZnMubWVudVRyZWUuc2V0Q3VycmVudEtleSkgew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLm1lbnVUcmVlLnNldEN1cnJlbnRLZXkodGhpcy5hY3RpdmVNZW51KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgDQogICAgICAgICAgLy8g5Yqg6L2956ys5LiA5Liq6KGM5Lia55qE5pWw5o2uDQogICAgICAgICAgYXdhaXQgdGhpcy5oYW5kbGVTZWxlY3QodGhpcy5hY3RpdmVNZW51KQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICBoYW5kbGVUcmVlTm9kZUNsaWNrKGRhdGEpIHsNCiAgICAgIC8vIOWPquacieeCueWHu+ihjOS4muiKgueCueaJjeWkhOeQhg0KICAgICAgaWYgKGRhdGEudHlwZSA9PT0gJ2luZHVzdHJ5Jykgew0KICAgICAgICB0aGlzLmhhbmRsZVNlbGVjdChTdHJpbmcoZGF0YS5pZCkpDQogICAgICAgIHRoaXMuaW5kdXN0cnlDb2RlID0gZGF0YS5pbmR1c3RyeUNvZGU7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZVNlYXJjaCh2YWx1ZSkgew0KICAgICAgdGhpcy5zZWFyY2hLZXl3b3JkID0gdmFsdWUNCiAgICB9LA0KICAgIGhpZ2hsaWdodFRleHQodGV4dCkgew0KICAgICAgaWYgKCF0aGlzLnNlYXJjaEtleXdvcmQpIHJldHVybiB0ZXh0DQogICAgICBjb25zdCByZWdleCA9IG5ldyBSZWdFeHAoYCgke3RoaXMuc2VhcmNoS2V5d29yZH0pYCwgJ2dpJykNCiAgICAgIHJldHVybiB0ZXh0LnJlcGxhY2UocmVnZXgsICc8c3BhbiBjbGFzcz0iaGlnaGxpZ2h0Ij4kMTwvc3Bhbj4nKQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlU2VsZWN0KGlkLCBrZWVwU2VsZWN0ZWROb2RlID0gZmFsc2UpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOW8gOWQr+WIh+aNouihjOS4mueahGxvYWRpbmcNCiAgICAgICAgdGhpcy5zd2l0Y2hpbmdJbmR1c3RyeSA9IHRydWUNCiAgICAgICAgLy8g56aB55So6aG16Z2i5rua5YqoDQogICAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnaGlkZGVuJw0KICAgICAgICANCiAgICAgICAgdGhpcy5hY3RpdmVNZW51ID0gaWQNCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkU2NlbmVUcmVlT3B0aW9ucyh0aGlzLmFjdGl2ZU1lbnUpDQogICAgICAgIA0KICAgICAgICAvLyDkv53lrZjlvZPliY3pgInkuK3nmoToioLngrkNCiAgICAgICAgY29uc3QgY3VycmVudFNlbGVjdGVkTm9kZSA9IGtlZXBTZWxlY3RlZE5vZGUgPyB0aGlzLnNlbGVjdGVkTm9kZSA6IG51bGwNCiAgICAgICAgDQogICAgICAgIC8vIOmHjee9ruS4u+mimOmAieaLqQ0KICAgICAgICB0aGlzLnNlbGVjdGVkVGhlbWUgPSBudWxsDQogICAgICAgIA0KICAgICAgICAvLyDku47miYHlubPljJboj5zljZXmlbDmja7kuK3ojrflj5blvZPliY3ooYzkuJrnmoQgaW5kdXN0cnlDb2RlDQogICAgICAgIGNvbnN0IGN1cnJlbnRJbmR1c3RyeSA9IHRoaXMuZmxhdE1lbnVEYXRhLmZpbmQoaXRlbSA9PiBTdHJpbmcoaXRlbS5pZCkgPT09IGlkKQ0KICAgICAgICBjb25zdCBpbmR1c3RyeUNvZGUgPSBjdXJyZW50SW5kdXN0cnkgPyBjdXJyZW50SW5kdXN0cnkuaW5kdXN0cnlDb2RlIDogbnVsbA0KICAgICAgICANCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0U2NlbmVWaWV3Q29uZmlnKHsgaW5kdXN0cnlDb2RlOiBpbmR1c3RyeUNvZGUgfSkNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgDQogICAgICAgICAgLy8g5ZCM5q2l5Li75qCH6aKY44CB5Ymv5qCH6aKY44CB6IOM5pmv5Zu+54mH44CBWE1M5paH5Lu2562JDQogICAgICAgICAgdGhpcy5mb3JtLnNjZW5lVmlld0NvbmZpZ0lkID0gcmVzLmRhdGEuc2NlbmVWaWV3Q29uZmlnSWQgfHwgJycNCiAgICAgICAgICB0aGlzLmZvcm0ubWFpblRpdGxlID0gcmVzLmRhdGEubWFpblRpdGxlIHx8ICcnDQogICAgICAgICAgdGhpcy5mb3JtLnN1YlRpdGxlID0gcmVzLmRhdGEuc3ViVGl0bGUgfHwgJycNCiAgICAgICAgICB0aGlzLmZvcm0uYmdJbWdVcmwgPSByZXMuZGF0YS5iYWNrZ3JvdW5kSW1nRmlsZVVybCB8fCAnJw0KICAgICAgICAgIHRoaXMuZm9ybS5iZ0ZpbGVVcmwgPSByZXMuZGF0YS5iYWNrZ3JvdW5kRmlsZVVybCB8fCAnJw0KICAgICAgICAgIHRoaXMuZm9ybS5wYW5vcmFtaWNWaWV3WG1sVXJsID0gcmVzLmRhdGEucGFub3JhbWljVmlld1htbFVybCB8fCAnJw0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOabtOaWsOiDjOaZr+aWh+S7tuWIl+ihqA0KICAgICAgICAgIHRoaXMudXBkYXRlQmdGaWxlTGlzdCgpDQogICAgICAgICAgDQogICAgICAgICAgLy8g5pu05pawWE1M5paH5Lu25YiX6KGoDQogICAgICAgICAgdGhpcy51cGRhdGVYbWxGaWxlTGlzdCgpDQogICAgICAgICAgDQogICAgICAgICAgLy8g5Zue5pi+5Li76aKY6YCJ5oupDQogICAgICAgICAgaWYgKHJlcy5kYXRhLnRoZW1lSW5mb1ZvKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkVGhlbWUgPSB7DQogICAgICAgICAgICAgIHRoZW1lSWQ6IHJlcy5kYXRhLnRoZW1lSW5mb1ZvLnRoZW1lSWQsDQogICAgICAgICAgICAgIHRoZW1lTmFtZTogcmVzLmRhdGEudGhlbWVJbmZvVm8udGhlbWVOYW1lLA0KICAgICAgICAgICAgICB0aGVtZUVmZmVjdEltZzogcmVzLmRhdGEudGhlbWVJbmZvVm8udGhlbWVFZmZlY3RJbWcsDQogICAgICAgICAgICAgIHJlbWFyazogcmVzLmRhdGEudGhlbWVJbmZvVm8ucmVtYXJrDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRUaGVtZSA9IG51bGwNCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgLy8g5aSE55CGIHNjZW5lRGVmYXVsdENvbmZpZ1ZvTGlzdO+8jOWKqOaAgeeUn+aIkCBjYXRlZ29yaWVzDQogICAgICAgICAgaWYgKHJlcy5kYXRhLnNjZW5lRGVmYXVsdENvbmZpZ1ZvTGlzdCAmJiBBcnJheS5pc0FycmF5KHJlcy5kYXRhLnNjZW5lRGVmYXVsdENvbmZpZ1ZvTGlzdCkpIHsNCiAgICAgICAgICAgIHRoaXMuY2F0ZWdvcmllcyA9IHJlcy5kYXRhLnNjZW5lRGVmYXVsdENvbmZpZ1ZvTGlzdC5tYXAoY29uZmlnSXRlbSA9PiAoew0KICAgICAgICAgICAgICBpZDogY29uZmlnSXRlbS5pZCwNCiAgICAgICAgICAgICAga2V5OiBjb25maWdJdGVtLmtleU5hbWUsDQogICAgICAgICAgICAgIG5hbWU6IGNvbmZpZ0l0ZW0ubmFtZSwNCiAgICAgICAgICAgICAgZW5hYmxlZDogY29uZmlnSXRlbS5rZXlWYWx1ZSA9PT0gJzAnLCAvLyBrZXlWYWx1ZeS4uicwJ+ihqOekuuWQr+eUqA0KICAgICAgICAgICAgICBlZGl0aW5nOiBmYWxzZSwNCiAgICAgICAgICAgICAgZWRpdGluZ05hbWU6ICcnLA0KICAgICAgICAgICAgICBvcmlnaW5hbE5hbWU6IGNvbmZpZ0l0ZW0ubmFtZSwNCiAgICAgICAgICAgICAgcmVtYXJrOiBjb25maWdJdGVtLnJlbWFyaywNCiAgICAgICAgICAgICAgY2xhc3NpZmljYXRpb246IGNvbmZpZ0l0ZW0uY2xhc3NpZmljYXRpb24sDQogICAgICAgICAgICAgIGRlZmF1bHRTdGF0dXM6IGNvbmZpZ0l0ZW0uZGVmYXVsdFN0YXR1cw0KICAgICAgICAgICAgfSkpDQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOafpeaJvuWcuuaZr+mFjee9ruWIhuexuw0KICAgICAgICAgICAgY29uc3Qgc2NlbmVDYXRlZ29yeSA9IHJlcy5kYXRhLnNjZW5lRGVmYXVsdENvbmZpZ1ZvTGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5rZXlOYW1lID09PSAnZGVmYXVsdF9zY2VuZScpDQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOWkhOeQhuinhumikeiusuino+aVsOaNrg0KICAgICAgICAgICAgaWYgKHNjZW5lQ2F0ZWdvcnkgJiYgc2NlbmVDYXRlZ29yeS5pbmR1c3RyeVNjZW5lSW5mb1ZvICYmIHNjZW5lQ2F0ZWdvcnkuaW5kdXN0cnlTY2VuZUluZm9Wby52aWRlb0V4cGxhbmF0aW9uVm8pIHsNCiAgICAgICAgICAgICAgY29uc3QgdmlkZW9EYXRhID0gc2NlbmVDYXRlZ29yeS5pbmR1c3RyeVNjZW5lSW5mb1ZvLnZpZGVvRXhwbGFuYXRpb25Wbw0KICAgICAgICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24gPSB7DQogICAgICAgICAgICAgICAgc3RhdHVzOiB2aWRlb0RhdGEuc3RhdHVzIHx8ICcwJywNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogdmlkZW9EYXRhLmJhY2tncm91bmRGaWxlVXJsIHx8ICcnLA0KICAgICAgICAgICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiB2aWRlb0RhdGEudmlkZW9TZWdtZW50ZWRWb0xpc3QgPyB2aWRlb0RhdGEudmlkZW9TZWdtZW50ZWRWb0xpc3QubWFwKHNlZyA9PiAoew0KICAgICAgICAgICAgICAgICAgdGltZTogc2VnLnRpbWUsDQogICAgICAgICAgICAgICAgICBzY2VuZUNvZGU6IHNlZy5zY2VuZUNvZGUsDQogICAgICAgICAgICAgICAgICBzY2VuZU5hbWU6IHNlZy5zY2VuZU5hbWUsDQogICAgICAgICAgICAgICAgICBzY2VuZUlkOiB0aGlzLmZpbmRTY2VuZUlkQnlDb2RlKHNlZy5zY2VuZUNvZGUpIC8vIOagueaNrnNjZW5lQ29kZeafpeaJvnNjZW5lSWQNCiAgICAgICAgICAgICAgICB9KSkgOiBbXQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24gPSB7DQogICAgICAgICAgICAgICAgc3RhdHVzOiAnMCcsDQogICAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6ICcnLA0KICAgICAgICAgICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiBbXQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOabtOaWsOinhumikeiusuino+aWh+S7tuWIl+ihqA0KICAgICAgICAgICAgdGhpcy51cGRhdGVWaWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QoKQ0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDlpITnkIblnLrmma/phY3nva7moJENCiAgICAgICAgICAgIGlmIChzY2VuZUNhdGVnb3J5ICYmIHNjZW5lQ2F0ZWdvcnkuaW5kdXN0cnlTY2VuZUluZm9WbyAmJiBzY2VuZUNhdGVnb3J5LmluZHVzdHJ5U2NlbmVJbmZvVm8uc2NlbmVMaXN0Vm8pIHsNCiAgICAgICAgICAgICAgdGhpcy5zY2VuZUNvbmZpZ1RyZWUgPSB0aGlzLmFkYXB0U2NlbmVUcmVlKHNjZW5lQ2F0ZWdvcnkuaW5kdXN0cnlTY2VuZUluZm9Wby5zY2VuZUxpc3RWbykNCiAgICAgICAgICAgICAgDQogICAgICAgICAgICAgIC8vIOWmguaenOmcgOimgeS/neaMgemAieS4reiKgueCuQ0KICAgICAgICAgICAgICBpZiAoa2VlcFNlbGVjdGVkTm9kZSAmJiBjdXJyZW50U2VsZWN0ZWROb2RlKSB7DQogICAgICAgICAgICAgICAgY29uc3Qgbm9kZVRvU2VsZWN0ID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIGN1cnJlbnRTZWxlY3RlZE5vZGUuaWQpDQogICAgICAgICAgICAgICAgaWYgKG5vZGVUb1NlbGVjdCkgew0KICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSBub2RlVG9TZWxlY3QNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSB0aGlzLnNjZW5lQ29uZmlnVHJlZS5sZW5ndGggPiAwID8gdGhpcy5zY2VuZUNvbmZpZ1RyZWVbMF0gOiBudWxsDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIC8vIOm7mOiupOmAieaLqeesrOS4gOS4quiKgueCuQ0KICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWROb2RlID0gdGhpcy5zY2VuZUNvbmZpZ1RyZWUubGVuZ3RoID4gMCA/IHRoaXMuc2NlbmVDb25maWdUcmVlWzBdIDogbnVsbA0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAvLyDmsqHmnInlnLrmma/mlbDmja7ml7bmuIXnqboNCiAgICAgICAgICAgICAgdGhpcy5zY2VuZUNvbmZpZ1RyZWUgPSBbXQ0KICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkTm9kZSA9IG51bGwNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgLy8g5aSE55CG572R57uc5pa55qGI5pWw5o2uDQogICAgICAgICAgaWYgKHJlcy5kYXRhLm5ldHdvcmtTb2x1dGlvblZvKSB7DQogICAgICAgICAgICBjb25zdCBuZXR3b3JrRGF0YSA9IHsNCiAgICAgICAgICAgICAgbmV0d29ya1ZpZGVvTGlzdDogcmVzLmRhdGEubmV0d29ya1NvbHV0aW9uVm8ubmV0d29ya1ZpZGVvTGlzdCB8fCBbXSwNCiAgICAgICAgICAgICAgdmlkZW9FeHBsYW5hdGlvblZvOiByZXMuZGF0YS5uZXR3b3JrU29sdXRpb25Wby52aWRlb0V4cGxhbmF0aW9uVm8gfHwgew0KICAgICAgICAgICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiAnJywNCiAgICAgICAgICAgICAgICB2aWRlb1NlZ21lbnRlZFZvTGlzdDogW10NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMubmV0d29ya1BsYW5EYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIG5ldHdvcmtEYXRhKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWkhOeQhuWVhuS4muS7t+WAvOaVsOaNrg0KICAgICAgICAgIGlmIChyZXMuZGF0YS5jb21tZXJjaWFsVmFsdWVMaXN0Vm8pIHsNCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmJ1c2luZXNzVmFsdWVEYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIHJlcy5kYXRhLmNvbW1lcmNpYWxWYWx1ZUxpc3RWbykNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpITnkIZWUueci+eOsOWcuuaVsOaNrg0KICAgICAgICAgIGlmIChyZXMuZGF0YS52ckluZm9MaXN0Vm8pIHsNCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnZyU2NlbmVEYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIHJlcy5kYXRhLnZySW5mb0xpc3RWbykNCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgLy8g5YW25LuW5pWw5o2u5aSE55CG6YC76L6R5L+d5oyB5LiN5Y+YLi4uDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L295pWw5o2u5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIC8vIOWFs+mXreWIh+aNouihjOS4mueahGxvYWRpbmcNCiAgICAgICAgdGhpcy5zd2l0Y2hpbmdJbmR1c3RyeSA9IGZhbHNlDQogICAgICAgIC8vIOaBouWkjemhtemdoua7muWKqA0KICAgICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJycNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUJlZm9yZVVwbG9hZChmaWxlKSB7DQogICAgICByZXR1cm4gZmFsc2UgLy8g5oum5oiq6buY6K6k5LiK5Lyg6KGM5Li6DQogICAgfSwNCiAgICBhZGRTZWdtZW50KCkgew0KICAgICAgdGhpcy5mb3JtLnZpZGVvU2VnbWVudGVkVm9MaXN0LnB1c2goeyB0aW1lOiAnJywgc2NlbmU6ICcnIH0pDQogICAgfSwNCiAgICByZW1vdmVTZWdtZW50KGluZGV4KSB7DQogICAgICBpZiAodGhpcy5mb3JtLnZpZGVvU2VnbWVudGVkVm9MaXN0Lmxlbmd0aCA+PSAxKSB7DQogICAgICAgIHRoaXMuZm9ybS52aWRlb1NlZ21lbnRlZFZvTGlzdC5zcGxpY2UoaW5kZXgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBiZWZvcmVVcGxvYWRJbnRyb2R1Y2VJbWcoZmlsZSwgdHlwZSwga2V5KSB7DQogICAgICBpZiAoIWZpbGUudHlwZS5zdGFydHNXaXRoKCdpbWFnZS8nKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKDlm77niYfmlofku7bvvIEnKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIA0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5LiK5Lyg5Zu+54mH77yM6K+356iN5YCZLi4uIikNCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2luZHVzdHJ5Q29kZScsIHRoaXMuaW5kdXN0cnlDb2RlKQ0KICAgICAgICANCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgdXBsb2FkU2NlbmVGaWxlKGZvcm1EYXRhKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDAgJiYgcmVzLmRhdGEpIHsNCiAgICAgICAgICBpZiAodHlwZSAmJiBrZXkpIHsNCiAgICAgICAgICAgIC8vIOmSiOWvueS7i+e7jeinhumikeWSjOinhumikeiusuino+eahOS4iuS8oO+8jOWNleeLrOS4iuS8oOWbvueJh+aXtuS9v+eUqCBmaWxlVXJsDQogICAgICAgICAgICB0aGlzW3R5cGVdW2tleV0gPSByZXMuZGF0YS5maWxlVXJsDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOmSiOWvueS4u+iDjOaZr+WbvueJh+eahOS4iuS8oA0KICAgICAgICAgICAgdGhpcy5mb3JtLmJnSW1nVXJsID0gcmVzLmRhdGEuZmlsZVVybA0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfkuIrkvKDlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDlpLHotKUnKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCkNCiAgICAgIH0NCiAgICAgIHJldHVybiBmYWxzZQ0KICAgIH0sDQogICAgYXN5bmMgYmVmb3JlVXBsb2FkSW50cm9kdWNlVmlkZW8oZmlsZSwgdHlwZSwga2V5KSB7DQogICAgICAvLyDlpoLmnpzmmK/kuLvog4zmma/mlofku7bkuIrkvKDvvIjmsqHmnIl0eXBl5ZKMa2V55Y+C5pWw77yJDQogICAgICBpZiAoIXR5cGUgJiYgIWtleSkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4iuS8oOaWh+S7tu+8jOivt+eojeWAmS4uLiIpDQogICAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZScsIGZpbGUpDQogICAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdpbmR1c3RyeUNvZGUnLCB0aGlzLmluZHVzdHJ5Q29kZSkNCiAgICAgICAgICANCiAgICAgICAgICBjb25zdCByZXMgPSBhd2FpdCB1cGxvYWRTY2VuZUZpbGUoZm9ybURhdGEpDQogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgICAvLyDorr7nva7og4zmma/mlofku7ZVUkwNCiAgICAgICAgICAgIHRoaXMuZm9ybS5iZ0ZpbGVVcmwgPSByZXMuZGF0YS5maWxlVXJsDQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOebtOaOpeimhuebluiDjOaZr+aWh+S7tuWIl+ihqA0KICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSByZXMuZGF0YS5maWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgICAgIHRoaXMuYmdGaWxlTGlzdCA9IFt7DQogICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICB1cmw6IHJlcy5kYXRhLmZpbGVVcmwsDQogICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICAgICAgfV0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5aaC5p6c5pivTVA05paH5Lu25LiU6L+U5Zue5LqGaW1nVXJs77yM6Ieq5Yqo6K6+572u6IOM5pmv5Zu+54mH6aaW5binDQogICAgICAgICAgICBpZiAoZmlsZS50eXBlID09PSAndmlkZW8vbXA0JyAmJiByZXMuZGF0YS5pbWdVcmwpIHsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLmJnSW1nVXJsID0gcmVzLmRhdGEuaW1nVXJsDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiK5Lyg5oiQ5Yqf77yM5bey6Ieq5Yqo55Sf5oiQ6IOM5pmv5Zu+54mH6aaW5binJykNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiK5Lyg5oiQ5YqfJykNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfkuIrkvKDlpLHotKUnKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDlpLHotKUnKQ0KICAgICAgICB9IGZpbmFsbHkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWFtuS7luinhumikeS4iuS8oOmAu+i+ke+8iOS7i+e7jeinhumikeOAgeiusuino+inhumikeetie+8iQ0KICAgICAgaWYgKCFmaWxlLnR5cGUuc3RhcnRzV2l0aCgndmlkZW8vJykgJiYgIWZpbGUubmFtZS5lbmRzV2l0aCgnLm1wNCcpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oE1QNOinhumikeaWh+S7tu+8gScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgDQogICAgICB0cnkgew0KICAgICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjkuIrkvKDop4bpopHvvIzor7fnqI3lgJkuLi4iKQ0KICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZScsIGZpbGUpDQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnaW5kdXN0cnlDb2RlJywgdGhpcy5pbmR1c3RyeUNvZGUpDQogICAgICAgIA0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCB1cGxvYWRTY2VuZUZpbGUoZm9ybURhdGEpDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCAmJiByZXMuZGF0YSkgew0KICAgICAgICAgIGlmICh0eXBlICYmIGtleSkgew0KICAgICAgICAgICAgLy8g6ZKI5a+55LuL57uN6KeG6aKR5ZKM6KeG6aKR6K6y6Kej55qE5LiK5LygDQogICAgICAgICAgICB0aGlzW3R5cGVdW2tleV0gPSByZXMuZGF0YS5maWxlVXJsDQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOebtOaOpeimhuebluWvueW6lOeahOaWh+S7tuWIl+ihqA0KICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSByZXMuZGF0YS5maWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgICAgIGlmICh0eXBlID09PSAnaW50cm9kdWNlVmlkZW8nICYmIGtleSA9PT0gJ2JhY2tncm91bmRGaWxlVXJsJykgew0KICAgICAgICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbew0KICAgICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICAgIHVybDogcmVzLmRhdGEuZmlsZVVybCwNCiAgICAgICAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgICAgICAgfV0NCiAgICAgICAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gJ3ZpZGVvRXhwbGFuYXRpb24nICYmIGtleSA9PT0gJ2JhY2tncm91bmRGaWxlVXJsJykgew0KICAgICAgICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCA9IFt7DQogICAgICAgICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgICAgICAgdXJsOiByZXMuZGF0YS5maWxlVXJsLA0KICAgICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICAgICAgICB9XQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDlpoLmnpzmmK/ku4vnu43op4bpopHkuIrkvKDvvIzkuJTov5Tlm57kuoZpbWdVcmzvvIzoh6rliqjorr7nva7ku4vnu43op4bpopHpppbluKcNCiAgICAgICAgICAgIGlmICh0eXBlID09PSAnaW50cm9kdWNlVmlkZW8nICYmIGtleSA9PT0gJ2JhY2tncm91bmRGaWxlVXJsJyAmJiByZXMuZGF0YS5pbWdVcmwpIHsNCiAgICAgICAgICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlby5iYWNrZ3JvdW5kSW1nRmlsZVVybCA9IHJlcy5kYXRhLmltZ1VybA0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKn++8jOW3suiHquWKqOeUn+aIkOS7i+e7jeinhumikemmluW4pycpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5LiK5Lyg5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIGJlZm9yZVVwbG9hZEV4cGxhbmF0aW9uVmlkZW8oZmlsZSkgew0KICAgICAgdGhpcy51cGxvYWRpbmdUeXBlID0gJ21wNCcNCiAgICAgIHRoaXMudXBsb2FkaW5nS2V5ID0gJ3ZpZGVvRXhwbGFuYXRpb25GaWxlVXJsJw0KICAgICAgcmV0dXJuIHRoaXMuaGFuZGxlQmVmb3JlVXBsb2FkKGZpbGUpDQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrmt7vliqDlnLrmma/phY3nva7oioLngrkNCiAgICBhZGRTY2VuZUNvbmZpZ05vZGUocGFyZW50SWQgPSBudWxsKSB7DQogICAgICBjb25zdCBuZXdOb2RlID0gew0KICAgICAgICBpZDogRGF0ZS5ub3coKSwgLy8g55Sf5oiQ5ZSv5LiASUQNCiAgICAgICAgbmFtZTogJ+aWsOWcuuaZrycsDQogICAgICAgIHR5cGU6ICdzY2VuZScsIC8vIOexu+Wei+S4uuWcuuaZrw0KICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICBjaGlsZHJlbjogW10sDQogICAgICAgIHBhcmVudElkOiBwYXJlbnRJZA0KICAgICAgfQ0KICAgICAgaWYgKHBhcmVudElkKSB7DQogICAgICAgIGNvbnN0IHBhcmVudE5vZGUgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgcGFyZW50SWQpDQogICAgICAgIGlmIChwYXJlbnROb2RlKSB7DQogICAgICAgICAgcGFyZW50Tm9kZS5jaGlsZHJlbi5wdXNoKG5ld05vZGUpDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2NlbmVDb25maWdUcmVlLnB1c2gobmV3Tm9kZSkNCiAgICAgIH0NCiAgICAgIHJldHVybiBuZXdOb2RlLmlkDQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrliKDpmaTlnLrmma/phY3nva7oioLngrkNCiAgICByZW1vdmVTY2VuZUNvbmZpZ05vZGUobm9kZUlkKSB7DQogICAgICB0aGlzLnNjZW5lQ29uZmlnVHJlZSA9IHRoaXMuc2NlbmVDb25maWdUcmVlLmZpbHRlcihub2RlID0+IG5vZGUuaWQgIT09IG5vZGVJZCkNCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8muafpeaJvuiKgueCuQ0KICAgIGZpbmROb2RlQnlJZChub2RlcywgaWQpIHsNCiAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiBub2Rlcykgew0KICAgICAgICBpZiAobm9kZS5pZCA9PT0gaWQpIHsNCiAgICAgICAgICByZXR1cm4gbm9kZQ0KICAgICAgICB9DQogICAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnN0IGZvdW5kID0gdGhpcy5maW5kTm9kZUJ5SWQobm9kZS5jaGlsZHJlbiwgaWQpDQogICAgICAgICAgaWYgKGZvdW5kKSB7DQogICAgICAgICAgICByZXR1cm4gZm91bmQNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBudWxsDQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrmt7vliqDlnLrmma/nmoTnl5vngrnku7flgLwNCiAgICBhZGRTY2VuZVBhaW5Qb2ludChub2RlSWQpIHsNCiAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgbm9kZUlkKQ0KICAgICAgaWYgKG5vZGUgJiYgbm9kZS50eXBlID09PSAnc2NlbmUnKSB7DQogICAgICAgIG5vZGUucGFpblBvaW50cyA9IG5vZGUucGFpblBvaW50cyB8fCBbXQ0KICAgICAgICBub2RlLnBhaW5Qb2ludHMucHVzaCh7IHRpdGxlOiAnJywgY29udGVudHM6IFsnJ10sIHNob3dUaW1lOiAnJyB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5Yig6Zmk5Zy65pmv55qE55eb54K55Lu35YC8DQogICAgcmVtb3ZlU2NlbmVQYWluUG9pbnQobm9kZUlkLCBpZHgpIHsNCiAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgbm9kZUlkKQ0KICAgICAgaWYgKG5vZGUgJiYgbm9kZS50eXBlID09PSAnc2NlbmUnKSB7DQogICAgICAgIG5vZGUucGFpblBvaW50cyA9IG5vZGUucGFpblBvaW50cyB8fCBbXQ0KICAgICAgICBub2RlLnBhaW5Qb2ludHMuc3BsaWNlKGlkeCwgMSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8mua3u+WKoOWcuuaZr+eXm+eCueWGheWuueeahOmhuQ0KICAgIGFkZFNjZW5lUGFpbkNvbnRlbnQobm9kZUlkLCBpZHgpIHsNCiAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgbm9kZUlkKQ0KICAgICAgaWYgKG5vZGUgJiYgbm9kZS50eXBlID09PSAnc2NlbmUnKSB7DQogICAgICAgIG5vZGUucGFpblBvaW50cyA9IG5vZGUucGFpblBvaW50cyB8fCBbXQ0KICAgICAgICBub2RlLnBhaW5Qb2ludHNbaWR4XS5jb250ZW50cy5wdXNoKCcnKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5Yig6Zmk5Zy65pmv55eb54K55YaF5a6555qE6aG5DQogICAgcmVtb3ZlU2NlbmVQYWluQ29udGVudChub2RlSWQsIGlkeCwgY2lkeCkgew0KICAgICAgY29uc3Qgbm9kZSA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBub2RlSWQpDQogICAgICBpZiAobm9kZSAmJiBub2RlLnR5cGUgPT09ICdzY2VuZScpIHsNCiAgICAgICAgbm9kZS5wYWluUG9pbnRzID0gbm9kZS5wYWluUG9pbnRzIHx8IFtdDQogICAgICAgIG5vZGUucGFpblBvaW50c1tpZHhdLmNvbnRlbnRzLnNwbGljZShjaWR4LCAxKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5re75Yqg5Zy65pmv55qE5oiQ5pys6aKE5Lyw5YaF5a65DQogICAgYWRkU2NlbmVDb3N0Q29udGVudChub2RlSWQpIHsNCiAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgbm9kZUlkKQ0KICAgICAgaWYgKG5vZGUgJiYgbm9kZS50eXBlID09PSAnY29zdEVzdGltYXRlJykgew0KICAgICAgICBub2RlLmNvbnRlbnRzID0gbm9kZS5jb250ZW50cyB8fCBbXQ0KICAgICAgICBub2RlLmNvbnRlbnRzLnB1c2goJycpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrliKDpmaTlnLrmma/nmoTmiJDmnKzpooTkvLDlhoXlrrkNCiAgICByZW1vdmVTY2VuZUNvc3RDb250ZW50KG5vZGVJZCwgY2lkeCkgew0KICAgICAgY29uc3Qgbm9kZSA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBub2RlSWQpDQogICAgICBpZiAobm9kZSAmJiBub2RlLnR5cGUgPT09ICdjb3N0RXN0aW1hdGUnKSB7DQogICAgICAgIG5vZGUuY29udGVudHMgPSBub2RlLmNvbnRlbnRzIHx8IFtdDQogICAgICAgIG5vZGUuY29udGVudHMuc3BsaWNlKGNpZHgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrkuIrkvKDlnLrmma/phY3nva7lm77niYcNCiAgICBiZWZvcmVVcGxvYWRTY2VuZUNvbmZpZ0ltZyhmaWxlLCB0eXBlLCBrZXkpIHsNCiAgICAgIGlmICghZmlsZS50eXBlLnN0YXJ0c1dpdGgoJ2ltYWdlLycpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oOWbvueJh+aWh+S7tu+8gScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgY29uc3QgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKQ0KICAgICAgcmVhZGVyLm9ubG9hZCA9IGUgPT4gew0KICAgICAgICB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgdHlwZSlba2V5XSA9IGUudGFyZ2V0LnJlc3VsdA0KICAgICAgfQ0KICAgICAgcmVhZGVyLnJlYWRBc0RhdGFVUkwoZmlsZSkNCiAgICAgIHJldHVybiBmYWxzZQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5LiK5Lyg5Zy65pmv6YWN572u5paH5Lu2DQogICAgYmVmb3JlVXBsb2FkU2NlbmVDb25maWdGaWxlKGZpbGUsIHR5cGUsIGtleSkgew0KICAgICAgLy8g6L+Z6YeM5Y+q5YGa5paH5Lu25ZCN5Zue5pi+DQogICAgICB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgdHlwZSlba2V5XSA9IGZpbGUubmFtZQ0KICAgICAgcmV0dXJuIGZhbHNlDQogICAgfSwNCiAgICBoYW5kbGVTY2VuZU5vZGVDbGljayhub2RlKSB7DQogICAgICB0aGlzLnNlbGVjdGVkTm9kZSA9IG5vZGUNCiAgICB9LA0KICAgIC8vIOmAgumFjeWHveaVsOenu+WIsG1ldGhvZHPkuK3vvIzkvpvmjqXlj6PmlbDmja7pgILphY3kvb/nlKgNCiAgICBhZGFwdFNjZW5lVHJlZShyYXdUcmVlLCBwYXJlbnQgPSBudWxsKSB7DQogICAgICBpZiAoIXJhd1RyZWUpIHJldHVybiBbXQ0KICAgICAgY29uc3QgYXJyID0gQXJyYXkuaXNBcnJheShyYXdUcmVlKSA/IHJhd1RyZWUgOiBbcmF3VHJlZV0NCiAgICAgIHJldHVybiBhcnIubWFwKChub2RlKSA9PiB7DQogICAgICAgIGNvbnN0IGFkYXB0ZWQgPSB7DQogICAgICAgICAgaWQ6IG5vZGUuc2NlbmVJZCwNCiAgICAgICAgICBzY2VuZUluZm9JZDogbm9kZS5zY2VuZUluZm9JZCB8fCBudWxsLA0KICAgICAgICAgIG5hbWU6IG5vZGUuc2NlbmVOYW1lLA0KICAgICAgICAgIGNvZGU6IG5vZGUuc2NlbmVDb2RlLA0KICAgICAgICAgIHg6IG5vZGUueCwNCiAgICAgICAgICB5OiBub2RlLnksDQogICAgICAgICAgdHlwZTogbm9kZS50eXBlLA0KICAgICAgICAgIHN0YXR1czogbm9kZS5zdGF0dXMgIT09IG51bGwgPyBub2RlLnN0YXR1cyA6ICcwJywNCiAgICAgICAgICBpc1VuZm9sZDogbm9kZS5pc1VuZm9sZCAhPT0gbnVsbCAmJiBub2RlLmlzVW5mb2xkICE9PSB1bmRlZmluZWQgPyBub2RlLmlzVW5mb2xkIDogJzEnLA0KICAgICAgICAgIGRpc3BsYXlMb2NhdGlvbjogbm9kZS5kaXNwbGF5TG9jYXRpb24gIT09IG51bGwgJiYgbm9kZS5kaXNwbGF5TG9jYXRpb24gIT09IHVuZGVmaW5lZCA/IG5vZGUuZGlzcGxheUxvY2F0aW9uIDogJzAnLA0KICAgICAgICAgIHRyZWVDbGFzc2lmaWNhdGlvbjogbm9kZS50cmVlQ2xhc3NpZmljYXRpb24gIT09IG51bGwgJiYgbm9kZS50cmVlQ2xhc3NpZmljYXRpb24gIT09IHVuZGVmaW5lZCA/IG5vZGUudHJlZUNsYXNzaWZpY2F0aW9uIDogJzMnLA0KICAgICAgICAgIGludHJvZHVjZVZpZGVvVm86IG5vZGUuaW50cm9kdWNlVmlkZW9WbyA/IHsNCiAgICAgICAgICAgIGlkOiBub2RlLmludHJvZHVjZVZpZGVvVm8uaWQgfHwgJycsDQogICAgICAgICAgICB0eXBlOiBub2RlLmludHJvZHVjZVZpZGVvVm8udHlwZSB8fCAnJywNCiAgICAgICAgICAgIHZpZXdJbmZvSWQ6IG5vZGUuaW50cm9kdWNlVmlkZW9Wby52aWV3SW5mb0lkIHx8ICcnLA0KICAgICAgICAgICAgc3RhdHVzOiBub2RlLmludHJvZHVjZVZpZGVvVm8uc3RhdHVzIHx8ICcwJywNCiAgICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiBub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEltZ0ZpbGVVcmwgfHwgJycsDQogICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmJhY2tncm91bmRGaWxlVXJsIHx8ICcnDQogICAgICAgICAgfSA6IHsgaWQ6ICcnLCB0eXBlOiAnJywgdmlld0luZm9JZDogJycsIHN0YXR1czogJzAnLCBiYWNrZ3JvdW5kSW1nRmlsZVVybDogJycsIGJhY2tncm91bmRGaWxlVXJsOiAnJyB9LA0KICAgICAgICAgIHRyYWRpdGlvbjogbm9kZS5zY2VuZVRyYWRpdGlvblZvID8gew0KICAgICAgICAgICAgbmFtZTogbm9kZS5zY2VuZVRyYWRpdGlvblZvLm5hbWUgfHwgJycsDQogICAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sS2V5OiBub2RlLnNjZW5lVHJhZGl0aW9uVm8ucGFub3JhbWljVmlld1htbEtleSB8fCAnJywNCiAgICAgICAgICAgIGJhY2tncm91bmRSZXNvdXJjZXM6IG5vZGUuc2NlbmVUcmFkaXRpb25Wby5zY2VuZVZpZGVvTGlzdCA/IA0KICAgICAgICAgICAgICBub2RlLnNjZW5lVHJhZGl0aW9uVm8uc2NlbmVWaWRlb0xpc3QubWFwKHYgPT4gKHsNCiAgICAgICAgICAgICAgICBpZDogdi5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIGxhYmVsOiB2LnRhZyB8fCAnJywNCiAgICAgICAgICAgICAgICBjb29yZGluYXRlczogdi5zY2VuZUZpbGVSZWxMaXN0ID8gdi5zY2VuZUZpbGVSZWxMaXN0Lm1hcChyZWwgPT4gKHsNCiAgICAgICAgICAgICAgICAgIGlkOiByZWwuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIGZpbGVJZDogcmVsLmZpbGVJZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgeDogcmVsLmNsaWNrWCB8fCAnJywNCiAgICAgICAgICAgICAgICAgIHk6IHJlbC5jbGlja1kgfHwgJycsDQogICAgICAgICAgICAgICAgICB3aWRlOiByZWwud2lkZSB8fCAnJywNCiAgICAgICAgICAgICAgICAgIGhpZ2g6IHJlbC5oaWdoIHx8ICcnLA0KICAgICAgICAgICAgICAgICAgeG1sS2V5OiByZWwueG1sS2V5IHx8ICcnLA0KICAgICAgICAgICAgICAgICAgc2NlbmVJZDogdGhpcy5maW5kU2NlbmVJZEJ5Q29kZShyZWwuYmluZFNjZW5lQ29kZSksDQogICAgICAgICAgICAgICAgICBzY2VuZUNvZGU6IHJlbC5iaW5kU2NlbmVDb2RlIHx8ICcnDQogICAgICAgICAgICAgICAgfSkpIDogW3sgaWQ6IG51bGwsIGZpbGVJZDogbnVsbCwgeDogJycsIHk6ICcnLCB3aWRlOiAnJywgaGlnaDogJycsIHNjZW5lSWQ6ICcnLCBzY2VuZUNvZGU6ICcnIH1dLA0KICAgICAgICAgICAgICAgIHdpZGU6IHYud2lkZSB8fCAwLA0KICAgICAgICAgICAgICAgIGhpZ2g6IHYuaGlnaCB8fCAwLA0KICAgICAgICAgICAgICAgIGJnSW1nOiB2LmJhY2tncm91bmRJbWdGaWxlVXJsIHx8ICcnLA0KICAgICAgICAgICAgICAgIGJnRmlsZTogdi5iYWNrZ3JvdW5kRmlsZVVybCB8fCAnJywNCiAgICAgICAgICAgICAgICBzdGF0dXM6IHYuc3RhdHVzIHx8ICcnLA0KICAgICAgICAgICAgICAgIHR5cGU6IHYudHlwZSB8fCAnJywNCiAgICAgICAgICAgICAgICB2aWV3SW5mb0lkOiB2LnZpZXdJbmZvSWQgfHwgJycNCiAgICAgICAgICAgICAgfSkpIDogW10sDQogICAgICAgICAgICBwYWluUG9pbnRzOiBub2RlLnNjZW5lVHJhZGl0aW9uVm8ucGFpblBvaW50TGlzdCA/DQogICAgICAgICAgICAgIChBcnJheS5pc0FycmF5KG5vZGUuc2NlbmVUcmFkaXRpb25Wby5wYWluUG9pbnRMaXN0KSA/IG5vZGUuc2NlbmVUcmFkaXRpb25Wby5wYWluUG9pbnRMaXN0Lm1hcChwID0+ICh7DQogICAgICAgICAgICAgICAgcGFpblBvaW50SWQ6IHAucGFpblBvaW50SWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICB0aXRsZTogcC5iaWdUaXRsZSB8fCAnJywNCiAgICAgICAgICAgICAgICBjb250ZW50czogcC5jb250ZW50IHx8IFtdLA0KICAgICAgICAgICAgICAgIHNob3dUaW1lOiBwLmRpc3BsYXlUaW1lIHx8ICcnDQogICAgICAgICAgICAgIH0pKSA6IFtdKSA6IFtdDQogICAgICAgICAgfSA6IHsgbmFtZTogJycsIHBhbm9yYW1pY1ZpZXdYbWxLZXk6ICcnLCBiYWNrZ3JvdW5kUmVzb3VyY2VzOiBbXSwgcGFpblBvaW50czogW10gfSwNCiAgICAgICAgICB3aXNkb201Zzogbm9kZS5zY2VuZTVnVm8gPyB7DQogICAgICAgICAgICBuYW1lOiBub2RlLnNjZW5lNWdWby5uYW1lIHx8ICcnLA0KICAgICAgICAgICAgcGFub3JhbWljVmlld1htbEtleTogbm9kZS5zY2VuZTVnVm8ucGFub3JhbWljVmlld1htbEtleSB8fCAnJywNCiAgICAgICAgICAgIGJhY2tncm91bmRSZXNvdXJjZXM6IG5vZGUuc2NlbmU1Z1ZvLnNjZW5lVmlkZW9MaXN0ID8gDQogICAgICAgICAgICAgIG5vZGUuc2NlbmU1Z1ZvLnNjZW5lVmlkZW9MaXN0Lm1hcCh2ID0+ICh7DQogICAgICAgICAgICAgICAgaWQ6IHYuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICB0YWc6IHYudGFnIHx8ICcnLA0KICAgICAgICAgICAgICAgIHN0YXR1czogdi5zdGF0dXMgfHwgJycsDQogICAgICAgICAgICAgICAgdHlwZTogdi50eXBlIHx8ICcnLA0KICAgICAgICAgICAgICAgIHZpZXdJbmZvSWQ6IHYudmlld0luZm9JZCB8fCAnJywNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogdi5iYWNrZ3JvdW5kSW1nRmlsZVVybCB8fCAnJywNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogdi5iYWNrZ3JvdW5kRmlsZVVybCB8fCAnJywNCiAgICAgICAgICAgICAgICBjb29yZGluYXRlczogdi5zY2VuZUZpbGVSZWxMaXN0ID8gdi5zY2VuZUZpbGVSZWxMaXN0Lm1hcChyZWwgPT4gKHsNCiAgICAgICAgICAgICAgICAgIGlkOiByZWwuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIGZpbGVJZDogcmVsLmZpbGVJZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgeDogcmVsLmNsaWNrWCB8fCAnJywNCiAgICAgICAgICAgICAgICAgIHk6IHJlbC5jbGlja1kgfHwgJycsDQogICAgICAgICAgICAgICAgICB3aWRlOiByZWwud2lkZSB8fCAnJywNCiAgICAgICAgICAgICAgICAgIGhpZ2g6IHJlbC5oaWdoIHx8ICcnLA0KICAgICAgICAgICAgICAgICAgeG1sS2V5OiByZWwueG1sS2V5IHx8ICcnLA0KICAgICAgICAgICAgICAgICAgc2NlbmVJZDogdGhpcy5maW5kU2NlbmVJZEJ5Q29kZShyZWwuYmluZFNjZW5lQ29kZSksDQogICAgICAgICAgICAgICAgICBzY2VuZUNvZGU6IHJlbC5iaW5kU2NlbmVDb2RlIHx8ICcnDQogICAgICAgICAgICAgICAgfSkpIDogW3sgaWQ6IG51bGwsIGZpbGVJZDogbnVsbCwgeDogJycsIHk6ICcnLCB3aWRlOiAnJywgaGlnaDogJycsIHNjZW5lSWQ6ICcnLCBzY2VuZUNvZGU6ICcnIH1dDQogICAgICAgICAgICAgIH0pKSA6IFtdLA0KICAgICAgICAgICAgcGFpblBvaW50czogbm9kZS5zY2VuZTVnVm8ucGFpblBvaW50TGlzdCA/IA0KICAgICAgICAgICAgICAoQXJyYXkuaXNBcnJheShub2RlLnNjZW5lNWdWby5wYWluUG9pbnRMaXN0KSA/IG5vZGUuc2NlbmU1Z1ZvLnBhaW5Qb2ludExpc3QubWFwKHAgPT4gKHsNCiAgICAgICAgICAgICAgICBwYWluUG9pbnRJZDogcC5wYWluUG9pbnRJZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIHRpdGxlOiBwLmJpZ1RpdGxlIHx8ICcnLA0KICAgICAgICAgICAgICAgIGNvbnRlbnRzOiBwLmNvbnRlbnQgfHwgW10sDQogICAgICAgICAgICAgICAgc2hvd1RpbWU6IHAuZGlzcGxheVRpbWUgfHwgJycNCiAgICAgICAgICAgICAgfSkpIDogW10pIDogW10NCiAgICAgICAgICB9IDogeyBuYW1lOiAnJywgcGFub3JhbWljVmlld1htbEtleTogJycsIGJhY2tncm91bmRSZXNvdXJjZXM6IFtdLCBwYWluUG9pbnRzOiBbXSB9LA0KICAgICAgICAgIGNvc3RFc3RpbWF0ZTogbm9kZS5jb3N0RXN0aW1hdGlvbkluZm9WbyA/IHsNCiAgICAgICAgICAgIHBhaW5Qb2ludElkOiBub2RlLmNvc3RFc3RpbWF0aW9uSW5mb1ZvLnBhaW5Qb2ludElkIHx8IG51bGwsDQogICAgICAgICAgICBzdGF0dXM6IG5vZGUuY29zdEVzdGltYXRpb25JbmZvVm8uc3RhdHVzIHx8ICcwJywNCiAgICAgICAgICAgIHRpdGxlOiBub2RlLmNvc3RFc3RpbWF0aW9uSW5mb1ZvLmJpZ1RpdGxlIHx8ICcnLA0KICAgICAgICAgICAgY29udGVudHM6IG5vZGUuY29zdEVzdGltYXRpb25JbmZvVm8uY29udGVudCB8fCBbXQ0KICAgICAgICAgIH0gOiB7IHBhaW5Qb2ludElkOiBudWxsLCBzdGF0dXM6ICcwJywgdGl0bGU6ICcnLCBjb250ZW50czogW10gfSwNCiAgICAgICAgICBjaGlsZHJlbjogW10sDQogICAgICAgICAgcGFyZW50DQogICAgICAgIH0NCiAgICAgICAgLy8g6YCS5b2S5aSE55CG5a2Q6IqC54K577yM5L+d5oyB5ZCO56uv6L+U5Zue55qE5Y6f5aeL6aG65bqPDQogICAgICAgIGFkYXB0ZWQuY2hpbGRyZW4gPSBub2RlLmNoaWxkcmVuID8gdGhpcy5hZGFwdFNjZW5lVHJlZShub2RlLmNoaWxkcmVuLCBhZGFwdGVkKSA6IFtdDQogICAgICAgIHJldHVybiBhZGFwdGVkDQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlU3VibWl0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5zdWJtaXR0aW5nID0gdHJ1ZQ0KICAgICAgICAvLyDku47miYHlubPljJboj5zljZXmlbDmja7kuK3ojrflj5blvZPliY3ooYzkuJrnmoQgaW5kdXN0cnlDb2RlDQogICAgICAgIGNvbnN0IGN1cnJlbnRJbmR1c3RyeSA9IHRoaXMuZmxhdE1lbnVEYXRhLmZpbmQoaXRlbSA9PiBTdHJpbmcoaXRlbS5pZCkgPT09IHRoaXMuYWN0aXZlTWVudSkNCiAgICAgICAgY29uc3QgaW5kdXN0cnlDb2RlID0gY3VycmVudEluZHVzdHJ5ID8gY3VycmVudEluZHVzdHJ5LmluZHVzdHJ5Q29kZSA6IG51bGwNCiAgICAgICAgDQogICAgICAgIC8vIOS/neWtmOW9k+WJjemAieS4reeahOiKgueCueeahOWujOaVtOS/oeaBrw0KICAgICAgICBjb25zdCBjdXJyZW50U2VsZWN0ZWROb2RlSWQgPSB0aGlzLnNlbGVjdGVkTm9kZSA/IHRoaXMuc2VsZWN0ZWROb2RlLmlkIDogbnVsbA0KICAgICAgICBjb25zdCBjdXJyZW50U2VsZWN0ZWROb2RlTmFtZSA9IHRoaXMuc2VsZWN0ZWROb2RlID8gdGhpcy5zZWxlY3RlZE5vZGUubmFtZSA6IG51bGwNCiAgICAgICAgDQogICAgICAgIC8vIOaehOW7uuaPkOS6pOaVsOaNrg0KICAgICAgICBjb25zdCBzdWJtaXREYXRhID0gew0KICAgICAgICAgIGluZHVzdHJ5SWQ6IHRoaXMuYWN0aXZlTWVudSwNCiAgICAgICAgICBpbmR1c3RyeUNvZGU6IGluZHVzdHJ5Q29kZSwgLy8g5paw5aKe5Y+C5pWwDQogICAgICAgICAgc2NlbmVWaWV3Q29uZmlnSWQ6IHRoaXMuZm9ybS5zY2VuZVZpZXdDb25maWdJZCB8fCBudWxsLA0KICAgICAgICAgIG1haW5UaXRsZTogdGhpcy5mb3JtLm1haW5UaXRsZSB8fCBudWxsLA0KICAgICAgICAgIHN1YlRpdGxlOiB0aGlzLmZvcm0uc3ViVGl0bGUgfHwgbnVsbCwNCiAgICAgICAgICB0aGVtZUlkOiB0aGlzLnNlbGVjdGVkVGhlbWUgPyB0aGlzLnNlbGVjdGVkVGhlbWUudGhlbWVJZCA6IG51bGwsDQogICAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6IHRoaXMuZm9ybS5iZ0ltZ1VybCB8fCBudWxsLA0KICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiB0aGlzLmZvcm0uYmdGaWxlVXJsIHx8IG51bGwsDQogICAgICAgICAgcGFub3JhbWljVmlld1htbFVybDogdGhpcy5mb3JtLnBhbm9yYW1pY1ZpZXdYbWxVcmwgfHwgbnVsbCwNCiAgICAgICAgICBuZXR3b3JrU29sdXRpb25JbmZvVm86IHsNCiAgICAgICAgICAgIG5ldHdvcmtWaWRlb0xpc3Q6ICh0aGlzLm5ldHdvcmtQbGFuRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdPy5uZXR3b3JrVmlkZW9MaXN0ICYmIEFycmF5LmlzQXJyYXkodGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XS5uZXR3b3JrVmlkZW9MaXN0KSkgPyANCiAgICAgICAgICAgICAgdGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XS5uZXR3b3JrVmlkZW9MaXN0Lm1hcChwbGFuID0+ICh7DQogICAgICAgICAgICAgICAgaWQ6IHBsYW4uaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICB0eXBlOiA0LA0KICAgICAgICAgICAgICAgIHRhZzogcGxhbi50YWcgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICBjbGlja1g6IHBsYW4uY2xpY2tYIHx8IG51bGwsDQogICAgICAgICAgICAgICAgY2xpY2tZOiBwbGFuLmNsaWNrWSB8fCBudWxsLA0KICAgICAgICAgICAgICAgIHdpZGU6IHBsYW4ud2lkZSB8fCBudWxsLA0KICAgICAgICAgICAgICAgIGhpZ2g6IHBsYW4uaGlnaCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiBwbGFuLmJhY2tncm91bmRJbWdGaWxlVXJsIHx8IG51bGwsDQogICAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHBsYW4uYmFja2dyb3VuZEZpbGVVcmwgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgICAgICAgICAgdmlld0luZm9JZDogbnVsbA0KICAgICAgICAgICAgICB9KSkgOiBbXSwNCiAgICAgICAgICAgIHZpZGVvRXhwbGFuYXRpb25Wbzogew0KICAgICAgICAgICAgICBzdGF0dXM6IHRoaXMubmV0d29ya1BsYW5EYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0/LnZpZGVvRXhwbGFuYXRpb25Wbz8uc3RhdHVzIHx8ICcwJywNCiAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHRoaXMubmV0d29ya1BsYW5EYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0/LnZpZGVvRXhwbGFuYXRpb25Wbz8uYmFja2dyb3VuZEZpbGVVcmwgfHwgbnVsbCwNCiAgICAgICAgICAgICAgdmlkZW9TZWdtZW50ZWRWb0xpc3Q6ICh0aGlzLm5ldHdvcmtQbGFuRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdPy52aWRlb0V4cGxhbmF0aW9uVm8/LnZpZGVvU2VnbWVudGVkVm9MaXN0Py5sZW5ndGgpID8gDQogICAgICAgICAgICAgICAgdGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XS52aWRlb0V4cGxhbmF0aW9uVm8udmlkZW9TZWdtZW50ZWRWb0xpc3QubWFwKHNlZyA9PiAoew0KICAgICAgICAgICAgICAgICAgdGltZTogc2VnLnRpbWUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIHNjZW5lQ29kZTogc2VnLnNjZW5lQ29kZSB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgc2NlbmVOYW1lOiBzZWcuc2NlbmVOYW1lIHx8IG51bGwNCiAgICAgICAgICAgICAgICB9KSkgOiBudWxsDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBzY2VuZURlZmF1bHRDb25maWdWb0xpc3Q6IHRoaXMuY2F0ZWdvcmllcy5tYXAoY2F0ID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGJhc2VDb25maWcgPSB7DQogICAgICAgICAgICAgIGlkOiBjYXQuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgaW5kdXN0cnlJZDogdGhpcy5hY3RpdmVNZW51IHx8IG51bGwsDQogICAgICAgICAgICAgIG5hbWU6IGNhdC5uYW1lLA0KICAgICAgICAgICAgICBrZXlOYW1lOiBjYXQua2V5LA0KICAgICAgICAgICAgICBrZXlWYWx1ZTogY2F0LmVuYWJsZWQgPyAnMCcgOiAnMScsDQogICAgICAgICAgICAgIHJlbWFyazogY2F0LnJlbWFyayB8fCBjYXQubmFtZSwNCiAgICAgICAgICAgICAgZGVmYXVsdFN0YXR1czogJzAnDQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGlmIChjYXQua2V5ID09PSAnZGVmYXVsdF9zY2VuZScpIHsNCiAgICAgICAgICAgICAgY29uc3QgY29udmVydGVkU2NlbmVMaXN0ID0gdGhpcy5jb252ZXJ0U2NlbmVUcmVlVG9BcGkodGhpcy5zY2VuZUNvbmZpZ1RyZWUpICAgICANCiAgICAgICAgICAgICAgYmFzZUNvbmZpZy5pbmR1c3RyeVNjZW5lSW5mb1ZvID0gew0KICAgICAgICAgICAgICAgIHZpZGVvRXhwbGFuYXRpb25Wbzogew0KICAgICAgICAgICAgICAgICAgc3RhdHVzOiB0aGlzLnZpZGVvRXhwbGFuYXRpb24uc3RhdHVzLA0KICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHRoaXMudmlkZW9FeHBsYW5hdGlvbi5iYWNrZ3JvdW5kRmlsZVVybCB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgdmlkZW9TZWdtZW50ZWRWb0xpc3Q6IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5sZW5ndGggPyB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3QgOiBudWxsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBzY2VuZUxpc3RWbzogY29udmVydGVkU2NlbmVMaXN0DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIGJhc2VDb25maWcuaW5kdXN0cnlTY2VuZUluZm9WbyA9IG51bGwNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgcmV0dXJuIGJhc2VDb25maWcNCiAgICAgICAgICB9KSwNCiAgICAgICAgICBjb21tZXJjaWFsVmFsdWVEVE86ICh0aGlzLmJ1c2luZXNzVmFsdWVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0gJiYgQXJyYXkuaXNBcnJheSh0aGlzLmJ1c2luZXNzVmFsdWVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0pKSA/IA0KICAgICAgICAgICAgdGhpcy5idXNpbmVzc1ZhbHVlRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdLm1hcCh2YWx1ZSA9PiAoew0KICAgICAgICAgICAgICBpZDogdmFsdWUuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgdmlld0luZm9JZDogdmFsdWUudmlld0luZm9JZCB8fCBudWxsLA0KICAgICAgICAgICAgICB0eXBlOiA1LA0KICAgICAgICAgICAgICB0YWc6IHZhbHVlLnRhZyB8fCBudWxsLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogdmFsdWUuYmFja2dyb3VuZEltZ0ZpbGVVcmwgfHwgbnVsbCwNCiAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHZhbHVlLmJhY2tncm91bmRGaWxlVXJsIHx8IG51bGwNCiAgICAgICAgICAgIH0pKSA6IFtdLA0KICAgICAgICAgIHZySW5mb0R0b0xpc3Q6ICh0aGlzLnZyU2NlbmVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0gJiYgQXJyYXkuaXNBcnJheSh0aGlzLnZyU2NlbmVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0pKSA/IA0KICAgICAgICAgICAgdGhpcy52clNjZW5lRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdLiBtYXAodnIgPT4gKHsNCiAgICAgICAgICAgICAgaWQ6IHZyLmlkIHx8IG51bGwsDQogICAgICAgICAgICAgIGluZHVzdHJ5SWQ6IHZyLmluZHVzdHJ5SWQgfHwgdGhpcy5hY3RpdmVNZW51LA0KICAgICAgICAgICAgICB0eXBlOiB2ci50eXBlIHx8IDYsDQogICAgICAgICAgICAgIHZpZXdJbmZvSWQ6IHZyLnZpZXdJbmZvSWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgbmFtZTogdnIubmFtZSB8fCAnJywNCiAgICAgICAgICAgICAgYWRkcmVzczogdnIuYWRkcmVzcyB8fCAnJw0KICAgICAgICAgICAgfSkpIDogW10sDQogICAgICAgIH0NCiAgICAgIA0KICAgICAgICBzY2VuZVZpZXdVcGQoc3VibWl0RGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgDQogICAgICAgICAgLy8g6YeN5paw5Yqg6L295pWw5o2uDQogICAgICAgICAgdGhpcy5oYW5kbGVTZWxlY3QodGhpcy5hY3RpdmVNZW51KS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgaWYgKGN1cnJlbnRTZWxlY3RlZE5vZGVJZCAmJiB0aGlzLnNjZW5lQ29uZmlnVHJlZS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIC8vIOW8uuWItuafpeaJvuW5tuiuvue9rumAieS4reiKgueCuQ0KICAgICAgICAgICAgICBjb25zdCBub2RlVG9TZWxlY3QgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgY3VycmVudFNlbGVjdGVkTm9kZUlkKQ0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgaWYgKG5vZGVUb1NlbGVjdCkgew0KICAgICAgICAgICAgICAgIC8vIOiuoeeul+W5tuiuvue9ruWxleW8gOi3r+W+hA0KICAgICAgICAgICAgICAgIGNvbnN0IHBhdGhJZHMgPSBbXQ0KICAgICAgICAgICAgICAgIGNvbnN0IGZpbmRQYXRoID0gKG5vZGVzLCB0YXJnZXRJZCwgY3VycmVudFBhdGggPSBbXSkgPT4gew0KICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBub2RlIG9mIG5vZGVzKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1BhdGggPSBbLi4uY3VycmVudFBhdGgsIG5vZGUuaWRdDQogICAgICAgICAgICAgICAgICAgIGlmIChub2RlLmlkID09PSB0YXJnZXRJZCkgew0KICAgICAgICAgICAgICAgICAgICAgIHBhdGhJZHMucHVzaCguLi5uZXdQYXRoKQ0KICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgaWYgKGZpbmRQYXRoKG5vZGUuY2hpbGRyZW4sIHRhcmdldElkLCBuZXdQYXRoKSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICBmaW5kUGF0aCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgY3VycmVudFNlbGVjdGVkTm9kZUlkKQ0KICAgICAgICAgICAgICAgIHRoaXMudHJlZUV4cGFuZGVkS2V5cyA9IHBhdGhJZHMuc2xpY2UoMCwgLTEpDQogICAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgLy8g5YWI6K6+572u6YCJ5Lit6IqC54K5DQogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSBub2RlVG9TZWxlY3QNCiAgICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICAvLyDlvLrliLbmm7TmlrDmoJHnu4Tku7bnmoTpgInkuK3nirbmgIENCiAgICAgICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIC8vIOaooeaLn+eCueWHu+iKgueCueadpeW8uuWItuabtOaWsOmAieS4reeKtuaAgQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZVNjZW5lTm9kZUNsaWNrKG5vZGVUb1NlbGVjdCkNCiAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICAgIA0KICAgICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk5YaF5a65OicsIHN1Ym1pdERhdGEpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmj5DkuqTlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aPkOS6pOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLnN1Ym1pdHRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgYWRkVmlkZW9TZWdtZW50KCkgew0KICAgICAgLy8g5aaC5p6c5Y6f5pWw57uE5Li656m677yM5YWI5Yid5aeL5YyWDQogICAgICBpZiAoIXRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCB8fCB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCA9IFt7IHRpbWU6ICcnLCBzY2VuZUlkOiAnJywgc2NlbmVOYW1lOiAnJywgc2NlbmVDb2RlOiAnJyB9XQ0KICAgICAgfQ0KICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0LnB1c2goeyB0aW1lOiAnJywgc2NlbmVJZDogJycsIHNjZW5lTmFtZTogJycsIHNjZW5lQ29kZTogJycgfSkNCiAgICB9LA0KICAgIHJlbW92ZVZpZGVvU2VnbWVudChpZHgpIHsNCiAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5zcGxpY2UoaWR4LCAxKQ0KICAgIH0sDQogICAgLy/pgJLlvZLph43mnoTnu5PmnoQNCiAgICBnZXREZWVwVHJlZU9wdGlvbnModHJlZSkgew0KICAgIHJldHVybiB0cmVlLm1hcChpdGVtID0+IHsNCiAgICAgIC8vIOWkjeWItuW9k+WJjeiKgueCueeahOWfuuehgOWxnuaApw0KICAgICAgY29uc3Qgbm9kZSA9IHsgLi4uaXRlbSB9Ow0KICAgICAgDQogICAgICAvLyDlpoLmnpzlrZjlnKggY2hpbGRyZW4g5LiU5LiN5Li656m677yM5YiZ6YCS5b2S5aSE55CGDQogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbm9kZS5jaGlsZHJlbiA9IHRoaXMuZ2V0RGVlcFRyZWVPcHRpb25zKG5vZGUuY2hpbGRyZW4pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5b2TIGNoaWxkcmVuIOS4uuepuuaIluS4jeWtmOWcqOaXtu+8jOWIoOmZpCBjaGlsZHJlbiDlsZ7mgKfvvIjlj6/pgInvvIkNCiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW47DQogICAgICB9DQogICAgICANCiAgICAgIHJldHVybiBub2RlOw0KICAgIH0pOw0KICB9LA0KICAgIGFzeW5jIGxvYWRTY2VuZVRyZWVPcHRpb25zKGlkKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDku47miYHlubPljJboj5zljZXmlbDmja7kuK3ojrflj5blvZPliY3ooYzkuJrnmoQgaW5kdXN0cnlDb2RlDQogICAgICAgIGNvbnN0IGN1cnJlbnRJbmR1c3RyeSA9IHRoaXMuZmxhdE1lbnVEYXRhLmZpbmQoaXRlbSA9PiBTdHJpbmcoaXRlbS5pZCkgPT09IGlkKQ0KICAgICAgICBjb25zdCBpbmR1c3RyeUNvZGUgPSBjdXJyZW50SW5kdXN0cnkgPyBjdXJyZW50SW5kdXN0cnkuaW5kdXN0cnlDb2RlIDogbnVsbA0KICAgICAgICANCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0U2NlbmVUcmVlTGlzdCh7IGluZHVzdHJ5Q29kZTogaW5kdXN0cnlDb2RlIH0pDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCAmJiBBcnJheS5pc0FycmF5KHJlcy5kYXRhKSkgew0KICAgICAgICAgIHRoaXMuc2NlbmVUcmVlT3B0aW9ucyA9IHRoaXMuZ2V0RGVlcFRyZWVPcHRpb25zKHJlcy5kYXRhKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3lnLrmma/moJHlpLHotKU6JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTY2VuZUNhc2NhZGVyQ2hhbmdlKHZhbCwgaWR4KSB7DQogICAgICAvLyDnoa7kv53mlbDnu4TlkozntKLlvJXkvY3nva7nmoTlr7nosaHlrZjlnKgNCiAgICAgIGlmICghdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0IHx8ICF0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XSkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgY29uc3QgZmluZFNjZW5lID0gKHRyZWUsIGlkKSA9PiB7DQogICAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiB0cmVlKSB7DQogICAgICAgICAgaWYgKG5vZGUuaWQgPT09IGlkKSByZXR1cm4gbm9kZQ0KICAgICAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgICBjb25zdCBmb3VuZCA9IGZpbmRTY2VuZShub2RlLmNoaWxkcmVuLCBpZCkNCiAgICAgICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBudWxsDQogICAgICB9DQogICAgICBjb25zdCBub2RlID0gZmluZFNjZW5lKHRoaXMuc2NlbmVUcmVlT3B0aW9ucywgdmFsKQ0KICAgICAgaWYgKG5vZGUpIHsNCiAgICAgICAgLy8g6K6+572u5Zy65pmvSUTlkoznm7jlhbPkv6Hmga8NCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0W2lkeF0uc2NlbmVJZCA9IHZhbA0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XS5zY2VuZU5hbWUgPSBub2RlLnNjZW5lTmFtZQ0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XS5zY2VuZUNvZGUgPSBub2RlLnNjZW5lQ29kZQ0KICAgICAgfQ0KICAgIH0sDQogICAgaXNTY2VuZURpc2FibGVkKGlkLCBjdXJyZW50SWR4KSB7DQogICAgICAvLyDpmaTlvZPliY3liIbmrrXlpJbvvIzlhbbku5bliIbmrrXlt7LpgInnmoRpZA0KICAgICAgcmV0dXJuIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5zb21lKChzZWcsIGlkeCkgPT4gaWR4ICE9PSBjdXJyZW50SWR4ICYmIHNlZy5zY2VuZUlkID09PSBpZCkNCiAgICB9LA0KICAgIC8vIOWwhuWcuuaZr+agkei9rOaNouS4uuaOpeWPo+agvOW8jw0KICAgIGNvbnZlcnRTY2VuZVRyZWVUb0FwaShzY2VuZVRyZWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKCLmj5DkuqTnmoTmlbDmja46Iiwgc2NlbmVUcmVlKTsNCiAgICAgIHJldHVybiBzY2VuZVRyZWUubWFwKG5vZGUgPT4gKHsNCiAgICAgICAgc2NlbmVJbmZvSWQ6IG5vZGUuc2NlbmVJbmZvSWQgfHwgbnVsbCwNCiAgICAgICAgc2NlbmVJZDogbm9kZS5pZCwNCiAgICAgICAgcGFyYW1JZDogbm9kZS5wYXJlbnQgPyBub2RlLnBhcmVudC5pZCA6IG51bGwsDQogICAgICAgIHNjZW5lTmFtZTogbm9kZS5uYW1lLA0KICAgICAgICBzY2VuZUNvZGU6IG5vZGUuY29kZSwNCiAgICAgICAgeDogbm9kZS54IHx8IG51bGwsDQogICAgICAgIHk6IG5vZGUueSB8fCBudWxsLA0KICAgICAgICB0eXBlOiBub2RlLnR5cGUgfHwgbnVsbCwNCiAgICAgICAgc3RhdHVzOiBub2RlLnN0YXR1cywNCiAgICAgICAgaXNVbmZvbGQ6IChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgPyAobm9kZS5pc1VuZm9sZCB8fCAnMScpIDogbnVsbCwNCiAgICAgICAgZGlzcGxheUxvY2F0aW9uOiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApID8gKG5vZGUuZGlzcGxheUxvY2F0aW9uIHx8ICcwJykgOiBudWxsLA0KICAgICAgICB0cmVlQ2xhc3NpZmljYXRpb246IChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgPyAobm9kZS50cmVlQ2xhc3NpZmljYXRpb24gfHwgJzMnKSA6IG51bGwsDQogICAgICAgIGludHJvZHVjZVZpZGVvVm86IG5vZGUuaW50cm9kdWNlVmlkZW9WbyA/IHsNCiAgICAgICAgICBpZDogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmlkIHx8IG51bGwsDQogICAgICAgICAgdHlwZTogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLnR5cGUgfHwgbnVsbCwNCiAgICAgICAgICB2aWV3SW5mb0lkOiBub2RlLmludHJvZHVjZVZpZGVvVm8udmlld0luZm9JZCB8fCBudWxsLA0KICAgICAgICAgIHN0YXR1czogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLnN0YXR1cyB8fCBudWxsLA0KICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiBub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEltZ0ZpbGVVcmwgfHwgbnVsbCwNCiAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmJhY2tncm91bmRGaWxlVXJsIHx8IG51bGwNCiAgICAgICAgfSA6IG51bGwsDQogICAgICAgIHNjZW5lVHJhZGl0aW9uVm86IG5vZGUudHJhZGl0aW9uID8gew0KICAgICAgICAgIG5hbWU6IG5vZGUudHJhZGl0aW9uLm5hbWUgfHwgbnVsbCwNCiAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sS2V5OiBub2RlLnRyYWRpdGlvbi5wYW5vcmFtaWNWaWV3WG1sS2V5IHx8IG51bGwsDQogICAgICAgICAgc2NlbmVWaWRlb0xpc3Q6IG5vZGUudHJhZGl0aW9uLmJhY2tncm91bmRSZXNvdXJjZXMgJiYgbm9kZS50cmFkaXRpb24uYmFja2dyb3VuZFJlc291cmNlcy5sZW5ndGggPyANCiAgICAgICAgICAgIG5vZGUudHJhZGl0aW9uLmJhY2tncm91bmRSZXNvdXJjZXMubWFwKHJlc291cmNlID0+ICh7DQogICAgICAgICAgICAgIGlkOiByZXNvdXJjZS5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICB0YWc6IHJlc291cmNlLmxhYmVsIHx8IG51bGwsDQogICAgICAgICAgICAgIHdpZGU6IHJlc291cmNlLndpZGUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgaGlnaDogcmVzb3VyY2UuaGlnaCB8fCBudWxsLA0KICAgICAgICAgICAgICBzdGF0dXM6IHJlc291cmNlLnN0YXR1cyB8fCBudWxsLA0KICAgICAgICAgICAgICB0eXBlOiAxLA0KICAgICAgICAgICAgICB2aWV3SW5mb0lkOiByZXNvdXJjZS52aWV3SW5mb0lkIHx8IG51bGwsDQogICAgICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiByZXNvdXJjZS5iZ0ltZyB8fCAnJywNCiAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHJlc291cmNlLmJnRmlsZSB8fCAnJywNCiAgICAgICAgICAgICAgc2NlbmVGaWxlUmVsTGlzdDogcmVzb3VyY2UuY29vcmRpbmF0ZXMgJiYgcmVzb3VyY2UuY29vcmRpbmF0ZXMubGVuZ3RoID8gDQogICAgICAgICAgICAgICAgcmVzb3VyY2UuY29vcmRpbmF0ZXMubWFwKGNvb3JkID0+ICh7DQogICAgICAgICAgICAgICAgICBpZDogY29vcmQuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIGZpbGVJZDogY29vcmQuZmlsZUlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBjbGlja1g6IGNvb3JkLnggIT09IHVuZGVmaW5lZCAmJiBjb29yZC54ICE9PSBudWxsID8gKGNvb3JkLnggPT09ICcnID8gJycgOiBjb29yZC54KSA6IG51bGwsDQogICAgICAgICAgICAgICAgICBjbGlja1k6IGNvb3JkLnkgIT09IHVuZGVmaW5lZCAmJiBjb29yZC55ICE9PSBudWxsID8gKGNvb3JkLnkgPT09ICcnID8gJycgOiBjb29yZC55KSA6IG51bGwsDQogICAgICAgICAgICAgICAgICB3aWRlOiBjb29yZC53aWRlICE9PSB1bmRlZmluZWQgJiYgY29vcmQud2lkZSAhPT0gbnVsbCA/IChjb29yZC53aWRlID09PSAnJyB8fCBjb29yZC53aWRlID09PSAwID8gJycgOiBjb29yZC53aWRlKSA6IG51bGwsDQogICAgICAgICAgICAgICAgICBoaWdoOiBjb29yZC5oaWdoICE9PSB1bmRlZmluZWQgJiYgY29vcmQuaGlnaCAhPT0gbnVsbCA/IChjb29yZC5oaWdoID09PSAnJyB8fCBjb29yZC5oaWdoID09PSAwID8gJycgOiBjb29yZC5oaWdoKSA6IG51bGwsDQogICAgICAgICAgICAgICAgICB4bWxLZXk6IGNvb3JkLnhtbEtleSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLnhtbEtleSAhPT0gbnVsbCA/IChjb29yZC54bWxLZXkgPT09ICcnID8gJycgOiBjb29yZC54bWxLZXkpIDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIGJpbmRTY2VuZUNvZGU6IGNvb3JkLnNjZW5lQ29kZSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLnNjZW5lQ29kZSAhPT0gbnVsbCA/IChjb29yZC5zY2VuZUNvZGUgPT09ICcnID8gJycgOiBjb29yZC5zY2VuZUNvZGUpIDogbnVsbA0KICAgICAgICAgICAgICAgIH0pKSA6IFtdDQogICAgICAgICAgICB9KSkgOiBudWxsLA0KICAgICAgICAgIHBhaW5Qb2ludExpc3Q6IG5vZGUudHJhZGl0aW9uLnBhaW5Qb2ludHMgJiYgbm9kZS50cmFkaXRpb24ucGFpblBvaW50cy5sZW5ndGggPyANCiAgICAgICAgICAgIG5vZGUudHJhZGl0aW9uLnBhaW5Qb2ludHMubWFwKHBhaW4gPT4gKHsNCiAgICAgICAgICAgICAgcGFpblBvaW50SWQ6IHBhaW4ucGFpblBvaW50SWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgYmlnVGl0bGU6IHBhaW4udGl0bGUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgY29udGVudDogcGFpbi5jb250ZW50cyB8fCBbXSwNCiAgICAgICAgICAgICAgZGlzcGxheVRpbWU6IHBhaW4uc2hvd1RpbWUgfHwgbnVsbA0KICAgICAgICAgICAgfSkpIDogbnVsbA0KICAgICAgICB9IDogbnVsbCwNCiAgICAgICAgc2NlbmU1Z1ZvOiBub2RlLndpc2RvbTVnID8gew0KICAgICAgICAgIG5hbWU6IG5vZGUud2lzZG9tNWcubmFtZSB8fCBudWxsLA0KICAgICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxLZXk6IG5vZGUud2lzZG9tNWcucGFub3JhbWljVmlld1htbEtleSB8fCBudWxsLA0KICAgICAgICAgIHNjZW5lVmlkZW9MaXN0OiBub2RlLndpc2RvbTVnLmJhY2tncm91bmRSZXNvdXJjZXMgJiYgbm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzLmxlbmd0aCA/IA0KICAgICAgICAgICAgbm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzLm1hcChyZXNvdXJjZSA9PiAoew0KICAgICAgICAgICAgICBpZDogcmVzb3VyY2UuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgdGFnOiByZXNvdXJjZS50YWcgfHwgbnVsbCwNCiAgICAgICAgICAgICAgc3RhdHVzOiByZXNvdXJjZS5zdGF0dXMgfHwgbnVsbCwNCiAgICAgICAgICAgICAgdHlwZTogMiwNCiAgICAgICAgICAgICAgdmlld0luZm9JZDogcmVzb3VyY2Uudmlld0luZm9JZCB8fCBudWxsLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogcmVzb3VyY2UuYmdJbWcgfHwgJycsDQogICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiByZXNvdXJjZS5iZ0ZpbGUgfHwgJycsDQogICAgICAgICAgICAgIHNjZW5lRmlsZVJlbExpc3Q6IHJlc291cmNlLmNvb3JkaW5hdGVzICYmIHJlc291cmNlLmNvb3JkaW5hdGVzLmxlbmd0aCA/IA0KICAgICAgICAgICAgICAgIHJlc291cmNlLmNvb3JkaW5hdGVzLm1hcChjb29yZCA9PiAoew0KICAgICAgICAgICAgICAgICAgaWQ6IGNvb3JkLmlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBmaWxlSWQ6IGNvb3JkLmZpbGVJZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgY2xpY2tYOiBjb29yZC54ICE9PSB1bmRlZmluZWQgJiYgY29vcmQueCAhPT0gbnVsbCA/IChjb29yZC54ID09PSAnJyA/ICcnIDogY29vcmQueCkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgY2xpY2tZOiBjb29yZC55ICE9PSB1bmRlZmluZWQgJiYgY29vcmQueSAhPT0gbnVsbCA/IChjb29yZC55ID09PSAnJyA/ICcnIDogY29vcmQueSkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgd2lkZTogY29vcmQud2lkZSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLndpZGUgIT09IG51bGwgPyAoY29vcmQud2lkZSA9PT0gJycgfHwgY29vcmQud2lkZSA9PT0gMCA/ICcnIDogY29vcmQud2lkZSkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgaGlnaDogY29vcmQuaGlnaCAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLmhpZ2ggIT09IG51bGwgPyAoY29vcmQuaGlnaCA9PT0gJycgfHwgY29vcmQuaGlnaCA9PT0gMCA/ICcnIDogY29vcmQuaGlnaCkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgeG1sS2V5OiBjb29yZC54bWxLZXkgIT09IHVuZGVmaW5lZCAmJiBjb29yZC54bWxLZXkgIT09IG51bGwgPyAoY29vcmQueG1sS2V5ID09PSAnJyA/ICcnIDogY29vcmQueG1sS2V5KSA6IG51bGwsDQogICAgICAgICAgICAgICAgICBiaW5kU2NlbmVDb2RlOiBjb29yZC5zY2VuZUNvZGUgIT09IHVuZGVmaW5lZCAmJiBjb29yZC5zY2VuZUNvZGUgIT09IG51bGwgPyAoY29vcmQuc2NlbmVDb2RlID09PSAnJyA/ICcnIDogY29vcmQuc2NlbmVDb2RlKSA6IG51bGwNCiAgICAgICAgICAgICAgICB9KSkgOiBbXQ0KICAgICAgICAgICAgfSkpIDogbnVsbCwNCiAgICAgICAgICBwYWluUG9pbnRMaXN0OiBub2RlLndpc2RvbTVnLnBhaW5Qb2ludHMgJiYgbm9kZS53aXNkb201Zy5wYWluUG9pbnRzLmxlbmd0aCA/IA0KICAgICAgICAgICAgbm9kZS53aXNkb201Zy5wYWluUG9pbnRzLm1hcChwYWluID0+ICh7DQogICAgICAgICAgICAgIHBhaW5Qb2ludElkOiBwYWluLnBhaW5Qb2ludElkIHx8IG51bGwsDQogICAgICAgICAgICAgIGJpZ1RpdGxlOiBwYWluLnRpdGxlIHx8IG51bGwsDQogICAgICAgICAgICAgIGNvbnRlbnQ6IHBhaW4uY29udGVudHMgfHwgW10sDQogICAgICAgICAgICAgIGRpc3BsYXlUaW1lOiBwYWluLnNob3dUaW1lIHx8IG51bGwNCiAgICAgICAgICAgIH0pKSA6IG51bGwNCiAgICAgICAgfSA6IG51bGwsDQogICAgICAgIGNvc3RFc3RpbWF0aW9uSW5mb1ZvOiBub2RlLmNvc3RFc3RpbWF0ZSA/IHsNCiAgICAgICAgICBwYWluUG9pbnRJZDogbm9kZS5jb3N0RXN0aW1hdGUucGFpblBvaW50SWQgfHwgbnVsbCwNCiAgICAgICAgICBzdGF0dXM6IG5vZGUuY29zdEVzdGltYXRlLnN0YXR1cyB8fCAnMCcsDQogICAgICAgICAgYmlnVGl0bGU6IG5vZGUuY29zdEVzdGltYXRlLnRpdGxlIHx8IG51bGwsDQogICAgICAgICAgY29udGVudDogbm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMgJiYgbm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMubGVuZ3RoID8gbm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMgOiBudWxsDQogICAgICAgIH0gOiBudWxsLA0KICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA/IHRoaXMuY29udmVydFNjZW5lVHJlZVRvQXBpKG5vZGUuY2hpbGRyZW4pIDogW10NCiAgICAgIH0pKQ0KICAgIH0sDQogICAgaGFuZGxlVGltZUNoYW5nZSh2YWwsIGlkeCkgew0KICAgICAgLy8g56Gu5L+d5pWw57uE5bey5Yid5aeL5YyWDQogICAgICBpZiAoIXRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCB8fCB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCA9IFt7IHRpbWU6IDAsIHNjZW5lSWQ6ICcnLCBzY2VuZU5hbWU6ICcnLCBzY2VuZUNvZGU6ICcnIH1dDQogICAgICB9DQogICAgICAvLyDmm7TmlrDlr7nlupTkvY3nva7nmoTml7bpl7TlgLwNCiAgICAgIGlmICh0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XSkgew0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XS50aW1lID0gdmFsDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmoLnmja5zY2VuZUNvZGXmn6Xmib7lr7nlupTnmoRzY2VuZUlkDQogICAgZmluZFNjZW5lSWRCeUNvZGUoc2NlbmVDb2RlKSB7DQogICAgICBpZiAoIXNjZW5lQ29kZSB8fCAhdGhpcy5zY2VuZVRyZWVPcHRpb25zKSByZXR1cm4gJycNCiAgICAgIA0KICAgICAgY29uc3QgZmluZEluVHJlZSA9ICh0cmVlKSA9PiB7DQogICAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiB0cmVlKSB7DQogICAgICAgICAgaWYgKG5vZGUuc2NlbmVDb2RlID09PSBzY2VuZUNvZGUpIHsNCiAgICAgICAgICAgIHJldHVybiBub2RlLmlkDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgICBjb25zdCBmb3VuZCA9IGZpbmRJblRyZWUobm9kZS5jaGlsZHJlbikNCiAgICAgICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBudWxsDQogICAgICB9DQogICAgICANCiAgICAgIHJldHVybiBmaW5kSW5UcmVlKHRoaXMuc2NlbmVUcmVlT3B0aW9ucykgfHwgJycNCiAgICB9LA0KICAgIC8vIOWkhOeQhuiDjOaZr+aWh+S7tuWIoOmZpA0KICAgIGhhbmRsZVJlbW92ZUJnRmlsZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLmJnRmlsZVVybCA9ICcnDQogICAgICB0aGlzLmZvcm0uYmdJbWdVcmwgPSAnJyAvLyDlkIzml7bmuIXnqbrog4zmma/lm77niYfpppbluKcNCiAgICAgIHRoaXMuYmdGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuW3suWIoOmZpCcpDQogICAgfSwNCiAgICAvLyDmm7TmlrDog4zmma/mlofku7bliJfooagNCiAgICB1cGRhdGVCZ0ZpbGVMaXN0KCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5iZ0ZpbGVVcmwpIHsNCiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB0aGlzLmZvcm0uYmdGaWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgdGhpcy5iZ0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHRoaXMuZm9ybS5iZ0ZpbGVVcmwsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmJnRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5aSE55CG5LuL57uN6KeG6aKR5paH5Lu25Yig6ZmkDQogICAgaGFuZGxlUmVtb3ZlSW50cm9kdWNlVmlkZW9GaWxlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmludHJvZHVjZVZpZGVvLmJhY2tncm91bmRGaWxlVXJsID0gJycNCiAgICAgIHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEltZ0ZpbGVVcmwgPSAnJyAvLyDlkIzml7bmuIXnqbrpppbluKflm77niYcNCiAgICAgIHRoaXMuaW50cm9kdWNlVmlkZW9GaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S7i+e7jeinhumikeW3suWIoOmZpCcpDQogICAgfSwNCiAgICAvLyDmm7TmlrDku4vnu43op4bpopHmlofku7bliJfooagNCiAgICB1cGRhdGVJbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0KCkgew0KICAgICAgaWYgKHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEZpbGVVcmwpIHsNCiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB0aGlzLmludHJvZHVjZVZpZGVvLmJhY2tncm91bmRGaWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEZpbGVVcmwsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5aSE55CG6K6y6Kej6KeG6aKR5paH5Lu25Yig6ZmkDQogICAgaGFuZGxlUmVtb3ZlVmlkZW9FeHBsYW5hdGlvbkZpbGUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi5iYWNrZ3JvdW5kRmlsZVVybCA9ICcnDQogICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+iusuino+inhumikeW3suWIoOmZpCcpDQogICAgfSwNCiAgICAvLyDmm7TmlrDorrLop6Pop4bpopHmlofku7bliJfooagNCiAgICB1cGRhdGVWaWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QoKSB7DQogICAgICBpZiAodGhpcy52aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gdGhpcy52aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdGhpcy52aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5aSE55CGWE1M5paH5Lu25Yig6ZmkDQogICAgaGFuZGxlUmVtb3ZlWG1sRmlsZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLnBhbm9yYW1pY1ZpZXdYbWxVcmwgPSAnJw0KICAgICAgdGhpcy54bWxGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ1hNTOaWh+S7tuW3suWIoOmZpCcpDQogICAgfSwNCiAgICANCiAgICAvLyDmm7TmlrBYTUzmlofku7bliJfooagNCiAgICB1cGRhdGVYbWxGaWxlTGlzdCgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucGFub3JhbWljVmlld1htbFVybCkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHRoaXMuZm9ybS5wYW5vcmFtaWNWaWV3WG1sVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgdGhpcy54bWxGaWxlTGlzdCA9IFt7DQogICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgdXJsOiB0aGlzLmZvcm0ucGFub3JhbWljVmlld1htbFVybCwNCiAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgfV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMueG1sRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5Zu+54mH6aKE6KeIDQogICAgcHJldmlld0ltYWdlKHVybCkgew0KICAgICAgaWYgKHVybCkgew0KICAgICAgICB0aGlzLnByZXZpZXdJbWFnZVVybCA9IHVybA0KICAgICAgICB0aGlzLnByZXZpZXdWaXNpYmxlID0gdHJ1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgY2xvc2VQcmV2aWV3KCkgew0KICAgICAgdGhpcy5wcmV2aWV3VmlzaWJsZSA9IGZhbHNlDQogICAgICB0aGlzLnByZXZpZXdJbWFnZVVybCA9ICcnDQogICAgfSwNCiAgICAvLyDliKDpmaTog4zmma/lm77niYcNCiAgICBkZWxldGVCZ0ltYWdlKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a5Yig6Zmk5q2k5Zu+54mH5ZCX77yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtLmJnSW1nVXJsID0gJycNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflm77niYflt7LliKDpmaQnKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICBhc3luYyBiZWZvcmVVcGxvYWRYbWxGaWxlKGZpbGUpIHsNCiAgICAgIC8vIOajgOafpeaWh+S7tuexu+Weiw0KICAgICAgaWYgKCFmaWxlLm5hbWUudG9Mb3dlckNhc2UoKS5lbmRzV2l0aCgnLnhtbCcpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oFhNTOaWh+S7tu+8gScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDmo4Dmn6Xmlofku7blpKflsI/vvIg1ME1C77yJDQogICAgICBpZiAoZmlsZS5zaXplID4gNTAgKiAxMDI0ICogMTAyNCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blpKflsI/kuI3og73otoXov4c1ME1C77yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4iuS8oFhNTOaWh+S7tu+8jOivt+eojeWAmS4uLiIpDQogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdpbmR1c3RyeUNvZGUnLCB0aGlzLmluZHVzdHJ5Q29kZSkNCiAgICAgICAgDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZFNjZW5lRmlsZShmb3JtRGF0YSkNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgLy8g6K6+572uWE1M5paH5Lu2VVJMDQogICAgICAgICAgdGhpcy5mb3JtLnBhbm9yYW1pY1ZpZXdYbWxVcmwgPSByZXMuZGF0YS5maWxlVXJsDQogICAgICAgICAgDQogICAgICAgICAgLy8g55u05o6l6KaG55uWWE1M5paH5Lu25YiX6KGoDQogICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSByZXMuZGF0YS5maWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgICB0aGlzLnhtbEZpbGVMaXN0ID0gW3sNCiAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgdXJsOiByZXMuZGF0YS5maWxlVXJsLA0KICAgICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgICAgfV0NCiAgICAgICAgICANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfkuIrkvKDlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDlpLHotKUnKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCkNCiAgICAgIH0NCiAgICAgIHJldHVybiBmYWxzZQ0KICAgIH0sDQogICAgLy8g5Li76aKY5Y+Y5pu05Zue6LCDDQogICAgb25UaGVtZUNoYW5nZSh0aGVtZSkgew0KICAgICAgLy8g5aaC5p6c5Li76aKY5pyJ6buY6K6k6IOM5pmv5Zu+77yM5Y+v5Lul6Ieq5Yqo6K6+572uDQogICAgICBpZiAodGhlbWUgJiYgdGhlbWUuZGVmYXVsdEJnSW1hZ2UpIHsNCiAgICAgICAgdGhpcy5mb3JtLmJnSW1nVXJsID0gdGhlbWUuZGVmYXVsdEJnSW1hZ2UNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOagvOW8j+WMluWdkOagh+aVsOaNrueUqOS6juaPkOS6pA0KICAgIGZvcm1hdENvb3JkaW5hdGVzRm9yU3VibWl0KGNvb3JkaW5hdGVzKSB7DQogICAgICBpZiAoIWNvb3JkaW5hdGVzIHx8ICFBcnJheS5pc0FycmF5KGNvb3JkaW5hdGVzKSkgew0KICAgICAgICByZXR1cm4geyBjbGlja1g6ICcnLCBjbGlja1k6ICcnIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgY29uc3QgeFZhbHVlcyA9IGNvb3JkaW5hdGVzLm1hcChjb29yZCA9PiBjb29yZC54IHx8ICcwJykuam9pbignLCcpDQogICAgICBjb25zdCB5VmFsdWVzID0gY29vcmRpbmF0ZXMubWFwKGNvb3JkID0+IGNvb3JkLnkgfHwgJzAnKS5qb2luKCcsJykNCiAgICAgIA0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgY2xpY2tYOiB4VmFsdWVzLA0KICAgICAgICBjbGlja1k6IHlWYWx1ZXMNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOino+aekOWdkOagh+Wtl+espuS4suS4uuWdkOagh+aVsOe7hA0KICAgIHBhcnNlQ29vcmRpbmF0ZXNGcm9tQXBpKGNsaWNrWCwgY2xpY2tZKSB7DQogICAgICBjb25zdCB4QXJyYXkgPSBjbGlja1ggPyBjbGlja1guc3BsaXQoJywnKSA6IFsnJ10NCiAgICAgIGNvbnN0IHlBcnJheSA9IGNsaWNrWSA/IGNsaWNrWS5zcGxpdCgnLCcpIDogWycnXQ0KICAgICAgDQogICAgICAvLyDlj5bovoPplb/nmoTmlbDnu4Tplb/luqbvvIznoa7kv53lnZDmoIflr7npvZANCiAgICAgIGNvbnN0IG1heExlbmd0aCA9IE1hdGgubWF4KHhBcnJheS5sZW5ndGgsIHlBcnJheS5sZW5ndGgpDQogICAgICBjb25zdCBjb29yZGluYXRlcyA9IFtdDQogICAgICANCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbWF4TGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29vcmRpbmF0ZXMucHVzaCh7DQogICAgICAgICAgeDogeEFycmF5W2ldIHx8ICcnLA0KICAgICAgICAgIHk6IHlBcnJheVtpXSB8fCAnJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDoh7PlsJHkv53or4HmnInkuIDkuKrlnZDmoIfnu4QNCiAgICAgIHJldHVybiBjb29yZGluYXRlcy5sZW5ndGggPiAwID8gY29vcmRpbmF0ZXMgOiBbeyB4OiAnJywgeTogJycgfV0NCiAgICB9LA0KICAgIC8vIOW8gOWni+e8lui+keagh+mimA0KICAgIHN0YXJ0RWRpdFRpdGxlKGluZGV4KSB7DQogICAgICBjb25zdCBjYXRlZ29yeSA9IHRoaXMuY2F0ZWdvcmllc1tpbmRleF0NCiAgICAgIGNhdGVnb3J5LmVkaXRpbmcgPSB0cnVlDQogICAgICBjYXRlZ29yeS5lZGl0aW5nTmFtZSA9IGNhdGVnb3J5Lm5hbWUNCiAgICAgIA0KICAgICAgLy8g5LiL5LiA5bin6IGa54Sm6L6T5YWl5qGGDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIC8vIOS9v+eUqOWKqOaAgXJlZuWQjeensA0KICAgICAgICBjb25zdCBpbnB1dFJlZiA9IHRoaXMuJHJlZnNbYHRpdGxlSW5wdXRfJHtpbmRleH1gXQ0KICAgICAgICBpZiAoaW5wdXRSZWYgJiYgaW5wdXRSZWZbMF0pIHsNCiAgICAgICAgICBpbnB1dFJlZlswXS5mb2N1cygpDQogICAgICAgICAgaW5wdXRSZWZbMF0uc2VsZWN0KCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5a6M5oiQ57yW6L6R5qCH6aKYDQogICAgZmluaXNoRWRpdFRpdGxlKGluZGV4KSB7DQogICAgICBjb25zdCBjYXRlZ29yeSA9IHRoaXMuY2F0ZWdvcmllc1tpbmRleF0NCiAgICAgIGlmIChjYXRlZ29yeS5lZGl0aW5nTmFtZSAmJiBjYXRlZ29yeS5lZGl0aW5nTmFtZS50cmltKCkpIHsNCiAgICAgICAgY2F0ZWdvcnkubmFtZSA9IGNhdGVnb3J5LmVkaXRpbmdOYW1lLnRyaW0oKQ0KICAgICAgfQ0KICAgICAgY2F0ZWdvcnkuZWRpdGluZyA9IGZhbHNlDQogICAgICBjYXRlZ29yeS5lZGl0aW5nTmFtZSA9ICcnDQogICAgfSwNCg0KICAgIC8vIOWPlua2iOe8lui+keagh+mimA0KICAgIGNhbmNlbEVkaXRUaXRsZShpbmRleCkgew0KICAgICAgY29uc3QgY2F0ZWdvcnkgPSB0aGlzLmNhdGVnb3JpZXNbaW5kZXhdDQogICAgICBjYXRlZ29yeS5lZGl0aW5nID0gZmFsc2UNCiAgICAgIGNhdGVnb3J5LmVkaXRpbmdOYW1lID0gJycNCiAgICB9LA0KICAgIC8vIOiuvue9ruS4iuS8oOaooeW8jw0KICAgIHNldFVwbG9hZE1vZGUodHlwZSwgbW9kZSkgew0KICAgICAgdGhpcy4kc2V0KHRoaXMudXBsb2FkTW9kZXMsIHR5cGUsIG1vZGUpDQogICAgfSwNCiAgICAvLyDog4zmma/mlofku7bpk77mjqXovpPlhaXlpITnkIYNCiAgICBoYW5kbGVCZ0ZpbGVVcmxJbnB1dCh2YWx1ZSkgew0KICAgICAgdGhpcy5iZ0ZpbGVMaXN0ID0gW10NCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy5iZ0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHZhbHVlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6KeG6aKR6K6y6Kej6ZO+5o6l6L6T5YWl5aSE55CGDQogICAgaGFuZGxlVmlkZW9FeHBsYW5hdGlvblVybElucHV0KHZhbHVlKSB7DQogICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCA9IFtdDQogICAgICBpZiAodmFsdWUpIHsNCiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB2YWx1ZS5zcGxpdCgnLycpLnBvcCgpIHx8ICflpJbpg6jpk77mjqXmlofku7YnDQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHZhbHVlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5LuL57uN6KeG6aKR6ZO+5o6l6L6T5YWl5aSE55CGDQogICAgaGFuZGxlSW50cm9kdWNlVmlkZW9VcmxJbnB1dCh2YWx1ZSkgew0KICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW10NCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHZhbHVlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5ZCM5q2l5paH5Lu2DQogICAgYXN5bmMgaGFuZGxlU3luY2hyb25pemVGaWxlKCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0uc2NlbmVWaWV3Q29uZmlnSWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjkv53lrZjphY3nva7lkI7lho3lkIzmraXmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5zeW5jaHJvbml6aW5nID0gdHJ1ZQ0KICAgICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjlkIzmraXmlofku7bvvIzor7fnqI3lgJkuLi4iKQ0KICAgICAgICANCiAgICAgICAgLy8g5L2/55SoRm9ybURhdGHmiJZVUkxTZWFyY2hQYXJhbXPkvKDpgJLooajljZXlj4LmlbANCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ3ZpZXdDb25maWdJZCcsIHRoaXMuZm9ybS5zY2VuZVZpZXdDb25maWdJZCkNCiAgICAgICAgDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHN5bmNocm9uaXphdGlvbkZpbGUoZm9ybURhdGEpDQogICAgICAgIA0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzLm1zZykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+aWh+S7tuWQjOatpeWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WQjOatpeaWh+S7tuWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25ZCM5q2l5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuc3luY2hyb25pemluZyA9IGZhbHNlDQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, null]}