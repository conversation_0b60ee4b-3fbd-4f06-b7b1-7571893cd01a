<template>
  <div class="page-container">
    <!-- ✅ 左侧菜单区域 -->
    <div class="menu-panel">
      <el-menu
        :default-active="activeMenu"
        class="menu-list"
        @select="handleSelect"
        background-color="#f5f7fa"
        text-color="#333"
        active-text-color="#409EFF"
      >
        <el-menu-item
          v-for="item in menuData"
          :key="item.id"
          :index="String(item.id)"
        >
          {{ item.industryName }}
        </el-menu-item>
      </el-menu>
    </div>

    <!-- ✅ 右侧内容区域 -->
    <div class="content-panel">
      <el-form :model="form" :rules="rules" ref="sceneForm" label-width="120px">
        <el-form-item label="主标题" required>
          <el-input v-model="form.mainTitle" placeholder="请输入主标题" />
        </el-form-item>

        <el-form-item label="副标题" required>
          <el-input v-model="form.subTitle" placeholder="请输入副标题" />
        </el-form-item>

        <el-form-item label="背景图片首帧">
          <el-upload
            class="upload"
            action="#"
            :show-file-list="!!form.bgImgUrl"
            list-type="picture-card"
            accept="image/*"
            :before-upload="beforeUploadIntroduceImg"
          >
            <img v-if="form.bgImgUrl" :src="form.bgImgUrl" class="el-upload-list__item-thumbnail" />
            <i v-else class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>

        <el-form-item label="背景文件" required>
          <el-upload
            class="upload"
            action="#"
            :show-file-list="false"
            :before-upload="handleBeforeUpload"
          >
            <el-button type="primary">上传背景文件</el-button>
          </el-upload>
        </el-form-item>

        <!-- ✅ 分类区域（支持折叠） -->
        <div
          v-for="(category, index) in categories"
          :key="category.key"
          class="category-block"
        >
          <div class="category-header">
            <div class="category-title">{{ category.name }}</div>
            <el-switch
              v-model="category.enabled"
              active-color="#13ce66"
              inactive-color="#ccc"
            />
          </div>

          <div v-show="category.enabled" class="category-body">
            <!-- 行业应用场景 -->
            <div v-if="category.key === 'default_scene'">
              <!-- 1. 介绍视频 -->
              <div class="sub-category-block">
                <div class="sub-category-header">
                  <span class="sub-category-title">介绍视频</span>
                  <el-switch v-model="form.introduceVideoStatus" />
                </div>
                <div v-show="form.introduceVideoStatus" class="sub-category-body">
                  <el-form-item label="介绍视频首帧" prop="introduceVideoImgUrl" v-if="form.introduceVideoStatus">
                    <el-upload
                      action="#"
                      :show-file-list="!!form.introduceVideoImgUrl"
                      list-type="picture-card"
                      accept="image/*"
                      :before-upload="beforeUploadIntroduceImg"
                    >
                      <img v-if="form.introduceVideoImgUrl" :src="form.introduceVideoImgUrl" class="el-upload-list__item-thumbnail" />
                      <i v-else class="el-icon-plus"></i>
                    </el-upload>
                  </el-form-item>
                  <el-form-item label="介绍视频" prop="introduceVideoFileUrl" v-if="form.introduceVideoStatus">
                    <el-upload
                      action="#"
                      :show-file-list="!!form.introduceVideoFileUrl"
                      accept=".mp4"
                      :before-upload="beforeUploadIntroduceVideo"
                    >
                      <el-button size="small" type="primary">点击上传</el-button>
                      <div slot="tip" class="el-upload__tip">只能上传mp4文件</div>
                    </el-upload>
                    <span v-if="form.introduceVideoFileUrl">{{ form.introduceVideoFileUrl }}</span>
                  </el-form-item>
                </div>
              </div>

              <!-- 2. 视频讲解 -->
              <div class="sub-category-block">
                <div class="sub-category-header">
                  <span class="sub-category-title">视频讲解</span>
                  <el-switch v-model="form.videoExplanationStatus" />
                </div>
                <div v-show="form.videoExplanationStatus" class="sub-category-body">
                  <el-form-item label="讲解视频" prop="videoExplanationFileUrl" v-if="form.videoExplanationStatus">
                    <el-upload
                      action="#"
                      :show-file-list="!!form.videoExplanationFileUrl"
                      accept=".mp4"
                      :before-upload="beforeUploadExplanationVideo"
                    >
                      <el-button size="small" type="primary">点击上传</el-button>
                      <div slot="tip" class="el-upload__tip">只能上传mp4文件</div>
                    </el-upload>
                    <span v-if="form.videoExplanationFileUrl">{{ form.videoExplanationFileUrl }}</span>
                  </el-form-item>
                  <el-form-item label="视频分段说明">
                    <div
                      v-for="(segment, index) in form.videoSegmentedVoList"
                      :key="index"
                      class="segment-item"
                    >
                      <el-input v-model="segment.time" placeholder="时间" style="width: 120px; margin-right: 10px;" />
                      <el-input v-model="segment.scene" placeholder="场景说明" style="margin-right: 10px;" />
                      <el-button type="danger" icon="el-icon-delete" circle @click="removeSegment(index)" />
                    </div>
                    <el-button type="primary" plain @click="addSegment">增加分段</el-button>
                  </el-form-item>
                </div>
              </div>

              <!-- 3. 场景配置 -->
              <div class="sub-category-block scene-config-container">
                <div class="sub-category-header">
                  <span class="sub-category-title">场景配置</span>
                </div>
                <div class="sub-category-body">
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <el-tree
                        :data="sceneConfigTree"
                        node-key="id"
                        :props="{ label: 'name', children: 'children' }"
                        @node-click="handleSceneNodeClick"
                        highlight-current
                        :expand-on-click-node="false"
                        :default-expanded-keys="sceneConfigTree.length ? [sceneConfigTree[0].id] : []"
                        :default-checked-keys="selectedNode ? [selectedNode.id] : []"
                      />
                    </el-col>
                    <el-col :span="18">
                      <scene-config-node v-if="selectedNode" :node="selectedNode" :rootTree="sceneConfigTree" />
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>

            <!-- 其他分类的占位内容 -->
            <div v-else>
              <p>这里是 <strong>{{ category.name }}</strong> 分类的内容区域。</p>
              <!-- 🔧 可在此添加组件/表单/展示逻辑 -->
            </div>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getIndustryList } from '@/api/view/industry'
import SceneConfigNode from './SceneConfigNode.vue'

export default {
  name: 'IndustryScenePage',
  components: {
    SceneConfigNode
  },
  data() {
    return {
      menuData: [],
      activeMenu: '',
      form: {
        mainTitle: '',
        subTitle: '',
        // 行业应用场景
        introduceVideoStatus: true,
        introduceVideoImgUrl: '',
        introduceVideoFileUrl: '',
        videoExplanationStatus: true,
        videoExplanationFileUrl: '',
        videoSegmentedVoList: []
      },
      sceneConfigTree: [],
      selectedNode: null,
      rules: {
        introduceVideoImgUrl: [
          { required: true, message: '请上传介绍视频首帧', trigger: 'change' }
        ],
        introduceVideoFileUrl: [
          { required: true, message: '请上传介绍视频', trigger: 'change' }
        ],
        videoExplanationFileUrl: [
          { required: true, message: '请上传讲解视频', trigger: 'change' }
        ]
      },
      uploadingType: '',
      uploadingKey: '',
      categories: [
        { name: '行业应用场景', key: 'default_scene', enabled: true },
        { name: '网络方案', key: 'default_plan', enabled: false },
        { name: '商业价值', key: 'default_value', enabled: false },
        { name: '落地案例', key: 'default_case', enabled: false },
        { name: '新手指引', key: 'default_guidance', enabled: false },
        { name: '行业生态链', key: 'default_ecology', enabled: false },
        { name: '集成报价器', key: 'default_quotation', enabled: false },
        { name: 'VR 看现场', key: 'default_vr', enabled: false },
        { name: '视频讲解', key: 'default_video', enabled: false },
        { name: '购物车', key: 'default_shopping', enabled: false },
        { name: '优化建议', key: 'default_suggestion', enabled: false },
        { name: '点赞', key: 'default_giveALike', enabled: false }
      ]
    }
  },
  created() {
    this.loadIndustryMenu()
    // 测试数据适配
    const raw = {
      "sceneInfoId": null,
      "sceneId": 7601897143697408,
      "paramId": null,
      "sceneName": "泛住宿酒店",
      "sceneCode": "ZXQY00",
      "x": null,
      "y": null,
      "type": null,
      "status": null,
      "sceneTraditionVo": {
          "name": null,
          "backgroundImgFileUrl": null,
          "backgroundFileUrl": null,
          "painPointList": {
              "painPointId": null,
              "bigTitle": null,
              "type": null,
              "content": [],
              "displayTime": null
          }
      },
      "scene5gVo": {
          "name": null,
          "backgroundImgFileUrl": null,
          "backgroundFileUrl": null,
          "painPointList": {
              "painPointId": null,
              "bigTitle": null,
              "type": null,
              "content": [],
              "displayTime": null
          }
      },
      "costEstimationInfoVo": {
          "bigTitle": null,
          "content": []
      },
      "children": [
          {
              "sceneInfoId": null,
              "sceneId": 7601898026139648,
              "paramId": 7601897143697408,
              "sceneName": "运营管理",
              "sceneCode": "ZXQY000",
              "x": null,
              "y": null,
              "type": null,
              "status": null,
              "sceneTraditionVo": {
                  "name": null,
                  "backgroundImgFileUrl": null,
                  "backgroundFileUrl": null,
                  "painPointList": {
                      "painPointId": null,
                      "bigTitle": null,
                      "type": null,
                      "content": [],
                      "displayTime": null
                  }
              },
              "scene5gVo": {
                  "name": null,
                  "backgroundImgFileUrl": null,
                  "backgroundFileUrl": null,
                  "painPointList": {
                      "painPointId": null,
                      "bigTitle": null,
                      "type": null,
                      "content": [],
                      "displayTime": null
                  }
              },
              "costEstimationInfoVo": {
                  "bigTitle": null,
                  "content": []
              },
              "children": [
                  {
                      "sceneInfoId": null,
                      "sceneId": 7329952946110464,
                      "paramId": 7601898026139648,
                      "sceneName": "泛住宿酒店-运营管理-无缝漫游",
                      "sceneCode": "ZXQY31",
                      "x": null,
                      "y": null,
                      "type": null,
                      "status": null,
                      "sceneTraditionVo": null,
                      "scene5gVo": null,
                      "costEstimationInfoVo": null,
                      "children": []
                  },
                  {
                      "sceneInfoId": null,
                      "sceneId": 7329952108445696,
                      "paramId": 7601898026139648,
                      "sceneName": "泛住宿酒店-运营管理-安防监控",
                      "sceneCode": "ZXQY30",
                      "x": null,
                      "y": null,
                      "type": null,
                      "status": null,
                      "sceneTraditionVo": null,
                      "scene5gVo": null,
                      "costEstimationInfoVo": null,
                      "children": []
                  },
                  {
                      "sceneInfoId": null,
                      "sceneId": 7329950450302976,
                      "paramId": 7601898026139648,
                      "sceneName": "泛住宿酒店-运营管理-设备管家",
                      "sceneCode": "ZXQY28",
                      "x": null,
                      "y": null,
                      "type": null,
                      "status": null,
                      "sceneTraditionVo": null,
                      "scene5gVo": null,
                      "costEstimationInfoVo": null,
                      "children": []
                  },
                  {
                      "sceneInfoId": null,
                      "sceneId": 7329951540609024,
                      "paramId": 7601898026139648,
                      "sceneName": "泛住宿酒店-运营管理-物联消防",
                      "sceneCode": "ZXQY29",
                      "x": null,
                      "y": null,
                      "type": null,
                      "status": null,
                      "sceneTraditionVo": null,
                      "scene5gVo": null,
                      "costEstimationInfoVo": null,
                      "children": []
                  }
              ]
          }
      ]
    }
    function adaptSceneTree(rawTree, parent = null) {
      if (!rawTree) return []
      // 兼容单对象和数组
      const arr = Array.isArray(rawTree) ? rawTree : [rawTree]
      return arr.map(node => {
        const adapted = {
          id: node.sceneId,
          name: node.sceneName,
          code: node.sceneCode,
          x: node.x,
          y: node.y,
          type: node.type,
          status: node.status !== null ? node.status : true,
          tradition: node.sceneTraditionVo ? {
            name: node.sceneTraditionVo.name || '',
            bgImg: node.sceneTraditionVo.backgroundImgFileUrl || '',
            bgFile: node.sceneTraditionVo.backgroundFileUrl || '',
            painPoints: node.sceneTraditionVo.painPointList ?
              (Array.isArray(node.sceneTraditionVo.painPointList) ? node.sceneTraditionVo.painPointList.map(p => ({
                title: p.bigTitle || '',
                contents: p.content || [],
                showTime: p.displayTime || ''
              })) :
              (node.sceneTraditionVo.painPointList.bigTitle ? [{
                title: node.sceneTraditionVo.painPointList.bigTitle || '',
                contents: node.sceneTraditionVo.painPointList.content || [],
                showTime: node.sceneTraditionVo.painPointList.displayTime || ''
              }] : [])
            ) : []
          } : { name: '', bgImg: '', bgFile: '', painPoints: [] },
          wisdom5g: node.scene5gVo ? {
            name: node.scene5gVo.name || '',
            bgImg: node.scene5gVo.backgroundImgFileUrl || '',
            bgFile: node.scene5gVo.backgroundFileUrl || '',
            painPoints: node.scene5gVo.painPointList ?
              (Array.isArray(node.scene5gVo.painPointList) ? node.scene5gVo.painPointList.map(p => ({
                title: p.bigTitle || '',
                contents: p.content || [],
                showTime: p.displayTime || ''
              })) :
              (node.scene5gVo.painPointList.bigTitle ? [{
                title: node.scene5gVo.painPointList.bigTitle || '',
                contents: node.scene5gVo.painPointList.content || [],
                showTime: node.scene5gVo.painPointList.displayTime || ''
              }] : [])
            ) : []
          } : { name: '', bgImg: '', bgFile: '', painPoints: [] },
          costEstimate: node.costEstimationInfoVo ? {
            title: node.costEstimationInfoVo.bigTitle || '',
            contents: node.costEstimationInfoVo.content || []
          } : { title: '', contents: [] },
          children: [],
          parent
        }
        adapted.children = node.children ? adaptSceneTree(node.children, adapted) : []
        return adapted
      })
    }
    this.sceneConfigTree = adaptSceneTree(raw)
    // 默认选中第一个节点
    if (this.sceneConfigTree.length > 0) {
      this.selectedNode = this.sceneConfigTree[0]
    }
  },
  methods: {
    async loadIndustryMenu() {
      try {
        const res = await getIndustryList()
        if (res.code === 0 && Array.isArray(res.data)) {
          this.menuData = res.data
          this.activeMenu = res.data.length ? String(res.data[0].id) : ''
        }
      } catch (error) {
        this.$message.error('加载行业列表失败')
      }
    },
    handleSelect(id) {
      this.activeMenu = id
    },
    handleBeforeUpload(file) {
      console.log('模拟上传：', file.name)
      return false // 拦截默认上传行为
    },
    addSegment() {
      this.form.videoSegmentedVoList.push({ time: '', scene: '' })
    },
    removeSegment(index) {
      if (this.form.videoSegmentedVoList.length >= 1) {
        this.form.videoSegmentedVoList.splice(index, 1)
      }
    },
    beforeUploadIntroduceImg(file) {
      this.uploadingType = 'img'
      this.uploadingKey = 'introduceVideoImgUrl'
      return this.handleBeforeUpload(file)
    },
    beforeUploadIntroduceVideo(file) {
      this.uploadingType = 'mp4'
      this.uploadingKey = 'introduceVideoFileUrl'
      return this.handleBeforeUpload(file)
    },
    beforeUploadExplanationVideo(file) {
      this.uploadingType = 'mp4'
      this.uploadingKey = 'videoExplanationFileUrl'
      return this.handleBeforeUpload(file)
    },
    // 新增方法：添加场景配置节点
    addSceneConfigNode(parentId = null) {
      const newNode = {
        id: Date.now(), // 生成唯一ID
        name: '新场景',
        type: 'scene', // 类型为场景
        enabled: true,
        children: [],
        parentId: parentId
      }
      if (parentId) {
        const parentNode = this.findNodeById(this.sceneConfigTree, parentId)
        if (parentNode) {
          parentNode.children.push(newNode)
        }
      } else {
        this.sceneConfigTree.push(newNode)
      }
      return newNode.id
    },
    // 新增方法：删除场景配置节点
    removeSceneConfigNode(nodeId) {
      this.sceneConfigTree = this.sceneConfigTree.filter(node => node.id !== nodeId)
    },
    // 新增方法：查找节点
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id)
          if (found) {
            return found
          }
        }
      }
      return null
    },
    // 新增方法：添加场景的痛点价值
    addScenePainPoint(nodeId) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'scene') {
        node.painPoints = node.painPoints || []
        node.painPoints.push({ title: '', contents: [''], showTime: '' })
      }
    },
    // 新增方法：删除场景的痛点价值
    removeScenePainPoint(nodeId, idx) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'scene') {
        node.painPoints = node.painPoints || []
        node.painPoints.splice(idx, 1)
      }
    },
    // 新增方法：添加场景痛点内容的项
    addScenePainContent(nodeId, idx) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'scene') {
        node.painPoints = node.painPoints || []
        node.painPoints[idx].contents.push('')
      }
    },
    // 新增方法：删除场景痛点内容的项
    removeScenePainContent(nodeId, idx, cidx) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'scene') {
        node.painPoints = node.painPoints || []
        node.painPoints[idx].contents.splice(cidx, 1)
      }
    },
    // 新增方法：添加场景的成本预估内容
    addSceneCostContent(nodeId) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'costEstimate') {
        node.contents = node.contents || []
        node.contents.push('')
      }
    },
    // 新增方法：删除场景的成本预估内容
    removeSceneCostContent(nodeId, cidx) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'costEstimate') {
        node.contents = node.contents || []
        node.contents.splice(cidx, 1)
      }
    },
    // 新增方法：上传场景配置图片
    beforeUploadSceneConfigImg(file, type, key) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件！')
        return false
      }
      const reader = new FileReader()
      reader.onload = e => {
        this.findNodeById(this.sceneConfigTree, type)[key] = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },
    // 新增方法：上传场景配置文件
    beforeUploadSceneConfigFile(file, type, key) {
      // 这里只做文件名回显
      this.findNodeById(this.sceneConfigTree, type)[key] = file.name
      return false
    },
    handleSceneNodeClick(node) {
      this.selectedNode = node
    }
  }
}
</script>

<style scoped>
.page-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.menu-panel {
  width: 250px;
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.menu-list {
  width: 100%;
  border: none;
}

.content-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #fff;
}

.category-block {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 15px;
  background-color: #f9f9f9;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.category-title {
  font-weight: bold;
  font-size: 16px;
}

.category-body {
  padding: 12px;
  background: #ffffff;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.sub-category-block {
  margin-bottom: 15px;
}
.sub-category-block:last-child {
  margin-bottom: 0;
}

.sub-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

.sub-category-title {
  font-weight: 500;
}

.sub-category-body {
  padding: 15px;
}

.segment-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.pain-point-block {
  margin-bottom: 15px;
}

.pain-point-block:last-child {
  margin-bottom: 0;
}

.mini-block {
  margin-bottom: 15px;
}

.mini-block:last-child {
  margin-bottom: 0;
}
</style>
