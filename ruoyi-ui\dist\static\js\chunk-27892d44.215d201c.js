(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-27892d44","chunk-afcb12b4","chunk-2d0dece3"],{"20f8":function(e,t,i){"use strict";i("7595")},"39e2":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"theme-selection-container"},[e._m(0),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading&&1===e.currentPage,expression:"loading && currentPage === 1"}],ref:"themeGrid",staticClass:"theme-grid",on:{scroll:e.handleScroll}},e._l(e.themeOptions,(function(t){return i("div",{key:t.themeId,staticClass:"theme-card",class:{selected:e.selectedTheme&&e.selectedTheme.themeId===t.themeId},on:{click:function(i){return e.selectTheme(t)}}},[i("div",{staticClass:"theme-preview"},[i("img",{attrs:{src:t.themeEffectImg,alt:t.themeName}}),i("div",{staticClass:"theme-overlay"},[e.selectedTheme&&e.selectedTheme.themeId===t.themeId?i("i",{staticClass:"el-icon-check"}):e._e(),i("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览大图"},on:{click:function(i){return i.stopPropagation(),e.previewImage(t.themeEffectImg)}}})])]),i("div",{staticClass:"theme-info"},[i("h3",[e._v(e._s(t.themeName))])])])})),0),e.loadingMore?i("div",{staticClass:"loading-more"},[i("i",{staticClass:"el-icon-loading"}),i("span",[e._v("加载中...")])]):e._e(),!e.hasMore&&e.themeOptions.length>0?i("div",{staticClass:"no-more"},[i("span",[e._v("没有更多数据了")])]):e._e(),e.loading||e.themeOptions.length?e._e():i("div",{staticClass:"empty-state"},[i("i",{staticClass:"el-icon-picture-outline"}),i("p",[e._v("暂无主题数据")])]),i("div",{staticClass:"theme-actions"},[i("el-button",{on:{click:function(t){return e.$emit("cancel")}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",disabled:!e.selectedTheme},on:{click:e.confirmSelection}},[e._v("确认选择")])],1),i("el-dialog",{attrs:{visible:e.previewVisible,title:"主题预览",width:"60%","append-to-body":""},on:{"update:visible":function(t){e.previewVisible=t},close:e.closePreview}},[i("div",{staticClass:"preview-container"},[i("img",{staticClass:"preview-image",attrs:{src:e.previewImageUrl}})])])],1)},a=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"theme-header"},[i("h2",[e._v("选择主题风格")]),i("p",[e._v("为您的行业场景选择合适的主题风格")])])}],s=i("c7eb"),c=i("2909"),r=i("1da1"),o=(i("99af"),i("86e4")),l={name:"ThemeSelection",data:function(){return{selectedTheme:null,themeOptions:[],loading:!1,loadingMore:!1,currentPage:1,pageSize:20,hasMore:!0,previewVisible:!1,previewImageUrl:""}},created:function(){this.loadThemeList()},methods:{loadThemeList:function(){var e=arguments,t=this;return Object(r["a"])(Object(s["a"])().mark((function i(){var n,a;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n=e.length>0&&void 0!==e[0]&&e[0],n?t.loadingMore=!0:(t.loading=!0,t.currentPage=1,t.themeOptions=[],t.hasMore=!0),i.prev=2,i.next=5,Object(o["d"])({page:t.currentPage,limit:t.pageSize});case 5:a=i.sent,0===a.code&&Array.isArray(a.data)?(t.themeOptions=n?[].concat(Object(c["a"])(t.themeOptions),Object(c["a"])(a.data)):a.data,t.hasMore=a.data.length===t.pageSize):t.$message.error(a.msg||"获取主题列表失败"),i.next=12;break;case 9:i.prev=9,i.t0=i["catch"](2),t.$message.error("获取主题列表失败");case 12:return i.prev=12,t.loading=!1,t.loadingMore=!1,i.finish(12);case 16:case"end":return i.stop()}}),i,null,[[2,9,12,16]])})))()},handleScroll:function(e){var t=e.target,i=t.scrollTop,n=t.scrollHeight,a=t.clientHeight;i+a>=n-50&&this.loadMore()},loadMore:function(){var e=this;return Object(r["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loadingMore&&e.hasMore){t.next=2;break}return t.abrupt("return");case 2:return e.currentPage++,t.next=5,e.loadThemeList(!0);case 5:case"end":return t.stop()}}),t)})))()},getThemeColor:function(e){var t=["#409EFF","#13ce66","#f56c6c","#909399","#e6a23c"],i=parseInt(e.themeId)%t.length;return t[i]},selectTheme:function(e){this.selectedTheme=e},confirmSelection:function(){this.selectedTheme?this.$emit("confirm",this.selectedTheme):this.$message.warning("请先选择一个主题")},previewImage:function(e){e&&(this.previewImageUrl=e,this.previewVisible=!0)},closePreview:function(){this.previewVisible=!1,this.previewImageUrl=""}}},d=l,u=(i("20f8"),i("2877")),h=Object(u["a"])(d,n,a,!1,null,"47dcd395",null);t["default"]=h.exports},7595:function(e,t,i){},8590:function(e,t,i){},"86e4":function(e,t,i){"use strict";i.d(t,"c",(function(){return s})),i.d(t,"d",(function(){return c})),i.d(t,"f",(function(){return r})),i.d(t,"e",(function(){return o})),i.d(t,"a",(function(){return l})),i.d(t,"b",(function(){return d})),i.d(t,"g",(function(){return u})),i.d(t,"h",(function(){return h}));var n=i("5530"),a=i("b775");function s(e){return Object(a["a"])({url:"/sceneConfig/detail",method:"get",params:e})}function c(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(a["a"])({url:"/sceneConfig/theme/list",method:"get",params:Object(n["a"])({page:1,limit:20},e)})}function r(e){return Object(a["a"])({url:"/sceneConfig/upd",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/sceneConfig/sceneFileInfo/del",method:"post",data:e})}function l(e){return Object(a["a"])({url:"/sceneConfig/background/file/del",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/sceneConfig/file/bind/del",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/sceneConfig/synchronization/file",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function h(e){var t;return e instanceof FormData?t=e:(t=new FormData,t.append("file",e)),Object(a["a"])({url:"/sceneConfig/upload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}},a5f5:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"theme-selector"},[e.selectedTheme?i("div",{staticClass:"selected-theme-preview"},[i("img",{staticClass:"theme-preview-img",attrs:{src:e.selectedTheme.themeEffectImg,alt:e.selectedTheme.themeName}}),i("div",{staticClass:"theme-preview-info"},[i("div",{staticClass:"theme-name"},[e._v(e._s(e.selectedTheme.themeName))])])]):i("el-button",{attrs:{type:"primary",plain:""},on:{click:e.openDialog}},[e._v("选择主题")]),e.selectedTheme?i("el-button",{attrs:{size:"small"},on:{click:e.openDialog}},[e._v("更换主题")]):e._e()],1),i("el-dialog",{attrs:{visible:e.dialogVisible,width:"80%","before-close":e.closeDialog,"show-close":!0},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("theme-selection",{on:{confirm:e.onThemeConfirm,cancel:e.closeDialog}})],1)],1)},a=[],s=i("39e2"),c={name:"ThemeSelectionDialog",components:{ThemeSelection:s["default"]},props:{value:{type:Object,default:null}},data:function(){return{dialogVisible:!1,selectedTheme:this.value}},watch:{value:function(e){this.selectedTheme=e},selectedTheme:function(e){this.$emit("input",e)}},methods:{openDialog:function(){this.dialogVisible=!0},closeDialog:function(){this.dialogVisible=!1},onThemeConfirm:function(e){this.selectedTheme=e,this.dialogVisible=!1,this.$emit("change",e),this.$message.success("已选择".concat(e.themeName,"主题"))}}},r=c,o=(i("d62a"),i("2877")),l=Object(o["a"])(r,n,a,!1,null,"b2b0aaba",null);t["default"]=l.exports},d62a:function(e,t,i){"use strict";i("8590")}}]);