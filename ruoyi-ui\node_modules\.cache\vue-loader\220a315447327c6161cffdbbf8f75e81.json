{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue?vue&type=template&id=2879166c&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue", "mtime": 1754893059597}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1743599730124}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}