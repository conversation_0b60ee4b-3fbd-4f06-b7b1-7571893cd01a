{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue", "mtime": 1754893059597}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\babel.config.js", "mtime": 1753326339083}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1743599728056}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}