(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3ee2cffa"],{"5c6e":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"network-plan-config"},[i("el-card",{staticClass:"video-explanation-card",attrs:{shadow:"never"}},[i("div",{staticClass:"video-header-row",attrs:{slot:"header"},slot:"header"},[i("span",[e._v("视频讲解")]),i("el-switch",{staticStyle:{float:"right"},attrs:{"active-value":"0","inactive-value":"1"},on:{change:e.handleVideoExplanationStatusChange},model:{value:e.videoExplanation.status,callback:function(t){e.$set(e.videoExplanation,"status",t)},expression:"videoExplanation.status"}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:"0"===e.videoExplanation.status,expression:"videoExplanation.status === '0'"}]},[i("el-form",{attrs:{"label-width":"120px",size:"small"}},[i("el-form-item",{attrs:{label:"讲解视频"}},[i("div",{staticStyle:{"margin-bottom":"8px"}},[i("el-radio-group",{attrs:{size:"small"},on:{input:e.handleVideoExplanationModeChange},model:{value:e.videoExplanationUploadMode,callback:function(t){e.videoExplanationUploadMode=t},expression:"videoExplanationUploadMode"}},[i("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),i("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===e.videoExplanationUploadMode?i("el-upload",{key:"video-explanation-"+(e.videoExplanation.backgroundFileUrl?"has":"empty"),attrs:{action:"#","show-file-list":!0,"file-list":e.videoExplanationFileList,accept:".mp4","before-upload":e.beforeUploadExplanationVideo,"http-request":function(){},"on-remove":e.handleRemoveVideoExplanationFile}},[i("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传mp4文件")])],1):i("el-input",{attrs:{value:e.videoExplanation.backgroundFileUrl,placeholder:"请输入视频链接"},on:{input:e.handleVideoExplanationUrlInput}})],1)],1)],1)]),i("div",{staticClass:"config-header",staticStyle:{"margin-top":"20px"}},[i("span",[e._v("网络方案配置")]),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.addNetworkPlan}},[i("i",{staticClass:"el-icon-plus"}),e._v(" 添加方案 ")])],1),i("div",{staticClass:"plan-list"},[i("el-row",{attrs:{gutter:20}},e._l(e.networkPlans,(function(t,a){return i("el-col",{key:a,attrs:{span:12}},[i("el-card",{staticClass:"plan-item",attrs:{shadow:"hover"}},[i("div",{staticClass:"plan-header",attrs:{slot:"header"},slot:"header"},[i("span",[e._v("方案 "+e._s(a+1))]),i("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.removePlan(a)}}})],1),i("el-form",{attrs:{model:t,"label-width":"80px",size:"small"}},[i("el-form-item",{attrs:{label:"名称",required:""}},[i("el-input",{attrs:{placeholder:"请输入方案名称"},on:{input:e.debouncedEmitChange},model:{value:t.tag,callback:function(i){e.$set(t,"tag",i)},expression:"plan.tag"}})],1),i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"图片"}},[i("el-upload",{key:"image-"+a+"-"+(t.id||"new"),staticClass:"image-upload",attrs:{action:"#","show-file-list":!1,"list-type":"picture-card",accept:"image/*","before-upload":function(t){return e.beforeUploadImage(t,a)},"http-request":function(){}}},[t.imageUrl?i("div",{staticClass:"image-preview-container"},[i("img",{staticClass:"upload-image",attrs:{src:t.imageUrl}}),i("div",{staticClass:"image-overlay"},[i("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览"},on:{click:function(i){return i.stopPropagation(),e.previewImage(t.imageUrl)}}}),i("i",{staticClass:"el-icon-delete delete-icon",attrs:{title:"删除"},on:{click:function(t){return t.stopPropagation(),e.deletePlanImage(a)}}})])]):i("i",{staticClass:"el-icon-plus"})]),i("div",{staticClass:"upload-tip"},[e._v("支持jpg/png，最大20MB")])],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"视频文件"}},[i("div",{staticStyle:{"margin-bottom":"8px"}},[i("el-radio-group",{attrs:{value:e.getUploadMode("video",a),size:"small"},on:{input:function(t){return e.setUploadMode("video",a,t)}}},[i("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),i("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===e.getUploadMode("video",a)?i("el-upload",{key:"video-"+a+"-"+(t.id||"new")+"-"+(t.videoUrl?"has":"empty"),attrs:{action:"#","show-file-list":!0,"file-list":t.videoFileList,accept:"video/*","before-upload":function(t){return e.beforeUploadVideo(t,a)},"http-request":function(){},"on-remove":function(t){return e.handleRemoveVideo(t,a)}}},[i("el-button",{attrs:{size:"small",type:"primary"}},[e._v("选择视频")]),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持mp4等格式，最大200MB")])],1):i("el-input",{attrs:{value:t.videoUrl,placeholder:"请输入视频链接"},on:{input:function(t){return e.handleVideoUrlInput(t,a)}}})],1)],1)],1)],1)],1)],1)})),1),e.networkPlans.length?e._e():i("div",{staticClass:"empty-state"},[i("i",{staticClass:"el-icon-document-add"}),i("p",[e._v("暂无网络方案，点击上方按钮添加")])])],1),i("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览",width:"60%","append-to-body":""},on:{"update:visible":function(t){e.previewVisible=t}}},[i("div",{staticClass:"preview-container"},[i("img",{staticClass:"preview-image",attrs:{src:e.previewImageUrl}})])])],1)},n=[],o=i("b85c"),s=i("c7eb"),l=i("1da1"),r=(i("d81d"),i("14d9"),i("a434"),i("b0c0"),i("e9c4"),i("ac1f"),i("00b4"),i("8a79"),i("5319"),i("2ca0"),i("86e4")),d={name:"NetworkPlanConfig",props:{value:{type:Object,default:function(){return{networkVideoList:[],videoExplanationVo:{status:"0",backgroundFileUrl:"",videoSegmentedVoList:[]}}}},sceneTreeOptions:{type:Array,default:function(){return[]}},leftTreeIndustryCode:{type:String,default:""}},data:function(){return{networkPlans:[],videoExplanation:{status:"0",backgroundFileUrl:"",videoSegmentedVoList:[]},videoExplanationFileList:[],sceneCascaderProps:{label:"sceneName",value:"id",children:"children",emitPath:!1},isUpdating:!1,emitTimer:null,videoUploadModes:{},videoExplanationUploadMode:"upload",previewVisible:!1,previewImageUrl:""}},computed:{videoSegmentedList:function(){return this.videoExplanation.videoSegmentedVoList&&0!==this.videoExplanation.videoSegmentedVoList.length?this.videoExplanation.videoSegmentedVoList:[{time:"",sceneId:"",sceneName:"",sceneCode:""}]}},watch:{value:{handler:function(e){if(!this.isUpdating&&e){var t,i,a,n=(e.networkVideoList||[]).map((function(e){return{id:e.id||null,tag:e.tag||"",x:e.clickX||"",y:e.clickY||"",width:e.wide||0,height:e.high||0,imageUrl:e.backgroundImgFileUrl||"",videoUrl:e.backgroundFileUrl||"",videoFileList:e.backgroundFileUrl?[{name:e.backgroundFileUrl.split("/").pop(),url:e.backgroundFileUrl,uid:Date.now()}]:[]}}));JSON.stringify(this.networkPlans)!==JSON.stringify(n)&&(this.networkPlans=n);var o={status:(null===(t=e.videoExplanationVo)||void 0===t?void 0:t.status)||"0",backgroundFileUrl:(null===(i=e.videoExplanationVo)||void 0===i?void 0:i.backgroundFileUrl)||"",videoSegmentedVoList:(null===(a=e.videoExplanationVo)||void 0===a?void 0:a.videoSegmentedVoList)||[]};JSON.stringify(this.videoExplanation)!==JSON.stringify(o)&&(this.videoExplanation=o,this.updateVideoExplanationFileList())}},immediate:!0,deep:!1}},methods:{emitChange:function(){var e=this;this.isUpdating||(this.emitTimer&&clearTimeout(this.emitTimer),this.emitTimer=setTimeout((function(){e.isUpdating=!0;var t={networkVideoList:e.networkPlans.map((function(e){return{id:e.id||null,tag:e.tag||null,clickX:e.x||null,clickY:e.y||null,wide:e.width||null,high:e.height||null,backgroundImgFileUrl:e.imageUrl||null,backgroundFileUrl:e.videoUrl||null}})),videoExplanationVo:{status:e.videoExplanation.status,backgroundFileUrl:e.videoExplanation.backgroundFileUrl||null,videoSegmentedVoList:e.videoExplanation.videoSegmentedVoList.length?e.videoExplanation.videoSegmentedVoList:null}};e.$emit("input",t),e.$nextTick((function(){setTimeout((function(){e.isUpdating=!1}),50)}))}),200))},addNetworkPlan:function(){var e={tag:"",x:"",y:"",width:0,height:0,imageUrl:"",videoUrl:"",videoFileList:[]};this.networkPlans.push(e),this.emitChange()},removePlan:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function i(){var a;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:a=t.networkPlans[e],t.$confirm("确定删除此网络方案吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(l["a"])(Object(s["a"])().mark((function i(){var n;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!a.id){i.next=17;break}return i.prev=1,t.$modal.loading("正在删除方案，请稍候..."),i.next=5,Object(r["e"])({id:a.id});case 5:n=i.sent,0===n.code?(t.networkPlans.splice(e,1),t.emitChange(),t.$message.success("删除成功")):t.$message.error(n.msg||"删除失败"),i.next=12;break;case 9:i.prev=9,i.t0=i["catch"](1),t.$message.error("删除失败");case 12:return i.prev=12,t.$modal.closeLoading(),i.finish(12);case 15:i.next=20;break;case 17:t.networkPlans.splice(e,1),t.emitChange(),t.$message.success("删除成功");case 20:case"end":return i.stop()}}),i,null,[[1,9,12,15]])})))).catch((function(){}));case 2:case"end":return i.stop()}}),i)})))()},validateCoordinate:function(e,t,i){var a=/^-?\d*\.?\d*$/;if(!a.test(e)){var n=this.networkPlans[i];n[t]=e.replace(/[^-\d.]/g,"")}this.debouncedEmitChange()},beforeUploadImage:function(e,t){var i=this;return Object(l["a"])(Object(s["a"])().mark((function a(){var n,o,l;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.type.startsWith("image/")){a.next=3;break}return i.$message.error("只能上传图片文件！"),a.abrupt("return",!1);case 3:if(!(e.size>20971520)){a.next=6;break}return i.$message.error("图片大小不能超过20MB！"),a.abrupt("return",!1);case 6:return a.prev=6,i.$modal.loading("正在上传图片，请稍候..."),n=new FormData,n.append("file",e),n.append("industryCode",i.leftTreeIndustryCode),a.next=13,Object(r["h"])(n);case 13:o=a.sent,0===o.code&&o.data?(l=i.networkPlans[t],l.imageUrl=o.data.fileUrl,i.debouncedEmitChange(),i.$message.success("上传成功")):i.$message.error(o.msg||"上传失败"),a.next=20;break;case 17:a.prev=17,a.t0=a["catch"](6),i.$message.error("上传失败");case 20:return a.prev=20,i.$modal.closeLoading(),a.finish(20);case 23:return a.abrupt("return",!1);case 24:case"end":return a.stop()}}),a,null,[[6,17,20,23]])})))()},beforeUploadVideo:function(e,t){var i=this;return Object(l["a"])(Object(s["a"])().mark((function a(){var n,o,l,d;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.type.startsWith("video/")){a.next=3;break}return i.$message.error("只能上传视频文件！"),a.abrupt("return",!1);case 3:if(!(e.size>209715200)){a.next=6;break}return i.$message.error("视频大小不能超过200MB！"),a.abrupt("return",!1);case 6:return a.prev=6,i.$modal.loading("正在上传视频，请稍候..."),n=new FormData,n.append("file",e),n.append("industryCode",i.leftTreeIndustryCode),a.next=13,Object(r["h"])(n);case 13:o=a.sent,0===o.code&&o.data?(l=i.networkPlans[t],d=o.data.fileUrl.split("/").pop(),l.videoUrl=o.data.fileUrl,l.videoFileList=[{name:d,url:o.data.fileUrl,uid:Date.now()}],i.debouncedEmitChange(),i.$message.success("上传成功")):i.$message.error(o.msg||"上传失败"),a.next=20;break;case 17:a.prev=17,a.t0=a["catch"](6),i.$message.error("上传失败");case 20:return a.prev=20,i.$modal.closeLoading(),a.finish(20);case 23:return a.abrupt("return",!1);case 24:case"end":return a.stop()}}),a,null,[[6,17,20,23]])})))()},handleRemoveVideo:function(e,t){var i=this;this.$set(this.networkPlans[t],"videoUrl",""),this.$set(this.networkPlans[t],"videoFileList",[]),this.$nextTick((function(){i.emitChange()})),this.$message.success("视频已删除")},beforeUploadExplanationVideo:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function i(){var a,n,o;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.type.startsWith("video/")||e.name.endsWith(".mp4")){i.next=3;break}return t.$message.error("只能上传MP4视频文件！"),i.abrupt("return",!1);case 3:return i.prev=3,t.$modal.loading("正在上传视频，请稍候..."),a=new FormData,a.append("file",e),a.append("industryCode",t.leftTreeIndustryCode),i.next=10,Object(r["h"])(a);case 10:n=i.sent,0===n.code&&n.data?(t.videoExplanation.backgroundFileUrl=n.data.fileUrl,o=n.data.fileUrl.split("/").pop(),t.videoExplanationFileList=[{name:o,url:n.data.fileUrl,uid:Date.now()}],t.debouncedEmitChange(),t.$message.success("上传成功")):t.$message.error(n.msg||"上传失败"),i.next=17;break;case 14:i.prev=14,i.t0=i["catch"](3),t.$message.error("上传失败");case 17:return i.prev=17,t.$modal.closeLoading(),i.finish(17);case 20:return i.abrupt("return",!1);case 21:case"end":return i.stop()}}),i,null,[[3,14,17,20]])})))()},handleRemoveVideoExplanationFile:function(){var e=this;this.videoExplanation.backgroundFileUrl="",this.videoExplanationFileList=[],this.$nextTick((function(){e.debouncedEmitChange()})),this.$message.success("讲解视频已删除")},updateVideoExplanationFileList:function(){if(this.videoExplanation.backgroundFileUrl){var e=this.videoExplanation.backgroundFileUrl.split("/").pop();this.videoExplanationFileList=[{name:e,url:this.videoExplanation.backgroundFileUrl,uid:Date.now()}]}else this.videoExplanationFileList=[]},addVideoSegment:function(){this.videoExplanation.videoSegmentedVoList&&0!==this.videoExplanation.videoSegmentedVoList.length||(this.videoExplanation.videoSegmentedVoList=[{time:"",sceneId:"",sceneName:"",sceneCode:""}]),this.videoExplanation.videoSegmentedVoList.push({time:"",sceneId:"",sceneName:"",sceneCode:""}),this.emitChange()},removeVideoSegment:function(e){this.videoExplanation.videoSegmentedVoList.splice(e,1),this.emitChange()},handleTimeChange:function(e,t){this.videoExplanation.videoSegmentedVoList&&0!==this.videoExplanation.videoSegmentedVoList.length||(this.videoExplanation.videoSegmentedVoList=[{time:0,sceneId:"",sceneName:"",sceneCode:""}]),this.videoExplanation.videoSegmentedVoList[t]&&(this.videoExplanation.videoSegmentedVoList[t].time=e,this.emitChange())},handleSceneCascaderChange:function(e,t){var i=function e(t,i){var a,n=Object(o["a"])(t);try{for(n.s();!(a=n.n()).done;){var s=a.value;if(s.id===i)return s;if(s.children&&s.children.length){var l=e(s.children,i);if(l)return l}}}catch(r){n.e(r)}finally{n.f()}return null},a=i(this.sceneTreeOptions,e);a&&(this.videoExplanation.videoSegmentedVoList[t].sceneName=a.sceneName,this.videoExplanation.videoSegmentedVoList[t].sceneCode=a.sceneCode,this.emitChange())},handleVideoExplanationStatusChange:function(){this.emitChange()},debouncedEmitChange:function(){var e=this;this.emitTimer&&clearTimeout(this.emitTimer),this.emitTimer=setTimeout((function(){e.emitChange()}),500)},previewImage:function(e){e&&(this.previewImageUrl=e,this.previewVisible=!0)},deletePlanImage:function(e){var t=this;this.$confirm("确定删除此图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.networkPlans[e].imageUrl="",t.debouncedEmitChange(),t.$message.success("图片已删除")})).catch((function(){}))},getUploadMode:function(e,t){return this.videoUploadModes[t]||this.$set(this.videoUploadModes,t,"upload"),this.videoUploadModes[t]},setUploadMode:function(e,t,i){this.$set(this.videoUploadModes,t,i)},handleVideoUrlInput:function(e,t){var i=this.networkPlans[t];if(i.videoUrl=e,i.videoFileList=[],e){var a=e.split("/").pop()||"外部链接文件";i.videoFileList=[{name:a,url:e,uid:Date.now()}]}this.debouncedEmitChange()},handleVideoExplanationModeChange:function(e){this.videoExplanationUploadMode=e},handleVideoExplanationUrlInput:function(e){if(this.videoExplanation.backgroundFileUrl=e,this.videoExplanationFileList=[],e){var t=e.split("/").pop()||"外部链接文件";this.videoExplanationFileList=[{name:t,url:e,uid:Date.now()}]}this.debouncedEmitChange()}},beforeDestroy:function(){this.emitTimer&&clearTimeout(this.emitTimer)}},c=d,u=(i("981b"),i("2877")),p=Object(u["a"])(c,a,n,!1,null,"b3dec5e0",null);t["default"]=p.exports},"81a3":function(e,t,i){},"8a79":function(e,t,i){"use strict";var a=i("23e7"),n=i("4625"),o=i("06cf").f,s=i("50c4"),l=i("577e"),r=i("5a34"),d=i("1d80"),c=i("ab13"),u=i("c430"),p=n("".slice),v=Math.min,m=c("endsWith"),h=!u&&!m&&!!function(){var e=o(String.prototype,"endsWith");return e&&!e.writable}();a({target:"String",proto:!0,forced:!h&&!m},{endsWith:function(e){var t=l(d(this));r(e);var i=arguments.length>1?arguments[1]:void 0,a=t.length,n=void 0===i?a:v(s(i),a),o=l(e);return p(t,n-o.length,n)===o}})},"981b":function(e,t,i){"use strict";i("81a3")}}]);