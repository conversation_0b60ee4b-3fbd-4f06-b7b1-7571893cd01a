(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0dece3"],{"86e4":function(n,t,e){"use strict";e.d(t,"c",(function(){return a})),e.d(t,"d",(function(){return u})),e.d(t,"f",(function(){return c})),e.d(t,"e",(function(){return i})),e.d(t,"a",(function(){return d})),e.d(t,"b",(function(){return f})),e.d(t,"g",(function(){return s})),e.d(t,"h",(function(){return l}));var o=e("5530"),r=e("b775");function a(n){return Object(r["a"])({url:"/sceneConfig/detail",method:"get",params:n})}function u(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(r["a"])({url:"/sceneConfig/theme/list",method:"get",params:Object(o["a"])({page:1,limit:20},n)})}function c(n){return Object(r["a"])({url:"/sceneConfig/upd",method:"post",data:n})}function i(n){return Object(r["a"])({url:"/sceneConfig/sceneFileInfo/del",method:"post",data:n})}function d(n){return Object(r["a"])({url:"/sceneConfig/background/file/del",method:"post",data:n})}function f(n){return Object(r["a"])({url:"/sceneConfig/file/bind/del",method:"post",data:n})}function s(n){return Object(r["a"])({url:"/sceneConfig/synchronization/file",method:"post",data:n,headers:{"Content-Type":"multipart/form-data"}})}function l(n){var t;return n instanceof FormData?t=n:(t=new FormData,t.append("file",n)),Object(r["a"])({url:"/sceneConfig/upload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}}}]);