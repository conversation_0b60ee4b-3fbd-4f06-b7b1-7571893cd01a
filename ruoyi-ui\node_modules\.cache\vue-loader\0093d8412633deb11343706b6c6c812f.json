{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue?vue&type=style&index=0&id=cbca8a74&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue", "mtime": 1754892133864}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743599735798}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743599729946}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743599731247}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog6ZmQ5Yi25LiK5Lyg5Zu+54mH55qE5pi+56S65aSn5bCPICovDQouaW1hZ2UtdXBsb2FkIC5lbC11cGxvYWQtLXBpY3R1cmUtY2FyZCB7DQogIHdpZHRoOiAxNDhweDsNCiAgaGVpZ2h0OiAxNDhweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KfQ0KDQoudXBsb2FkLWltYWdlIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgb2JqZWN0LWZpdDogY292ZXI7DQogIGRpc3BsYXk6IGJsb2NrOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQp9DQoNCi8qIOiDjOaZr+WbvueJh+mmluW4p+WbvueJh+Wkp+Wwj+aOp+WItiAqLw0KLmltYWdlLXVwbG9hZCAuZWwtdXBsb2FkLWxpc3RfX2l0ZW0tdGh1bWJuYWlsIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgb2JqZWN0LWZpdDogY292ZXI7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCn0NCg0KLyog5LiK5Lyg5qGG5Lmf5re75Yqg5ZyG6KeSICovDQouaW1hZ2UtdXBsb2FkIC5lbC11cGxvYWQtLXBpY3R1cmUtY2FyZCB7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg0KLm1pbmktYmxvY2sgew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQouYmFja2dyb3VuZC1yZXNvdXJjZS1pdGVtIHsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi5yZXNvdXJjZS1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIHBhZGRpbmctYm90dG9tOiA4cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOw0KfQ0KDQoucmVzb3VyY2UtdGl0bGUgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLnBhaW4tcG9pbnQtYmxvY2sgew0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHBhZGRpbmc6IDE2cHg7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7DQp9DQoNCi5wYWluLXBvaW50LWJsb2NrOmxhc3QtY2hpbGQgew0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQovKiDlm77niYfpooTop4jmoLflvI8gKi8NCi5wcmV2aWV3LWNvbnRhaW5lciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnByZXZpZXctaW1hZ2Ugew0KICBtYXgtd2lkdGg6IDEwMCU7DQogIG1heC1oZWlnaHQ6IDcwdmg7DQogIG9iamVjdC1maXQ6IGNvbnRhaW47DQp9DQoNCi8qIOWbvueJh+aCrOWBnOaTjeS9nOagt+W8jyAqLw0KLmltYWdlLXByZXZpZXctY29udGFpbmVyIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCn0NCg0KLmltYWdlLW92ZXJsYXkgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMDsNCiAgbGVmdDogMDsNCiAgcmlnaHQ6IDA7DQogIGJvdHRvbTogMDsNCiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjUpOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgb3BhY2l0eTogMDsNCiAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQp9DQoNCi5pbWFnZS1wcmV2aWV3LWNvbnRhaW5lcjpob3ZlciAuaW1hZ2Utb3ZlcmxheSB7DQogIG9wYWNpdHk6IDE7DQp9DQoNCi5wcmV2aWV3LWljb24sDQouZGVsZXRlLWljb24gew0KICBjb2xvcjogd2hpdGU7DQogIGZvbnQtc2l6ZTogMjBweDsNCiAgbWFyZ2luOiAwIDEwcHg7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnM7DQp9DQoNCi5wcmV2aWV3LWljb246aG92ZXIsDQouZGVsZXRlLWljb246aG92ZXIgew0KICB0cmFuc2Zvcm06IHNjYWxlKDEuMik7DQp9DQoNCi5jb29yZGluYXRlLWdyb3VwIHsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBwYWRkaW5nOiAxMnB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYmZjOw0KfQ0KDQouY29vcmRpbmF0ZS1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KLyog56Gu5L+d5LuL57uN6KeG6aKR5ZKM5oiQ5pys6aKE5Lyw5Y2h54mH6auY5bqm5LiA6Ie0ICovDQoubWluaS1ibG9jayAuZWwtY2FyZCB7DQogIGhlaWdodDogMTAwJTsNCn0NCg0KLm1pbmktYmxvY2sgLmVsLWNhcmRfX2JvZHkgew0KICBtaW4taGVpZ2h0OiAzMDBweDsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCn0NCg0KLyog5LuL57uN6KeG6aKR5ZKM5oiQ5pys6aKE5Lyw5bm25o6S5pe255qE6auY5bqm57uf5LiAICovDQouZWwtcm93IC5lbC1jb2wgLm1pbmktYmxvY2sgew0KICBoZWlnaHQ6IDEwMCU7DQp9DQoNCi5lbC1yb3cgLmVsLWNvbCAubWluaS1ibG9jayAuZWwtY2FyZCB7DQogIGhlaWdodDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCn0NCg0KLmVsLXJvdyAuZWwtY29sIC5taW5pLWJsb2NrIC5lbC1jYXJkX19ib2R5IHsNCiAgZmxleDogMTsNCiAgbWluLWhlaWdodDogMzAwcHg7DQp9DQo="}, {"version": 3, "sources": ["SceneConfigNode.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo1CA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "SceneConfigNode.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <el-card class=\"mini-block\" shadow=\"never\">\r\n    <div slot=\"header\">\r\n      <span>{{ node.name }}</span>\r\n      <el-switch v-model=\"node.status\" style=\"margin-left: 16px;\" :active-value=\"'0'\" :inactive-value=\"'1'\" @change=\"onStatusChange\" />\r\n    </div>\r\n    <div v-show=\"node.status === '0'\" class=\"sub-category-body\">\r\n      <!-- 主表单项 - 编码名称并排，坐标并排 -->\r\n      <el-form label-width=\"120px\">\r\n        <el-row :gutter=\"16\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"场景编码\">\r\n              <el-input v-model=\"node.code\" disabled type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"场景名称\">\r\n              <el-input v-model=\"node.name\" disabled type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"16\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"坐标X\" required>\r\n              <el-input v-model=\"node.x\" placeholder=\"请输入坐标X（百分比）\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"坐标Y\" required>\r\n              <el-input v-model=\"node.y\" placeholder=\"请输入坐标Y（百分比）\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"场景类型\" required>\r\n          <el-select v-model=\"node.type\" placeholder=\"请选择类型\">\r\n            <el-option label=\"默认\" value=\"1\" />\r\n            <el-option label=\"AI\" value=\"2\" />\r\n            <el-option label=\"三化\" value=\"3\" />\r\n            <el-option label=\"AI+三化\" value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <!-- 只有存在下级菜单时才显示 -->\r\n        <el-row :gutter=\"16\" v-if=\"hasChildren\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否展开下级\" required>\r\n              <el-radio-group v-model=\"node.isUnfold\">\r\n                <el-radio label=\"0\">展示</el-radio>\r\n                <el-radio label=\"1\">关闭</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"下级展示位置\" required>\r\n              <el-radio-group v-model=\"node.displayLocation\">\r\n                <el-radio label=\"0\">默认</el-radio>\r\n                <el-radio label=\"1\">右下</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"下级菜单分类\" required>\r\n              <el-radio-group v-model=\"node.treeClassification\">\r\n                <el-radio label=\"1\">传统</el-radio>\r\n                <el-radio label=\"2\">5G</el-radio>\r\n                <el-radio label=\"3\">全部</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 介绍视频和成本预估并排布局 -->\r\n      <el-row :gutter=\"20\">\r\n        <!-- 左侧：介绍视频模块 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"mini-block\" shadow=\"never\">\r\n            <div slot=\"header\">\r\n              <span>介绍视频</span>\r\n              <el-switch \r\n                v-model=\"node.introduceVideoVo.status\" \r\n                style=\"float:right;\"\r\n                :active-value=\"'0'\" \r\n                :inactive-value=\"'1'\" \r\n                @change=\"onIntroduceVideoStatusChange\" />\r\n            </div>\r\n            <div v-show=\"node.introduceVideoVo.status === '0'\">\r\n              <el-form label-width=\"120px\">\r\n                <el-form-item label=\"介绍视频首帧\">\r\n                  <el-upload\r\n                    class=\"upload image-upload\"\r\n                    action=\"#\"\r\n                    :show-file-list=\"false\"\r\n                    list-type=\"picture-card\"\r\n                    accept=\"image/*\"\r\n                    :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'introduceVideoVo', 'backgroundImgFileUrl', null, null)\"\r\n                    :http-request=\"() => {}\"\r\n                  >\r\n                    <div v-if=\"node.introduceVideoVo.backgroundImgFileUrl\" class=\"image-preview-container\">\r\n                      <img :src=\"node.introduceVideoVo.backgroundImgFileUrl\" class=\"upload-image\" />\r\n                      <div class=\"image-overlay\">\r\n                        <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(node.introduceVideoVo.backgroundImgFileUrl)\" title=\"预览\"></i>\r\n                        <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteIntroduceVideoImg\" title=\"删除\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <i v-else class=\"el-icon-plus\"></i>\r\n                  </el-upload>\r\n                </el-form-item>\r\n                <el-form-item label=\"介绍视频\">\r\n                  <div style=\"margin-bottom: 8px;\">\r\n                    <el-radio-group :value=\"uploadModes.introduceVideo || 'upload'\" @input=\"value => setUploadMode('introduceVideo', value)\" size=\"small\">\r\n                      <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                      <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                  \r\n                  <!-- 上传模式 -->\r\n                  <el-upload\r\n                    v-if=\"(uploadModes.introduceVideo || 'upload') === 'upload'\"\r\n                    action=\"#\"\r\n                    :show-file-list=\"true\"\r\n                    :file-list=\"getIntroduceVideoFileList()\"\r\n                    accept=\".mp4\"\r\n                    :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'introduceVideoVo', 'backgroundFileUrl', null, null)\"\r\n                    :http-request=\"() => {}\"\r\n                    :on-remove=\"handleRemoveIntroduceVideo\"\r\n                  >\r\n                    <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传mp4文件</div>\r\n                  </el-upload>\r\n                  \r\n                  <!-- 链接模式 -->\r\n                  <el-input\r\n                    v-else\r\n                    v-model=\"node.introduceVideoVo.backgroundFileUrl\"\r\n                    placeholder=\"请输入视频链接\"\r\n                    @input=\"handleIntroduceVideoUrlInput\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        \r\n        <!-- 右侧：成本预估模块 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"mini-block\" shadow=\"never\">\r\n            <div slot=\"header\">\r\n              <span>成本预估</span>\r\n              <el-switch v-model=\"node.costEstimate.status\" style=\"float:right;\" :active-value=\"'0'\" :inactive-value=\"'1'\" />\r\n            </div>\r\n            <div v-show=\"node.costEstimate.status === '0'\">\r\n              <el-form label-width=\"120px\">\r\n                <el-form-item label=\"大标题\" required>\r\n                  <el-input v-model=\"node.costEstimate.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"内容\" required>\r\n                  <div v-for=\"(content, cidx) in node.costEstimate.contents\" :key=\"'costEstimate-content-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                    <el-input v-model=\"node.costEstimate.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                    <el-button icon=\"el-icon-delete\" @click=\"removeCostContent(cidx)\" circle size=\"mini\" />\r\n                  </div>\r\n                  <el-button type=\"primary\" plain @click=\"addCostContent\">增加内容</el-button>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <!-- 传统小类 -->\r\n      <el-card class=\"mini-block\" shadow=\"never\">\r\n        <div slot=\"header\">传统</div>\r\n        <el-form label-width=\"120px\">\r\n          <el-form-item label=\"名称\" required>\r\n            <el-input v-model=\"node.tradition.name\" placeholder=\"请输入名称\" type=\"text\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"全景图唯一标识\" required>\r\n            <el-input \r\n              v-model=\"node.tradition.panoramicViewXmlKey\" \r\n              placeholder=\"请输入全景图唯一标识（仅英文）\" \r\n              type=\"text\"\r\n              @input=\"validateXmlKey($event, 'tradition')\"\r\n            />\r\n          </el-form-item>\r\n          \r\n          <!-- 传统模块背景资源 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">背景资源</div>\r\n            <div v-for=\"(resource, idx) in node.tradition.backgroundResources\" :key=\"idx\" class=\"background-resource-item\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">背景资源 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removeBackgroundResource('tradition', idx)\">删除</el-button>\r\n              </div>\r\n              <!-- <el-form-item label=\"标签\" required>\r\n                <el-input v-model=\"resource.label\" placeholder=\"请输入标签\" type=\"text\" />\r\n              </el-form-item> -->\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"坐标组\" required>\r\n                    <div v-for=\"(coord, coordIdx) in resource.coordinates\" :key=\"coordIdx\" class=\"coordinate-group\">\r\n                      <div class=\"coordinate-header\">\r\n                        <span>坐标组 {{ coordIdx + 1 }}</span>\r\n                        <el-button \r\n                          type=\"danger\" \r\n                          size=\"mini\" \r\n                          plain \r\n                          @click=\"removeCoordinate('tradition', idx, coordIdx)\"\r\n                        >\r\n                          删除\r\n                        </el-button>\r\n                      </div>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"X坐标\">\r\n                            <el-input v-model=\"coord.x\" placeholder=\"请输入X坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"Y坐标\">\r\n                            <el-input v-model=\"coord.y\" placeholder=\"请输入Y坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"绑定场景\">\r\n                            <el-cascader\r\n                              v-model=\"coord.sceneId\"\r\n                              :options=\"sceneTreeOptions\"\r\n                              :props=\"sceneCascaderProps\"\r\n                              filterable\r\n                              check-strictly\r\n                              placeholder=\"选择场景\"\r\n                              style=\"width: 100%;\"\r\n                              @change=\"val => handleSceneCascaderChange(val, 'tradition', idx, coordIdx)\"\r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"宽度\">\r\n                            <el-input-number \r\n                              :value=\"coord.wide\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'wide')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入宽度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"高度\">\r\n                            <el-input-number \r\n                              :value=\"coord.high\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'high')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入高度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"全景xml标签\">\r\n                            <el-input v-model=\"coord.xmlKey\" placeholder=\"请输入全景xml标签\" style=\"width: 100%\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                    <el-button type=\"primary\" size=\"mini\" plain @click=\"addCoordinate('tradition', idx)\">\r\n                      增加坐标组\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'tradition', 'backgroundResources', idx, 'bgImg')\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"resource.bgImg\" class=\"image-preview-container\">\r\n                        <img :src=\"resource.bgImg\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(resource.bgImg)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBackgroundImg('tradition', idx)\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\" required>\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.tradition[idx] || 'upload'\" @input=\"value => setUploadMode('tradition', idx, value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.tradition[idx] || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"getFileList('tradition', idx)\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'tradition', 'backgroundResources', idx, 'bgFile')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"(file, fileList) => handleRemoveBackgroundFile(node, 'tradition', idx)\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"resource.bgFile\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"value => handleUrlInput('tradition', idx, value)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addBackgroundResource('tradition')\">增加背景资源</el-button>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 传统模块痛点价值 - 统一输入框长度 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">痛点价值</div>\r\n            <div v-for=\"(point, idx) in node.tradition.painPoints\" :key=\"'tradition-' + idx\" class=\"pain-point-block\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">痛点价值 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removePainPoint('tradition', idx)\">删除</el-button>\r\n              </div>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"大标题\" required>\r\n                    <el-input v-model=\"point.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"展示时间\" required>\r\n                    <el-input-number v-model=\"point.showTime\" :min=\"0\" style=\"width: 100%\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-form-item label=\"内容\" required>\r\n                <el-row :gutter=\"16\">\r\n                  <el-col :span=\"16\">\r\n                    <div v-for=\"(content, cidx) in point.contents\" :key=\"'tradition-content-' + idx + '-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                      <el-input v-model=\"point.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                      <el-button icon=\"el-icon-delete\" @click=\"removePainContent('tradition', idx, cidx)\" circle size=\"mini\" />\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addPainContent('tradition', idx)\">增加内容</el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addPainPoint('tradition')\">增加痛点价值</el-button>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </el-card>\r\n      <!-- 5G智慧小类 -->\r\n      <el-card class=\"mini-block\" shadow=\"never\">\r\n        <div slot=\"header\">5G智慧</div>\r\n        <el-form label-width=\"120px\">\r\n          <el-form-item label=\"名称\" required>\r\n            <el-input v-model=\"node.wisdom5g.name\" placeholder=\"请输入名称\" type=\"text\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"全景图唯一标识\" required>\r\n            <el-input \r\n              v-model=\"node.wisdom5g.panoramicViewXmlKey\" \r\n              placeholder=\"请输入全景图唯一标识（仅英文）\" \r\n              type=\"text\"\r\n              @input=\"validateXmlKey($event, 'wisdom5g')\"\r\n            />\r\n          </el-form-item>\r\n          \r\n          <!-- 5G智慧模块背景资源 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">背景资源</div>\r\n            <div v-for=\"(resource, idx) in node.wisdom5g.backgroundResources\" :key=\"'wisdom5g-bg-' + idx\" class=\"background-resource-item\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">背景资源 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removeBackgroundResource('wisdom5g', idx)\">删除</el-button>\r\n              </div>\r\n              <!-- <el-form-item label=\"标签\" required>\r\n                <el-input v-model=\"resource.label\" placeholder=\"请输入标签\" type=\"text\" />\r\n              </el-form-item> -->\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"坐标组\" required>\r\n                    <div v-for=\"(coord, coordIdx) in resource.coordinates\" :key=\"coordIdx\" class=\"coordinate-group\">\r\n                      <div class=\"coordinate-header\">\r\n                        <span>坐标组 {{ coordIdx + 1 }}</span>\r\n                        <el-button \r\n                          type=\"danger\" \r\n                          size=\"mini\" \r\n                          plain \r\n                          @click=\"removeCoordinate('wisdom5g', idx, coordIdx)\"\r\n                        >\r\n                          删除\r\n                        </el-button>\r\n                      </div>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"X坐标\">\r\n                            <el-input v-model=\"coord.x\" placeholder=\"请输入X坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"Y坐标\">\r\n                            <el-input v-model=\"coord.y\" placeholder=\"请输入Y坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"绑定场景\">\r\n                            <el-cascader\r\n                              v-model=\"coord.sceneId\"\r\n                              :options=\"sceneTreeOptions\"\r\n                              :props=\"sceneCascaderProps\"\r\n                              filterable\r\n                              check-strictly\r\n                              placeholder=\"选择场景\"\r\n                              style=\"width: 100%;\"\r\n                              @change=\"val => handleSceneCascaderChange(val, 'wisdom5g', idx, coordIdx)\"\r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"宽度\">\r\n                            <el-input-number \r\n                              :value=\"coord.wide\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'wide')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入宽度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"高度\">\r\n                            <el-input-number \r\n                              :value=\"coord.high\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'high')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入高度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"全景xml标签\">\r\n                            <el-input v-model=\"coord.xmlKey\" placeholder=\"请输入全景xml标签\" style=\"width: 100%\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                    <el-button type=\"primary\" size=\"mini\" plain @click=\"addCoordinate('wisdom5g', idx)\">\r\n                      增加坐标组\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'wisdom5g', 'backgroundResources', idx, 'bgImg')\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"resource.bgImg\" class=\"image-preview-container\">\r\n                        <img :src=\"resource.bgImg\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(resource.bgImg)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBackgroundImg('wisdom5g', idx)\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\" required>\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.wisdom5g[idx] || 'upload'\" @input=\"value => setUploadMode('wisdom5g', idx, value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.wisdom5g[idx] || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"getFileList('wisdom5g', idx)\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'wisdom5g', 'backgroundResources', idx, 'bgFile')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"(file, fileList) => handleRemoveBackgroundFile(node, 'wisdom5g', idx)\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"resource.bgFile\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"value => handleUrlInput('wisdom5g', idx, value)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addBackgroundResource('wisdom5g')\">增加背景资源</el-button>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 5G智慧模块痛点价值 - 统一输入框长度 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">痛点价值</div>\r\n            <div v-for=\"(point, idx) in node.wisdom5g.painPoints\" :key=\"'wisdom5g-' + idx\" class=\"pain-point-block\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">痛点价值 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removePainPoint('wisdom5g', idx)\">删除</el-button>\r\n              </div>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"大标题\" required>\r\n                    <el-input v-model=\"point.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"展示时间\" required>\r\n                    <el-input-number v-model=\"point.showTime\" :min=\"0\" style=\"width: 100%\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-form-item label=\"内容\" required>\r\n                <el-row :gutter=\"16\">\r\n                  <el-col :span=\"16\">\r\n                    <div v-for=\"(content, cidx) in point.contents\" :key=\"'wisdom5g-content-' + idx + '-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                      <el-input v-model=\"point.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                      <el-button icon=\"el-icon-delete\" @click=\"removePainContent('wisdom5g', idx, cidx)\" circle size=\"mini\" />\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addPainContent('wisdom5g', idx)\">增加内容</el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addPainPoint('wisdom5g')\">增加痛点价值</el-button>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </el-card>\r\n    </div>\r\n    \r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"previewVisible\"\r\n      title=\"图片预览\"\r\n      width=\"60%\"\r\n      append-to-body\r\n    >\r\n      <div class=\"preview-container\">\r\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\r\n      </div>\r\n    </el-dialog>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport { uploadSceneFile, backgroundFileDel, fileBindDel } from '@/api/view/sceneView'\r\n\r\nexport default {\r\n  name: 'SceneConfigNode',\r\n  props: {\r\n    node: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    rootTree: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    sceneTreeOptions: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    leftTreeIndustryCode: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  watch: {\r\n    'node.status'(val) {\r\n      if (val === '0') {\r\n        this.findAndOpenParent(this.node.id, this.rootTree)\r\n      }\r\n    },\r\n    node: {\r\n      handler(newNode, oldNode) {\r\n        if (newNode && newNode !== oldNode) {\r\n          this.$nextTick(() => {\r\n            // 初始化节点数据\r\n            this.initNodeData()\r\n            \r\n            // 确保introduceVideoVo存在且有完整数据时才处理\r\n            if (newNode.introduceVideoVo && newNode.introduceVideoVo.hasOwnProperty('status')) {\r\n              // 数据已存在，不需要初始化，直接更新文件列表\r\n              this.updateIntroduceVideoFileList()\r\n            } else if (!newNode.introduceVideoVo) {\r\n              // 数据不存在时才初始化\r\n              this.initIntroduceVideo()\r\n            }\r\n            // 重新初始化文件列表，清除可能的继承问题\r\n            this.initFileLists()\r\n          })\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    // 监听introduceVideoVo整个对象的变化\r\n    'node.introduceVideoVo': {\r\n      handler(newVal) {\r\n        if (newVal && newVal.status !== undefined) {\r\n          this.updateIntroduceVideoFileList()\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      fileLists: {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: []\r\n      },\r\n      uploadModes: {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: 'upload'\r\n      },\r\n      // 添加介绍视频文件列表缓存\r\n      introduceVideoFileList: [],\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      // 场景级联选择器配置\r\n      sceneCascaderProps: {\r\n        label: 'sceneName',\r\n        value: 'id',\r\n        children: 'children',\r\n        emitPath: false,\r\n        checkStrictly: true\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.initIntroduceVideo()\r\n    this.initNodeData()\r\n  },\r\n  mounted() {\r\n    this.initData()\r\n    this.initFileLists()\r\n  },\r\n  methods: {\r\n    initData() {\r\n      // 确保5G智慧模块有默认结构并深拷贝避免引用共享\r\n      if (!this.node.wisdom5g) {\r\n        this.$set(this.node, 'wisdom5g', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 深拷贝现有的5G智慧数据，避免多个场景共享引用\r\n        const wisdom5gCopy = JSON.parse(JSON.stringify(this.node.wisdom5g))\r\n        this.$set(this.node, 'wisdom5g', wisdom5gCopy)\r\n        \r\n        // 修正字段映射\r\n        if (this.node.wisdom5g.backgroundResources) {\r\n          this.node.wisdom5g.backgroundResources.forEach(resource => {\r\n            if (resource.backgroundImgFileUrl && !resource.bgImg) {\r\n              resource.bgImg = resource.backgroundImgFileUrl\r\n            }\r\n            if (resource.backgroundFileUrl && !resource.bgFile) {\r\n              resource.bgFile = resource.backgroundFileUrl\r\n            }\r\n            if (resource.tag && !resource.label) {\r\n              resource.label = resource.tag\r\n            }\r\n          })\r\n        }\r\n      }\r\n      \r\n      // 同样处理传统模块\r\n      if (!this.node.tradition) {\r\n        this.$set(this.node, 'tradition', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 深拷贝传统模块数据\r\n        const traditionCopy = JSON.parse(JSON.stringify(this.node.tradition))\r\n        this.$set(this.node, 'tradition', traditionCopy)\r\n      }\r\n    },\r\n    // 初始化文件列表\r\n    initFileLists() {\r\n      // 清空所有文件列表，避免继承问题\r\n      this.fileLists = {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: []\r\n      }\r\n      \r\n      // 初始化传统模块的文件列表\r\n      if (this.node.tradition && this.node.tradition.backgroundResources) {\r\n        this.node.tradition.backgroundResources.forEach((resource, idx) => {\r\n          // 只有当资源确实有文件时才创建文件列表\r\n          if (resource.bgFile && resource.bgFile.trim()) {\r\n            const fileName = resource.bgFile.split('/').pop()\r\n            this.$set(this.fileLists.tradition, idx, [{\r\n              name: fileName,\r\n              url: resource.bgFile,\r\n              uid: Date.now() + idx\r\n            }])\r\n          } else {\r\n            // 明确设置为空数组\r\n            this.$set(this.fileLists.tradition, idx, [])\r\n          }\r\n        })\r\n      }\r\n      \r\n      // 初始化5G智慧模块的文件列表\r\n      if (this.node.wisdom5g && this.node.wisdom5g.backgroundResources) {\r\n        this.node.wisdom5g.backgroundResources.forEach((resource, idx) => {\r\n          // 只有当资源确实有文件时才创建文件列表\r\n          if (resource.bgFile && resource.bgFile.trim()) {\r\n            const fileName = resource.bgFile.split('/').pop()\r\n            this.$set(this.fileLists.wisdom5g, idx, [{\r\n              name: fileName,\r\n              url: resource.bgFile,\r\n              uid: Date.now() + idx + 1000\r\n            }])\r\n          } else {\r\n            // 明确设置为空数组\r\n            this.$set(this.fileLists.wisdom5g, idx, [])\r\n          }\r\n        })\r\n      }\r\n    },\r\n    // 初始化介绍视频对象\r\n    initIntroduceVideo() {\r\n      if (!this.node.introduceVideoVo) {\r\n        this.$set(this.node, 'introduceVideoVo', {\r\n          id: '',\r\n          type: '',\r\n          viewInfoId: '',\r\n          status: '0',\r\n          backgroundImgFileUrl: '',\r\n          backgroundFileUrl: ''\r\n        })\r\n      }\r\n      // 完全删除status的重新设置，保持接口返回的原始值\r\n      this.updateIntroduceVideoFileList()\r\n    },\r\n    // 确保其他数据结构也有默认值\r\n    initNodeData() {\r\n      // 确保基础字段有默认值 - 只在真正没有值时才设置默认值\r\n      if (this.node.isUnfold === undefined || this.node.isUnfold === null || this.node.isUnfold === '') {\r\n        this.$set(this.node, 'isUnfold', '1')\r\n      }\r\n      if (this.node.displayLocation === undefined || this.node.displayLocation === null || this.node.displayLocation === '') {\r\n        this.$set(this.node, 'displayLocation', '0')\r\n      }\r\n      if (this.node.treeClassification === undefined || this.node.treeClassification === null || this.node.treeClassification === '') {\r\n        this.$set(this.node, 'treeClassification', '3')\r\n      }\r\n      \r\n      // 确保传统模块有默认结构\r\n      if (!this.node.tradition) {\r\n        this.$set(this.node, 'tradition', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      }\r\n      \r\n      // 确保5G智慧模块有默认结构并修正字段映射\r\n      if (!this.node.wisdom5g) {\r\n        this.$set(this.node, 'wisdom5g', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 修正5G智慧模块的字段映射\r\n        if (this.node.wisdom5g.backgroundResources) {\r\n          this.node.wisdom5g.backgroundResources.forEach(resource => {\r\n            // 确保字段名称正确\r\n            if (resource.backgroundImgFileUrl && !resource.bgImg) {\r\n              resource.bgImg = resource.backgroundImgFileUrl\r\n            }\r\n            if (resource.backgroundFileUrl && !resource.bgFile) {\r\n              resource.bgFile = resource.backgroundFileUrl\r\n            }\r\n            if (resource.tag && !resource.label) {\r\n              resource.label = resource.tag\r\n            }\r\n          })\r\n        }\r\n      }\r\n      \r\n      // 确保成本预估有默认结构\r\n      if (!this.node.costEstimate) {\r\n        this.$set(this.node, 'costEstimate', {\r\n          status: '0',\r\n          title: '',\r\n          contents: []\r\n        })\r\n      }\r\n      \r\n      // 确保成本预估有status字段\r\n      if (this.node.costEstimate && this.node.costEstimate.status === undefined) {\r\n        this.$set(this.node.costEstimate, 'status', '0')\r\n      }\r\n    },\r\n    // 开关联动\r\n    onStatusChange(val) {\r\n      if (val === '0') {\r\n        // 开启时递归开启所有父级\r\n        let p = this.parent\r\n        while (p) {\r\n          if (p.status !== '0') p.status = '0'\r\n          p = p.parent\r\n        }\r\n      } else {\r\n        // 关闭时递归关闭所有子级\r\n        function closeChildren(node) {\r\n          if (node.children && node.children.length) {\r\n            node.children.forEach(child => {\r\n              child.status = '1'\r\n              closeChildren(child)\r\n            })\r\n          }\r\n        }\r\n        closeChildren(this.node)\r\n      }\r\n    },\r\n    // 递归查找并开启所有父级\r\n    findAndOpenParent(id, tree) {\r\n      function helper(nodes, parent) {\r\n        for (let node of nodes) {\r\n          if (node.id === id) {\r\n            if (parent) parent.status = '0'\r\n            return true\r\n          }\r\n          if (node.children && node.children.length) {\r\n            if (helper(node.children, node)) {\r\n              if (parent) parent.status = '0'\r\n              return true\r\n            }\r\n          }\r\n        }\r\n        return false\r\n      }\r\n      helper(tree, null)\r\n    },\r\n    // 新增背景资源管理方法\r\n    addBackgroundResource(type) {\r\n      if (!this.node[type].backgroundResources) {\r\n        this.$set(this.node[type], 'backgroundResources', [])\r\n      }\r\n      \r\n      const newIdx = this.node[type].backgroundResources.length\r\n      \r\n      // 创建完全独立的新资源对象，不包含默认坐标组\r\n      const newResource = { \r\n        id: null,\r\n        tag: '', \r\n        status: '',\r\n        type: '',\r\n        viewInfoId: '',\r\n        bgImg: '', \r\n        bgFile: '',\r\n        coordinates: [] // 改为空数组，不默认添加坐标组\r\n      }\r\n      \r\n      // 使用深拷贝确保对象完全独立\r\n      const independentResource = JSON.parse(JSON.stringify(newResource))\r\n      this.node[type].backgroundResources.push(independentResource)\r\n      \r\n      // 初始化对应的独立文件列表\r\n      this.$set(this.fileLists[type], newIdx, [])\r\n      console.log('添加新背景资源:', independentResource)\r\n    },\r\n    \r\n    // 删除背景资源\r\n    async removeBackgroundResource(type, idx) {\r\n      const resource = this.node[type].backgroundResources[idx]\r\n      \r\n      this.$confirm('确定删除此背景资源吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        // 如果背景资源有ID，调用删除接口\r\n        if (resource.id) {\r\n          try {\r\n            this.$modal.loading(\"正在删除背景资源，请稍候...\")\r\n            const res = await backgroundFileDel({ id: resource.id })\r\n            if (res.code === 0) {\r\n              this.node[type].backgroundResources.splice(idx, 1)\r\n              this.$message.success('删除成功')\r\n            } else {\r\n              this.$message.error(res.msg || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('删除失败')\r\n          } finally {\r\n            this.$modal.closeLoading()\r\n          }\r\n        } else {\r\n          // 没有ID的新背景资源，直接从数组中移除\r\n          this.node[type].backgroundResources.splice(idx, 1)\r\n          this.$message.success('删除成功')\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 添加坐标组\r\n    addCoordinate(type, resourceIdx) {\r\n      const resource = this.node[type].backgroundResources[resourceIdx]\r\n      if (!resource.coordinates) {\r\n        this.$set(resource, 'coordinates', [])\r\n      }\r\n      resource.coordinates.push({ \r\n        id: 0,\r\n        fileId: 0,\r\n        x: '', \r\n        y: '', \r\n        wide: '', \r\n        high: '', \r\n        sceneId: '', \r\n        sceneCode: '',\r\n        xmlKey: ''\r\n      })\r\n    },\r\n    \r\n    // 删除坐标组\r\n    async removeCoordinate(type, resourceIdx, coordIdx) {\r\n      const coord = this.node[type].backgroundResources[resourceIdx].coordinates[coordIdx]\r\n      \r\n      this.$confirm('确定删除此坐标组吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        // 如果坐标组有ID，调用删除接口\r\n        if (coord.id) {\r\n          try {\r\n            this.$modal.loading(\"正在删除坐标组，请稍候...\")\r\n            const res = await fileBindDel({ id: coord.id })\r\n            if (res.code === 0) {\r\n              this.node[type].backgroundResources[resourceIdx].coordinates.splice(coordIdx, 1)\r\n              this.$message.success('删除成功')\r\n            } else {\r\n              this.$message.error(res.msg || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('删除失败')\r\n          } finally {\r\n            this.$modal.closeLoading()\r\n          }\r\n        } else {\r\n          // 没有ID的新坐标组，直接从数组中移除\r\n          this.node[type].backgroundResources[resourceIdx].coordinates.splice(coordIdx, 1)\r\n          this.$message.success('删除成功')\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 场景级联选择器变化处理\r\n    handleSceneCascaderChange(val, type, resourceIdx, coordIdx) {\r\n      const scene = this.findSceneById(this.sceneTreeOptions, val)\r\n      if (scene) {\r\n        const coord = this.node[type].backgroundResources[resourceIdx].coordinates[coordIdx]\r\n        coord.sceneCode = scene.sceneCode || ''\r\n        console.log('选择的场景:', scene, '设置sceneCode:', coord.sceneCode)\r\n      }\r\n    },\r\n    \r\n    // 根据ID查找场景\r\n    findSceneById(tree, id) {\r\n      if (!tree || !Array.isArray(tree)) return null\r\n      \r\n      for (const node of tree) {\r\n        if (node.id === id) {\r\n          return node\r\n        }\r\n        if (node.children && node.children.length) {\r\n          const found = this.findSceneById(node.children, id)\r\n          if (found) return found\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    \r\n    // 格式化坐标数据用于提交\r\n    formatCoordinatesForSubmit(coordinates) {\r\n      if (!coordinates || !Array.isArray(coordinates)) {\r\n        return { clickX: '', clickY: '' }\r\n      }\r\n      \r\n      const xValues = coordinates.map(coord => coord.x || '0').join(',')\r\n      const yValues = coordinates.map(coord => coord.y || '0').join(',')\r\n      \r\n      return {\r\n        clickX: xValues,\r\n        clickY: yValues\r\n      }\r\n    },\r\n    \r\n    // 修改现有的上传方法\r\n    async beforeUploadSceneConfigImg(file, node, type, arrayKeyOrKey, indexOrUndefined, keyOrUndefined) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传图片，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.leftTreeIndustryCode)\r\n        formData.append('sceneCode', node.code)\r\n\r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 单独上传背景图片首帧时，使用 fileUrl\r\n          const imageUrl = res.data.fileUrl\r\n          \r\n          // 判断是否为数组形式的上传\r\n          if (typeof indexOrUndefined === 'number' && keyOrUndefined) {\r\n            // 数组形式：node[type][arrayKey][index][key]\r\n            this.$set(node[type][arrayKeyOrKey][indexOrUndefined], keyOrUndefined, imageUrl)\r\n            console.log('上传成功，设置图片URL:', imageUrl)\r\n            console.log('当前resource:', node[type][arrayKeyOrKey][indexOrUndefined])\r\n            this.$message.success('上传成功')\r\n          } else {\r\n            // 单个字段形式：node[type][key]\r\n            this.$set(node[type], arrayKeyOrKey, imageUrl)\r\n            this.$message.success('上传成功')\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('上传错误:', error)\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    \r\n    async beforeUploadSceneConfigFile(file, node, type, arrayKeyOrKey, indexOrUndefined, keyOrUndefined) {\r\n      try {\r\n        this.$modal.loading(\"正在上传文件，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.leftTreeIndustryCode)\r\n        formData.append('sceneCode', node.code)\r\n\r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 判断是否为数组形式的上传\r\n          if (typeof indexOrUndefined === 'number' && keyOrUndefined) {\r\n            // 数组形式处理...\r\n            this.$set(node[type][arrayKeyOrKey][indexOrUndefined], keyOrUndefined, res.data.fileUrl)\r\n            \r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            this.$set(this.fileLists[type], indexOrUndefined, [{\r\n              name: fileName,\r\n              url: res.data.fileUrl,\r\n              uid: Date.now()\r\n            }])\r\n            \r\n            if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n              this.$set(node[type][arrayKeyOrKey][indexOrUndefined], 'bgImg', res.data.imgUrl)\r\n              this.$message.success('上传成功，已自动生成背景图片首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          } else {\r\n            // 单个字段形式：node[type][key]\r\n            this.$set(node[type], arrayKeyOrKey, res.data.fileUrl)\r\n            \r\n            // 如果是介绍视频上传\r\n            if (type === 'introduceVideoVo' && arrayKeyOrKey === 'backgroundFileUrl') {\r\n              // 更新介绍视频文件列表\r\n              this.updateIntroduceVideoFileList()\r\n              \r\n              if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n                this.$set(node[type], 'backgroundImgFileUrl', res.data.imgUrl)\r\n                this.$message.success('上传成功，已自动生成介绍视频首帧')\r\n              } else {\r\n                this.$message.success('上传成功')\r\n              }\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    addPainPoint(type) {\r\n      this.node[type].painPoints = this.node[type].painPoints || []\r\n      this.node[type].painPoints.push({ title: '', contents: [''], showTime: '' })\r\n    },\r\n    removePainPoint(type, idx) {\r\n      this.node[type].painPoints.splice(idx, 1)\r\n    },\r\n    addPainContent(type, idx) {\r\n      this.node[type].painPoints[idx].contents.push('')\r\n    },\r\n    removePainContent(type, idx, cidx) {\r\n      this.node[type].painPoints[idx].contents.splice(cidx, 1)\r\n    },\r\n    addCostContent() {\r\n      this.node.costEstimate.contents.push('')\r\n    },\r\n    removeCostContent(cidx) {\r\n      this.node.costEstimate.contents.splice(cidx, 1)\r\n    },\r\n    // 获取背景文件列表\r\n    getBackgroundFileList(node, type, idx) {\r\n      const resource = node[type].backgroundResources[idx]\r\n      if (resource && resource.bgFile) {\r\n        const fileName = resource.bgFile.split('/').pop()\r\n        return [{\r\n          name: fileName,\r\n          url: resource.bgFile,\r\n          uid: Date.now() + idx\r\n        }]\r\n      }\r\n      return []\r\n    },\r\n    // 处理背景文件删除\r\n    handleRemoveBackgroundFile(node, type, idx) {\r\n      const resource = node[type].backgroundResources[idx]\r\n      resource.bgFile = ''\r\n      resource.bgImg = '' // 同时清空背景图片首帧\r\n      this.$set(this.fileLists[type], idx, [])\r\n      this.$message.success('文件已删除')\r\n    },\r\n    // 获取文件列表 - 确保返回正确的文件列表\r\n    getFileList(type, idx) {\r\n      // 检查对应的资源是否真的有文件\r\n      const resource = this.node[type] && this.node[type].backgroundResources && this.node[type].backgroundResources[idx]\r\n      \r\n      if (!resource || !resource.bgFile || !resource.bgFile.trim()) {\r\n        // 如果没有文件，返回空数组\r\n        return []\r\n      }\r\n      \r\n      // 如果文件列表中有数据且与资源文件匹配，返回文件列表\r\n      if (this.fileLists[type] && this.fileLists[type][idx] && this.fileLists[type][idx].length > 0) {\r\n        const fileList = this.fileLists[type][idx]\r\n        // 验证文件列表中的URL是否与资源中的文件URL匹配\r\n        if (fileList[0].url === resource.bgFile) {\r\n          return fileList\r\n        }\r\n      }\r\n      \r\n      // 如果文件列表不匹配或为空，但资源有文件，重新创建文件列表\r\n      if (resource.bgFile && resource.bgFile.trim()) {\r\n        const fileName = resource.bgFile.split('/').pop()\r\n        const newFileList = [{\r\n          name: fileName,\r\n          url: resource.bgFile,\r\n          uid: Date.now()\r\n        }]\r\n        this.$set(this.fileLists[type], idx, newFileList)\r\n        return newFileList\r\n      }\r\n      \r\n      return []\r\n    },\r\n    getUploadMode(type, idx) {\r\n      if (!this.uploadModes[type][idx]) {\r\n        // 默认根据是否已有文件URL来判断模式\r\n        const resource = this.node[type].backgroundResources[idx]\r\n        const hasFile = resource && resource.bgFile\r\n        this.$set(this.uploadModes[type], idx, hasFile ? 'upload' : 'upload')\r\n      }\r\n      return this.uploadModes[type][idx]\r\n    },\r\n    setUploadMode(type, idx, value) {\r\n      if (typeof idx === 'string') {\r\n        // 介绍视频模式：type='introduceVideo', idx='upload'或'url'\r\n        this.$set(this.uploadModes, type, idx)\r\n      } else {\r\n        // 背景资源模式：type='tradition'或'wisdom5g', idx=数字索引, value='upload'或'url'\r\n        this.$set(this.uploadModes[type], idx, value)\r\n      }\r\n    },\r\n    handleUrlInput(type, idx, value) {\r\n      // 清空文件列表\r\n      this.$set(this.fileLists[type], idx, [])\r\n      \r\n      // 如果输入了链接，创建对应的文件列表项\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.$set(this.fileLists[type], idx, [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }])\r\n      }\r\n    },\r\n    // 更新介绍视频文件列表\r\n    updateIntroduceVideoFileList() {\r\n      if (this.node.introduceVideoVo && this.node.introduceVideoVo.backgroundFileUrl) {\r\n        const fileName = this.node.introduceVideoVo.backgroundFileUrl.split('/').pop()\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: this.node.introduceVideoVo.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.introduceVideoFileList = []\r\n      }\r\n    },\r\n    getIntroduceVideoFileList() {\r\n      return this.introduceVideoFileList\r\n    },\r\n    handleRemoveIntroduceVideo() {\r\n      if (this.node.introduceVideoVo) {\r\n        this.node.introduceVideoVo.backgroundFileUrl = ''\r\n        this.node.introduceVideoVo.backgroundImgFileUrl = ''\r\n        this.$message.success('介绍视频已删除')\r\n        // 同时更新文件列表\r\n        this.updateIntroduceVideoFileList()\r\n      }\r\n    },\r\n    // 介绍视频开关变化\r\n    onIntroduceVideoStatusChange(val) {\r\n      // 简单处理，不需要复杂逻辑\r\n    },\r\n    \r\n    // 图片预览\r\n    previewImage(url) {\r\n      if (url) {\r\n        this.previewImageUrl = url\r\n        this.previewVisible = true\r\n      }\r\n    },\r\n    \r\n    // 删除介绍视频图片\r\n    deleteIntroduceVideoImg() {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.node.introduceVideoVo.backgroundImgFileUrl = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 删除背景资源图片\r\n    deleteBackgroundImg(type, idx) {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.node[type].backgroundResources[idx].bgImg = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    // 验证XML Key输入（只允许英文、数字和下划线）\r\n    validateXmlKey(value, type) {\r\n      // 只保留英文字母、数字和下划线\r\n      const filteredValue = value.replace(/[^a-zA-Z0-9_]/g, '')\r\n      this.node[type].panoramicViewXmlKey = filteredValue\r\n    },\r\n    handleIntroduceVideoUrlInput(value) {\r\n      this.introduceVideoFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n      this.updateIntroduceVideoFileList()\r\n    },\r\n    // 处理坐标数字输入\r\n    handleCoordNumberInput(val, coord, field) {\r\n      // 如果val为null或undefined，设置为空字符串\r\n      if (val === null || val === undefined) {\r\n        coord[field] = ''\r\n      } else {\r\n        coord[field] = val\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断当前节点是否有子节点\r\n    hasChildren() {\r\n      return this.node && this.node.children && this.node.children.length > 0\r\n    }\r\n  },\r\n  components: {\r\n    SceneConfigNode: null // 递归注册，主页面import时补全\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 限制上传图片的显示大小 */\r\n.image-upload .el-upload--picture-card {\r\n  width: 148px;\r\n  height: 148px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.upload-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 背景图片首帧图片大小控制 */\r\n.image-upload .el-upload-list__item-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 上传框也添加圆角 */\r\n.image-upload .el-upload--picture-card {\r\n  border-radius: 8px;\r\n}\r\n\r\n.mini-block {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.background-resource-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  background-color: #fafafa;\r\n  position: relative;\r\n}\r\n\r\n.resource-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.resource-title {\r\n  font-weight: bold;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.pain-point-block {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.pain-point-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 图片预览样式 */\r\n.preview-container {\r\n  text-align: center;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 70vh;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 图片悬停操作样式 */\r\n.image-preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  border-radius: 6px;\r\n}\r\n\r\n.image-preview-container:hover .image-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.preview-icon,\r\n.delete-icon {\r\n  color: white;\r\n  font-size: 20px;\r\n  margin: 0 10px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.preview-icon:hover,\r\n.delete-icon:hover {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.coordinate-group {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  margin-bottom: 12px;\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.coordinate-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: bold;\r\n  color: #606266;\r\n}\r\n\r\n/* 确保介绍视频和成本预估卡片高度一致 */\r\n.mini-block .el-card {\r\n  height: 100%;\r\n}\r\n\r\n.mini-block .el-card__body {\r\n  min-height: 300px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 介绍视频和成本预估并排时的高度统一 */\r\n.el-row .el-col .mini-block {\r\n  height: 100%;\r\n}\r\n\r\n.el-row .el-col .mini-block .el-card {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.el-row .el-col .mini-block .el-card__body {\r\n  flex: 1;\r\n  min-height: 300px;\r\n}\r\n</style> \r\n"]}]}