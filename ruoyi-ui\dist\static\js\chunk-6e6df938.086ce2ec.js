(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e6df938"],{1113:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"business-value-config"},[n("div",{staticClass:"config-header"},[n("span",[e._v("商业价值配置")]),n("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.addBusinessValue}},[n("i",{staticClass:"el-icon-plus"}),e._v(" 添加商业价值 ")])],1),n("div",{staticClass:"value-list"},[n("el-row",{attrs:{gutter:20}},e._l(e.businessValues,(function(t,i){return n("el-col",{key:i,attrs:{span:12}},[n("el-card",{staticClass:"value-item",attrs:{shadow:"hover"}},[n("div",{staticClass:"value-header",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("商业价值 "+e._s(i+1))]),n("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.removeValue(i)}}})],1),n("el-form",{attrs:{model:t,"label-width":"80px",size:"small"}},[n("el-form-item",{attrs:{label:"名称",required:""}},[n("el-input",{attrs:{placeholder:"请输入商业价值名称"},on:{input:e.debouncedEmitChange},model:{value:t.tag,callback:function(n){e.$set(t,"tag",n)},expression:"value.tag"}})],1),n("el-row",{attrs:{gutter:16}},[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"图片"}},[n("el-upload",{key:"image-"+i+"-"+(t.id||"new"),staticClass:"image-upload",attrs:{action:"#","show-file-list":!1,"list-type":"picture-card",accept:"image/*","before-upload":function(t){return e.beforeUploadImage(t,i)},"http-request":function(){}}},[t.imageUrl?n("div",{staticClass:"image-preview-container"},[n("img",{staticClass:"upload-image",attrs:{src:t.imageUrl}}),n("div",{staticClass:"image-overlay"},[n("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览"},on:{click:function(n){return n.stopPropagation(),e.previewImage(t.imageUrl)}}}),n("i",{staticClass:"el-icon-delete delete-icon",attrs:{title:"删除"},on:{click:function(t){return t.stopPropagation(),e.deleteValueImage(i)}}})])]):n("i",{staticClass:"el-icon-plus"})]),n("div",{staticClass:"upload-tip"},[e._v("支持jpg/png，最大20MB")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"视频文件"}},[n("div",{staticStyle:{"margin-bottom":"8px"}},[n("el-radio-group",{attrs:{value:e.getUploadMode("video",i),size:"small"},on:{input:function(t){return e.setUploadMode("video",i,t)}}},[n("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),n("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===e.getUploadMode("video",i)?n("el-upload",{key:"video-"+i+"-"+(t.id||"new")+"-"+(t.videoUrl?"has":"empty"),attrs:{action:"#","show-file-list":!0,"file-list":t.videoFileList,accept:"video/*","before-upload":function(t){return e.beforeUploadVideo(t,i)},"http-request":function(){},"on-remove":function(t){return e.handleRemoveVideo(t,i)}}},[n("el-button",{attrs:{size:"small",type:"primary"}},[e._v("选择视频")]),n("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持mp4格式，最大200MB")])],1):n("el-input",{attrs:{value:t.videoUrl,placeholder:"请输入视频链接"},on:{input:function(t){return e.handleVideoUrlInput(t,i)}}})],1)],1)],1)],1)],1)],1)})),1),e.businessValues.length?e._e():n("div",{staticClass:"empty-state"},[n("i",{staticClass:"el-icon-document-add"}),n("p",[e._v("暂无商业价值，点击上方按钮添加")])])],1),n("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览",width:"60%","append-to-body":""},on:{"update:visible":function(t){e.previewVisible=t}}},[n("div",{staticClass:"preview-container"},[n("img",{staticClass:"preview-image",attrs:{src:e.previewImageUrl}})])])],1)},a=[],r=n("c7eb"),s=n("1da1"),o=(n("d81d"),n("14d9"),n("a434"),n("b0c0"),n("e9c4"),n("8a79"),n("2ca0"),n("86e4")),l={name:"BusinessValueConfig",props:{value:{type:Array,default:function(){return[]}},leftTreeIndustryCode:{type:String,default:""}},data:function(){return{businessValues:[],isUpdating:!1,emitTimer:null,previewVisible:!1,previewImageUrl:"",videoUploadModes:{}}},watch:{value:{handler:function(e){if(!this.isUpdating&&e&&Array.isArray(e)){var t=e.map((function(e){return{id:e.id||null,viewInfoId:e.viewInfoId||null,tag:e.tag||"",imageUrl:e.backgroundImgFileUrl||"",videoUrl:e.backgroundFileUrl||"",videoFileList:e.backgroundFileUrl?[{name:e.backgroundFileUrl.split("/").pop(),url:e.backgroundFileUrl,uid:Date.now()}]:[]}}));JSON.stringify(this.businessValues)!==JSON.stringify(t)&&(this.businessValues=t)}},immediate:!0,deep:!1}},methods:{debouncedEmitChange:function(){var e=this;this.emitTimer&&clearTimeout(this.emitTimer),this.emitTimer=setTimeout((function(){e.emitChange()}),200)},emitChange:function(){var e=this;this.emitTimer&&clearTimeout(this.emitTimer),this.emitTimer=setTimeout((function(){e.isUpdating=!0;var t=e.businessValues.map((function(e){return{id:e.id||null,viewInfoId:e.viewInfoId||null,type:5,tag:e.tag||null,backgroundImgFileUrl:e.imageUrl||null,backgroundFileUrl:e.videoUrl||null}}));e.$emit("input",t),e.$nextTick((function(){setTimeout((function(){e.isUpdating=!1}),50)}))}),200)},addBusinessValue:function(){var e={tag:"",imageUrl:"",videoUrl:"",videoFileList:[]};this.businessValues.push(e),this.emitChange()},removeValue:function(e){var t=this;return Object(s["a"])(Object(r["a"])().mark((function n(){var i;return Object(r["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=t.businessValues[e],t.$confirm("确定删除此商业价值吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(s["a"])(Object(r["a"])().mark((function n(){var a;return Object(r["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!i.id){n.next=17;break}return n.prev=1,t.$modal.loading("正在删除商业价值，请稍候..."),n.next=5,Object(o["e"])({id:i.id});case 5:a=n.sent,0===a.code?(t.businessValues.splice(e,1),t.emitChange(),t.$message.success("删除成功")):t.$message.error(a.msg||"删除失败"),n.next=12;break;case 9:n.prev=9,n.t0=n["catch"](1),t.$message.error("删除失败");case 12:return n.prev=12,t.$modal.closeLoading(),n.finish(12);case 15:n.next=20;break;case 17:t.businessValues.splice(e,1),t.emitChange(),t.$message.success("删除成功");case 20:case"end":return n.stop()}}),n,null,[[1,9,12,15]])})))).catch((function(){}));case 2:case"end":return n.stop()}}),n)})))()},beforeUploadImage:function(e,t){var n=this;return Object(s["a"])(Object(r["a"])().mark((function i(){var a,s,l;return Object(r["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.type.startsWith("image/")){i.next=3;break}return n.$message.error("只能上传图片文件！"),i.abrupt("return",!1);case 3:if(!(e.size>20971520)){i.next=6;break}return n.$message.error("图片大小不能超过20MB！"),i.abrupt("return",!1);case 6:return i.prev=6,n.$modal.loading("正在上传图片，请稍候..."),a=new FormData,a.append("file",e),a.append("industryCode",n.leftTreeIndustryCode),i.next=13,Object(o["h"])(a);case 13:s=i.sent,0===s.code&&s.data?(l=n.businessValues[t],l.imageUrl=s.data.fileUrl,n.debouncedEmitChange(),n.$message.success("上传成功")):n.$message.error(s.msg||"上传失败"),i.next=20;break;case 17:i.prev=17,i.t0=i["catch"](6),n.$message.error("上传失败");case 20:return i.prev=20,n.$modal.closeLoading(),i.finish(20);case 23:return i.abrupt("return",!1);case 24:case"end":return i.stop()}}),i,null,[[6,17,20,23]])})))()},beforeUploadVideo:function(e,t){var n=this;return Object(s["a"])(Object(r["a"])().mark((function i(){var a,s,l,c;return Object(r["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.type.startsWith("video/")||e.name.endsWith(".mp4")){i.next=3;break}return n.$message.error("只能上传MP4视频文件！"),i.abrupt("return",!1);case 3:if(!(e.size>209715200)){i.next=6;break}return n.$message.error("视频大小不能超过200MB！"),i.abrupt("return",!1);case 6:return i.prev=6,n.$modal.loading("正在上传视频，请稍候..."),a=new FormData,a.append("file",e),a.append("industryCode",n.leftTreeIndustryCode),i.next=13,Object(o["h"])(a);case 13:s=i.sent,0===s.code&&s.data?(l=n.businessValues[t],c=s.data.fileUrl.split("/").pop(),l.videoUrl=s.data.fileUrl,l.videoFileList=[{name:c,url:s.data.fileUrl,uid:Date.now()}],n.debouncedEmitChange(),n.$message.success("上传成功")):n.$message.error(s.msg||"上传失败"),i.next=20;break;case 17:i.prev=17,i.t0=i["catch"](6),n.$message.error("上传失败");case 20:return i.prev=20,n.$modal.closeLoading(),i.finish(20);case 23:return i.abrupt("return",!1);case 24:case"end":return i.stop()}}),i,null,[[6,17,20,23]])})))()},handleRemoveVideo:function(e,t){var n=this.businessValues[t];n.videoUrl="",n.videoFileList=[],this.emitChange()},previewImage:function(e){e&&(this.previewImageUrl=e,this.previewVisible=!0)},deleteValueImage:function(e){var t=this;this.$confirm("确定删除此图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.businessValues[e].imageUrl="",t.debouncedEmitChange(),t.$message.success("图片已删除")})).catch((function(){}))},getUploadMode:function(e,t){return this.videoUploadModes[t]||this.$set(this.videoUploadModes,t,"upload"),this.videoUploadModes[t]},setUploadMode:function(e,t,n){this.$set(this.videoUploadModes,t,n)},handleVideoUrlInput:function(e,t){var n=this.businessValues[t];if(n.videoUrl=e,n.videoFileList=[],e){var i=e.split("/").pop()||"外部链接文件";n.videoFileList=[{name:i,url:e,uid:Date.now()}]}this.debouncedEmitChange()}}},c=l,d=(n("578d"),n("2877")),u=Object(d["a"])(c,i,a,!1,null,"283b8466",null);t["default"]=u.exports},"1e4b":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"page-container"},[n("div",{staticClass:"menu-panel"},[n("div",{staticClass:"menu-search"},[n("el-input",{attrs:{placeholder:"搜索菜单...","prefix-icon":"el-icon-search",clearable:""},on:{input:e.handleSearch},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}})],1),n("el-tree",{ref:"menuTree",staticClass:"menu-tree",attrs:{data:e.filteredMenuData,props:{label:"name",children:"children"},"node-key":"id","current-node-key":e.activeMenu,"highlight-current":"","expand-on-click-node":!1,"default-expanded-keys":e.menuData.map((function(e){return e.id}))},on:{"node-click":e.handleTreeNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){t.node;var i=t.data;return n("span",{staticClass:"custom-tree-node"},[n("span",{domProps:{innerHTML:e._s(e.highlightText(i.name))}})])}}])})],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.switchingIndustry,expression:"switchingIndustry"}],staticClass:"content-panel",attrs:{"element-loading-text":"正在切换行业..."}},[n("el-form",{ref:"sceneForm",attrs:{model:e.form,"label-width":"120px"}},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-card",{staticClass:"mini-block",attrs:{shadow:"never"}},[n("div",{attrs:{slot:"header"},slot:"header"},[e._v("基本信息")]),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"主标题",required:""}},[n("el-input",{attrs:{placeholder:"请输入主标题"},model:{value:e.form.mainTitle,callback:function(t){e.$set(e.form,"mainTitle",t)},expression:"form.mainTitle"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"副标题",required:""}},[n("el-input",{attrs:{placeholder:"请输入副标题"},model:{value:e.form.subTitle,callback:function(t){e.$set(e.form,"subTitle",t)},expression:"form.subTitle"}})],1)],1)],1),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"背景图片首帧"}},[n("el-upload",{staticClass:"upload image-upload",attrs:{action:"#","show-file-list":!1,"list-type":"picture-card",accept:"image/*","before-upload":e.beforeUploadIntroduceImg,"http-request":function(){}}},[e.form.bgImgUrl?n("div",{staticClass:"image-preview-container"},[n("img",{staticClass:"upload-image",attrs:{src:e.form.bgImgUrl}}),n("div",{staticClass:"image-overlay"},[n("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览"},on:{click:function(t){return t.stopPropagation(),e.previewImage(e.form.bgImgUrl)}}}),n("i",{staticClass:"el-icon-delete delete-icon",attrs:{title:"删除"},on:{click:function(t){return t.stopPropagation(),e.deleteBgImage(t)}}})])]):n("i",{staticClass:"el-icon-plus"})])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"主题选择"}},[n("theme-selection-dialog",{on:{change:e.onThemeChange},model:{value:e.selectedTheme,callback:function(t){e.selectedTheme=t},expression:"selectedTheme"}})],1)],1)],1),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"背景文件"}},[n("div",{staticStyle:{"margin-bottom":"8px"}},[n("el-radio-group",{attrs:{size:"small"},on:{input:function(t){return e.setUploadMode("bgFile",t)}},model:{value:e.uploadModes.bgFile||"upload",callback:function(t){e.$set(e.uploadModes,"bgFile || 'upload'",t)},expression:"uploadModes.bgFile || 'upload'"}},[n("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),n("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===(e.uploadModes.bgFile||"upload")?n("el-upload",{staticClass:"upload",attrs:{action:"#","show-file-list":!0,"file-list":e.bgFileList,"before-upload":function(t){return e.beforeUploadIntroduceVideo(t)},"http-request":function(){},"on-remove":e.handleRemoveBgFile}},[n("el-button",{attrs:{type:"primary"}},[e._v("上传背景文件")])],1):n("el-input",{attrs:{placeholder:"请输入文件链接"},on:{input:e.handleBgFileUrlInput},model:{value:e.form.bgFileUrl,callback:function(t){e.$set(e.form,"bgFileUrl",t)},expression:"form.bgFileUrl"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"XML文件"}},[n("el-upload",{staticClass:"upload",attrs:{action:"#","show-file-list":!0,"file-list":e.xmlFileList,accept:".xml","before-upload":e.beforeUploadXmlFile,"http-request":function(){},"on-remove":e.handleRemoveXmlFile}},[n("el-button",{attrs:{type:"primary"}},[e._v("上传XML文件")]),n("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传xml文件，且不超过50MB")])],1)],1)],1)],1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-card",{staticClass:"mini-block video-card",attrs:{shadow:"never"}},[n("div",{staticClass:"video-header-row",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("视频讲解")]),n("el-switch",{staticStyle:{float:"right"},attrs:{"active-value":"0","inactive-value":"1"},model:{value:e.videoExplanation.status,callback:function(t){e.$set(e.videoExplanation,"status",t)},expression:"videoExplanation.status"}})],1),n("div",{directives:[{name:"show",rawName:"v-show",value:"0"===e.videoExplanation.status,expression:"videoExplanation.status === '0'"}]},[n("el-collapse-transition",[n("div",{staticClass:"video-card-yu"},[n("el-form-item",{attrs:{label:"讲解视频"}},[n("div",{staticStyle:{"margin-bottom":"8px"}},[n("el-radio-group",{attrs:{size:"small"},on:{input:function(t){return e.setUploadMode("videoExplanation",t)}},model:{value:e.uploadModes.videoExplanation||"upload",callback:function(t){e.$set(e.uploadModes,"videoExplanation || 'upload'",t)},expression:"uploadModes.videoExplanation || 'upload'"}},[n("el-radio-button",{attrs:{label:"upload"}},[e._v("上传文件")]),n("el-radio-button",{attrs:{label:"url"}},[e._v("填写链接")])],1)],1),"upload"===(e.uploadModes.videoExplanation||"upload")?n("el-upload",{attrs:{action:"#","show-file-list":!0,"file-list":e.videoExplanationFileList,accept:".mp4","before-upload":function(t){return e.beforeUploadIntroduceVideo(t,"videoExplanation","backgroundFileUrl")},"http-request":function(){},"on-remove":e.handleRemoveVideoExplanationFile}},[n("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),n("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传mp4文件")])],1):n("el-input",{attrs:{placeholder:"请输入视频链接"},on:{input:e.handleVideoExplanationUrlInput},model:{value:e.videoExplanation.backgroundFileUrl,callback:function(t){e.$set(e.videoExplanation,"backgroundFileUrl",t)},expression:"videoExplanation.backgroundFileUrl"}})],1),n("el-form-item",{attrs:{label:"视频分段说明"}},[n("div",{staticClass:"segment-scroll"},e._l(e.videoSegmentedList,(function(t,i){return n("div",{key:i,staticStyle:{display:"flex","align-items":"center","margin-bottom":"8px"}},[n("el-input-number",{staticStyle:{width:"120px","margin-right":"10px"},attrs:{min:0,max:999999,placeholder:"时间"},on:{change:function(t){return e.handleTimeChange(t,i)}},model:{value:t.time,callback:function(n){e.$set(t,"time",n)},expression:"seg.time"}}),n("span",{staticStyle:{"margin-right":"10px",color:"#606266"}},[e._v("秒")]),n("el-cascader",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{options:e.sceneTreeOptions,props:e.sceneCascaderProps,filterable:"","check-strictly":"",placeholder:"所属场景"},on:{change:function(t){return e.handleSceneCascaderChange(t,i)}},model:{value:t.sceneId,callback:function(n){e.$set(t,"sceneId",n)},expression:"seg.sceneId"}}),n("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.removeVideoSegment(i)}}})],1)})),0),n("el-button",{staticStyle:{"margin-top":"8px"},attrs:{type:"primary",plain:""},on:{click:e.addVideoSegment}},[e._v("增加分段")])],1)],1)])],1)])],1)],1)],1),e._l(e.categories,(function(t,i){return n("div",{key:t.key,staticClass:"category-block"},[n("div",{staticClass:"category-header"},[n("div",{staticClass:"category-title",on:{dblclick:function(t){return e.startEditTitle(i)}}},[t.editing?n("el-input",{staticStyle:{width:"200px"},attrs:{"data-edit-index":i,size:"small",placeholder:"请输入标题"},on:{blur:function(t){return e.finishEditTitle(i)},keyup:[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.finishEditTitle(i)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"esc",27,t.key,["Esc","Escape"])?null:e.cancelEditTitle(i)}]},model:{value:t.editingName,callback:function(n){e.$set(t,"editingName",n)},expression:"category.editingName"}}):n("span",[e._v(e._s(t.name))])],1),n("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ccc"},model:{value:t.enabled,callback:function(n){e.$set(t,"enabled",n)},expression:"category.enabled"}})],1),n("div",{directives:[{name:"show",rawName:"v-show",value:t.enabled,expression:"category.enabled"}],staticClass:"category-body"},["default_scene"===t.key?n("div",[n("el-form",{attrs:{"label-width":"120px"}},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:24}},[n("el-card",{staticClass:"mini-block scene-config-container",staticStyle:{"margin-top":"20px"},attrs:{shadow:"never"}},[n("div",{attrs:{slot:"header"},slot:"header"},[e._v("场景配置")]),n("div",[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:6}},[n("el-tree",{ref:"sceneTree",refInFor:!0,attrs:{data:e.sceneConfigTree,"node-key":"id",props:{label:"name",children:"children"},"highlight-current":"","expand-on-click-node":!1,"default-expanded-keys":e.treeExpandedKeys.length>0?e.treeExpandedKeys:e.sceneConfigTree.length?[e.sceneConfigTree[0].id]:[],"current-node-key":e.selectedNode?e.selectedNode.id:null,sort:!1},on:{"node-click":e.handleSceneNodeClick}})],1),n("el-col",{attrs:{span:18}},[e.selectedNode?n("SceneConfigNode",{attrs:{node:e.selectedNode,"root-tree":e.sceneConfigTree,"scene-tree-options":e.sceneTreeOptions,"left-tree-industry-code":e.industryCode}}):e._e()],1)],1)],1)])],1)],1)],1)],1):"default_plan"===t.key?n("div",[n("network-plan-config",{attrs:{"left-tree-industry-code":e.industryCode},model:{value:e.networkPlanData,callback:function(t){e.networkPlanData=t},expression:"networkPlanData"}})],1):"default_value"===t.key?n("div",[n("business-value-config",{attrs:{"left-tree-industry-code":e.industryCode},model:{value:e.businessValueData,callback:function(t){e.businessValueData=t},expression:"businessValueData"}})],1):"default_vr"===t.key?n("div",[n("vr-scene-config",{model:{value:e.vrSceneData,callback:function(t){e.vrSceneData=t},expression:"vrSceneData"}})],1):n("div",[n("p",[e._v("这里是 "),n("strong",[e._v(e._s(t.name))]),e._v(" 分类的内容区域。")])])])])})),n("div",{staticClass:"submit-footer"},[n("div",{staticClass:"form-actions"},[n("el-tooltip",{attrs:{effect:"dark",content:"链接填写完成后，点击【提交】后再点击【同步】按钮",placement:"top"}},[n("el-button",{attrs:{type:"success",disabled:!e.form.sceneViewConfigId,loading:e.synchronizing},on:{click:e.handleSynchronizeFile}},[e._v(" "+e._s(e.synchronizing?"同步中...":"同步文件")+" ")])],1),n("el-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary",loading:e.submitting},on:{click:e.handleSubmit}},[e._v(" "+e._s(e.submitting?"提交中...":"提交")+" ")])],1)])],2),n("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览",width:"60%","append-to-body":""},on:{"update:visible":function(t){e.previewVisible=t},close:e.closePreview}},[n("div",{staticClass:"preview-container"},[n("img",{staticClass:"preview-image",attrs:{src:e.previewImageUrl}})])])],1)},a=[],r=n("b85c"),s=n("c7eb"),o=n("2909"),l=n("1da1"),c=n("5530"),d=(n("99af"),n("4de4"),n("7db0"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("fb6a"),n("a434"),n("b0c0"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("25f0"),n("8a79"),n("2532"),n("3ca3"),n("5319"),n("841c"),n("2ca0"),n("498a"),n("159b"),n("ddb0"),n("9861"),n("88a7"),n("271a"),n("5494"),n("af49")),u=n("4b51"),p=n("86e4"),m=n("5c6e"),h=n("1113"),f=n("fa87"),g=n("a5f5"),v=n("bc3a"),b=n.n(v),y={name:"IndustryScenePage",components:{SceneConfigNode:u["default"],NetworkPlanConfig:m["default"],BusinessValueConfig:h["default"],VrSceneConfig:f["default"],ThemeSelectionDialog:g["default"]},data:function(){var e=this;return{menuData:[],flatMenuData:[],activeMenu:"",industryCode:"",selectedTheme:null,form:{mainTitle:"",subTitle:"",bgImgUrl:"",bgFileUrl:"",panoramicViewXmlUrl:""},sceneConfigTree:[],selectedNode:null,loading:!1,switchingIndustry:!1,rules:{introduceVideoImgUrl:[{required:!0,message:"请上传介绍视频首帧",trigger:"change"}],introduceVideoFileUrl:[{required:!0,message:"请上传介绍视频",trigger:"change"}],videoExplanationFileUrl:[{required:!0,message:"请上传讲解视频",trigger:"change"}]},uploadingType:"",uploadingKey:"",categories:[],introduceVideo:{status:"0",backgroundImgFileUrl:"",backgroundFileUrl:""},videoExplanation:{status:"0",backgroundFileUrl:"",videoSegmentedVoList:[]},sceneTreeOptions:[],sceneCascaderProps:{label:"sceneName",value:"id",children:"children",emitPath:!1,checkStrictly:!0,disabled:function(t){var n=!(!e.videoExplanation||!e.videoExplanation.videoSegmentedVoList)&&e.videoExplanation.videoSegmentedVoList.some((function(e){return e.sceneId===t.id}));return n}},bgFileList:[],videoExplanationFileList:[],xmlFileList:[],networkPlanDataMap:{},businessValueDataMap:{},vrSceneDataMap:{},previewVisible:!1,previewImageUrl:"",searchKeyword:"",treeExpandedKeys:[],uploadModes:{bgFile:"upload",videoExplanation:"upload",introduceVideo:"upload"},synchronizing:!1,submitting:!1}},computed:{videoSegmentedList:function(){return this.videoExplanation.videoSegmentedVoList&&0!==this.videoExplanation.videoSegmentedVoList.length?this.videoExplanation.videoSegmentedVoList:[{time:"",sceneId:"",sceneName:"",sceneCode:""}]},networkPlanData:{get:function(){var e=this.networkPlanDataMap[this.activeMenu];return e||{networkVideoList:[],videoExplanationVo:{status:"0",backgroundFileUrl:"",videoSegmentedVoList:[]}}},set:function(e){this.$set(this.networkPlanDataMap,this.activeMenu,e)}},businessValueData:{get:function(){return this.businessValueDataMap[this.activeMenu]||[]},set:function(e){this.$set(this.businessValueDataMap,this.activeMenu,e)}},vrSceneData:{get:function(){return this.vrSceneDataMap[this.activeMenu]||[]},set:function(e){this.$set(this.vrSceneDataMap,this.activeMenu,e)}},filteredMenuData:function(){var e=this;if(!this.searchKeyword)return this.menuData;var t=function t(n){return n.map((function(n){var i=n.children?t(n.children):[],a=n.name&&n.name.toLowerCase().includes(e.searchKeyword.toLowerCase());return a||i.length>0?Object(c["a"])(Object(c["a"])({},n),{},{children:i}):null})).filter(Boolean)};return t(this.menuData)}},created:function(){this.initTokenFromUrl(),this.loadIndustryMenu()},methods:{initTokenFromUrl:function(){var e=new URLSearchParams(window.location.search),t=e.get("token");if(t)localStorage.setItem("external-token",t),b.a.defaults.headers.common["Authorization"]="Bearer ".concat(t),console.log("从URL获取到token:",t);else{var n=localStorage.getItem("external-token");n&&(b.a.defaults.headers.common["Authorization"]="Bearer ".concat(n))}},loadIndustryMenu:function(){var e=this;return Object(l["a"])(Object(s["a"])().mark((function t(){var n;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(d["a"])();case 2:if(n=t.sent,0!==n.code||!Array.isArray(n.data)){t.next=13;break}if(e.menuData=n.data.map((function(e){return{id:"plate_".concat(e.plateKey),name:e.plateName,type:"plate",children:e.industryTreeListVos?e.industryTreeListVos.map((function(e){return{id:e.id,name:e.industryName,industryCode:e.industryCode,plate:e.plate,type:"industry"}})):[]}})),e.flatMenuData=[],n.data.forEach((function(t){var n;t.industryTreeListVos&&(n=e.flatMenuData).push.apply(n,Object(o["a"])(t.industryTreeListVos))})),!e.flatMenuData.length){t.next=13;break}return e.activeMenu=String(e.flatMenuData[0].id),e.industryCode=e.flatMenuData[0].industryCode,e.$nextTick((function(){e.$refs.menuTree&&e.$refs.menuTree.setCurrentKey&&e.$refs.menuTree.setCurrentKey(e.activeMenu)})),t.next=13,e.handleSelect(e.activeMenu);case 13:case"end":return t.stop()}}),t)})))()},handleTreeNodeClick:function(e){"industry"===e.type&&(this.handleSelect(String(e.id)),this.industryCode=e.industryCode)},handleSearch:function(e){this.searchKeyword=e},highlightText:function(e){if(!this.searchKeyword)return e;var t=new RegExp("(".concat(this.searchKeyword,")"),"gi");return e.replace(t,'<span class="highlight">$1</span>')},handleSelect:function(e){var t=arguments,n=this;return Object(l["a"])(Object(s["a"])().mark((function i(){var a,r,o,l,c,d,u,m,h;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return a=t.length>1&&void 0!==t[1]&&t[1],i.prev=1,n.switchingIndustry=!0,document.body.style.overflow="hidden",n.activeMenu=e,i.next=7,n.loadSceneTreeOptions(n.activeMenu);case 7:return r=a?n.selectedNode:null,n.selectedTheme=null,o=n.flatMenuData.find((function(t){return String(t.id)===e})),l=o?o.industryCode:null,i.next=13,Object(p["c"])({industryCode:l});case 13:c=i.sent,0===c.code&&c.data&&(n.form.sceneViewConfigId=c.data.sceneViewConfigId||"",n.form.mainTitle=c.data.mainTitle||"",n.form.subTitle=c.data.subTitle||"",n.form.bgImgUrl=c.data.backgroundImgFileUrl||"",n.form.bgFileUrl=c.data.backgroundFileUrl||"",n.form.panoramicViewXmlUrl=c.data.panoramicViewXmlUrl||"",n.updateBgFileList(),n.updateXmlFileList(),c.data.themeInfoVo?n.selectedTheme={themeId:c.data.themeInfoVo.themeId,themeName:c.data.themeInfoVo.themeName,themeEffectImg:c.data.themeInfoVo.themeEffectImg,remark:c.data.themeInfoVo.remark}:n.selectedTheme=null,c.data.sceneDefaultConfigVoList&&Array.isArray(c.data.sceneDefaultConfigVoList)&&(n.categories=c.data.sceneDefaultConfigVoList.map((function(e){return{id:e.id,key:e.keyName,name:e.name,enabled:"0"===e.keyValue,editing:!1,editingName:"",originalName:e.name,remark:e.remark,classification:e.classification,defaultStatus:e.defaultStatus}})),d=c.data.sceneDefaultConfigVoList.find((function(e){return"default_scene"===e.keyName})),d&&d.industrySceneInfoVo&&d.industrySceneInfoVo.videoExplanationVo?(u=d.industrySceneInfoVo.videoExplanationVo,n.videoExplanation={status:u.status||"0",backgroundFileUrl:u.backgroundFileUrl||"",videoSegmentedVoList:u.videoSegmentedVoList?u.videoSegmentedVoList.map((function(e){return{time:e.time,sceneCode:e.sceneCode,sceneName:e.sceneName,sceneId:n.findSceneIdByCode(e.sceneCode)}})):[]}):n.videoExplanation={status:"0",backgroundFileUrl:"",videoSegmentedVoList:[]},n.updateVideoExplanationFileList(),d&&d.industrySceneInfoVo&&d.industrySceneInfoVo.sceneListVo?(n.sceneConfigTree=n.adaptSceneTree(d.industrySceneInfoVo.sceneListVo),a&&r?(m=n.findNodeById(n.sceneConfigTree,r.id),n.selectedNode=m||(n.sceneConfigTree.length>0?n.sceneConfigTree[0]:null)):n.selectedNode=n.sceneConfigTree.length>0?n.sceneConfigTree[0]:null):(n.sceneConfigTree=[],n.selectedNode=null)),c.data.networkSolutionVo&&(h={networkVideoList:c.data.networkSolutionVo.networkVideoList||[],videoExplanationVo:c.data.networkSolutionVo.videoExplanationVo||{status:"0",backgroundFileUrl:"",videoSegmentedVoList:[]}},n.$set(n.networkPlanDataMap,n.activeMenu,h)),c.data.commercialValueListVo&&n.$set(n.businessValueDataMap,n.activeMenu,c.data.commercialValueListVo),c.data.vrInfoListVo&&n.$set(n.vrSceneDataMap,n.activeMenu,c.data.vrInfoListVo)),i.next=21;break;case 17:i.prev=17,i.t0=i["catch"](1),console.error("加载数据失败:",i.t0),n.$message.error("加载数据失败");case 21:return i.prev=21,n.switchingIndustry=!1,document.body.style.overflow="",i.finish(21);case 25:case"end":return i.stop()}}),i,null,[[1,17,21,25]])})))()},handleBeforeUpload:function(e){return!1},addSegment:function(){this.form.videoSegmentedVoList.push({time:"",scene:""})},removeSegment:function(e){this.form.videoSegmentedVoList.length>=1&&this.form.videoSegmentedVoList.splice(e,1)},beforeUploadIntroduceImg:function(e,t,n){var i=this;return Object(l["a"])(Object(s["a"])().mark((function a(){var r,o;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.type.startsWith("image/")){a.next=3;break}return i.$message.error("只能上传图片文件！"),a.abrupt("return",!1);case 3:return a.prev=3,i.$modal.loading("正在上传图片，请稍候..."),r=new FormData,r.append("file",e),r.append("industryCode",i.industryCode),a.next=10,Object(p["h"])(r);case 10:o=a.sent,0===o.code&&o.data?(t&&n?i[t][n]=o.data.fileUrl:i.form.bgImgUrl=o.data.fileUrl,i.$message.success("上传成功")):i.$message.error(o.msg||"上传失败"),a.next=17;break;case 14:a.prev=14,a.t0=a["catch"](3),i.$message.error("上传失败");case 17:return a.prev=17,i.$modal.closeLoading(),a.finish(17);case 20:return a.abrupt("return",!1);case 21:case"end":return a.stop()}}),a,null,[[3,14,17,20]])})))()},beforeUploadIntroduceVideo:function(e,t,n){var i=this;return Object(l["a"])(Object(s["a"])().mark((function a(){var r,o,l,c,d,u;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t||n){a.next=19;break}return a.prev=1,i.$modal.loading("正在上传文件，请稍候..."),r=new FormData,r.append("file",e),r.append("industryCode",i.industryCode),a.next=8,Object(p["h"])(r);case 8:o=a.sent,0===o.code&&o.data?(i.form.bgFileUrl=o.data.fileUrl,l=o.data.fileUrl.split("/").pop(),i.bgFileList=[{name:l,url:o.data.fileUrl,uid:Date.now()}],"video/mp4"===e.type&&o.data.imgUrl?(i.form.bgImgUrl=o.data.imgUrl,i.$message.success("上传成功，已自动生成背景图片首帧")):i.$message.success("上传成功")):i.$message.error(o.msg||"上传失败"),a.next=15;break;case 12:a.prev=12,a.t0=a["catch"](1),i.$message.error("上传失败");case 15:return a.prev=15,i.$modal.closeLoading(),a.finish(15);case 18:return a.abrupt("return",!1);case 19:if(e.type.startsWith("video/")||e.name.endsWith(".mp4")){a.next=22;break}return i.$message.error("只能上传MP4视频文件！"),a.abrupt("return",!1);case 22:return a.prev=22,i.$modal.loading("正在上传视频，请稍候..."),c=new FormData,c.append("file",e),c.append("industryCode",i.industryCode),a.next=29,Object(p["h"])(c);case 29:d=a.sent,0===d.code&&d.data?t&&n&&(i[t][n]=d.data.fileUrl,u=d.data.fileUrl.split("/").pop(),"introduceVideo"===t&&"backgroundFileUrl"===n?i.introduceVideoFileList=[{name:u,url:d.data.fileUrl,uid:Date.now()}]:"videoExplanation"===t&&"backgroundFileUrl"===n&&(i.videoExplanationFileList=[{name:u,url:d.data.fileUrl,uid:Date.now()}]),"introduceVideo"===t&&"backgroundFileUrl"===n&&d.data.imgUrl?(i.introduceVideo.backgroundImgFileUrl=d.data.imgUrl,i.$message.success("上传成功，已自动生成介绍视频首帧")):i.$message.success("上传成功")):i.$message.error(d.msg||"上传失败"),a.next=36;break;case 33:a.prev=33,a.t1=a["catch"](22),i.$message.error("上传失败");case 36:return a.prev=36,i.$modal.closeLoading(),a.finish(36);case 39:return a.abrupt("return",!1);case 40:case"end":return a.stop()}}),a,null,[[1,12,15,18],[22,33,36,39]])})))()},beforeUploadExplanationVideo:function(e){return this.uploadingType="mp4",this.uploadingKey="videoExplanationFileUrl",this.handleBeforeUpload(e)},addSceneConfigNode:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t={id:Date.now(),name:"新场景",type:"scene",enabled:!0,children:[],parentId:e};if(e){var n=this.findNodeById(this.sceneConfigTree,e);n&&n.children.push(t)}else this.sceneConfigTree.push(t);return t.id},removeSceneConfigNode:function(e){this.sceneConfigTree=this.sceneConfigTree.filter((function(t){return t.id!==e}))},findNodeById:function(e,t){var n,i=Object(r["a"])(e);try{for(i.s();!(n=i.n()).done;){var a=n.value;if(a.id===t)return a;if(a.children&&a.children.length>0){var s=this.findNodeById(a.children,t);if(s)return s}}}catch(o){i.e(o)}finally{i.f()}return null},addScenePainPoint:function(e){var t=this.findNodeById(this.sceneConfigTree,e);t&&"scene"===t.type&&(t.painPoints=t.painPoints||[],t.painPoints.push({title:"",contents:[""],showTime:""}))},removeScenePainPoint:function(e,t){var n=this.findNodeById(this.sceneConfigTree,e);n&&"scene"===n.type&&(n.painPoints=n.painPoints||[],n.painPoints.splice(t,1))},addScenePainContent:function(e,t){var n=this.findNodeById(this.sceneConfigTree,e);n&&"scene"===n.type&&(n.painPoints=n.painPoints||[],n.painPoints[t].contents.push(""))},removeScenePainContent:function(e,t,n){var i=this.findNodeById(this.sceneConfigTree,e);i&&"scene"===i.type&&(i.painPoints=i.painPoints||[],i.painPoints[t].contents.splice(n,1))},addSceneCostContent:function(e){var t=this.findNodeById(this.sceneConfigTree,e);t&&"costEstimate"===t.type&&(t.contents=t.contents||[],t.contents.push(""))},removeSceneCostContent:function(e,t){var n=this.findNodeById(this.sceneConfigTree,e);n&&"costEstimate"===n.type&&(n.contents=n.contents||[],n.contents.splice(t,1))},beforeUploadSceneConfigImg:function(e,t,n){var i=this;if(!e.type.startsWith("image/"))return this.$message.error("只能上传图片文件！"),!1;var a=new FileReader;return a.onload=function(e){i.findNodeById(i.sceneConfigTree,t)[n]=e.target.result},a.readAsDataURL(e),!1},beforeUploadSceneConfigFile:function(e,t,n){return this.findNodeById(this.sceneConfigTree,t)[n]=e.name,!1},handleSceneNodeClick:function(e){this.selectedNode=e},adaptSceneTree:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!e)return[];var i=Array.isArray(e)?e:[e];return i.map((function(e){var i={id:e.sceneId,sceneInfoId:e.sceneInfoId||null,name:e.sceneName,code:e.sceneCode,x:e.x,y:e.y,type:e.type,status:null!==e.status?e.status:"0",isUnfold:null!==e.isUnfold&&void 0!==e.isUnfold?e.isUnfold:"1",displayLocation:null!==e.displayLocation&&void 0!==e.displayLocation?e.displayLocation:"0",treeClassification:null!==e.treeClassification&&void 0!==e.treeClassification?e.treeClassification:"3",introduceVideoVo:e.introduceVideoVo?{id:e.introduceVideoVo.id||"",type:e.introduceVideoVo.type||"",viewInfoId:e.introduceVideoVo.viewInfoId||"",status:e.introduceVideoVo.status||"0",backgroundImgFileUrl:e.introduceVideoVo.backgroundImgFileUrl||"",backgroundFileUrl:e.introduceVideoVo.backgroundFileUrl||""}:{id:"",type:"",viewInfoId:"",status:"0",backgroundImgFileUrl:"",backgroundFileUrl:""},tradition:e.sceneTraditionVo?{name:e.sceneTraditionVo.name||"",panoramicViewXmlKey:e.sceneTraditionVo.panoramicViewXmlKey||"",backgroundResources:e.sceneTraditionVo.sceneVideoList?e.sceneTraditionVo.sceneVideoList.map((function(e){return{id:e.id||null,label:e.tag||"",coordinates:e.sceneFileRelList?e.sceneFileRelList.map((function(e){return{id:e.id||null,fileId:e.fileId||null,x:e.clickX||"",y:e.clickY||"",wide:e.wide||"",high:e.high||"",xmlKey:e.xmlKey||"",sceneId:t.findSceneIdByCode(e.bindSceneCode),sceneCode:e.bindSceneCode||""}})):[{id:null,fileId:null,x:"",y:"",wide:"",high:"",sceneId:"",sceneCode:""}],wide:e.wide||0,high:e.high||0,bgImg:e.backgroundImgFileUrl||"",bgFile:e.backgroundFileUrl||"",status:e.status||"",type:e.type||"",viewInfoId:e.viewInfoId||""}})):[],painPoints:e.sceneTraditionVo.painPointList&&Array.isArray(e.sceneTraditionVo.painPointList)?e.sceneTraditionVo.painPointList.map((function(e){return{painPointId:e.painPointId||null,title:e.bigTitle||"",contents:e.content||[],showTime:e.displayTime||""}})):[]}:{name:"",panoramicViewXmlKey:"",backgroundResources:[],painPoints:[]},wisdom5g:e.scene5gVo?{name:e.scene5gVo.name||"",panoramicViewXmlKey:e.scene5gVo.panoramicViewXmlKey||"",backgroundResources:e.scene5gVo.sceneVideoList?e.scene5gVo.sceneVideoList.map((function(e){return{id:e.id||null,tag:e.tag||"",status:e.status||"",type:e.type||"",viewInfoId:e.viewInfoId||"",backgroundImgFileUrl:e.backgroundImgFileUrl||"",backgroundFileUrl:e.backgroundFileUrl||"",coordinates:e.sceneFileRelList?e.sceneFileRelList.map((function(e){return{id:e.id||null,fileId:e.fileId||null,x:e.clickX||"",y:e.clickY||"",wide:e.wide||"",high:e.high||"",xmlKey:e.xmlKey||"",sceneId:t.findSceneIdByCode(e.bindSceneCode),sceneCode:e.bindSceneCode||""}})):[{id:null,fileId:null,x:"",y:"",wide:"",high:"",sceneId:"",sceneCode:""}]}})):[],painPoints:e.scene5gVo.painPointList&&Array.isArray(e.scene5gVo.painPointList)?e.scene5gVo.painPointList.map((function(e){return{painPointId:e.painPointId||null,title:e.bigTitle||"",contents:e.content||[],showTime:e.displayTime||""}})):[]}:{name:"",panoramicViewXmlKey:"",backgroundResources:[],painPoints:[]},costEstimate:e.costEstimationInfoVo?{painPointId:e.costEstimationInfoVo.painPointId||null,status:e.costEstimationInfoVo.status||"0",title:e.costEstimationInfoVo.bigTitle||"",contents:e.costEstimationInfoVo.content||[]}:{painPointId:null,status:"0",title:"",contents:[]},children:[],parent:n};return i.children=e.children?t.adaptSceneTree(e.children,i):[],i}))},handleSubmit:function(){var e=this;return Object(l["a"])(Object(s["a"])().mark((function t(){var n,i,a,l,c,d,u,m;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{e.submitting=!0,c=e.flatMenuData.find((function(t){return String(t.id)===e.activeMenu})),d=c?c.industryCode:null,u=e.selectedNode?e.selectedNode.id:null,e.selectedNode?e.selectedNode.name:null,m={industryId:e.activeMenu,industryCode:d,sceneViewConfigId:e.form.sceneViewConfigId||null,mainTitle:e.form.mainTitle||null,subTitle:e.form.subTitle||null,themeId:e.selectedTheme?e.selectedTheme.themeId:null,backgroundImgFileUrl:e.form.bgImgUrl||null,backgroundFileUrl:e.form.bgFileUrl||null,panoramicViewXmlUrl:e.form.panoramicViewXmlUrl||null,networkSolutionInfoVo:{networkVideoList:null!==(n=e.networkPlanDataMap[e.activeMenu])&&void 0!==n&&n.networkVideoList&&Array.isArray(e.networkPlanDataMap[e.activeMenu].networkVideoList)?e.networkPlanDataMap[e.activeMenu].networkVideoList.map((function(e){return{id:e.id||null,type:4,tag:e.tag||null,clickX:e.clickX||null,clickY:e.clickY||null,wide:e.wide||null,high:e.high||null,backgroundImgFileUrl:e.backgroundImgFileUrl||null,backgroundFileUrl:e.backgroundFileUrl||null,status:null,viewInfoId:null}})):[],videoExplanationVo:{status:(null===(i=e.networkPlanDataMap[e.activeMenu])||void 0===i||null===(i=i.videoExplanationVo)||void 0===i?void 0:i.status)||"0",backgroundFileUrl:(null===(a=e.networkPlanDataMap[e.activeMenu])||void 0===a||null===(a=a.videoExplanationVo)||void 0===a?void 0:a.backgroundFileUrl)||null,videoSegmentedVoList:null!==(l=e.networkPlanDataMap[e.activeMenu])&&void 0!==l&&null!==(l=l.videoExplanationVo)&&void 0!==l&&null!==(l=l.videoSegmentedVoList)&&void 0!==l&&l.length?e.networkPlanDataMap[e.activeMenu].videoExplanationVo.videoSegmentedVoList.map((function(e){return{time:e.time||null,sceneCode:e.sceneCode||null,sceneName:e.sceneName||null}})):null}},sceneDefaultConfigVoList:e.categories.map((function(t){var n={id:t.id||null,industryId:e.activeMenu||null,name:t.name,keyName:t.key,keyValue:t.enabled?"0":"1",remark:t.remark||t.name,defaultStatus:"0"};if("default_scene"===t.key){var i=e.convertSceneTreeToApi(e.sceneConfigTree);n.industrySceneInfoVo={videoExplanationVo:{status:e.videoExplanation.status,backgroundFileUrl:e.videoExplanation.backgroundFileUrl||null,videoSegmentedVoList:e.videoExplanation.videoSegmentedVoList.length?e.videoExplanation.videoSegmentedVoList:null},sceneListVo:i}}else n.industrySceneInfoVo=null;return n})),commercialValueDTO:e.businessValueDataMap[e.activeMenu]&&Array.isArray(e.businessValueDataMap[e.activeMenu])?e.businessValueDataMap[e.activeMenu].map((function(e){return{id:e.id||null,viewInfoId:e.viewInfoId||null,type:5,tag:e.tag||null,backgroundImgFileUrl:e.backgroundImgFileUrl||null,backgroundFileUrl:e.backgroundFileUrl||null}})):[],vrInfoDtoList:e.vrSceneDataMap[e.activeMenu]&&Array.isArray(e.vrSceneDataMap[e.activeMenu])?e.vrSceneDataMap[e.activeMenu].map((function(t){return{id:t.id||null,industryId:t.industryId||e.activeMenu,type:t.type||6,viewInfoId:t.viewInfoId||null,name:t.name||"",address:t.address||""}})):[]},Object(p["f"])(m).then((function(t){e.$modal.msgSuccess("修改成功"),e.handleSelect(e.activeMenu).then((function(){if(u&&e.sceneConfigTree.length>0){var t=e.findNodeById(e.sceneConfigTree,u);if(t){var n=[],i=function e(t,i){var a,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],l=Object(r["a"])(t);try{for(l.s();!(a=l.n()).done;){var c=a.value,d=[].concat(Object(o["a"])(s),[c.id]);if(c.id===i)return n.push.apply(n,Object(o["a"])(d)),!0;if(c.children&&c.children.length>0&&e(c.children,i,d))return!0}}catch(u){l.e(u)}finally{l.f()}return!1};i(e.sceneConfigTree,u),e.treeExpandedKeys=n.slice(0,-1),e.selectedNode=t,e.$nextTick((function(){e.$nextTick((function(){e.handleSceneNodeClick(t)}))}))}}}))})),console.log("提交内容:",m)}catch(s){console.error("提交失败:",s),e.$message.error("提交失败")}finally{e.submitting=!1}case 1:case"end":return t.stop()}}),t)})))()},addVideoSegment:function(){this.videoExplanation.videoSegmentedVoList&&0!==this.videoExplanation.videoSegmentedVoList.length||(this.videoExplanation.videoSegmentedVoList=[{time:"",sceneId:"",sceneName:"",sceneCode:""}]),this.videoExplanation.videoSegmentedVoList.push({time:"",sceneId:"",sceneName:"",sceneCode:""})},removeVideoSegment:function(e){this.videoExplanation.videoSegmentedVoList.splice(e,1)},getDeepTreeOptions:function(e){var t=this;return e.map((function(e){var n=Object(c["a"])({},e);return n.children&&n.children.length>0?n.children=t.getDeepTreeOptions(n.children):delete n.children,n}))},loadSceneTreeOptions:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function n(){var i,a,r;return Object(s["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,i=t.flatMenuData.find((function(t){return String(t.id)===e})),a=i?i.industryCode:null,n.next=5,Object(d["b"])({industryCode:a});case 5:r=n.sent,0===r.code&&Array.isArray(r.data)&&(t.sceneTreeOptions=t.getDeepTreeOptions(r.data)),n.next=12;break;case 9:n.prev=9,n.t0=n["catch"](0),console.error("加载场景树失败:",n.t0);case 12:case"end":return n.stop()}}),n,null,[[0,9]])})))()},handleSceneCascaderChange:function(e,t){if(this.videoExplanation.videoSegmentedVoList&&this.videoExplanation.videoSegmentedVoList[t]){var n=function e(t,n){var i,a=Object(r["a"])(t);try{for(a.s();!(i=a.n()).done;){var s=i.value;if(s.id===n)return s;if(s.children&&s.children.length){var o=e(s.children,n);if(o)return o}}}catch(l){a.e(l)}finally{a.f()}return null},i=n(this.sceneTreeOptions,e);i&&(this.videoExplanation.videoSegmentedVoList[t].sceneId=e,this.videoExplanation.videoSegmentedVoList[t].sceneName=i.sceneName,this.videoExplanation.videoSegmentedVoList[t].sceneCode=i.sceneCode)}},isSceneDisabled:function(e,t){return this.videoExplanation.videoSegmentedVoList.some((function(n,i){return i!==t&&n.sceneId===e}))},convertSceneTreeToApi:function(e){var t=this;return console.log("提交的数据:",e),e.map((function(e){return{sceneInfoId:e.sceneInfoId||null,sceneId:e.id,paramId:e.parent?e.parent.id:null,sceneName:e.name,sceneCode:e.code,x:e.x||null,y:e.y||null,type:e.type||null,status:e.status,isUnfold:e.children&&e.children.length>0?e.isUnfold||"1":null,displayLocation:e.children&&e.children.length>0?e.displayLocation||"0":null,treeClassification:e.children&&e.children.length>0?e.treeClassification||"3":null,introduceVideoVo:e.introduceVideoVo?{id:e.introduceVideoVo.id||null,type:e.introduceVideoVo.type||null,viewInfoId:e.introduceVideoVo.viewInfoId||null,status:e.introduceVideoVo.status||null,backgroundImgFileUrl:e.introduceVideoVo.backgroundImgFileUrl||null,backgroundFileUrl:e.introduceVideoVo.backgroundFileUrl||null}:null,sceneTraditionVo:e.tradition?{name:e.tradition.name||null,panoramicViewXmlKey:e.tradition.panoramicViewXmlKey||null,sceneVideoList:e.tradition.backgroundResources&&e.tradition.backgroundResources.length?e.tradition.backgroundResources.map((function(e){return{id:e.id||null,tag:e.label||null,wide:e.wide||null,high:e.high||null,status:e.status||null,type:1,viewInfoId:e.viewInfoId||null,backgroundImgFileUrl:e.bgImg||"",backgroundFileUrl:e.bgFile||"",sceneFileRelList:e.coordinates&&e.coordinates.length?e.coordinates.map((function(e){return{id:e.id||null,fileId:e.fileId||null,clickX:void 0!==e.x&&null!==e.x?""===e.x?"":e.x:null,clickY:void 0!==e.y&&null!==e.y?""===e.y?"":e.y:null,wide:void 0!==e.wide&&null!==e.wide?""===e.wide||0===e.wide?"":e.wide:null,high:void 0!==e.high&&null!==e.high?""===e.high||0===e.high?"":e.high:null,xmlKey:void 0!==e.xmlKey&&null!==e.xmlKey?""===e.xmlKey?"":e.xmlKey:null,bindSceneCode:void 0!==e.sceneCode&&null!==e.sceneCode?""===e.sceneCode?"":e.sceneCode:null}})):[]}})):null,painPointList:e.tradition.painPoints&&e.tradition.painPoints.length?e.tradition.painPoints.map((function(e){return{painPointId:e.painPointId||null,bigTitle:e.title||null,content:e.contents||[],displayTime:e.showTime||null}})):null}:null,scene5gVo:e.wisdom5g?{name:e.wisdom5g.name||null,panoramicViewXmlKey:e.wisdom5g.panoramicViewXmlKey||null,sceneVideoList:e.wisdom5g.backgroundResources&&e.wisdom5g.backgroundResources.length?e.wisdom5g.backgroundResources.map((function(e){return{id:e.id||null,tag:e.tag||null,status:e.status||null,type:2,viewInfoId:e.viewInfoId||null,backgroundImgFileUrl:e.bgImg||"",backgroundFileUrl:e.bgFile||"",sceneFileRelList:e.coordinates&&e.coordinates.length?e.coordinates.map((function(e){return{id:e.id||null,fileId:e.fileId||null,clickX:void 0!==e.x&&null!==e.x?""===e.x?"":e.x:null,clickY:void 0!==e.y&&null!==e.y?""===e.y?"":e.y:null,wide:void 0!==e.wide&&null!==e.wide?""===e.wide||0===e.wide?"":e.wide:null,high:void 0!==e.high&&null!==e.high?""===e.high||0===e.high?"":e.high:null,xmlKey:void 0!==e.xmlKey&&null!==e.xmlKey?""===e.xmlKey?"":e.xmlKey:null,bindSceneCode:void 0!==e.sceneCode&&null!==e.sceneCode?""===e.sceneCode?"":e.sceneCode:null}})):[]}})):null,painPointList:e.wisdom5g.painPoints&&e.wisdom5g.painPoints.length?e.wisdom5g.painPoints.map((function(e){return{painPointId:e.painPointId||null,bigTitle:e.title||null,content:e.contents||[],displayTime:e.showTime||null}})):null}:null,costEstimationInfoVo:e.costEstimate?{painPointId:e.costEstimate.painPointId||null,status:e.costEstimate.status||"0",bigTitle:e.costEstimate.title||null,content:e.costEstimate.contents&&e.costEstimate.contents.length?e.costEstimate.contents:null}:null,children:e.children&&e.children.length?t.convertSceneTreeToApi(e.children):[]}}))},handleTimeChange:function(e,t){this.videoExplanation.videoSegmentedVoList&&0!==this.videoExplanation.videoSegmentedVoList.length||(this.videoExplanation.videoSegmentedVoList=[{time:0,sceneId:"",sceneName:"",sceneCode:""}]),this.videoExplanation.videoSegmentedVoList[t]&&(this.videoExplanation.videoSegmentedVoList[t].time=e)},findSceneIdByCode:function(e){if(!e||!this.sceneTreeOptions)return"";var t=function t(n){var i,a=Object(r["a"])(n);try{for(a.s();!(i=a.n()).done;){var s=i.value;if(s.sceneCode===e)return s.id;if(s.children&&s.children.length){var o=t(s.children);if(o)return o}}}catch(l){a.e(l)}finally{a.f()}return null};return t(this.sceneTreeOptions)||""},handleRemoveBgFile:function(e,t){this.form.bgFileUrl="",this.form.bgImgUrl="",this.bgFileList=[],this.$message.success("文件已删除")},updateBgFileList:function(){if(this.form.bgFileUrl){var e=this.form.bgFileUrl.split("/").pop();this.bgFileList=[{name:e,url:this.form.bgFileUrl,uid:Date.now()}]}else this.bgFileList=[]},handleRemoveIntroduceVideoFile:function(e,t){this.introduceVideo.backgroundFileUrl="",this.introduceVideo.backgroundImgFileUrl="",this.introduceVideoFileList=[],this.$message.success("介绍视频已删除")},updateIntroduceVideoFileList:function(){if(this.introduceVideo.backgroundFileUrl){var e=this.introduceVideo.backgroundFileUrl.split("/").pop();this.introduceVideoFileList=[{name:e,url:this.introduceVideo.backgroundFileUrl,uid:Date.now()}]}else this.introduceVideoFileList=[]},handleRemoveVideoExplanationFile:function(e,t){this.videoExplanation.backgroundFileUrl="",this.videoExplanationFileList=[],this.$message.success("讲解视频已删除")},updateVideoExplanationFileList:function(){if(this.videoExplanation.backgroundFileUrl){var e=this.videoExplanation.backgroundFileUrl.split("/").pop();this.videoExplanationFileList=[{name:e,url:this.videoExplanation.backgroundFileUrl,uid:Date.now()}]}else this.videoExplanationFileList=[]},handleRemoveXmlFile:function(e,t){this.form.panoramicViewXmlUrl="",this.xmlFileList=[],this.$message.success("XML文件已删除")},updateXmlFileList:function(){if(this.form.panoramicViewXmlUrl){var e=this.form.panoramicViewXmlUrl.split("/").pop();this.xmlFileList=[{name:e,url:this.form.panoramicViewXmlUrl,uid:Date.now()}]}else this.xmlFileList=[]},previewImage:function(e){e&&(this.previewImageUrl=e,this.previewVisible=!0)},closePreview:function(){this.previewVisible=!1,this.previewImageUrl=""},deleteBgImage:function(){var e=this;this.$confirm("确定删除此图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.form.bgImgUrl="",e.$message.success("图片已删除")})).catch((function(){}))},beforeUploadXmlFile:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function n(){var i,a,r;return Object(s["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.name.toLowerCase().endsWith(".xml")){n.next=3;break}return t.$message.error("只能上传XML文件！"),n.abrupt("return",!1);case 3:if(!(e.size>52428800)){n.next=6;break}return t.$message.error("文件大小不能超过50MB！"),n.abrupt("return",!1);case 6:return n.prev=6,t.$modal.loading("正在上传XML文件，请稍候..."),i=new FormData,i.append("file",e),i.append("industryCode",t.industryCode),n.next=13,Object(p["h"])(i);case 13:a=n.sent,0===a.code&&a.data?(t.form.panoramicViewXmlUrl=a.data.fileUrl,r=a.data.fileUrl.split("/").pop(),t.xmlFileList=[{name:r,url:a.data.fileUrl,uid:Date.now()}],t.$message.success("上传成功")):t.$message.error(a.msg||"上传失败"),n.next=20;break;case 17:n.prev=17,n.t0=n["catch"](6),t.$message.error("上传失败");case 20:return n.prev=20,t.$modal.closeLoading(),n.finish(20);case 23:return n.abrupt("return",!1);case 24:case"end":return n.stop()}}),n,null,[[6,17,20,23]])})))()},onThemeChange:function(e){e&&e.defaultBgImage&&(this.form.bgImgUrl=e.defaultBgImage)},formatCoordinatesForSubmit:function(e){if(!e||!Array.isArray(e))return{clickX:"",clickY:""};var t=e.map((function(e){return e.x||"0"})).join(","),n=e.map((function(e){return e.y||"0"})).join(",");return{clickX:t,clickY:n}},parseCoordinatesFromApi:function(e,t){for(var n=e?e.split(","):[""],i=t?t.split(","):[""],a=Math.max(n.length,i.length),r=[],s=0;s<a;s++)r.push({x:n[s]||"",y:i[s]||""});return r.length>0?r:[{x:"",y:""}]},startEditTitle:function(e){var t=this,n=this.categories[e];n.editing=!0,n.editingName=n.name,this.$nextTick((function(){var n=t.$refs["titleInput_".concat(e)];n&&n[0]&&(n[0].focus(),n[0].select())}))},finishEditTitle:function(e){var t=this.categories[e];t.editingName&&t.editingName.trim()&&(t.name=t.editingName.trim()),t.editing=!1,t.editingName=""},cancelEditTitle:function(e){var t=this.categories[e];t.editing=!1,t.editingName=""},setUploadMode:function(e,t){this.$set(this.uploadModes,e,t)},handleBgFileUrlInput:function(e){if(this.bgFileList=[],e){var t=e.split("/").pop()||"外部链接文件";this.bgFileList=[{name:t,url:e,uid:Date.now()}]}},handleVideoExplanationUrlInput:function(e){if(this.videoExplanationFileList=[],e){var t=e.split("/").pop()||"外部链接文件";this.videoExplanationFileList=[{name:t,url:e,uid:Date.now()}]}},handleIntroduceVideoUrlInput:function(e){if(this.introduceVideoFileList=[],e){var t=e.split("/").pop()||"外部链接文件";this.introduceVideoFileList=[{name:t,url:e,uid:Date.now()}]}},handleSynchronizeFile:function(){var e=this;return Object(l["a"])(Object(s["a"])().mark((function t(){var n,i;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.form.sceneViewConfigId){t.next=3;break}return e.$message.warning("请先保存配置后再同步文件"),t.abrupt("return");case 3:return t.prev=3,e.synchronizing=!0,e.$modal.loading("正在同步文件，请稍候..."),n=new FormData,n.append("viewConfigId",e.form.sceneViewConfigId),t.next=10,Object(p["g"])(n);case 10:i=t.sent,0===i.code?e.$message.success(i.msg):e.$message.error(i.msg||"文件同步失败"),t.next=18;break;case 14:t.prev=14,t.t0=t["catch"](3),console.error("同步文件失败:",t.t0),e.$message.error("文件同步失败");case 18:return t.prev=18,e.synchronizing=!1,e.$modal.closeLoading(),t.finish(18);case 22:case"end":return t.stop()}}),t,null,[[3,14,18,22]])})))()}}},w=y,k=(n("a870"),n("2877")),V=Object(k["a"])(w,i,a,!1,null,"dd143a2a",null);t["default"]=V.exports},"20f8":function(e,t,n){"use strict";n("7595")},"271a":function(e,t,n){"use strict";var i=n("cb2d"),a=n("e330"),r=n("577e"),s=n("d6d6"),o=URLSearchParams,l=o.prototype,c=a(l.getAll),d=a(l.has),u=new o("a=1");!u.has("a",2)&&u.has("a",void 0)||i(l,"has",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return d(this,e);var i=c(this,e);s(t,1);var a=r(n),o=0;while(o<i.length)if(i[o++]===a)return!0;return!1}),{enumerable:!0,unsafe:!0})},"296f":function(e,t,n){},"39e2":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"theme-selection-container"},[e._m(0),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading&&1===e.currentPage,expression:"loading && currentPage === 1"}],ref:"themeGrid",staticClass:"theme-grid",on:{scroll:e.handleScroll}},e._l(e.themeOptions,(function(t){return n("div",{key:t.themeId,staticClass:"theme-card",class:{selected:e.selectedTheme&&e.selectedTheme.themeId===t.themeId},on:{click:function(n){return e.selectTheme(t)}}},[n("div",{staticClass:"theme-preview"},[n("img",{attrs:{src:t.themeEffectImg,alt:t.themeName}}),n("div",{staticClass:"theme-overlay"},[e.selectedTheme&&e.selectedTheme.themeId===t.themeId?n("i",{staticClass:"el-icon-check"}):e._e(),n("i",{staticClass:"el-icon-zoom-in preview-icon",attrs:{title:"预览大图"},on:{click:function(n){return n.stopPropagation(),e.previewImage(t.themeEffectImg)}}})])]),n("div",{staticClass:"theme-info"},[n("h3",[e._v(e._s(t.themeName))])])])})),0),e.loadingMore?n("div",{staticClass:"loading-more"},[n("i",{staticClass:"el-icon-loading"}),n("span",[e._v("加载中...")])]):e._e(),!e.hasMore&&e.themeOptions.length>0?n("div",{staticClass:"no-more"},[n("span",[e._v("没有更多数据了")])]):e._e(),e.loading||e.themeOptions.length?e._e():n("div",{staticClass:"empty-state"},[n("i",{staticClass:"el-icon-picture-outline"}),n("p",[e._v("暂无主题数据")])]),n("div",{staticClass:"theme-actions"},[n("el-button",{on:{click:function(t){return e.$emit("cancel")}}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",disabled:!e.selectedTheme},on:{click:e.confirmSelection}},[e._v("确认选择")])],1),n("el-dialog",{attrs:{visible:e.previewVisible,title:"主题预览",width:"60%","append-to-body":""},on:{"update:visible":function(t){e.previewVisible=t},close:e.closePreview}},[n("div",{staticClass:"preview-container"},[n("img",{staticClass:"preview-image",attrs:{src:e.previewImageUrl}})])])],1)},a=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"theme-header"},[n("h2",[e._v("选择主题风格")]),n("p",[e._v("为您的行业场景选择合适的主题风格")])])}],r=n("c7eb"),s=n("2909"),o=n("1da1"),l=(n("99af"),n("86e4")),c={name:"ThemeSelection",data:function(){return{selectedTheme:null,themeOptions:[],loading:!1,loadingMore:!1,currentPage:1,pageSize:20,hasMore:!0,previewVisible:!1,previewImageUrl:""}},created:function(){this.loadThemeList()},methods:{loadThemeList:function(){var e=arguments,t=this;return Object(o["a"])(Object(r["a"])().mark((function n(){var i,a;return Object(r["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i=e.length>0&&void 0!==e[0]&&e[0],i?t.loadingMore=!0:(t.loading=!0,t.currentPage=1,t.themeOptions=[],t.hasMore=!0),n.prev=2,n.next=5,Object(l["d"])({page:t.currentPage,limit:t.pageSize});case 5:a=n.sent,0===a.code&&Array.isArray(a.data)?(t.themeOptions=i?[].concat(Object(s["a"])(t.themeOptions),Object(s["a"])(a.data)):a.data,t.hasMore=a.data.length===t.pageSize):t.$message.error(a.msg||"获取主题列表失败"),n.next=12;break;case 9:n.prev=9,n.t0=n["catch"](2),t.$message.error("获取主题列表失败");case 12:return n.prev=12,t.loading=!1,t.loadingMore=!1,n.finish(12);case 16:case"end":return n.stop()}}),n,null,[[2,9,12,16]])})))()},handleScroll:function(e){var t=e.target,n=t.scrollTop,i=t.scrollHeight,a=t.clientHeight;n+a>=i-50&&this.loadMore()},loadMore:function(){var e=this;return Object(o["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loadingMore&&e.hasMore){t.next=2;break}return t.abrupt("return");case 2:return e.currentPage++,t.next=5,e.loadThemeList(!0);case 5:case"end":return t.stop()}}),t)})))()},getThemeColor:function(e){var t=["#409EFF","#13ce66","#f56c6c","#909399","#e6a23c"],n=parseInt(e.themeId)%t.length;return t[n]},selectTheme:function(e){this.selectedTheme=e},confirmSelection:function(){this.selectedTheme?this.$emit("confirm",this.selectedTheme):this.$message.warning("请先选择一个主题")},previewImage:function(e){e&&(this.previewImageUrl=e,this.previewVisible=!0)},closePreview:function(){this.previewVisible=!1,this.previewImageUrl=""}}},d=c,u=(n("20f8"),n("2877")),p=Object(u["a"])(d,i,a,!1,null,"47dcd395",null);t["default"]=p.exports},5046:function(e,t,n){"use strict";n("7789")},5352:function(e,t,n){"use strict";n("e260");var i=n("23e7"),a=n("da84"),r=n("157a"),s=n("c65b"),o=n("e330"),l=n("83ab"),c=n("f354"),d=n("cb2d"),u=n("edd0"),p=n("6964"),m=n("d44e"),h=n("dcc3"),f=n("69f3"),g=n("19aa"),v=n("1626"),b=n("1a2d"),y=n("0366"),w=n("f5df"),k=n("825a"),V=n("861d"),x=n("577e"),I=n("7c73"),C=n("5c6c"),U=n("9a1f"),T=n("35a1"),L=n("4754"),S=n("d6d6"),F=n("b622"),E=n("addb"),$=F("iterator"),M="URLSearchParams",O=M+"Iterator",P=f.set,_=f.getterFor(M),D=f.getterFor(O),j=r("fetch"),N=r("Request"),R=r("Headers"),B=N&&N.prototype,A=R&&R.prototype,z=a.RegExp,K=a.TypeError,X=a.decodeURIComponent,q=a.encodeURIComponent,W=o("".charAt),Y=o([].join),J=o([].push),H=o("".replace),G=o([].shift),Q=o([].splice),Z=o("".split),ee=o("".slice),te=/\+/g,ne=Array(4),ie=function(e){return ne[e-1]||(ne[e-1]=z("((?:%[\\da-f]{2}){"+e+"})","gi"))},ae=function(e){try{return X(e)}catch(t){return e}},re=function(e){var t=H(e,te," "),n=4;try{return X(t)}catch(i){while(n)t=H(t,ie(n--),ae);return t}},se=/[!'()~]|%20/g,oe={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},le=function(e){return oe[e]},ce=function(e){return H(q(e),se,le)},de=h((function(e,t){P(this,{type:O,target:_(e).entries,index:0,kind:t})}),M,(function(){var e=D(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=void 0,L(void 0,!0);var i=t[n];switch(e.kind){case"keys":return L(i.key,!1);case"values":return L(i.value,!1)}return L([i.key,i.value],!1)}),!0),ue=function(e){this.entries=[],this.url=null,void 0!==e&&(V(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===W(e,0)?ee(e,1):e:x(e)))};ue.prototype={type:M,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,i,a,r,o,l,c=this.entries,d=T(e);if(d){t=U(e,d),n=t.next;while(!(i=s(n,t)).done){if(a=U(k(i.value)),r=a.next,(o=s(r,a)).done||(l=s(r,a)).done||!s(r,a).done)throw new K("Expected sequence with length 2");J(c,{key:x(o.value),value:x(l.value)})}}else for(var u in e)b(e,u)&&J(c,{key:u,value:x(e[u])})},parseQuery:function(e){if(e){var t,n,i=this.entries,a=Z(e,"&"),r=0;while(r<a.length)t=a[r++],t.length&&(n=Z(t,"="),J(i,{key:re(G(n)),value:re(Y(n,"="))}))}},serialize:function(){var e,t=this.entries,n=[],i=0;while(i<t.length)e=t[i++],J(n,ce(e.key)+"="+ce(e.value));return Y(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var pe=function(){g(this,me);var e=arguments.length>0?arguments[0]:void 0,t=P(this,new ue(e));l||(this.size=t.entries.length)},me=pe.prototype;if(p(me,{append:function(e,t){var n=_(this);S(arguments.length,2),J(n.entries,{key:x(e),value:x(t)}),l||this.length++,n.updateURL()},delete:function(e){var t=_(this),n=S(arguments.length,1),i=t.entries,a=x(e),r=n<2?void 0:arguments[1],s=void 0===r?r:x(r),o=0;while(o<i.length){var c=i[o];if(c.key!==a||void 0!==s&&c.value!==s)o++;else if(Q(i,o,1),void 0!==s)break}l||(this.size=i.length),t.updateURL()},get:function(e){var t=_(this).entries;S(arguments.length,1);for(var n=x(e),i=0;i<t.length;i++)if(t[i].key===n)return t[i].value;return null},getAll:function(e){var t=_(this).entries;S(arguments.length,1);for(var n=x(e),i=[],a=0;a<t.length;a++)t[a].key===n&&J(i,t[a].value);return i},has:function(e){var t=_(this).entries,n=S(arguments.length,1),i=x(e),a=n<2?void 0:arguments[1],r=void 0===a?a:x(a),s=0;while(s<t.length){var o=t[s++];if(o.key===i&&(void 0===r||o.value===r))return!0}return!1},set:function(e,t){var n=_(this);S(arguments.length,1);for(var i,a=n.entries,r=!1,s=x(e),o=x(t),c=0;c<a.length;c++)i=a[c],i.key===s&&(r?Q(a,c--,1):(r=!0,i.value=o));r||J(a,{key:s,value:o}),l||(this.size=a.length),n.updateURL()},sort:function(){var e=_(this);E(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){var t,n=_(this).entries,i=y(e,arguments.length>1?arguments[1]:void 0),a=0;while(a<n.length)t=n[a++],i(t.value,t.key,this)},keys:function(){return new de(this,"keys")},values:function(){return new de(this,"values")},entries:function(){return new de(this,"entries")}},{enumerable:!0}),d(me,$,me.entries,{name:"entries"}),d(me,"toString",(function(){return _(this).serialize()}),{enumerable:!0}),l&&u(me,"size",{get:function(){return _(this).entries.length},configurable:!0,enumerable:!0}),m(pe,M),i({global:!0,constructor:!0,forced:!c},{URLSearchParams:pe}),!c&&v(R)){var he=o(A.has),fe=o(A.set),ge=function(e){if(V(e)){var t,n=e.body;if(w(n)===M)return t=e.headers?new R(e.headers):new R,he(t,"content-type")||fe(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),I(e,{body:C(0,x(n)),headers:C(0,t)})}return e};if(v(j)&&i({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return j(e,arguments.length>1?ge(arguments[1]):{})}}),v(N)){var ve=function(e){return g(this,B),new N(e,arguments.length>1?ge(arguments[1]):{})};B.constructor=ve,ve.prototype=B,i({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:ve})}}e.exports={URLSearchParams:pe,getState:_}},5494:function(e,t,n){"use strict";var i=n("83ab"),a=n("e330"),r=n("edd0"),s=URLSearchParams.prototype,o=a(s.forEach);i&&!("size"in s)&&r(s,"size",{get:function(){var e=0;return o(this,(function(){e++})),e},configurable:!0,enumerable:!0})},"578d":function(e,t,n){"use strict";n("296f")},6964:function(e,t,n){"use strict";var i=n("cb2d");e.exports=function(e,t,n){for(var a in t)i(e,a,t[a],n);return e}},7595:function(e,t,n){},7789:function(e,t,n){},8590:function(e,t,n){},"88a7":function(e,t,n){"use strict";var i=n("cb2d"),a=n("e330"),r=n("577e"),s=n("d6d6"),o=URLSearchParams,l=o.prototype,c=a(l.append),d=a(l["delete"]),u=a(l.forEach),p=a([].push),m=new o("a=1&a=2&b=3");m["delete"]("a",1),m["delete"]("b",void 0),m+""!=="a=2"&&i(l,"delete",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return d(this,e);var i=[];u(this,(function(e,t){p(i,{key:t,value:e})})),s(t,1);var a,o=r(e),l=r(n),m=0,h=0,f=!1,g=i.length;while(m<g)a=i[m++],f||a.key===o?(f=!0,d(this,a.key)):h++;while(h<g)a=i[h++],a.key===o&&a.value===l||c(this,a.key,a.value)}),{enumerable:!0,unsafe:!0})},9861:function(e,t,n){"use strict";n("5352")},"9b3d":function(e,t,n){},a5f5:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"theme-selector"},[e.selectedTheme?n("div",{staticClass:"selected-theme-preview"},[n("img",{staticClass:"theme-preview-img",attrs:{src:e.selectedTheme.themeEffectImg,alt:e.selectedTheme.themeName}}),n("div",{staticClass:"theme-preview-info"},[n("div",{staticClass:"theme-name"},[e._v(e._s(e.selectedTheme.themeName))])])]):n("el-button",{attrs:{type:"primary",plain:""},on:{click:e.openDialog}},[e._v("选择主题")]),e.selectedTheme?n("el-button",{attrs:{size:"small"},on:{click:e.openDialog}},[e._v("更换主题")]):e._e()],1),n("el-dialog",{attrs:{visible:e.dialogVisible,width:"80%","before-close":e.closeDialog,"show-close":!0},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("theme-selection",{on:{confirm:e.onThemeConfirm,cancel:e.closeDialog}})],1)],1)},a=[],r=n("39e2"),s={name:"ThemeSelectionDialog",components:{ThemeSelection:r["default"]},props:{value:{type:Object,default:null}},data:function(){return{dialogVisible:!1,selectedTheme:this.value}},watch:{value:function(e){this.selectedTheme=e},selectedTheme:function(e){this.$emit("input",e)}},methods:{openDialog:function(){this.dialogVisible=!0},closeDialog:function(){this.dialogVisible=!1},onThemeConfirm:function(e){this.selectedTheme=e,this.dialogVisible=!1,this.$emit("change",e),this.$message.success("已选择".concat(e.themeName,"主题"))}}},o=s,l=(n("d62a"),n("2877")),c=Object(l["a"])(o,i,a,!1,null,"b2b0aaba",null);t["default"]=c.exports},a870:function(e,t,n){"use strict";n("9b3d")},addb:function(e,t,n){"use strict";var i=n("f36a"),a=Math.floor,r=function(e,t){var n=e.length;if(n<8){var s,o,l=1;while(l<n){o=l,s=e[l];while(o&&t(e[o-1],s)>0)e[o]=e[--o];o!==l++&&(e[o]=s)}}else{var c=a(n/2),d=r(i(e,0,c),t),u=r(i(e,c),t),p=d.length,m=u.length,h=0,f=0;while(h<p||f<m)e[h+f]=h<p&&f<m?t(d[h],u[f])<=0?d[h++]:u[f++]:h<p?d[h++]:u[f++]}return e};e.exports=r},af49:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var i=n("b775");function a(){return Object(i["a"])({url:"/industry/list",method:"get"})}function r(e){return Object(i["a"])({url:"/industry/scene/tree/list",method:"get",params:e})}},d62a:function(e,t,n){"use strict";n("8590")},f354:function(e,t,n){"use strict";var i=n("d039"),a=n("b622"),r=n("83ab"),s=n("c430"),o=a("iterator");e.exports=!i((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n=new URLSearchParams("a=1&a=2&b=3"),i="";return e.pathname="c%20d",t.forEach((function(e,n){t["delete"]("b"),i+=n+e})),n["delete"]("a",2),n["delete"]("b",void 0),s&&(!e.toJSON||!n.has("a",1)||n.has("a",2)||!n.has("a",void 0)||n.has("b"))||!t.size&&(s||!r)||!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[o]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==i||"x"!==new URL("http://x",void 0).host}))},fa87:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"vr-scene-config"},[n("div",{staticClass:"config-header"},[n("span",[e._v("VR看现场配置")]),n("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.addVr}},[n("i",{staticClass:"el-icon-plus"}),e._v(" 添加VR场景 ")])],1),n("div",{staticClass:"vr-list"},[n("div",{staticClass:"vr-scroll-container"},[n("el-row",{attrs:{gutter:20}},e._l(e.vrList,(function(t,i){return n("el-col",{key:i,attrs:{span:8}},[n("el-card",{staticClass:"vr-item",attrs:{shadow:"hover"}},[n("div",{staticClass:"vr-header",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("VR场景 "+e._s(i+1))]),n("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.removeVr(i)}}})],1),n("el-form",{attrs:{"label-width":"60px",size:"small"}},[n("el-form-item",{attrs:{label:"名称",required:""}},[n("el-input",{attrs:{placeholder:"请输入VR场景名称"},on:{input:e.emitChange},model:{value:t.name,callback:function(n){e.$set(t,"name",n)},expression:"vr.name"}})],1),n("el-form-item",{attrs:{label:"地址",required:""}},[n("el-input",{attrs:{placeholder:"请输入VR场景地址（http://或https://开头）"},on:{input:function(n){return e.validateAddress(t,i)},blur:function(n){return e.validateAddressOnBlur(t,i)},paste:function(n){return e.handlePaste(t,i,n)}},model:{value:t.address,callback:function(n){e.$set(t,"address",n)},expression:"vr.address"}}),t.addressError?n("div",{staticClass:"error-text"},[e._v(" "+e._s(t.addressError)+" ")]):e._e()],1)],1)],1)],1)})),1)],1),e.vrList.length?e._e():n("div",{staticClass:"empty-state"},[n("i",{staticClass:"el-icon-document-add"}),n("p",[e._v("暂无VR场景，点击上方按钮添加")])])])])},a=[],r=n("c7eb"),s=n("1da1"),o=(n("14d9"),n("a434"),n("e9c4"),n("b64b"),n("ac1f"),n("00b4"),n("2ca0"),n("86e4")),l={name:"VrSceneConfig",props:{value:{type:Array,default:function(){return[]}}},data:function(){return{vrList:[]}},watch:{value:{handler:function(e){this.vrList=Array.isArray(e)?JSON.parse(JSON.stringify(e)):[]},immediate:!0,deep:!0}},methods:{addVr:function(){this.vrList.push({id:null,industryId:null,type:null,viewInfoId:null,name:"",address:"",addressError:""}),this.emitChange()},removeVr:function(e){var t=this;return Object(s["a"])(Object(r["a"])().mark((function n(){var i;return Object(r["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=t.vrList[e],t.$confirm("确定删除此VR场景吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(s["a"])(Object(r["a"])().mark((function n(){var a;return Object(r["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!i.id){n.next=17;break}return n.prev=1,t.$modal.loading("正在删除VR场景，请稍候..."),n.next=5,Object(o["e"])({id:i.id});case 5:a=n.sent,0===a.code?(t.vrList.splice(e,1),t.emitChange(),t.$message.success("删除成功")):t.$message.error(a.msg||"删除失败"),n.next=12;break;case 9:n.prev=9,n.t0=n["catch"](1),t.$message.error("删除失败");case 12:return n.prev=12,t.$modal.closeLoading(),n.finish(12);case 15:n.next=20;break;case 17:t.vrList.splice(e,1),t.emitChange(),t.$message.success("删除成功");case 20:case"end":return n.stop()}}),n,null,[[1,9,12,15]])})))).catch((function(){}));case 2:case"end":return n.stop()}}),n)})))()},emitChange:function(){this.$emit("input",this.vrList)},validateAddress:function(e,t){this.$set(e,"addressError",""),this.emitChange()},validateAddressOnBlur:function(e,t){if(e.address){var n=/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;n.test(e.address)?this.$set(e,"addressError",""):this.$set(e,"addressError","请输入有效的链接地址（必须以http://或https://开头）")}else this.$set(e,"addressError","请输入VR场景地址")},handlePaste:function(e,t,n){var i=n.clipboardData.getData("text");if(i&&!i.startsWith("http://")&&!i.startsWith("https://"))return n.preventDefault(),this.$message.warning("请粘贴有效的链接地址（以http://或https://开头）"),!1}}},c=l,d=(n("5046"),n("2877")),u=Object(d["a"])(c,i,a,!1,null,"5d23cdcd",null);t["default"]=u.exports}}]);