{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754893587771}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\babel.config.js", "mtime": 1753326339083}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_industry", "require", "_SceneConfigNode", "_interopRequireDefault", "_sceneView", "_NetworkPlanConfig", "_BusinessValueConfig", "_VrSceneConfig", "_ThemeSelectionDialog", "_axios", "name", "components", "SceneConfigNode", "NetworkPlanConfig", "BusinessValueConfig", "VrSceneConfig", "ThemeSelectionDialog", "data", "_this", "menuData", "flatMenuData", "activeMenu", "industryCode", "selectedTheme", "form", "mainTitle", "subTitle", "bgImgUrl", "bgFileUrl", "panoramicViewXmlUrl", "sceneConfigTree", "selectedNode", "loading", "switchingIndustry", "rules", "introduceVideoImgUrl", "required", "message", "trigger", "introduceVideoFileUrl", "videoExplanationFileUrl", "uploadingType", "uploadingKey", "categories", "introduceVideo", "status", "backgroundImgFileUrl", "backgroundFileUrl", "videoExplanation", "videoSegmentedVoList", "sceneTreeOptions", "sceneCascaderProps", "label", "value", "children", "emitPath", "checkStrictly", "disabled", "isSelected", "some", "seg", "sceneId", "id", "bgFileList", "videoExplanationFileList", "xmlFileList", "networkPlanDataMap", "businessValueDataMap", "vrSceneDataMap", "previewVisible", "previewImageUrl", "searchKeyword", "treeExpandedKeys", "uploadModes", "bgFile", "synchronizing", "submitting", "computed", "videoSegmentedList", "length", "time", "scene<PERSON><PERSON>", "sceneCode", "networkPlanData", "get", "networkVideoList", "videoExplanationVo", "set", "$set", "businessValueData", "vrSceneData", "val", "filteredMenuData", "_this2", "filterTree", "nodes", "map", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchesSearch", "toLowerCase", "includes", "_objectSpread2", "default", "filter", "Boolean", "created", "initTokenFromUrl", "loadIndustryMenu", "methods", "urlParams", "URLSearchParams", "window", "location", "search", "token", "localStorage", "setItem", "axios", "defaults", "headers", "common", "concat", "console", "log", "storedToken", "getItem", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "getIndustryList", "sent", "code", "Array", "isArray", "plate", "<PERSON><PERSON><PERSON>", "plateName", "type", "industryTreeListVos", "industry", "industryName", "for<PERSON>ach", "_this3$flatMenuData", "push", "apply", "_toConsumableArray2", "String", "$nextTick", "$refs", "menuTree", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleSelect", "stop", "handleTreeNodeClick", "handleSearch", "highlightText", "text", "regex", "RegExp", "replace", "_arguments", "arguments", "_this4", "_callee2", "keepSelectedNode", "currentSelectedNode", "currentIndustry", "sceneCategory", "videoData", "nodeToSelect", "networkData", "_callee2$", "_context2", "undefined", "document", "body", "style", "overflow", "loadSceneTreeOptions", "find", "item", "getSceneViewConfig", "sceneViewConfigId", "updateBgFileList", "updateXmlFileList", "themeInfoVo", "themeId", "themeName", "themeEffectImg", "remark", "sceneDefaultConfigVoList", "configItem", "key", "keyName", "enabled", "keyValue", "editing", "editingName", "originalName", "classification", "defaultStatus", "industrySceneInfoVo", "findSceneIdByCode", "updateVideoExplanationFileList", "sceneListVo", "adaptSceneTree", "findNodeById", "networkSolutionVo", "commercialValueListVo", "vrInfoListVo", "t0", "error", "$message", "finish", "handleBeforeUpload", "file", "addSegment", "scene", "removeSegment", "index", "splice", "beforeUploadIntroduceImg", "_this5", "_callee3", "formData", "_callee3$", "_context3", "startsWith", "abrupt", "$modal", "FormData", "append", "uploadSceneFile", "fileUrl", "success", "msg", "closeLoading", "beforeUploadIntroduceVideo", "_this6", "_callee4", "fileName", "_formData", "_res", "_fileName", "_callee4$", "_context4", "split", "pop", "url", "uid", "Date", "now", "imgUrl", "endsWith", "introduceVideoFileList", "t1", "beforeUploadExplanationVideo", "addSceneConfigNode", "parentId", "newNode", "parentNode", "removeSceneConfigNode", "nodeId", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "found", "err", "e", "f", "addScenePainPoint", "painPoints", "title", "contents", "showTime", "removeScenePainPoint", "idx", "addScenePainContent", "removeScenePainContent", "cidx", "addSceneCostContent", "removeSceneCostContent", "beforeUploadSceneConfigImg", "_this7", "reader", "FileReader", "onload", "target", "result", "readAsDataURL", "beforeUploadSceneConfigFile", "handleSceneNodeClick", "rawTree", "_this8", "parent", "arr", "adapted", "sceneInfoId", "x", "y", "isUnfold", "displayLocation", "treeClassification", "introduceVideoVo", "viewInfoId", "tradition", "sceneTraditionVo", "panoramicViewXmlKey", "backgroundResources", "sceneVideoList", "v", "tag", "coordinates", "sceneFileRelList", "rel", "fileId", "clickX", "clickY", "wide", "high", "xmlKey", "bindSceneCode", "bgImg", "painPointList", "p", "painPointId", "bigTitle", "content", "displayTime", "wisdom5g", "scene5gVo", "costEstimate", "costEstimationInfoVo", "handleSubmit", "_this9", "_callee5", "_this9$networkPlanDat", "_this9$networkPlanDat2", "_this9$networkPlanDat3", "_this9$networkPlanDat4", "currentSelectedNodeId", "currentSelectedNodeName", "submitData", "response", "pathIds", "<PERSON><PERSON><PERSON>", "_callee5$", "_context5", "industryId", "networkSolutionInfoVo", "plan", "cat", "baseConfig", "convertedSceneList", "convertSceneTreeToApi", "commercialValueDTO", "vrInfoDtoList", "vr", "address", "sceneViewUpd", "msgSuccess", "targetId", "currentPath", "_iterator2", "_step2", "newPath", "slice", "addVideoSegment", "removeVideoSegment", "getDeepTreeOptions", "tree", "_this0", "_this1", "_callee6", "_callee6$", "_context6", "getSceneTreeList", "handleSceneCascaderChange", "findScene", "_iterator3", "_step3", "isSceneDisabled", "currentIdx", "sceneTree", "_this10", "paramId", "resource", "coord", "pain", "handleTimeChange", "findInTree", "_iterator4", "_step4", "handleRemoveBgFile", "fileList", "handleRemoveIntroduceVideoFile", "updateIntroduceVideoFileList", "handleRemoveVideoExplanationFile", "handleRemoveXmlFile", "previewImage", "closePreview", "deleteBgImage", "_this11", "$confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "beforeUploadXmlFile", "_this12", "_callee7", "_callee7$", "_context7", "size", "onThemeChange", "theme", "defaultBgImage", "formatCoordinatesForSubmit", "xValues", "join", "yV<PERSON><PERSON>", "parseCoordinatesFromApi", "xArray", "yArray", "max<PERSON><PERSON><PERSON>", "Math", "max", "i", "startEditTitle", "_this13", "category", "inputRef", "focus", "select", "finishEditTitle", "trim", "cancelEditTitle", "setUploadMode", "mode", "handleBgFileUrlInput", "handleVideoExplanationUrlInput", "handleIntroduceVideoUrlInput", "handleSynchronizeFile", "_this14", "_callee8", "_callee8$", "_context8", "warning", "synchronizationFile"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <!-- ✅ 左侧菜单区域 -->\r\n    <div class=\"menu-panel\">\r\n      <!-- 搜索框 -->\r\n      <div class=\"menu-search\">\r\n        <el-input\r\n          v-model=\"searchKeyword\"\r\n          placeholder=\"搜索菜单...\"\r\n          prefix-icon=\"el-icon-search\"\r\n          clearable\r\n          @input=\"handleSearch\"\r\n        />\r\n      </div>\r\n      \r\n      <el-tree\r\n        ref=\"menuTree\"\r\n        :data=\"filteredMenuData\"\r\n        :props=\"{ label: 'name', children: 'children' }\"\r\n        node-key=\"id\"\r\n        :current-node-key=\"activeMenu\"\r\n        @node-click=\"handleTreeNodeClick\"\r\n        highlight-current\r\n        :expand-on-click-node=\"false\"\r\n        :default-expanded-keys=\"menuData.map(item => item.id)\"\r\n        class=\"menu-tree\"\r\n      >\r\n        <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\r\n          <span v-html=\"highlightText(data.name)\"></span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n\r\n    <!-- ✅ 右侧内容区域 -->\r\n    <div class=\"content-panel\" v-loading=\"switchingIndustry\" element-loading-text=\"正在切换行业...\">\r\n      <el-form :model=\"form\" ref=\"sceneForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <!-- 左侧：基本信息 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"mini-block\" shadow=\"never\">\r\n              <div slot=\"header\">基本信息</div>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"主标题\" required>\r\n                    <el-input v-model=\"form.mainTitle\" placeholder=\"请输入主标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"副标题\" required>\r\n                    <el-input v-model=\"form.subTitle\" placeholder=\"请输入副标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"beforeUploadIntroduceImg\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"form.bgImgUrl\" class=\"image-preview-container\">\r\n                        <img :src=\"form.bgImgUrl\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(form.bgImgUrl)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBgImage\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"主题选择\">\r\n                    <theme-selection-dialog \r\n                      v-model=\"selectedTheme\"\r\n                      @change=\"onThemeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\">\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.bgFile || 'upload'\" @input=\"value => setUploadMode('bgFile', value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.bgFile || 'upload') === 'upload'\"\r\n                      class=\"upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"bgFileList\"\r\n                      :before-upload=\"file => beforeUploadIntroduceVideo(file)\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveBgFile\"\r\n                    >\r\n                      <el-button type=\"primary\">上传背景文件</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"form.bgFileUrl\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"handleBgFileUrlInput\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"XML文件\">\r\n                    <el-upload\r\n                      class=\"upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"xmlFileList\"\r\n                      accept=\".xml\"\r\n                      :before-upload=\"beforeUploadXmlFile\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveXmlFile\"\r\n                    >\r\n                      <el-button type=\"primary\">上传XML文件</el-button>\r\n                      <div slot=\"tip\" class=\"el-upload__tip\">只能上传xml文件，且不超过50MB</div>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-card>\r\n          </el-col>\r\n          \r\n          <!-- 右侧：视频讲解 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"mini-block video-card\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"video-header-row\">\r\n                <span>视频讲解</span>\r\n                <el-switch v-model=\"videoExplanation.status\" :active-value=\"'0'\" :inactive-value=\"'1'\" style=\"float:right;\" />\r\n              </div>\r\n              <div v-show=\"videoExplanation.status === '0'\">\r\n              <el-collapse-transition>\r\n                <div class=\"video-card-yu\">\r\n                  <el-form-item label=\"讲解视频\">\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.videoExplanation || 'upload'\" @input=\"value => setUploadMode('videoExplanation', value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.videoExplanation || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"videoExplanationFileList\"\r\n                      accept=\".mp4\"\r\n                      :before-upload=\"file => beforeUploadIntroduceVideo(file, 'videoExplanation', 'backgroundFileUrl')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveVideoExplanationFile\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                      <div slot=\"tip\" class=\"el-upload__tip\">只能上传mp4文件</div>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"videoExplanation.backgroundFileUrl\"\r\n                      placeholder=\"请输入视频链接\"\r\n                      @input=\"handleVideoExplanationUrlInput\"\r\n                    />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"视频分段说明\">\r\n                    <div class=\"segment-scroll\">\r\n                      <div v-for=\"(seg, idx) in videoSegmentedList\" :key=\"idx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                        <el-input-number\r\n                          v-model=\"seg.time\"\r\n                          :min=\"0\"\r\n                          :max=\"999999\"\r\n                          placeholder=\"时间\"\r\n                          style=\"width: 120px; margin-right: 10px;\"\r\n                          @change=\"val => handleTimeChange(val, idx)\"\r\n                        />\r\n                        <span style=\"margin-right: 10px; color: #606266;\">秒</span>\r\n                        <el-cascader\r\n                          v-model=\"seg.sceneId\"\r\n                          :options=\"sceneTreeOptions\"\r\n                          :props=\"sceneCascaderProps\"\r\n                          filterable\r\n                          check-strictly\r\n                          placeholder=\"所属场景\"\r\n                          style=\"width: 200px; margin-right: 10px;\"\r\n                          @change=\"val => handleSceneCascaderChange(val, idx)\"\r\n                        />\r\n                        <el-button type=\"danger\" icon=\"el-icon-delete\" circle @click=\"removeVideoSegment(idx)\" />\r\n                      </div>\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addVideoSegment\" style=\"margin-top: 8px;\">增加分段</el-button>\r\n                  </el-form-item>\r\n                </div>\r\n              </el-collapse-transition>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <!-- ✅ 分类区域（支持折叠） -->\r\n      <div\r\n        v-for=\"(category, index) in categories\"\r\n        :key=\"category.key\"\r\n        class=\"category-block\"\r\n      >\r\n        <div class=\"category-header\">\r\n          <div class=\"category-title\" @dblclick=\"startEditTitle(index)\">\r\n            <el-input\r\n              v-if=\"category.editing\"\r\n              v-model=\"category.editingName\"\r\n              @blur=\"finishEditTitle(index)\"\r\n              @keyup.enter=\"finishEditTitle(index)\"\r\n              @keyup.esc=\"cancelEditTitle(index)\"\r\n              :data-edit-index=\"index\"\r\n              size=\"small\"\r\n              style=\"width: 200px;\"\r\n              placeholder=\"请输入标题\"\r\n            />\r\n            <span v-else>{{ category.name }}</span>\r\n          </div>\r\n          <el-switch\r\n            v-model=\"category.enabled\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ccc\"\r\n          />\r\n        </div>\r\n\r\n        <div v-show=\"category.enabled\" class=\"category-body\">\r\n          <div v-if=\"category.key === 'default_scene'\">\r\n            <el-form label-width=\"120px\">\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"24\">\r\n                  <!-- 场景配置分块 -->\r\n                  <el-card class=\"mini-block scene-config-container\" shadow=\"never\" style=\"margin-top: 20px;\">\r\n                    <div slot=\"header\">场景配置</div>\r\n                    <div>\r\n                      <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                          <el-tree\r\n                            ref=\"sceneTree\"\r\n                            :data=\"sceneConfigTree\"\r\n                            node-key=\"id\"\r\n                            :props=\"{ label: 'name', children: 'children' }\"\r\n                            @node-click=\"handleSceneNodeClick\"\r\n                            highlight-current\r\n                            :expand-on-click-node=\"false\"\r\n                            :default-expanded-keys=\"treeExpandedKeys.length > 0 ? treeExpandedKeys : (sceneConfigTree.length ? [sceneConfigTree[0].id] : [])\"\r\n                            :current-node-key=\"selectedNode ? selectedNode.id : null\"\r\n                            :sort=\"false\"\r\n                          />\r\n                        </el-col>\r\n                        <el-col :span=\"18\">\r\n                          <SceneConfigNode \r\n                            v-if=\"selectedNode\" \r\n                            :node=\"selectedNode\" \r\n                            :root-tree=\"sceneConfigTree\"\r\n                            :scene-tree-options=\"sceneTreeOptions\"\r\n                            :left-tree-industry-code=\"industryCode\"\r\n                          />\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                  </el-card>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n\r\n          <!-- 其他分类的占位内容 -->\r\n          <div v-else-if=\"category.key === 'default_plan'\">\r\n            <network-plan-config v-model=\"networkPlanData\" :left-tree-industry-code=\"industryCode\" />\r\n          </div>\r\n          <div v-else-if=\"category.key === 'default_value'\">\r\n            <business-value-config v-model=\"businessValueData\" :left-tree-industry-code=\"industryCode\" />\r\n          </div>\r\n          <div v-else-if=\"category.key === 'default_vr'\">\r\n            <vr-scene-config v-model=\"vrSceneData\" />\r\n          </div>\r\n          <div v-else>\r\n            <p>这里是 <strong>{{ category.name }}</strong> 分类的内容区域。</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit-footer\">\r\n      <div class=\"form-actions\">\r\n        <!-- 同步文件按钮加 tooltip -->\r\n        <el-tooltip \r\n          effect=\"dark\" \r\n          content=\"链接填写完成后，点击【提交】后再点击【同步】按钮\" \r\n          placement=\"top\"\r\n        >\r\n          <el-button \r\n            type=\"success\" \r\n            @click=\"handleSynchronizeFile\" \r\n            :disabled=\"!form.sceneViewConfigId\"\r\n            :loading=\"synchronizing\"\r\n          >\r\n            {{ synchronizing ? '同步中...' : '同步文件' }}\r\n          </el-button>\r\n        </el-tooltip>\r\n\r\n        <el-button \r\n          type=\"primary\" \r\n          @click=\"handleSubmit\" \r\n          :loading=\"submitting\" \r\n          style=\"margin-left: 30px;\"\r\n        >\r\n          {{ submitting ? '提交中...' : '提交' }}\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    </div>\r\n    \r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"previewVisible\"\r\n      title=\"图片预览\"\r\n      width=\"60%\"\r\n      append-to-body\r\n      @close=\"closePreview\"\r\n    >\r\n      <div class=\"preview-container\">\r\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getIndustryList, getSceneTreeList } from '@/api/view/industry'\r\nimport SceneConfigNode from './SceneConfigNode.vue'\r\nimport { getSceneViewConfig, sceneViewUpd, uploadSceneFile, synchronizationFile } from '@/api/view/sceneView'\r\nimport NetworkPlanConfig from './NetworkPlanConfig.vue'\r\nimport BusinessValueConfig from './BusinessValueConfig.vue'\r\nimport VrSceneConfig from './VrSceneConfig.vue'\r\nimport ThemeSelectionDialog from './ThemeSelectionDialog.vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'IndustryScenePage',\r\n  components: {\r\n    SceneConfigNode,\r\n    NetworkPlanConfig,\r\n    BusinessValueConfig,\r\n    VrSceneConfig,\r\n    ThemeSelectionDialog\r\n  },\r\n  data() {\r\n    return {\r\n      menuData: [], // 原始菜单数据\r\n      flatMenuData: [], // 扁平化的行业数据，用于搜索和业务逻辑\r\n      activeMenu: '',\r\n      industryCode: '',\r\n      selectedTheme: null, // 当前选择的主题\r\n      form: {\r\n        mainTitle: '',\r\n        subTitle: '',\r\n        bgImgUrl: '',\r\n        bgFileUrl: '',\r\n        panoramicViewXmlUrl: ''\r\n      },\r\n      sceneConfigTree: [],\r\n      selectedNode: null,\r\n      loading: false, // 页面加载状态\r\n      switchingIndustry: false, // 新增：切换行业的loading状态\r\n      rules: {\r\n        introduceVideoImgUrl: [\r\n          { required: true, message: '请上传介绍视频首帧', trigger: 'change' }\r\n        ],\r\n        introduceVideoFileUrl: [\r\n          { required: true, message: '请上传介绍视频', trigger: 'change' }\r\n        ],\r\n        videoExplanationFileUrl: [\r\n          { required: true, message: '请上传讲解视频', trigger: 'change' }\r\n        ]\r\n      },\r\n      uploadingType: '',\r\n      uploadingKey: '',\r\n      categories: [], // 改为空数组，从后端动态获取\r\n      introduceVideo: {\r\n        status: '0',\r\n        backgroundImgFileUrl: '',\r\n        backgroundFileUrl: ''\r\n      },\r\n      videoExplanation: {\r\n        status: '0',\r\n        backgroundFileUrl: '',\r\n        videoSegmentedVoList: []\r\n      },\r\n      sceneTreeOptions: [],\r\n      sceneCascaderProps: {\r\n        label: 'sceneName',\r\n        value: 'id',\r\n        children: 'children',\r\n        emitPath: false,\r\n        checkStrictly: true,\r\n        disabled: (data) => {\r\n          // 允许所有节点可选，只要没有被其他分段选中\r\n          const isSelected = this.videoExplanation && this.videoExplanation.videoSegmentedVoList\r\n            ? this.videoExplanation.videoSegmentedVoList.some(seg => seg.sceneId === data.id)\r\n            : false\r\n          return isSelected\r\n        }\r\n      },\r\n      bgFileList: [], // 背景文件列表\r\n      videoExplanationFileList: [], // 讲解视频文件列表\r\n      xmlFileList: [], // XML文件列表\r\n      networkPlanDataMap: {}, // 改为对象，按菜单ID存储\r\n      businessValueDataMap: {}, // 商业价值数据映射\r\n      vrSceneDataMap: {}, // VR看现场数据映射\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      searchKeyword: '',\r\n      treeExpandedKeys: [], // 新增：保存树的展开状态\r\n      uploadModes: {\r\n        bgFile: 'upload',\r\n        videoExplanation: 'upload',\r\n        introduceVideo: 'upload'\r\n      },\r\n      synchronizing: false,\r\n      submitting: false // 添加这个属性\r\n    }\r\n  },\r\n  computed: {\r\n    videoSegmentedList() {\r\n      // 如果没有数据，默认返回一行空数据\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        return [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      return this.videoExplanation.videoSegmentedVoList\r\n    },\r\n    networkPlanData: {\r\n      get() {\r\n        const menuData = this.networkPlanDataMap[this.activeMenu]\r\n        if (!menuData) {\r\n          return {\r\n            networkVideoList: [],\r\n            videoExplanationVo: {\r\n              status: '0',\r\n              backgroundFileUrl: '',\r\n              videoSegmentedVoList: []\r\n            }\r\n          }\r\n        }\r\n        return menuData\r\n      },\r\n      set(value) {\r\n        this.$set(this.networkPlanDataMap, this.activeMenu, value)\r\n      }\r\n    },\r\n    businessValueData: {\r\n      get() {\r\n        return this.businessValueDataMap[this.activeMenu] || []\r\n      },\r\n      set(value) {\r\n        this.$set(this.businessValueDataMap, this.activeMenu, value)\r\n      }\r\n    },\r\n    vrSceneData: {\r\n      get() {\r\n        return this.vrSceneDataMap[this.activeMenu] || []\r\n      },\r\n      set(val) {\r\n        this.$set(this.vrSceneDataMap, this.activeMenu, val)\r\n      }\r\n    },\r\n    filteredMenuData() {\r\n      if (!this.searchKeyword) {\r\n        return this.menuData\r\n      }\r\n      \r\n      // 递归过滤树形数据\r\n      const filterTree = (nodes) => {\r\n        return nodes.map(node => {\r\n          const filteredChildren = node.children ? filterTree(node.children) : []\r\n          const matchesSearch = node.name && node.name.toLowerCase().includes(this.searchKeyword.toLowerCase())\r\n          \r\n          if (matchesSearch || filteredChildren.length > 0) {\r\n            return {\r\n              ...node,\r\n              children: filteredChildren\r\n            }\r\n          }\r\n          return null\r\n        }).filter(Boolean)\r\n      }\r\n      \r\n      return filterTree(this.menuData)\r\n    }\r\n  },\r\n  created() {\r\n    // 从URL获取token并设置\r\n    this.initTokenFromUrl()\r\n    this.loadIndustryMenu()\r\n  },\r\n  methods: {\r\n    // 从URL获取token并设置\r\n    initTokenFromUrl() {\r\n      const urlParams = new URLSearchParams(window.location.search)\r\n  const token = urlParams.get('token')\r\n  \r\n  if (token) {\r\n    localStorage.setItem('external-token', token)\r\n    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    console.log('从URL获取到token:', token)\r\n  } else {\r\n    const storedToken = localStorage.getItem('external-token')\r\n    if (storedToken) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`\r\n    }\r\n  }\r\n    },\r\n    async loadIndustryMenu() {\r\n      const res = await getIndustryList()\r\n      if (res.code === 0 && Array.isArray(res.data)) {\r\n        // 转换数据结构为树形菜单\r\n        this.menuData = res.data.map(plate => ({\r\n          id: `plate_${plate.plateKey}`,\r\n          name: plate.plateName,\r\n          type: 'plate',\r\n          children: plate.industryTreeListVos ? plate.industryTreeListVos.map(industry => ({\r\n            id: industry.id,\r\n            name: industry.industryName,\r\n            industryCode: industry.industryCode,\r\n            plate: industry.plate,\r\n            type: 'industry'\r\n          })) : []\r\n        }))\r\n        \r\n        // 创建扁平化的行业数据，用于业务逻辑\r\n        this.flatMenuData = []\r\n        res.data.forEach(plate => {\r\n          if (plate.industryTreeListVos) {\r\n            this.flatMenuData.push(...plate.industryTreeListVos)\r\n          }\r\n        })\r\n        \r\n        // 默认选中第一个行业\r\n        if (this.flatMenuData.length) {\r\n          this.activeMenu = String(this.flatMenuData[0].id)\r\n          this.industryCode = this.flatMenuData[0].industryCode\r\n          // 等待DOM更新后设置树组件的当前节点\r\n          this.$nextTick(() => {\r\n            // 确保树组件已渲染并设置当前选中节点\r\n            if (this.$refs.menuTree && this.$refs.menuTree.setCurrentKey) {\r\n              this.$refs.menuTree.setCurrentKey(this.activeMenu)\r\n            }\r\n          })\r\n          \r\n          // 加载第一个行业的数据\r\n          await this.handleSelect(this.activeMenu)\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleTreeNodeClick(data) {\r\n      // 只有点击行业节点才处理\r\n      if (data.type === 'industry') {\r\n        this.handleSelect(String(data.id))\r\n        this.industryCode = data.industryCode;\r\n      }\r\n    },\r\n\r\n    handleSearch(value) {\r\n      this.searchKeyword = value\r\n    },\r\n    highlightText(text) {\r\n      if (!this.searchKeyword) return text\r\n      const regex = new RegExp(`(${this.searchKeyword})`, 'gi')\r\n      return text.replace(regex, '<span class=\"highlight\">$1</span>')\r\n    },\r\n    async handleSelect(id, keepSelectedNode = false) {\r\n      try {\r\n        // 开启切换行业的loading\r\n        this.switchingIndustry = true\r\n        // 禁用页面滚动\r\n        document.body.style.overflow = 'hidden'\r\n        \r\n        this.activeMenu = id\r\n        await this.loadSceneTreeOptions(this.activeMenu)\r\n        \r\n        // 保存当前选中的节点\r\n        const currentSelectedNode = keepSelectedNode ? this.selectedNode : null\r\n        \r\n        // 重置主题选择\r\n        this.selectedTheme = null\r\n        \r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        const res = await getSceneViewConfig({ industryCode: industryCode })\r\n        if (res.code === 0 && res.data) {\r\n          \r\n          // 同步主标题、副标题、背景图片、XML文件等\r\n          this.form.sceneViewConfigId = res.data.sceneViewConfigId || ''\r\n          this.form.mainTitle = res.data.mainTitle || ''\r\n          this.form.subTitle = res.data.subTitle || ''\r\n          this.form.bgImgUrl = res.data.backgroundImgFileUrl || ''\r\n          this.form.bgFileUrl = res.data.backgroundFileUrl || ''\r\n          this.form.panoramicViewXmlUrl = res.data.panoramicViewXmlUrl || ''\r\n          \r\n          // 更新背景文件列表\r\n          this.updateBgFileList()\r\n          \r\n          // 更新XML文件列表\r\n          this.updateXmlFileList()\r\n          \r\n          // 回显主题选择\r\n          if (res.data.themeInfoVo) {\r\n            this.selectedTheme = {\r\n              themeId: res.data.themeInfoVo.themeId,\r\n              themeName: res.data.themeInfoVo.themeName,\r\n              themeEffectImg: res.data.themeInfoVo.themeEffectImg,\r\n              remark: res.data.themeInfoVo.remark\r\n            }\r\n          } else {\r\n            this.selectedTheme = null\r\n          }\r\n          \r\n          // 处理 sceneDefaultConfigVoList，动态生成 categories\r\n          if (res.data.sceneDefaultConfigVoList && Array.isArray(res.data.sceneDefaultConfigVoList)) {\r\n            this.categories = res.data.sceneDefaultConfigVoList.map(configItem => ({\r\n              id: configItem.id,\r\n              key: configItem.keyName,\r\n              name: configItem.name,\r\n              enabled: configItem.keyValue === '0', // keyValue为'0'表示启用\r\n              editing: false,\r\n              editingName: '',\r\n              originalName: configItem.name,\r\n              remark: configItem.remark,\r\n              classification: configItem.classification,\r\n              defaultStatus: configItem.defaultStatus\r\n            }))\r\n            \r\n            // 查找场景配置分类\r\n            const sceneCategory = res.data.sceneDefaultConfigVoList.find(item => item.keyName === 'default_scene')\r\n            \r\n            // 处理视频讲解数据\r\n            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.videoExplanationVo) {\r\n              const videoData = sceneCategory.industrySceneInfoVo.videoExplanationVo\r\n              this.videoExplanation = {\r\n                status: videoData.status || '0',\r\n                backgroundFileUrl: videoData.backgroundFileUrl || '',\r\n                videoSegmentedVoList: videoData.videoSegmentedVoList ? videoData.videoSegmentedVoList.map(seg => ({\r\n                  time: seg.time,\r\n                  sceneCode: seg.sceneCode,\r\n                  sceneName: seg.sceneName,\r\n                  sceneId: this.findSceneIdByCode(seg.sceneCode) // 根据sceneCode查找sceneId\r\n                })) : []\r\n              }\r\n            } else {\r\n              this.videoExplanation = {\r\n                status: '0',\r\n                backgroundFileUrl: '',\r\n                videoSegmentedVoList: []\r\n              }\r\n            }\r\n            \r\n            // 更新视频讲解文件列表\r\n            this.updateVideoExplanationFileList()\r\n            \r\n            // 处理场景配置树\r\n            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.sceneListVo) {\r\n              this.sceneConfigTree = this.adaptSceneTree(sceneCategory.industrySceneInfoVo.sceneListVo)\r\n              \r\n              // 如果需要保持选中节点\r\n              if (keepSelectedNode && currentSelectedNode) {\r\n                const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNode.id)\r\n                if (nodeToSelect) {\r\n                  this.selectedNode = nodeToSelect\r\n                } else {\r\n                  this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null\r\n                }\r\n              } else {\r\n                // 默认选择第一个节点\r\n                this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null\r\n              }\r\n            } else {\r\n              // 没有场景数据时清空\r\n              this.sceneConfigTree = []\r\n              this.selectedNode = null\r\n            }\r\n          }\r\n          \r\n          // 处理网络方案数据\r\n          if (res.data.networkSolutionVo) {\r\n            const networkData = {\r\n              networkVideoList: res.data.networkSolutionVo.networkVideoList || [],\r\n              videoExplanationVo: res.data.networkSolutionVo.videoExplanationVo || {\r\n                status: '0',\r\n                backgroundFileUrl: '',\r\n                videoSegmentedVoList: []\r\n              }\r\n            }\r\n            this.$set(this.networkPlanDataMap, this.activeMenu, networkData)\r\n          }\r\n\r\n          // 处理商业价值数据\r\n          if (res.data.commercialValueListVo) {\r\n            this.$set(this.businessValueDataMap, this.activeMenu, res.data.commercialValueListVo)\r\n          }\r\n\r\n          // 处理VR看现场数据\r\n          if (res.data.vrInfoListVo) {\r\n            this.$set(this.vrSceneDataMap, this.activeMenu, res.data.vrInfoListVo)\r\n          }\r\n          \r\n          // 其他数据处理逻辑保持不变...\r\n        }\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n        this.$message.error('加载数据失败')\r\n      } finally {\r\n        // 关闭切换行业的loading\r\n        this.switchingIndustry = false\r\n        // 恢复页面滚动\r\n        document.body.style.overflow = ''\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n      return false // 拦截默认上传行为\r\n    },\r\n    addSegment() {\r\n      this.form.videoSegmentedVoList.push({ time: '', scene: '' })\r\n    },\r\n    removeSegment(index) {\r\n      if (this.form.videoSegmentedVoList.length >= 1) {\r\n        this.form.videoSegmentedVoList.splice(index, 1)\r\n      }\r\n    },\r\n    async beforeUploadIntroduceImg(file, type, key) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传图片，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          if (type && key) {\r\n            // 针对介绍视频和视频讲解的上传，单独上传图片时使用 fileUrl\r\n            this[type][key] = res.data.fileUrl\r\n          } else {\r\n            // 针对主背景图片的上传\r\n            this.form.bgImgUrl = res.data.fileUrl\r\n          }\r\n          this.$message.success('上传成功')\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    async beforeUploadIntroduceVideo(file, type, key) {\r\n      // 如果是主背景文件上传（没有type和key参数）\r\n      if (!type && !key) {\r\n        try {\r\n          this.$modal.loading(\"正在上传文件，请稍候...\")\r\n          const formData = new FormData()\r\n          formData.append('file', file)\r\n          formData.append('industryCode', this.industryCode)\r\n          \r\n          const res = await uploadSceneFile(formData)\r\n          if (res.code === 0 && res.data) {\r\n            // 设置背景文件URL\r\n            this.form.bgFileUrl = res.data.fileUrl\r\n            \r\n            // 直接覆盖背景文件列表\r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            this.bgFileList = [{\r\n              name: fileName,\r\n              url: res.data.fileUrl,\r\n              uid: Date.now()\r\n            }]\r\n            \r\n            // 如果是MP4文件且返回了imgUrl，自动设置背景图片首帧\r\n            if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n              this.form.bgImgUrl = res.data.imgUrl\r\n              this.$message.success('上传成功，已自动生成背景图片首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || '上传失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('上传失败')\r\n        } finally {\r\n          this.$modal.closeLoading()\r\n        }\r\n        return false\r\n      }\r\n      \r\n      // 其他视频上传逻辑（介绍视频、讲解视频等）\r\n      if (!file.type.startsWith('video/') && !file.name.endsWith('.mp4')) {\r\n        this.$message.error('只能上传MP4视频文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传视频，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          if (type && key) {\r\n            // 针对介绍视频和视频讲解的上传\r\n            this[type][key] = res.data.fileUrl\r\n            \r\n            // 直接覆盖对应的文件列表\r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            if (type === 'introduceVideo' && key === 'backgroundFileUrl') {\r\n              this.introduceVideoFileList = [{\r\n                name: fileName,\r\n                url: res.data.fileUrl,\r\n                uid: Date.now()\r\n              }]\r\n            } else if (type === 'videoExplanation' && key === 'backgroundFileUrl') {\r\n              this.videoExplanationFileList = [{\r\n                name: fileName,\r\n                url: res.data.fileUrl,\r\n                uid: Date.now()\r\n              }]\r\n            }\r\n            \r\n            // 如果是介绍视频上传，且返回了imgUrl，自动设置介绍视频首帧\r\n            if (type === 'introduceVideo' && key === 'backgroundFileUrl' && res.data.imgUrl) {\r\n              this.introduceVideo.backgroundImgFileUrl = res.data.imgUrl\r\n              this.$message.success('上传成功，已自动生成介绍视频首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    beforeUploadExplanationVideo(file) {\r\n      this.uploadingType = 'mp4'\r\n      this.uploadingKey = 'videoExplanationFileUrl'\r\n      return this.handleBeforeUpload(file)\r\n    },\r\n    // 新增方法：添加场景配置节点\r\n    addSceneConfigNode(parentId = null) {\r\n      const newNode = {\r\n        id: Date.now(), // 生成唯一ID\r\n        name: '新场景',\r\n        type: 'scene', // 类型为场景\r\n        enabled: true,\r\n        children: [],\r\n        parentId: parentId\r\n      }\r\n      if (parentId) {\r\n        const parentNode = this.findNodeById(this.sceneConfigTree, parentId)\r\n        if (parentNode) {\r\n          parentNode.children.push(newNode)\r\n        }\r\n      } else {\r\n        this.sceneConfigTree.push(newNode)\r\n      }\r\n      return newNode.id\r\n    },\r\n    // 新增方法：删除场景配置节点\r\n    removeSceneConfigNode(nodeId) {\r\n      this.sceneConfigTree = this.sceneConfigTree.filter(node => node.id !== nodeId)\r\n    },\r\n    // 新增方法：查找节点\r\n    findNodeById(nodes, id) {\r\n      for (const node of nodes) {\r\n        if (node.id === id) {\r\n          return node\r\n        }\r\n        if (node.children && node.children.length > 0) {\r\n          const found = this.findNodeById(node.children, id)\r\n          if (found) {\r\n            return found\r\n          }\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    // 新增方法：添加场景的痛点价值\r\n    addScenePainPoint(nodeId) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints.push({ title: '', contents: [''], showTime: '' })\r\n      }\r\n    },\r\n    // 新增方法：删除场景的痛点价值\r\n    removeScenePainPoint(nodeId, idx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints.splice(idx, 1)\r\n      }\r\n    },\r\n    // 新增方法：添加场景痛点内容的项\r\n    addScenePainContent(nodeId, idx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints[idx].contents.push('')\r\n      }\r\n    },\r\n    // 新增方法：删除场景痛点内容的项\r\n    removeScenePainContent(nodeId, idx, cidx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints[idx].contents.splice(cidx, 1)\r\n      }\r\n    },\r\n    // 新增方法：添加场景的成本预估内容\r\n    addSceneCostContent(nodeId) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'costEstimate') {\r\n        node.contents = node.contents || []\r\n        node.contents.push('')\r\n      }\r\n    },\r\n    // 新增方法：删除场景的成本预估内容\r\n    removeSceneCostContent(nodeId, cidx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'costEstimate') {\r\n        node.contents = node.contents || []\r\n        node.contents.splice(cidx, 1)\r\n      }\r\n    },\r\n    // 新增方法：上传场景配置图片\r\n    beforeUploadSceneConfigImg(file, type, key) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      const reader = new FileReader()\r\n      reader.onload = e => {\r\n        this.findNodeById(this.sceneConfigTree, type)[key] = e.target.result\r\n      }\r\n      reader.readAsDataURL(file)\r\n      return false\r\n    },\r\n    // 新增方法：上传场景配置文件\r\n    beforeUploadSceneConfigFile(file, type, key) {\r\n      // 这里只做文件名回显\r\n      this.findNodeById(this.sceneConfigTree, type)[key] = file.name\r\n      return false\r\n    },\r\n    handleSceneNodeClick(node) {\r\n      this.selectedNode = node\r\n    },\r\n    // 适配函数移到methods中，供接口数据适配使用\r\n    adaptSceneTree(rawTree, parent = null) {\r\n      if (!rawTree) return []\r\n      const arr = Array.isArray(rawTree) ? rawTree : [rawTree]\r\n      return arr.map((node) => {\r\n        const adapted = {\r\n          id: node.sceneId,\r\n          sceneInfoId: node.sceneInfoId || null,\r\n          name: node.sceneName,\r\n          code: node.sceneCode,\r\n          x: node.x,\r\n          y: node.y,\r\n          type: node.type,\r\n          status: node.status !== null ? node.status : '0',\r\n          isUnfold: node.isUnfold !== null && node.isUnfold !== undefined ? node.isUnfold : '1',\r\n          displayLocation: node.displayLocation !== null && node.displayLocation !== undefined ? node.displayLocation : '0',\r\n          treeClassification: node.treeClassification !== null && node.treeClassification !== undefined ? node.treeClassification : '3',\r\n          introduceVideoVo: node.introduceVideoVo ? {\r\n            id: node.introduceVideoVo.id || '',\r\n            type: node.introduceVideoVo.type || '',\r\n            viewInfoId: node.introduceVideoVo.viewInfoId || '',\r\n            status: node.introduceVideoVo.status || '0',\r\n            backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || '',\r\n            backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || ''\r\n          } : { id: '', type: '', viewInfoId: '', status: '0', backgroundImgFileUrl: '', backgroundFileUrl: '' },\r\n          tradition: node.sceneTraditionVo ? {\r\n            name: node.sceneTraditionVo.name || '',\r\n            panoramicViewXmlKey: node.sceneTraditionVo.panoramicViewXmlKey || '',\r\n            backgroundResources: node.sceneTraditionVo.sceneVideoList ? \r\n              node.sceneTraditionVo.sceneVideoList.map(v => ({\r\n                id: v.id || null,\r\n                label: v.tag || '',\r\n                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({\r\n                  id: rel.id || null,\r\n                  fileId: rel.fileId || null,\r\n                  x: rel.clickX || '',\r\n                  y: rel.clickY || '',\r\n                  wide: rel.wide || '',\r\n                  high: rel.high || '',\r\n                  xmlKey: rel.xmlKey || '',\r\n                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),\r\n                  sceneCode: rel.bindSceneCode || ''\r\n                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }],\r\n                wide: v.wide || 0,\r\n                high: v.high || 0,\r\n                bgImg: v.backgroundImgFileUrl || '',\r\n                bgFile: v.backgroundFileUrl || '',\r\n                status: v.status || '',\r\n                type: v.type || '',\r\n                viewInfoId: v.viewInfoId || ''\r\n              })) : [],\r\n            painPoints: node.sceneTraditionVo.painPointList ?\r\n              (Array.isArray(node.sceneTraditionVo.painPointList) ? node.sceneTraditionVo.painPointList.map(p => ({\r\n                painPointId: p.painPointId || null,\r\n                title: p.bigTitle || '',\r\n                contents: p.content || [],\r\n                showTime: p.displayTime || ''\r\n              })) : []) : []\r\n          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },\r\n          wisdom5g: node.scene5gVo ? {\r\n            name: node.scene5gVo.name || '',\r\n            panoramicViewXmlKey: node.scene5gVo.panoramicViewXmlKey || '',\r\n            backgroundResources: node.scene5gVo.sceneVideoList ? \r\n              node.scene5gVo.sceneVideoList.map(v => ({\r\n                id: v.id || null,\r\n                tag: v.tag || '',\r\n                status: v.status || '',\r\n                type: v.type || '',\r\n                viewInfoId: v.viewInfoId || '',\r\n                backgroundImgFileUrl: v.backgroundImgFileUrl || '',\r\n                backgroundFileUrl: v.backgroundFileUrl || '',\r\n                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({\r\n                  id: rel.id || null,\r\n                  fileId: rel.fileId || null,\r\n                  x: rel.clickX || '',\r\n                  y: rel.clickY || '',\r\n                  wide: rel.wide || '',\r\n                  high: rel.high || '',\r\n                  xmlKey: rel.xmlKey || '',\r\n                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),\r\n                  sceneCode: rel.bindSceneCode || ''\r\n                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }]\r\n              })) : [],\r\n            painPoints: node.scene5gVo.painPointList ? \r\n              (Array.isArray(node.scene5gVo.painPointList) ? node.scene5gVo.painPointList.map(p => ({\r\n                painPointId: p.painPointId || null,\r\n                title: p.bigTitle || '',\r\n                contents: p.content || [],\r\n                showTime: p.displayTime || ''\r\n              })) : []) : []\r\n          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },\r\n          costEstimate: node.costEstimationInfoVo ? {\r\n            painPointId: node.costEstimationInfoVo.painPointId || null,\r\n            status: node.costEstimationInfoVo.status || '0',\r\n            title: node.costEstimationInfoVo.bigTitle || '',\r\n            contents: node.costEstimationInfoVo.content || []\r\n          } : { painPointId: null, status: '0', title: '', contents: [] },\r\n          children: [],\r\n          parent\r\n        }\r\n        // 递归处理子节点，保持后端返回的原始顺序\r\n        adapted.children = node.children ? this.adaptSceneTree(node.children, adapted) : []\r\n        return adapted\r\n      })\r\n    },\r\n    async handleSubmit() {\r\n      try {\r\n        this.submitting = true\r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === this.activeMenu)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        // 保存当前选中的节点的完整信息\r\n        const currentSelectedNodeId = this.selectedNode ? this.selectedNode.id : null\r\n        const currentSelectedNodeName = this.selectedNode ? this.selectedNode.name : null\r\n        \r\n        // 构建提交数据\r\n        const submitData = {\r\n          industryId: this.activeMenu,\r\n          industryCode: industryCode, // 新增参数\r\n          sceneViewConfigId: this.form.sceneViewConfigId || null,\r\n          mainTitle: this.form.mainTitle || null,\r\n          subTitle: this.form.subTitle || null,\r\n          themeId: this.selectedTheme ? this.selectedTheme.themeId : null,\r\n          backgroundImgFileUrl: this.form.bgImgUrl || null,\r\n          backgroundFileUrl: this.form.bgFileUrl || null,\r\n          panoramicViewXmlUrl: this.form.panoramicViewXmlUrl || null,\r\n          networkSolutionInfoVo: {\r\n            networkVideoList: (this.networkPlanDataMap[this.activeMenu]?.networkVideoList && Array.isArray(this.networkPlanDataMap[this.activeMenu].networkVideoList)) ? \r\n              this.networkPlanDataMap[this.activeMenu].networkVideoList.map(plan => ({\r\n                id: plan.id || null,\r\n                type: 4,\r\n                tag: plan.tag || null,\r\n                clickX: plan.clickX || null,\r\n                clickY: plan.clickY || null,\r\n                wide: plan.wide || null,\r\n                high: plan.high || null,\r\n                backgroundImgFileUrl: plan.backgroundImgFileUrl || null,\r\n                backgroundFileUrl: plan.backgroundFileUrl || null,\r\n                status: null,\r\n                viewInfoId: null\r\n              })) : [],\r\n            videoExplanationVo: {\r\n              status: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.status || '0',\r\n              backgroundFileUrl: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.backgroundFileUrl || null,\r\n              videoSegmentedVoList: (this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.videoSegmentedVoList?.length) ? \r\n                this.networkPlanDataMap[this.activeMenu].videoExplanationVo.videoSegmentedVoList.map(seg => ({\r\n                  time: seg.time || null,\r\n                  sceneCode: seg.sceneCode || null,\r\n                  sceneName: seg.sceneName || null\r\n                })) : null\r\n            }\r\n          },\r\n          sceneDefaultConfigVoList: this.categories.map(cat => {\r\n            const baseConfig = {\r\n              id: cat.id || null,\r\n              industryId: this.activeMenu || null,\r\n              name: cat.name,\r\n              keyName: cat.key,\r\n              keyValue: cat.enabled ? '0' : '1',\r\n              remark: cat.remark || cat.name,\r\n              defaultStatus: '0'\r\n            }\r\n\r\n            if (cat.key === 'default_scene') {\r\n              const convertedSceneList = this.convertSceneTreeToApi(this.sceneConfigTree)     \r\n              baseConfig.industrySceneInfoVo = {\r\n                videoExplanationVo: {\r\n                  status: this.videoExplanation.status,\r\n                  backgroundFileUrl: this.videoExplanation.backgroundFileUrl || null,\r\n                  videoSegmentedVoList: this.videoExplanation.videoSegmentedVoList.length ? this.videoExplanation.videoSegmentedVoList : null\r\n                },\r\n                sceneListVo: convertedSceneList\r\n              }\r\n            } else {\r\n              baseConfig.industrySceneInfoVo = null\r\n            }\r\n            \r\n            return baseConfig\r\n          }),\r\n          commercialValueDTO: (this.businessValueDataMap[this.activeMenu] && Array.isArray(this.businessValueDataMap[this.activeMenu])) ? \r\n            this.businessValueDataMap[this.activeMenu].map(value => ({\r\n              id: value.id || null,\r\n              viewInfoId: value.viewInfoId || null,\r\n              type: 5,\r\n              tag: value.tag || null,\r\n              backgroundImgFileUrl: value.backgroundImgFileUrl || null,\r\n              backgroundFileUrl: value.backgroundFileUrl || null\r\n            })) : [],\r\n          vrInfoDtoList: (this.vrSceneDataMap[this.activeMenu] && Array.isArray(this.vrSceneDataMap[this.activeMenu])) ? \r\n            this.vrSceneDataMap[this.activeMenu]. map(vr => ({\r\n              id: vr.id || null,\r\n              industryId: vr.industryId || this.activeMenu,\r\n              type: vr.type || 6,\r\n              viewInfoId: vr.viewInfoId || null,\r\n              name: vr.name || '',\r\n              address: vr.address || ''\r\n            })) : [],\r\n        }\r\n      \r\n        const response = await sceneViewUpd(submitData)\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n\r\n        // 重新加载数据\r\n        await this.handleSelect(this.activeMenu)\r\n\r\n        if (currentSelectedNodeId && this.sceneConfigTree.length > 0) {\r\n          // 强制查找并设置选中节点\r\n          const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNodeId)\r\n\r\n          if (nodeToSelect) {\r\n            // 计算并设置展开路径\r\n            const pathIds = []\r\n            const findPath = (nodes, targetId, currentPath = []) => {\r\n              for (const node of nodes) {\r\n                const newPath = [...currentPath, node.id]\r\n                if (node.id === targetId) {\r\n                  pathIds.push(...newPath)\r\n                  return true\r\n                }\r\n                if (node.children && node.children.length > 0) {\r\n                  if (findPath(node.children, targetId, newPath)) {\r\n                    return true\r\n                  }\r\n                }\r\n              }\r\n              return false\r\n            }\r\n\r\n            findPath(this.sceneConfigTree, currentSelectedNodeId)\r\n            this.treeExpandedKeys = pathIds.slice(0, -1)\r\n\r\n            // 先设置选中节点\r\n            this.selectedNode = nodeToSelect\r\n\r\n            // 强制更新树组件的选中状态\r\n            this.$nextTick(() => {\r\n              this.$nextTick(() => {\r\n                // 模拟点击节点来强制更新选中状态\r\n                this.handleSceneNodeClick(nodeToSelect)\r\n              })\r\n            })\r\n          }\r\n        }\r\n        \r\n        console.log('提交内容:', submitData)\r\n      } catch (error) {\r\n        console.error('提交失败:', error)\r\n        this.$message.error('提交失败')\r\n      } finally {\r\n        this.submitting = false\r\n      }\r\n    },\r\n    addVideoSegment() {\r\n      // 如果原数组为空，先初始化\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        this.videoExplanation.videoSegmentedVoList = [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      this.videoExplanation.videoSegmentedVoList.push({ time: '', sceneId: '', sceneName: '', sceneCode: '' })\r\n    },\r\n    removeVideoSegment(idx) {\r\n      this.videoExplanation.videoSegmentedVoList.splice(idx, 1)\r\n    },\r\n    //递归重构结构\r\n    getDeepTreeOptions(tree) {\r\n    return tree.map(item => {\r\n      // 复制当前节点的基础属性\r\n      const node = { ...item };\r\n      \r\n      // 如果存在 children 且不为空，则递归处理\r\n      if (node.children && node.children.length > 0) {\r\n        node.children = this.getDeepTreeOptions(node.children);\r\n      } else {\r\n        // 当 children 为空或不存在时，删除 children 属性（可选）\r\n        delete node.children;\r\n      }\r\n      \r\n      return node;\r\n    });\r\n  },\r\n    async loadSceneTreeOptions(id) {\r\n      try {\r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        const res = await getSceneTreeList({ industryCode: industryCode })\r\n        if (res.code === 0 && Array.isArray(res.data)) {\r\n          this.sceneTreeOptions = this.getDeepTreeOptions(res.data)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载场景树失败:', error)\r\n      }\r\n    },\r\n    handleSceneCascaderChange(val, idx) {\r\n      // 确保数组和索引位置的对象存在\r\n      if (!this.videoExplanation.videoSegmentedVoList || !this.videoExplanation.videoSegmentedVoList[idx]) {\r\n        return\r\n      }\r\n      \r\n      const findScene = (tree, id) => {\r\n        for (const node of tree) {\r\n          if (node.id === id) return node\r\n          if (node.children && node.children.length) {\r\n            const found = findScene(node.children, id)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      const node = findScene(this.sceneTreeOptions, val)\r\n      if (node) {\r\n        // 设置场景ID和相关信息\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneId = val\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneName = node.sceneName\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneCode = node.sceneCode\r\n      }\r\n    },\r\n    isSceneDisabled(id, currentIdx) {\r\n      // 除当前分段外，其他分段已选的id\r\n      return this.videoExplanation.videoSegmentedVoList.some((seg, idx) => idx !== currentIdx && seg.sceneId === id)\r\n    },\r\n    // 将场景树转换为接口格式\r\n    convertSceneTreeToApi(sceneTree) {\r\n      console.log(\"提交的数据:\", sceneTree);\r\n      return sceneTree.map(node => ({\r\n        sceneInfoId: node.sceneInfoId || null,\r\n        sceneId: node.id,\r\n        paramId: node.parent ? node.parent.id : null,\r\n        sceneName: node.name,\r\n        sceneCode: node.code,\r\n        x: node.x || null,\r\n        y: node.y || null,\r\n        type: node.type || null,\r\n        status: node.status,\r\n        isUnfold: (node.children && node.children.length > 0) ? (node.isUnfold || '1') : null,\r\n        displayLocation: (node.children && node.children.length > 0) ? (node.displayLocation || '0') : null,\r\n        treeClassification: (node.children && node.children.length > 0) ? (node.treeClassification || '3') : null,\r\n        introduceVideoVo: node.introduceVideoVo ? {\r\n          id: node.introduceVideoVo.id || null,\r\n          type: node.introduceVideoVo.type || null,\r\n          viewInfoId: node.introduceVideoVo.viewInfoId || null,\r\n          status: node.introduceVideoVo.status || null,\r\n          backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || null,\r\n          backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || null\r\n        } : null,\r\n        sceneTraditionVo: node.tradition ? {\r\n          name: node.tradition.name || null,\r\n          panoramicViewXmlKey: node.tradition.panoramicViewXmlKey || null,\r\n          sceneVideoList: node.tradition.backgroundResources && node.tradition.backgroundResources.length ? \r\n            node.tradition.backgroundResources.map(resource => ({\r\n              id: resource.id || null,\r\n              tag: resource.label || null,\r\n              wide: resource.wide || null,\r\n              high: resource.high || null,\r\n              status: resource.status || null,\r\n              type: 1,\r\n              viewInfoId: resource.viewInfoId || null,\r\n              backgroundImgFileUrl: resource.bgImg || '',\r\n              backgroundFileUrl: resource.bgFile || '',\r\n              sceneFileRelList: resource.coordinates && resource.coordinates.length ? \r\n                resource.coordinates.map(coord => ({\r\n                  id: coord.id || null,\r\n                  fileId: coord.fileId || null,\r\n                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,\r\n                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,\r\n                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,\r\n                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,\r\n                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,\r\n                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null\r\n                })) : []\r\n            })) : null,\r\n          painPointList: node.tradition.painPoints && node.tradition.painPoints.length ? \r\n            node.tradition.painPoints.map(pain => ({\r\n              painPointId: pain.painPointId || null,\r\n              bigTitle: pain.title || null,\r\n              content: pain.contents || [],\r\n              displayTime: pain.showTime || null\r\n            })) : null\r\n        } : null,\r\n        scene5gVo: node.wisdom5g ? {\r\n          name: node.wisdom5g.name || null,\r\n          panoramicViewXmlKey: node.wisdom5g.panoramicViewXmlKey || null,\r\n          sceneVideoList: node.wisdom5g.backgroundResources && node.wisdom5g.backgroundResources.length ? \r\n            node.wisdom5g.backgroundResources.map(resource => ({\r\n              id: resource.id || null,\r\n              tag: resource.tag || null,\r\n              status: resource.status || null,\r\n              type: 2,\r\n              viewInfoId: resource.viewInfoId || null,\r\n              backgroundImgFileUrl: resource.bgImg || '',\r\n              backgroundFileUrl: resource.bgFile || '',\r\n              sceneFileRelList: resource.coordinates && resource.coordinates.length ? \r\n                resource.coordinates.map(coord => ({\r\n                  id: coord.id || null,\r\n                  fileId: coord.fileId || null,\r\n                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,\r\n                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,\r\n                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,\r\n                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,\r\n                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,\r\n                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null\r\n                })) : []\r\n            })) : null,\r\n          painPointList: node.wisdom5g.painPoints && node.wisdom5g.painPoints.length ? \r\n            node.wisdom5g.painPoints.map(pain => ({\r\n              painPointId: pain.painPointId || null,\r\n              bigTitle: pain.title || null,\r\n              content: pain.contents || [],\r\n              displayTime: pain.showTime || null\r\n            })) : null\r\n        } : null,\r\n        costEstimationInfoVo: node.costEstimate ? {\r\n          painPointId: node.costEstimate.painPointId || null,\r\n          status: node.costEstimate.status || '0',\r\n          bigTitle: node.costEstimate.title || null,\r\n          content: node.costEstimate.contents && node.costEstimate.contents.length ? node.costEstimate.contents : null\r\n        } : null,\r\n        children: node.children && node.children.length ? this.convertSceneTreeToApi(node.children) : []\r\n      }))\r\n    },\r\n    handleTimeChange(val, idx) {\r\n      // 确保数组已初始化\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        this.videoExplanation.videoSegmentedVoList = [{ time: 0, sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      // 更新对应位置的时间值\r\n      if (this.videoExplanation.videoSegmentedVoList[idx]) {\r\n        this.videoExplanation.videoSegmentedVoList[idx].time = val\r\n      }\r\n    },\r\n    // 根据sceneCode查找对应的sceneId\r\n    findSceneIdByCode(sceneCode) {\r\n      if (!sceneCode || !this.sceneTreeOptions) return ''\r\n      \r\n      const findInTree = (tree) => {\r\n        for (const node of tree) {\r\n          if (node.sceneCode === sceneCode) {\r\n            return node.id\r\n          }\r\n          if (node.children && node.children.length) {\r\n            const found = findInTree(node.children)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      \r\n      return findInTree(this.sceneTreeOptions) || ''\r\n    },\r\n    // 处理背景文件删除\r\n    handleRemoveBgFile(file, fileList) {\r\n      this.form.bgFileUrl = ''\r\n      this.form.bgImgUrl = '' // 同时清空背景图片首帧\r\n      this.bgFileList = []\r\n      this.$message.success('文件已删除')\r\n    },\r\n    // 更新背景文件列表\r\n    updateBgFileList() {\r\n      if (this.form.bgFileUrl) {\r\n        const fileName = this.form.bgFileUrl.split('/').pop()\r\n        this.bgFileList = [{\r\n          name: fileName,\r\n          url: this.form.bgFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.bgFileList = []\r\n      }\r\n    },\r\n    // 处理介绍视频文件删除\r\n    handleRemoveIntroduceVideoFile(file, fileList) {\r\n      this.introduceVideo.backgroundFileUrl = ''\r\n      this.introduceVideo.backgroundImgFileUrl = '' // 同时清空首帧图片\r\n      this.introduceVideoFileList = []\r\n      this.$message.success('介绍视频已删除')\r\n    },\r\n    // 更新介绍视频文件列表\r\n    updateIntroduceVideoFileList() {\r\n      if (this.introduceVideo.backgroundFileUrl) {\r\n        const fileName = this.introduceVideo.backgroundFileUrl.split('/').pop()\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: this.introduceVideo.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.introduceVideoFileList = []\r\n      }\r\n    },\r\n    // 处理讲解视频文件删除\r\n    handleRemoveVideoExplanationFile(file, fileList) {\r\n      this.videoExplanation.backgroundFileUrl = ''\r\n      this.videoExplanationFileList = []\r\n      this.$message.success('讲解视频已删除')\r\n    },\r\n    // 更新讲解视频文件列表\r\n    updateVideoExplanationFileList() {\r\n      if (this.videoExplanation.backgroundFileUrl) {\r\n        const fileName = this.videoExplanation.backgroundFileUrl.split('/').pop()\r\n        this.videoExplanationFileList = [{\r\n          name: fileName,\r\n          url: this.videoExplanation.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.videoExplanationFileList = []\r\n      }\r\n    },\r\n    // 处理XML文件删除\r\n    handleRemoveXmlFile(file, fileList) {\r\n      this.form.panoramicViewXmlUrl = ''\r\n      this.xmlFileList = []\r\n      this.$message.success('XML文件已删除')\r\n    },\r\n    \r\n    // 更新XML文件列表\r\n    updateXmlFileList() {\r\n      if (this.form.panoramicViewXmlUrl) {\r\n        const fileName = this.form.panoramicViewXmlUrl.split('/').pop()\r\n        this.xmlFileList = [{\r\n          name: fileName,\r\n          url: this.form.panoramicViewXmlUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.xmlFileList = []\r\n      }\r\n    },\r\n    // 图片预览\r\n    previewImage(url) {\r\n      if (url) {\r\n        this.previewImageUrl = url\r\n        this.previewVisible = true\r\n      }\r\n    },\r\n    closePreview() {\r\n      this.previewVisible = false\r\n      this.previewImageUrl = ''\r\n    },\r\n    // 删除背景图片\r\n    deleteBgImage() {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.bgImgUrl = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    async beforeUploadXmlFile(file) {\r\n      // 检查文件类型\r\n      if (!file.name.toLowerCase().endsWith('.xml')) {\r\n        this.$message.error('只能上传XML文件！')\r\n        return false\r\n      }\r\n      \r\n      // 检查文件大小（50MB）\r\n      if (file.size > 50 * 1024 * 1024) {\r\n        this.$message.error('文件大小不能超过50MB！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传XML文件，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 设置XML文件URL\r\n          this.form.panoramicViewXmlUrl = res.data.fileUrl\r\n          \r\n          // 直接覆盖XML文件列表\r\n          const fileName = res.data.fileUrl.split('/').pop()\r\n          this.xmlFileList = [{\r\n            name: fileName,\r\n            url: res.data.fileUrl,\r\n            uid: Date.now()\r\n          }]\r\n          \r\n          this.$message.success('上传成功')\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    // 主题变更回调\r\n    onThemeChange(theme) {\r\n      // 如果主题有默认背景图，可以自动设置\r\n      if (theme && theme.defaultBgImage) {\r\n        this.form.bgImgUrl = theme.defaultBgImage\r\n      }\r\n    },\r\n    // 格式化坐标数据用于提交\r\n    formatCoordinatesForSubmit(coordinates) {\r\n      if (!coordinates || !Array.isArray(coordinates)) {\r\n        return { clickX: '', clickY: '' }\r\n      }\r\n      \r\n      const xValues = coordinates.map(coord => coord.x || '0').join(',')\r\n      const yValues = coordinates.map(coord => coord.y || '0').join(',')\r\n      \r\n      return {\r\n        clickX: xValues,\r\n        clickY: yValues\r\n      }\r\n    },\r\n    // 解析坐标字符串为坐标数组\r\n    parseCoordinatesFromApi(clickX, clickY) {\r\n      const xArray = clickX ? clickX.split(',') : ['']\r\n      const yArray = clickY ? clickY.split(',') : ['']\r\n      \r\n      // 取较长的数组长度，确保坐标对齐\r\n      const maxLength = Math.max(xArray.length, yArray.length)\r\n      const coordinates = []\r\n      \r\n      for (let i = 0; i < maxLength; i++) {\r\n        coordinates.push({\r\n          x: xArray[i] || '',\r\n          y: yArray[i] || ''\r\n        })\r\n      }\r\n      \r\n      // 至少保证有一个坐标组\r\n      return coordinates.length > 0 ? coordinates : [{ x: '', y: '' }]\r\n    },\r\n    // 开始编辑标题\r\n    startEditTitle(index) {\r\n      const category = this.categories[index]\r\n      category.editing = true\r\n      category.editingName = category.name\r\n      \r\n      // 下一帧聚焦输入框\r\n      this.$nextTick(() => {\r\n        // 使用动态ref名称\r\n        const inputRef = this.$refs[`titleInput_${index}`]\r\n        if (inputRef && inputRef[0]) {\r\n          inputRef[0].focus()\r\n          inputRef[0].select()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 完成编辑标题\r\n    finishEditTitle(index) {\r\n      const category = this.categories[index]\r\n      if (category.editingName && category.editingName.trim()) {\r\n        category.name = category.editingName.trim()\r\n      }\r\n      category.editing = false\r\n      category.editingName = ''\r\n    },\r\n\r\n    // 取消编辑标题\r\n    cancelEditTitle(index) {\r\n      const category = this.categories[index]\r\n      category.editing = false\r\n      category.editingName = ''\r\n    },\r\n    // 设置上传模式\r\n    setUploadMode(type, mode) {\r\n      this.$set(this.uploadModes, type, mode)\r\n    },\r\n    // 背景文件链接输入处理\r\n    handleBgFileUrlInput(value) {\r\n      this.bgFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.bgFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 视频讲解链接输入处理\r\n    handleVideoExplanationUrlInput(value) {\r\n      this.videoExplanationFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.videoExplanationFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 介绍视频链接输入处理\r\n    handleIntroduceVideoUrlInput(value) {\r\n      this.introduceVideoFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 同步文件\r\n    async handleSynchronizeFile() {\r\n      if (!this.form.sceneViewConfigId) {\r\n        this.$message.warning('请先保存配置后再同步文件')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.synchronizing = true\r\n        this.$modal.loading(\"正在同步文件，请稍候...\")\r\n        \r\n        // 使用FormData或URLSearchParams传递表单参数\r\n        const formData = new FormData()\r\n        formData.append('viewConfigId', this.form.sceneViewConfigId)\r\n        \r\n        const res = await synchronizationFile(formData)\r\n        \r\n        if (res.code === 0) {\r\n          this.$message.success(res.msg)\r\n        } else {\r\n          this.$message.error(res.msg || '文件同步失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('同步文件失败:', error)\r\n        this.$message.error('文件同步失败')\r\n      } finally {\r\n        this.synchronizing = false\r\n        this.$modal.closeLoading()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.page-container {\r\n  display: flex;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.menu-panel {\r\n  width: 250px;\r\n  background-color: #f5f7fa;\r\n  border-right: 1px solid #e4e7ed;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n  background-color: #f5f7fa;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.menu-tree {\r\n  background-color: #f5f7fa;\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 8px 0;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content:hover {\r\n  background-color: #e6f7ff;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.menu-tree::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-track {\r\n  background-color: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-thumb {\r\n  background-color: #c0c0c0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-thumb:hover {\r\n  background-color: #a0a0a0;\r\n}\r\n\r\n.content-panel {\r\n  flex: 1;\r\n  padding: 20px 20px 80px 20px;\r\n  overflow-y: auto;\r\n  background-color: #fff;\r\n  position: relative;\r\n}\r\n.mini-block {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.category-block {\r\n  margin-top: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.category-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.category-title:hover {\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n.category-title span {\r\n  display: inline-block;\r\n  min-width: 100px;\r\n}\r\n\r\n.category-body {\r\n  padding: 12px;\r\n  background: #ffffff;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.sub-category-block {\r\n  margin-bottom: 15px;\r\n}\r\n.sub-category-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.sub-category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px;\r\n  background-color: #fafafa;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.sub-category-title {\r\n  font-weight: 500;\r\n}\r\n\r\n.sub-category-body {\r\n  padding: 15px;\r\n}\r\n\r\n.segment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.pain-point-block {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.pain-point-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mini-block {\r\n  margin-bottom: 20px;\r\n  min-height: 450px; /* 设置统一的最小高度 */\r\n}\r\n\r\n.mini-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 确保卡片内容区域也有合适的高度 */\r\n.mini-block .el-card__body {\r\n  min-height: 450px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 视频卡片保持原有样式 */\r\n.video-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 450px;\r\n}\r\n\r\n.video-card .el-card__body {\r\n  flex: 1;\r\n  overflow: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 450px;\r\n}\r\n.video-card-yu{\r\n  min-height: 300px;\r\n}\r\n.video-card .el-card__body {\r\n  flex: 1;\r\n  overflow: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.segment-scroll {\r\n  max-height: 150px;\r\n  overflow-y: auto;\r\n  border: 1px solid #eee;\r\n  border-radius: 4px;\r\n  padding: 8px;\r\n  background: #fafbfc;\r\n}\r\n.scene-config-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 限制上传图片的显示大小 */\r\n.image-upload .el-upload--picture-card {\r\n  width: 148px;\r\n  height: 148px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.upload-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 介绍视频首帧图片大小控制 */\r\n.image-upload .el-upload-list__item-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 上传框也添加圆角 */\r\n.image-upload .el-upload--picture-card {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 图片预览样式 */\r\n.preview-container {\r\n  text-align: center;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 70vh;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 图片悬停操作样式 */\r\n.image-preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  border-radius: 6px;\r\n}\r\n\r\n.image-preview-container:hover .image-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.preview-icon,\r\n.delete-icon {\r\n  color: white;\r\n  font-size: 20px;\r\n  margin: 0 10px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.preview-icon:hover,\r\n.delete-icon:hover {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.submit-footer {\r\n  position: fixed;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 250px;\r\n  height: 60px;\r\n  background: #fff;\r\n  border-top: 1px solid #e4e7ed;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  padding: 0 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n}\r\n\r\n.submit-footer .el-button {\r\n  min-width: 100px;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.menu-search .el-input {\r\n  border-radius: 20px;\r\n}\r\n\r\n.menu-search .el-input__inner {\r\n  border-radius: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\n.highlight {\r\n  background-color: #ffeb3b;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.menu-tree {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content:hover {\r\n  background-color: #e6f7ff;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.custom-tree-node {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.highlight {\r\n  background-color: yellow;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n  justify-content: flex-end;\r\n  padding: 0 20px;\r\n}\r\n</style>\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n}\r\n\r\n.submit-footer .el-button {\r\n  min-width: 100px;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.menu-search .el-input {\r\n  border-radius: 20px;\r\n}\r\n\r\n.menu-search .el-input__inner {\r\n  border-radius: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\n.highlight {\r\n  background-color: #ffeb3b;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.menu-tree {\r\n  background\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0VA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,kBAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,oBAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,cAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,qBAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,MAAA,GAAAN,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAS,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,mBAAA,EAAAA,4BAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,oBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;MAAA;MACAC,YAAA;MAAA;MACAC,UAAA;MACAC,YAAA;MACAC,aAAA;MAAA;MACAC,IAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,mBAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,OAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,KAAA;QACAC,oBAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,qBAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,uBAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,aAAA;MACAC,YAAA;MACAC,UAAA;MAAA;MACAC,cAAA;QACAC,MAAA;QACAC,oBAAA;QACAC,iBAAA;MACA;MACAC,gBAAA;QACAH,MAAA;QACAE,iBAAA;QACAE,oBAAA;MACA;MACAC,gBAAA;MACAC,kBAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,aAAA;QACAC,QAAA,WAAAA,SAAAxC,IAAA;UACA;UACA,IAAAyC,UAAA,GAAAxC,KAAA,CAAA8B,gBAAA,IAAA9B,KAAA,CAAA8B,gBAAA,CAAAC,oBAAA,GACA/B,KAAA,CAAA8B,gBAAA,CAAAC,oBAAA,CAAAU,IAAA,WAAAC,GAAA;YAAA,OAAAA,GAAA,CAAAC,OAAA,KAAA5C,IAAA,CAAA6C,EAAA;UAAA,KACA;UACA,OAAAJ,UAAA;QACA;MACA;MACAK,UAAA;MAAA;MACAC,wBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,cAAA;MAAA;MACA;MACAC,cAAA;MACAC,eAAA;MACAC,aAAA;MACAC,gBAAA;MAAA;MACAC,WAAA;QACAC,MAAA;QACA1B,gBAAA;QACAJ,cAAA;MACA;MACA+B,aAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA;MACA,UAAA9B,gBAAA,CAAAC,oBAAA,SAAAD,gBAAA,CAAAC,oBAAA,CAAA8B,MAAA;QACA;UAAAC,IAAA;UAAAnB,OAAA;UAAAoB,SAAA;UAAAC,SAAA;QAAA;MACA;MACA,YAAAlC,gBAAA,CAAAC,oBAAA;IACA;IACAkC,eAAA;MACAC,GAAA,WAAAA,IAAA;QACA,IAAAjE,QAAA,QAAA+C,kBAAA,MAAA7C,UAAA;QACA,KAAAF,QAAA;UACA;YACAkE,gBAAA;YACAC,kBAAA;cACAzC,MAAA;cACAE,iBAAA;cACAE,oBAAA;YACA;UACA;QACA;QACA,OAAA9B,QAAA;MACA;MACAoE,GAAA,WAAAA,IAAAlC,KAAA;QACA,KAAAmC,IAAA,MAAAtB,kBAAA,OAAA7C,UAAA,EAAAgC,KAAA;MACA;IACA;IACAoC,iBAAA;MACAL,GAAA,WAAAA,IAAA;QACA,YAAAjB,oBAAA,MAAA9C,UAAA;MACA;MACAkE,GAAA,WAAAA,IAAAlC,KAAA;QACA,KAAAmC,IAAA,MAAArB,oBAAA,OAAA9C,UAAA,EAAAgC,KAAA;MACA;IACA;IACAqC,WAAA;MACAN,GAAA,WAAAA,IAAA;QACA,YAAAhB,cAAA,MAAA/C,UAAA;MACA;MACAkE,GAAA,WAAAA,IAAAI,GAAA;QACA,KAAAH,IAAA,MAAApB,cAAA,OAAA/C,UAAA,EAAAsE,GAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,UAAAtB,aAAA;QACA,YAAApD,QAAA;MACA;;MAEA;MACA,IAAA2E,UAAA,YAAAA,WAAAC,KAAA;QACA,OAAAA,KAAA,CAAAC,GAAA,WAAAC,IAAA;UACA,IAAAC,gBAAA,GAAAD,IAAA,CAAA3C,QAAA,GAAAwC,UAAA,CAAAG,IAAA,CAAA3C,QAAA;UACA,IAAA6C,aAAA,GAAAF,IAAA,CAAAvF,IAAA,IAAAuF,IAAA,CAAAvF,IAAA,CAAA0F,WAAA,GAAAC,QAAA,CAAAR,MAAA,CAAAtB,aAAA,CAAA6B,WAAA;UAEA,IAAAD,aAAA,IAAAD,gBAAA,CAAAnB,MAAA;YACA,WAAAuB,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAN,IAAA;cACA3C,QAAA,EAAA4C;YAAA;UAEA;UACA;QACA,GAAAM,MAAA,CAAAC,OAAA;MACA;MAEA,OAAAX,UAAA,MAAA3E,QAAA;IACA;EACA;EACAuF,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,gBAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACA;IACAF,gBAAA,WAAAA,iBAAA;MACA,IAAAG,SAAA,OAAAC,eAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA;MACA,IAAAC,KAAA,GAAAL,SAAA,CAAA1B,GAAA;MAEA,IAAA+B,KAAA;QACAC,YAAA,CAAAC,OAAA,mBAAAF,KAAA;QACAG,cAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAC,MAAA,8BAAAC,MAAA,CAAAP,KAAA;QACAQ,OAAA,CAAAC,GAAA,kBAAAT,KAAA;MACA;QACA,IAAAU,WAAA,GAAAT,YAAA,CAAAU,OAAA;QACA,IAAAD,WAAA;UACAP,cAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAC,MAAA,8BAAAC,MAAA,CAAAG,WAAA;QACA;MACA;IACA;IACAjB,gBAAA,WAAAA,iBAAA;MAAA,IAAAmB,MAAA;MAAA,WAAAC,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,yBAAA;YAAA;cAAAN,GAAA,GAAAG,QAAA,CAAAI,IAAA;cAAA,MACAP,GAAA,CAAAQ,IAAA,UAAAC,KAAA,CAAAC,OAAA,CAAAV,GAAA,CAAAnH,IAAA;gBAAAsH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA;cACAV,MAAA,CAAA5G,QAAA,GAAAiH,GAAA,CAAAnH,IAAA,CAAA+E,GAAA,WAAA+C,KAAA;gBAAA;kBACAjF,EAAA,WAAA4D,MAAA,CAAAqB,KAAA,CAAAC,QAAA;kBACAtI,IAAA,EAAAqI,KAAA,CAAAE,SAAA;kBACAC,IAAA;kBACA5F,QAAA,EAAAyF,KAAA,CAAAI,mBAAA,GAAAJ,KAAA,CAAAI,mBAAA,CAAAnD,GAAA,WAAAoD,QAAA;oBAAA;sBACAtF,EAAA,EAAAsF,QAAA,CAAAtF,EAAA;sBACApD,IAAA,EAAA0I,QAAA,CAAAC,YAAA;sBACA/H,YAAA,EAAA8H,QAAA,CAAA9H,YAAA;sBACAyH,KAAA,EAAAK,QAAA,CAAAL,KAAA;sBACAG,IAAA;oBACA;kBAAA;gBACA;cAAA;;cAEA;cACAnB,MAAA,CAAA3G,YAAA;cACAgH,GAAA,CAAAnH,IAAA,CAAAqI,OAAA,WAAAP,KAAA;gBACA,IAAAA,KAAA,CAAAI,mBAAA;kBAAA,IAAAI,mBAAA;kBACA,CAAAA,mBAAA,GAAAxB,MAAA,CAAA3G,YAAA,EAAAoI,IAAA,CAAAC,KAAA,CAAAF,mBAAA,MAAAG,mBAAA,CAAAnD,OAAA,EAAAwC,KAAA,CAAAI,mBAAA;gBACA;cACA;;cAEA;cAAA,KACApB,MAAA,CAAA3G,YAAA,CAAA2D,MAAA;gBAAAwD,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAV,MAAA,CAAA1G,UAAA,GAAAsI,MAAA,CAAA5B,MAAA,CAAA3G,YAAA,IAAA0C,EAAA;cACAiE,MAAA,CAAAzG,YAAA,GAAAyG,MAAA,CAAA3G,YAAA,IAAAE,YAAA;cACA;cACAyG,MAAA,CAAA6B,SAAA;gBACA;gBACA,IAAA7B,MAAA,CAAA8B,KAAA,CAAAC,QAAA,IAAA/B,MAAA,CAAA8B,KAAA,CAAAC,QAAA,CAAAC,aAAA;kBACAhC,MAAA,CAAA8B,KAAA,CAAAC,QAAA,CAAAC,aAAA,CAAAhC,MAAA,CAAA1G,UAAA;gBACA;cACA;;cAEA;cAAAkH,QAAA,CAAAE,IAAA;cAAA,OACAV,MAAA,CAAAiC,YAAA,CAAAjC,MAAA,CAAA1G,UAAA;YAAA;YAAA;cAAA,OAAAkH,QAAA,CAAA0B,IAAA;UAAA;QAAA,GAAA9B,OAAA;MAAA;IAGA;IAEA+B,mBAAA,WAAAA,oBAAAjJ,IAAA;MACA;MACA,IAAAA,IAAA,CAAAiI,IAAA;QACA,KAAAc,YAAA,CAAAL,MAAA,CAAA1I,IAAA,CAAA6C,EAAA;QACA,KAAAxC,YAAA,GAAAL,IAAA,CAAAK,YAAA;MACA;IACA;IAEA6I,YAAA,WAAAA,aAAA9G,KAAA;MACA,KAAAkB,aAAA,GAAAlB,KAAA;IACA;IACA+G,aAAA,WAAAA,cAAAC,IAAA;MACA,UAAA9F,aAAA,SAAA8F,IAAA;MACA,IAAAC,KAAA,OAAAC,MAAA,KAAA7C,MAAA,MAAAnD,aAAA;MACA,OAAA8F,IAAA,CAAAG,OAAA,CAAAF,KAAA;IACA;IACAN,YAAA,WAAAA,aAAAlG,EAAA;MAAA,IAAA2G,UAAA,GAAAC,SAAA;QAAAC,MAAA;MAAA,WAAA3C,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA0C,SAAA;QAAA,IAAAC,gBAAA,EAAAC,mBAAA,EAAAC,eAAA,EAAAzJ,YAAA,EAAA8G,GAAA,EAAA4C,aAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,WAAA;QAAA,WAAAlD,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAA+C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7C,IAAA,GAAA6C,SAAA,CAAA5C,IAAA;YAAA;cAAAoC,gBAAA,GAAAJ,UAAA,CAAA1F,MAAA,QAAA0F,UAAA,QAAAa,SAAA,GAAAb,UAAA;cAAAY,SAAA,CAAA7C,IAAA;cAEA;cACAmC,MAAA,CAAA1I,iBAAA;cACA;cACAsJ,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;cAEAf,MAAA,CAAAtJ,UAAA,GAAAyC,EAAA;cAAAuH,SAAA,CAAA5C,IAAA;cAAA,OACAkC,MAAA,CAAAgB,oBAAA,CAAAhB,MAAA,CAAAtJ,UAAA;YAAA;cAEA;cACAyJ,mBAAA,GAAAD,gBAAA,GAAAF,MAAA,CAAA5I,YAAA,SAEA;cACA4I,MAAA,CAAApJ,aAAA;;cAEA;cACAwJ,eAAA,GAAAJ,MAAA,CAAAvJ,YAAA,CAAAwK,IAAA,WAAAC,IAAA;gBAAA,OAAAlC,MAAA,CAAAkC,IAAA,CAAA/H,EAAA,MAAAA,EAAA;cAAA;cACAxC,YAAA,GAAAyJ,eAAA,GAAAA,eAAA,CAAAzJ,YAAA;cAAA+J,SAAA,CAAA5C,IAAA;cAAA,OAEA,IAAAqD,6BAAA;gBAAAxK,YAAA,EAAAA;cAAA;YAAA;cAAA8G,GAAA,GAAAiD,SAAA,CAAA1C,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAR,GAAA,CAAAnH,IAAA;gBAEA;gBACA0J,MAAA,CAAAnJ,IAAA,CAAAuK,iBAAA,GAAA3D,GAAA,CAAAnH,IAAA,CAAA8K,iBAAA;gBACApB,MAAA,CAAAnJ,IAAA,CAAAC,SAAA,GAAA2G,GAAA,CAAAnH,IAAA,CAAAQ,SAAA;gBACAkJ,MAAA,CAAAnJ,IAAA,CAAAE,QAAA,GAAA0G,GAAA,CAAAnH,IAAA,CAAAS,QAAA;gBACAiJ,MAAA,CAAAnJ,IAAA,CAAAG,QAAA,GAAAyG,GAAA,CAAAnH,IAAA,CAAA6B,oBAAA;gBACA6H,MAAA,CAAAnJ,IAAA,CAAAI,SAAA,GAAAwG,GAAA,CAAAnH,IAAA,CAAA8B,iBAAA;gBACA4H,MAAA,CAAAnJ,IAAA,CAAAK,mBAAA,GAAAuG,GAAA,CAAAnH,IAAA,CAAAY,mBAAA;;gBAEA;gBACA8I,MAAA,CAAAqB,gBAAA;;gBAEA;gBACArB,MAAA,CAAAsB,iBAAA;;gBAEA;gBACA,IAAA7D,GAAA,CAAAnH,IAAA,CAAAiL,WAAA;kBACAvB,MAAA,CAAApJ,aAAA;oBACA4K,OAAA,EAAA/D,GAAA,CAAAnH,IAAA,CAAAiL,WAAA,CAAAC,OAAA;oBACAC,SAAA,EAAAhE,GAAA,CAAAnH,IAAA,CAAAiL,WAAA,CAAAE,SAAA;oBACAC,cAAA,EAAAjE,GAAA,CAAAnH,IAAA,CAAAiL,WAAA,CAAAG,cAAA;oBACAC,MAAA,EAAAlE,GAAA,CAAAnH,IAAA,CAAAiL,WAAA,CAAAI;kBACA;gBACA;kBACA3B,MAAA,CAAApJ,aAAA;gBACA;;gBAEA;gBACA,IAAA6G,GAAA,CAAAnH,IAAA,CAAAsL,wBAAA,IAAA1D,KAAA,CAAAC,OAAA,CAAAV,GAAA,CAAAnH,IAAA,CAAAsL,wBAAA;kBACA5B,MAAA,CAAAhI,UAAA,GAAAyF,GAAA,CAAAnH,IAAA,CAAAsL,wBAAA,CAAAvG,GAAA,WAAAwG,UAAA;oBAAA;sBACA1I,EAAA,EAAA0I,UAAA,CAAA1I,EAAA;sBACA2I,GAAA,EAAAD,UAAA,CAAAE,OAAA;sBACAhM,IAAA,EAAA8L,UAAA,CAAA9L,IAAA;sBACAiM,OAAA,EAAAH,UAAA,CAAAI,QAAA;sBAAA;sBACAC,OAAA;sBACAC,WAAA;sBACAC,YAAA,EAAAP,UAAA,CAAA9L,IAAA;sBACA4L,MAAA,EAAAE,UAAA,CAAAF,MAAA;sBACAU,cAAA,EAAAR,UAAA,CAAAQ,cAAA;sBACAC,aAAA,EAAAT,UAAA,CAAAS;oBACA;kBAAA;;kBAEA;kBACAjC,aAAA,GAAA5C,GAAA,CAAAnH,IAAA,CAAAsL,wBAAA,CAAAX,IAAA,WAAAC,IAAA;oBAAA,OAAAA,IAAA,CAAAa,OAAA;kBAAA,IAEA;kBACA,IAAA1B,aAAA,IAAAA,aAAA,CAAAkC,mBAAA,IAAAlC,aAAA,CAAAkC,mBAAA,CAAA5H,kBAAA;oBACA2F,SAAA,GAAAD,aAAA,CAAAkC,mBAAA,CAAA5H,kBAAA;oBACAqF,MAAA,CAAA3H,gBAAA;sBACAH,MAAA,EAAAoI,SAAA,CAAApI,MAAA;sBACAE,iBAAA,EAAAkI,SAAA,CAAAlI,iBAAA;sBACAE,oBAAA,EAAAgI,SAAA,CAAAhI,oBAAA,GAAAgI,SAAA,CAAAhI,oBAAA,CAAA+C,GAAA,WAAApC,GAAA;wBAAA;0BACAoB,IAAA,EAAApB,GAAA,CAAAoB,IAAA;0BACAE,SAAA,EAAAtB,GAAA,CAAAsB,SAAA;0BACAD,SAAA,EAAArB,GAAA,CAAAqB,SAAA;0BACApB,OAAA,EAAA8G,MAAA,CAAAwC,iBAAA,CAAAvJ,GAAA,CAAAsB,SAAA;wBACA;sBAAA;oBACA;kBACA;oBACAyF,MAAA,CAAA3H,gBAAA;sBACAH,MAAA;sBACAE,iBAAA;sBACAE,oBAAA;oBACA;kBACA;;kBAEA;kBACA0H,MAAA,CAAAyC,8BAAA;;kBAEA;kBACA,IAAApC,aAAA,IAAAA,aAAA,CAAAkC,mBAAA,IAAAlC,aAAA,CAAAkC,mBAAA,CAAAG,WAAA;oBACA1C,MAAA,CAAA7I,eAAA,GAAA6I,MAAA,CAAA2C,cAAA,CAAAtC,aAAA,CAAAkC,mBAAA,CAAAG,WAAA;;oBAEA;oBACA,IAAAxC,gBAAA,IAAAC,mBAAA;sBACAI,YAAA,GAAAP,MAAA,CAAA4C,YAAA,CAAA5C,MAAA,CAAA7I,eAAA,EAAAgJ,mBAAA,CAAAhH,EAAA;sBACA,IAAAoH,YAAA;wBACAP,MAAA,CAAA5I,YAAA,GAAAmJ,YAAA;sBACA;wBACAP,MAAA,CAAA5I,YAAA,GAAA4I,MAAA,CAAA7I,eAAA,CAAAiD,MAAA,OAAA4F,MAAA,CAAA7I,eAAA;sBACA;oBACA;sBACA;sBACA6I,MAAA,CAAA5I,YAAA,GAAA4I,MAAA,CAAA7I,eAAA,CAAAiD,MAAA,OAAA4F,MAAA,CAAA7I,eAAA;oBACA;kBACA;oBACA;oBACA6I,MAAA,CAAA7I,eAAA;oBACA6I,MAAA,CAAA5I,YAAA;kBACA;gBACA;;gBAEA;gBACA,IAAAqG,GAAA,CAAAnH,IAAA,CAAAuM,iBAAA;kBACArC,WAAA;oBACA9F,gBAAA,EAAA+C,GAAA,CAAAnH,IAAA,CAAAuM,iBAAA,CAAAnI,gBAAA;oBACAC,kBAAA,EAAA8C,GAAA,CAAAnH,IAAA,CAAAuM,iBAAA,CAAAlI,kBAAA;sBACAzC,MAAA;sBACAE,iBAAA;sBACAE,oBAAA;oBACA;kBACA;kBACA0H,MAAA,CAAAnF,IAAA,CAAAmF,MAAA,CAAAzG,kBAAA,EAAAyG,MAAA,CAAAtJ,UAAA,EAAA8J,WAAA;gBACA;;gBAEA;gBACA,IAAA/C,GAAA,CAAAnH,IAAA,CAAAwM,qBAAA;kBACA9C,MAAA,CAAAnF,IAAA,CAAAmF,MAAA,CAAAxG,oBAAA,EAAAwG,MAAA,CAAAtJ,UAAA,EAAA+G,GAAA,CAAAnH,IAAA,CAAAwM,qBAAA;gBACA;;gBAEA;gBACA,IAAArF,GAAA,CAAAnH,IAAA,CAAAyM,YAAA;kBACA/C,MAAA,CAAAnF,IAAA,CAAAmF,MAAA,CAAAvG,cAAA,EAAAuG,MAAA,CAAAtJ,UAAA,EAAA+G,GAAA,CAAAnH,IAAA,CAAAyM,YAAA;gBACA;;gBAEA;cACA;cAAArC,SAAA,CAAA5C,IAAA;cAAA;YAAA;cAAA4C,SAAA,CAAA7C,IAAA;cAAA6C,SAAA,CAAAsC,EAAA,GAAAtC,SAAA;cAEA1D,OAAA,CAAAiG,KAAA,YAAAvC,SAAA,CAAAsC,EAAA;cACAhD,MAAA,CAAAkD,QAAA,CAAAD,KAAA;YAAA;cAAAvC,SAAA,CAAA7C,IAAA;cAEA;cACAmC,MAAA,CAAA1I,iBAAA;cACA;cACAsJ,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;cAAA,OAAAL,SAAA,CAAAyC,MAAA;YAAA;YAAA;cAAA,OAAAzC,SAAA,CAAApB,IAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IAEA;IACAmD,kBAAA,WAAAA,mBAAAC,IAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAzM,IAAA,CAAAyB,oBAAA,CAAAuG,IAAA;QAAAxE,IAAA;QAAAkJ,KAAA;MAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MACA,SAAA5M,IAAA,CAAAyB,oBAAA,CAAA8B,MAAA;QACA,KAAAvD,IAAA,CAAAyB,oBAAA,CAAAoL,MAAA,CAAAD,KAAA;MACA;IACA;IACAE,wBAAA,WAAAA,yBAAAN,IAAA,EAAA9E,IAAA,EAAAuD,GAAA;MAAA,IAAA8B,MAAA;MAAA,WAAAvG,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAsG,SAAA;QAAA,IAAAC,QAAA,EAAArG,GAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAqG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnG,IAAA,GAAAmG,SAAA,CAAAlG,IAAA;YAAA;cAAA,IACAuF,IAAA,CAAA9E,IAAA,CAAA0F,UAAA;gBAAAD,SAAA,CAAAlG,IAAA;gBAAA;cAAA;cACA8F,MAAA,CAAAV,QAAA,CAAAD,KAAA;cAAA,OAAAe,SAAA,CAAAE,MAAA,WACA;YAAA;cAAAF,SAAA,CAAAnG,IAAA;cAIA+F,MAAA,CAAAO,MAAA,CAAA9M,OAAA;cACAyM,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,SAAAhB,IAAA;cACAS,QAAA,CAAAO,MAAA,iBAAAT,MAAA,CAAAjN,YAAA;cAAAqN,SAAA,CAAAlG,IAAA;cAAA,OAEA,IAAAwG,0BAAA,EAAAR,QAAA;YAAA;cAAArG,GAAA,GAAAuG,SAAA,CAAAhG,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAR,GAAA,CAAAnH,IAAA;gBACA,IAAAiI,IAAA,IAAAuD,GAAA;kBACA;kBACA8B,MAAA,CAAArF,IAAA,EAAAuD,GAAA,IAAArE,GAAA,CAAAnH,IAAA,CAAAiO,OAAA;gBACA;kBACA;kBACAX,MAAA,CAAA/M,IAAA,CAAAG,QAAA,GAAAyG,GAAA,CAAAnH,IAAA,CAAAiO,OAAA;gBACA;gBACAX,MAAA,CAAAV,QAAA,CAAAsB,OAAA;cACA;gBACAZ,MAAA,CAAAV,QAAA,CAAAD,KAAA,CAAAxF,GAAA,CAAAgH,GAAA;cACA;cAAAT,SAAA,CAAAlG,IAAA;cAAA;YAAA;cAAAkG,SAAA,CAAAnG,IAAA;cAAAmG,SAAA,CAAAhB,EAAA,GAAAgB,SAAA;cAEAJ,MAAA,CAAAV,QAAA,CAAAD,KAAA;YAAA;cAAAe,SAAA,CAAAnG,IAAA;cAEA+F,MAAA,CAAAO,MAAA,CAAAO,YAAA;cAAA,OAAAV,SAAA,CAAAb,MAAA;YAAA;cAAA,OAAAa,SAAA,CAAAE,MAAA,WAEA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAA1E,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IACA;IACAc,0BAAA,WAAAA,2BAAAtB,IAAA,EAAA9E,IAAA,EAAAuD,GAAA;MAAA,IAAA8C,MAAA;MAAA,WAAAvH,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAsH,SAAA;QAAA,IAAAf,QAAA,EAAArG,GAAA,EAAAqH,QAAA,EAAAC,SAAA,EAAAC,IAAA,EAAAC,SAAA;QAAA,WAAA3H,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAwH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtH,IAAA,GAAAsH,SAAA,CAAArH,IAAA;YAAA;cAAA,MAEA,CAAAS,IAAA,KAAAuD,GAAA;gBAAAqD,SAAA,CAAArH,IAAA;gBAAA;cAAA;cAAAqH,SAAA,CAAAtH,IAAA;cAEA+G,MAAA,CAAAT,MAAA,CAAA9M,OAAA;cACAyM,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,SAAAhB,IAAA;cACAS,QAAA,CAAAO,MAAA,iBAAAO,MAAA,CAAAjO,YAAA;cAAAwO,SAAA,CAAArH,IAAA;cAAA,OAEA,IAAAwG,0BAAA,EAAAR,QAAA;YAAA;cAAArG,GAAA,GAAA0H,SAAA,CAAAnH,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAR,GAAA,CAAAnH,IAAA;gBACA;gBACAsO,MAAA,CAAA/N,IAAA,CAAAI,SAAA,GAAAwG,GAAA,CAAAnH,IAAA,CAAAiO,OAAA;;gBAEA;gBACAO,QAAA,GAAArH,GAAA,CAAAnH,IAAA,CAAAiO,OAAA,CAAAa,KAAA,MAAAC,GAAA;gBACAT,MAAA,CAAAxL,UAAA;kBACArD,IAAA,EAAA+O,QAAA;kBACAQ,GAAA,EAAA7H,GAAA,CAAAnH,IAAA,CAAAiO,OAAA;kBACAgB,GAAA,EAAAC,IAAA,CAAAC,GAAA;gBACA;;gBAEA;gBACA,IAAApC,IAAA,CAAA9E,IAAA,oBAAAd,GAAA,CAAAnH,IAAA,CAAAoP,MAAA;kBACAd,MAAA,CAAA/N,IAAA,CAAAG,QAAA,GAAAyG,GAAA,CAAAnH,IAAA,CAAAoP,MAAA;kBACAd,MAAA,CAAA1B,QAAA,CAAAsB,OAAA;gBACA;kBACAI,MAAA,CAAA1B,QAAA,CAAAsB,OAAA;gBACA;cACA;gBACAI,MAAA,CAAA1B,QAAA,CAAAD,KAAA,CAAAxF,GAAA,CAAAgH,GAAA;cACA;cAAAU,SAAA,CAAArH,IAAA;cAAA;YAAA;cAAAqH,SAAA,CAAAtH,IAAA;cAAAsH,SAAA,CAAAnC,EAAA,GAAAmC,SAAA;cAEAP,MAAA,CAAA1B,QAAA,CAAAD,KAAA;YAAA;cAAAkC,SAAA,CAAAtH,IAAA;cAEA+G,MAAA,CAAAT,MAAA,CAAAO,YAAA;cAAA,OAAAS,SAAA,CAAAhC,MAAA;YAAA;cAAA,OAAAgC,SAAA,CAAAjB,MAAA,WAEA;YAAA;cAAA,MAIA,CAAAb,IAAA,CAAA9E,IAAA,CAAA0F,UAAA,eAAAZ,IAAA,CAAAtN,IAAA,CAAA4P,QAAA;gBAAAR,SAAA,CAAArH,IAAA;gBAAA;cAAA;cACA8G,MAAA,CAAA1B,QAAA,CAAAD,KAAA;cAAA,OAAAkC,SAAA,CAAAjB,MAAA,WACA;YAAA;cAAAiB,SAAA,CAAAtH,IAAA;cAIA+G,MAAA,CAAAT,MAAA,CAAA9M,OAAA;cACAyM,SAAA,OAAAM,QAAA;cACAN,SAAA,CAAAO,MAAA,SAAAhB,IAAA;cACAS,SAAA,CAAAO,MAAA,iBAAAO,MAAA,CAAAjO,YAAA;cAAAwO,SAAA,CAAArH,IAAA;cAAA,OAEA,IAAAwG,0BAAA,EAAAR,SAAA;YAAA;cAAArG,IAAA,GAAA0H,SAAA,CAAAnH,IAAA;cACA,IAAAP,IAAA,CAAAQ,IAAA,UAAAR,IAAA,CAAAnH,IAAA;gBACA,IAAAiI,IAAA,IAAAuD,GAAA;kBACA;kBACA8C,MAAA,CAAArG,IAAA,EAAAuD,GAAA,IAAArE,IAAA,CAAAnH,IAAA,CAAAiO,OAAA;;kBAEA;kBACAO,SAAA,GAAArH,IAAA,CAAAnH,IAAA,CAAAiO,OAAA,CAAAa,KAAA,MAAAC,GAAA;kBACA,IAAA9G,IAAA,yBAAAuD,GAAA;oBACA8C,MAAA,CAAAgB,sBAAA;sBACA7P,IAAA,EAAA+O,SAAA;sBACAQ,GAAA,EAAA7H,IAAA,CAAAnH,IAAA,CAAAiO,OAAA;sBACAgB,GAAA,EAAAC,IAAA,CAAAC,GAAA;oBACA;kBACA,WAAAlH,IAAA,2BAAAuD,GAAA;oBACA8C,MAAA,CAAAvL,wBAAA;sBACAtD,IAAA,EAAA+O,SAAA;sBACAQ,GAAA,EAAA7H,IAAA,CAAAnH,IAAA,CAAAiO,OAAA;sBACAgB,GAAA,EAAAC,IAAA,CAAAC,GAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAlH,IAAA,yBAAAuD,GAAA,4BAAArE,IAAA,CAAAnH,IAAA,CAAAoP,MAAA;oBACAd,MAAA,CAAA3M,cAAA,CAAAE,oBAAA,GAAAsF,IAAA,CAAAnH,IAAA,CAAAoP,MAAA;oBACAd,MAAA,CAAA1B,QAAA,CAAAsB,OAAA;kBACA;oBACAI,MAAA,CAAA1B,QAAA,CAAAsB,OAAA;kBACA;gBACA;cACA;gBACAI,MAAA,CAAA1B,QAAA,CAAAD,KAAA,CAAAxF,IAAA,CAAAgH,GAAA;cACA;cAAAU,SAAA,CAAArH,IAAA;cAAA;YAAA;cAAAqH,SAAA,CAAAtH,IAAA;cAAAsH,SAAA,CAAAU,EAAA,GAAAV,SAAA;cAEAP,MAAA,CAAA1B,QAAA,CAAAD,KAAA;YAAA;cAAAkC,SAAA,CAAAtH,IAAA;cAEA+G,MAAA,CAAAT,MAAA,CAAAO,YAAA;cAAA,OAAAS,SAAA,CAAAhC,MAAA;YAAA;cAAA,OAAAgC,SAAA,CAAAjB,MAAA,WAEA;YAAA;YAAA;cAAA,OAAAiB,SAAA,CAAA7F,IAAA;UAAA;QAAA,GAAAuF,QAAA;MAAA;IACA;IACAiB,4BAAA,WAAAA,6BAAAzC,IAAA;MACA,KAAAvL,aAAA;MACA,KAAAC,YAAA;MACA,YAAAqL,kBAAA,CAAAC,IAAA;IACA;IACA;IACA0C,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,QAAA,GAAAjG,SAAA,CAAA3F,MAAA,QAAA2F,SAAA,QAAAY,SAAA,GAAAZ,SAAA;MACA,IAAAkG,OAAA;QACA9M,EAAA,EAAAqM,IAAA,CAAAC,GAAA;QAAA;QACA1P,IAAA;QACAwI,IAAA;QAAA;QACAyD,OAAA;QACArJ,QAAA;QACAqN,QAAA,EAAAA;MACA;MACA,IAAAA,QAAA;QACA,IAAAE,UAAA,QAAAtD,YAAA,MAAAzL,eAAA,EAAA6O,QAAA;QACA,IAAAE,UAAA;UACAA,UAAA,CAAAvN,QAAA,CAAAkG,IAAA,CAAAoH,OAAA;QACA;MACA;QACA,KAAA9O,eAAA,CAAA0H,IAAA,CAAAoH,OAAA;MACA;MACA,OAAAA,OAAA,CAAA9M,EAAA;IACA;IACA;IACAgN,qBAAA,WAAAA,sBAAAC,MAAA;MACA,KAAAjP,eAAA,QAAAA,eAAA,CAAA0E,MAAA,WAAAP,IAAA;QAAA,OAAAA,IAAA,CAAAnC,EAAA,KAAAiN,MAAA;MAAA;IACA;IACA;IACAxD,YAAA,WAAAA,aAAAxH,KAAA,EAAAjC,EAAA;MAAA,IAAAkN,SAAA,OAAAC,2BAAA,CAAA1K,OAAA,EACAR,KAAA;QAAAmL,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAApL,IAAA,GAAAiL,KAAA,CAAA7N,KAAA;UACA,IAAA4C,IAAA,CAAAnC,EAAA,KAAAA,EAAA;YACA,OAAAmC,IAAA;UACA;UACA,IAAAA,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA;YACA,IAAAuM,KAAA,QAAA/D,YAAA,CAAAtH,IAAA,CAAA3C,QAAA,EAAAQ,EAAA;YACA,IAAAwN,KAAA;cACA,OAAAA,KAAA;YACA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;MAAA;QAAAP,SAAA,CAAAS,CAAA;MAAA;MACA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAX,MAAA;MACA,IAAA9K,IAAA,QAAAsH,YAAA,MAAAzL,eAAA,EAAAiP,MAAA;MACA,IAAA9K,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA0L,UAAA,GAAA1L,IAAA,CAAA0L,UAAA;QACA1L,IAAA,CAAA0L,UAAA,CAAAnI,IAAA;UAAAoI,KAAA;UAAAC,QAAA;UAAAC,QAAA;QAAA;MACA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAAhB,MAAA,EAAAiB,GAAA;MACA,IAAA/L,IAAA,QAAAsH,YAAA,MAAAzL,eAAA,EAAAiP,MAAA;MACA,IAAA9K,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA0L,UAAA,GAAA1L,IAAA,CAAA0L,UAAA;QACA1L,IAAA,CAAA0L,UAAA,CAAAtD,MAAA,CAAA2D,GAAA;MACA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAlB,MAAA,EAAAiB,GAAA;MACA,IAAA/L,IAAA,QAAAsH,YAAA,MAAAzL,eAAA,EAAAiP,MAAA;MACA,IAAA9K,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA0L,UAAA,GAAA1L,IAAA,CAAA0L,UAAA;QACA1L,IAAA,CAAA0L,UAAA,CAAAK,GAAA,EAAAH,QAAA,CAAArI,IAAA;MACA;IACA;IACA;IACA0I,sBAAA,WAAAA,uBAAAnB,MAAA,EAAAiB,GAAA,EAAAG,IAAA;MACA,IAAAlM,IAAA,QAAAsH,YAAA,MAAAzL,eAAA,EAAAiP,MAAA;MACA,IAAA9K,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA0L,UAAA,GAAA1L,IAAA,CAAA0L,UAAA;QACA1L,IAAA,CAAA0L,UAAA,CAAAK,GAAA,EAAAH,QAAA,CAAAxD,MAAA,CAAA8D,IAAA;MACA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAArB,MAAA;MACA,IAAA9K,IAAA,QAAAsH,YAAA,MAAAzL,eAAA,EAAAiP,MAAA;MACA,IAAA9K,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA4L,QAAA,GAAA5L,IAAA,CAAA4L,QAAA;QACA5L,IAAA,CAAA4L,QAAA,CAAArI,IAAA;MACA;IACA;IACA;IACA6I,sBAAA,WAAAA,uBAAAtB,MAAA,EAAAoB,IAAA;MACA,IAAAlM,IAAA,QAAAsH,YAAA,MAAAzL,eAAA,EAAAiP,MAAA;MACA,IAAA9K,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA4L,QAAA,GAAA5L,IAAA,CAAA4L,QAAA;QACA5L,IAAA,CAAA4L,QAAA,CAAAxD,MAAA,CAAA8D,IAAA;MACA;IACA;IACA;IACAG,0BAAA,WAAAA,2BAAAtE,IAAA,EAAA9E,IAAA,EAAAuD,GAAA;MAAA,IAAA8F,MAAA;MACA,KAAAvE,IAAA,CAAA9E,IAAA,CAAA0F,UAAA;QACA,KAAAf,QAAA,CAAAD,KAAA;QACA;MACA;MACA,IAAA4E,MAAA,OAAAC,UAAA;MACAD,MAAA,CAAAE,MAAA,aAAAlB,CAAA;QACAe,MAAA,CAAAhF,YAAA,CAAAgF,MAAA,CAAAzQ,eAAA,EAAAoH,IAAA,EAAAuD,GAAA,IAAA+E,CAAA,CAAAmB,MAAA,CAAAC,MAAA;MACA;MACAJ,MAAA,CAAAK,aAAA,CAAA7E,IAAA;MACA;IACA;IACA;IACA8E,2BAAA,WAAAA,4BAAA9E,IAAA,EAAA9E,IAAA,EAAAuD,GAAA;MACA;MACA,KAAAc,YAAA,MAAAzL,eAAA,EAAAoH,IAAA,EAAAuD,GAAA,IAAAuB,IAAA,CAAAtN,IAAA;MACA;IACA;IACAqS,oBAAA,WAAAA,qBAAA9M,IAAA;MACA,KAAAlE,YAAA,GAAAkE,IAAA;IACA;IACA;IACAqH,cAAA,WAAAA,eAAA0F,OAAA;MAAA,IAAAC,MAAA;MAAA,IAAAC,MAAA,GAAAxI,SAAA,CAAA3F,MAAA,QAAA2F,SAAA,QAAAY,SAAA,GAAAZ,SAAA;MACA,KAAAsI,OAAA;MACA,IAAAG,GAAA,GAAAtK,KAAA,CAAAC,OAAA,CAAAkK,OAAA,IAAAA,OAAA,IAAAA,OAAA;MACA,OAAAG,GAAA,CAAAnN,GAAA,WAAAC,IAAA;QACA,IAAAmN,OAAA;UACAtP,EAAA,EAAAmC,IAAA,CAAApC,OAAA;UACAwP,WAAA,EAAApN,IAAA,CAAAoN,WAAA;UACA3S,IAAA,EAAAuF,IAAA,CAAAhB,SAAA;UACA2D,IAAA,EAAA3C,IAAA,CAAAf,SAAA;UACAoO,CAAA,EAAArN,IAAA,CAAAqN,CAAA;UACAC,CAAA,EAAAtN,IAAA,CAAAsN,CAAA;UACArK,IAAA,EAAAjD,IAAA,CAAAiD,IAAA;UACArG,MAAA,EAAAoD,IAAA,CAAApD,MAAA,YAAAoD,IAAA,CAAApD,MAAA;UACA2Q,QAAA,EAAAvN,IAAA,CAAAuN,QAAA,aAAAvN,IAAA,CAAAuN,QAAA,KAAAlI,SAAA,GAAArF,IAAA,CAAAuN,QAAA;UACAC,eAAA,EAAAxN,IAAA,CAAAwN,eAAA,aAAAxN,IAAA,CAAAwN,eAAA,KAAAnI,SAAA,GAAArF,IAAA,CAAAwN,eAAA;UACAC,kBAAA,EAAAzN,IAAA,CAAAyN,kBAAA,aAAAzN,IAAA,CAAAyN,kBAAA,KAAApI,SAAA,GAAArF,IAAA,CAAAyN,kBAAA;UACAC,gBAAA,EAAA1N,IAAA,CAAA0N,gBAAA;YACA7P,EAAA,EAAAmC,IAAA,CAAA0N,gBAAA,CAAA7P,EAAA;YACAoF,IAAA,EAAAjD,IAAA,CAAA0N,gBAAA,CAAAzK,IAAA;YACA0K,UAAA,EAAA3N,IAAA,CAAA0N,gBAAA,CAAAC,UAAA;YACA/Q,MAAA,EAAAoD,IAAA,CAAA0N,gBAAA,CAAA9Q,MAAA;YACAC,oBAAA,EAAAmD,IAAA,CAAA0N,gBAAA,CAAA7Q,oBAAA;YACAC,iBAAA,EAAAkD,IAAA,CAAA0N,gBAAA,CAAA5Q,iBAAA;UACA;YAAAe,EAAA;YAAAoF,IAAA;YAAA0K,UAAA;YAAA/Q,MAAA;YAAAC,oBAAA;YAAAC,iBAAA;UAAA;UACA8Q,SAAA,EAAA5N,IAAA,CAAA6N,gBAAA;YACApT,IAAA,EAAAuF,IAAA,CAAA6N,gBAAA,CAAApT,IAAA;YACAqT,mBAAA,EAAA9N,IAAA,CAAA6N,gBAAA,CAAAC,mBAAA;YACAC,mBAAA,EAAA/N,IAAA,CAAA6N,gBAAA,CAAAG,cAAA,GACAhO,IAAA,CAAA6N,gBAAA,CAAAG,cAAA,CAAAjO,GAAA,WAAAkO,CAAA;cAAA;gBACApQ,EAAA,EAAAoQ,CAAA,CAAApQ,EAAA;gBACAV,KAAA,EAAA8Q,CAAA,CAAAC,GAAA;gBACAC,WAAA,EAAAF,CAAA,CAAAG,gBAAA,GAAAH,CAAA,CAAAG,gBAAA,CAAArO,GAAA,WAAAsO,GAAA;kBAAA;oBACAxQ,EAAA,EAAAwQ,GAAA,CAAAxQ,EAAA;oBACAyQ,MAAA,EAAAD,GAAA,CAAAC,MAAA;oBACAjB,CAAA,EAAAgB,GAAA,CAAAE,MAAA;oBACAjB,CAAA,EAAAe,GAAA,CAAAG,MAAA;oBACAC,IAAA,EAAAJ,GAAA,CAAAI,IAAA;oBACAC,IAAA,EAAAL,GAAA,CAAAK,IAAA;oBACAC,MAAA,EAAAN,GAAA,CAAAM,MAAA;oBACA/Q,OAAA,EAAAoP,MAAA,CAAA9F,iBAAA,CAAAmH,GAAA,CAAAO,aAAA;oBACA3P,SAAA,EAAAoP,GAAA,CAAAO,aAAA;kBACA;gBAAA;kBAAA/Q,EAAA;kBAAAyQ,MAAA;kBAAAjB,CAAA;kBAAAC,CAAA;kBAAAmB,IAAA;kBAAAC,IAAA;kBAAA9Q,OAAA;kBAAAqB,SAAA;gBAAA;gBACAwP,IAAA,EAAAR,CAAA,CAAAQ,IAAA;gBACAC,IAAA,EAAAT,CAAA,CAAAS,IAAA;gBACAG,KAAA,EAAAZ,CAAA,CAAApR,oBAAA;gBACA4B,MAAA,EAAAwP,CAAA,CAAAnR,iBAAA;gBACAF,MAAA,EAAAqR,CAAA,CAAArR,MAAA;gBACAqG,IAAA,EAAAgL,CAAA,CAAAhL,IAAA;gBACA0K,UAAA,EAAAM,CAAA,CAAAN,UAAA;cACA;YAAA;YACAjC,UAAA,EAAA1L,IAAA,CAAA6N,gBAAA,CAAAiB,aAAA,GACAlM,KAAA,CAAAC,OAAA,CAAA7C,IAAA,CAAA6N,gBAAA,CAAAiB,aAAA,IAAA9O,IAAA,CAAA6N,gBAAA,CAAAiB,aAAA,CAAA/O,GAAA,WAAAgP,CAAA;cAAA;gBACAC,WAAA,EAAAD,CAAA,CAAAC,WAAA;gBACArD,KAAA,EAAAoD,CAAA,CAAAE,QAAA;gBACArD,QAAA,EAAAmD,CAAA,CAAAG,OAAA;gBACArD,QAAA,EAAAkD,CAAA,CAAAI,WAAA;cACA;YAAA;UACA;YAAA1U,IAAA;YAAAqT,mBAAA;YAAAC,mBAAA;YAAArC,UAAA;UAAA;UACA0D,QAAA,EAAApP,IAAA,CAAAqP,SAAA;YACA5U,IAAA,EAAAuF,IAAA,CAAAqP,SAAA,CAAA5U,IAAA;YACAqT,mBAAA,EAAA9N,IAAA,CAAAqP,SAAA,CAAAvB,mBAAA;YACAC,mBAAA,EAAA/N,IAAA,CAAAqP,SAAA,CAAArB,cAAA,GACAhO,IAAA,CAAAqP,SAAA,CAAArB,cAAA,CAAAjO,GAAA,WAAAkO,CAAA;cAAA;gBACApQ,EAAA,EAAAoQ,CAAA,CAAApQ,EAAA;gBACAqQ,GAAA,EAAAD,CAAA,CAAAC,GAAA;gBACAtR,MAAA,EAAAqR,CAAA,CAAArR,MAAA;gBACAqG,IAAA,EAAAgL,CAAA,CAAAhL,IAAA;gBACA0K,UAAA,EAAAM,CAAA,CAAAN,UAAA;gBACA9Q,oBAAA,EAAAoR,CAAA,CAAApR,oBAAA;gBACAC,iBAAA,EAAAmR,CAAA,CAAAnR,iBAAA;gBACAqR,WAAA,EAAAF,CAAA,CAAAG,gBAAA,GAAAH,CAAA,CAAAG,gBAAA,CAAArO,GAAA,WAAAsO,GAAA;kBAAA;oBACAxQ,EAAA,EAAAwQ,GAAA,CAAAxQ,EAAA;oBACAyQ,MAAA,EAAAD,GAAA,CAAAC,MAAA;oBACAjB,CAAA,EAAAgB,GAAA,CAAAE,MAAA;oBACAjB,CAAA,EAAAe,GAAA,CAAAG,MAAA;oBACAC,IAAA,EAAAJ,GAAA,CAAAI,IAAA;oBACAC,IAAA,EAAAL,GAAA,CAAAK,IAAA;oBACAC,MAAA,EAAAN,GAAA,CAAAM,MAAA;oBACA/Q,OAAA,EAAAoP,MAAA,CAAA9F,iBAAA,CAAAmH,GAAA,CAAAO,aAAA;oBACA3P,SAAA,EAAAoP,GAAA,CAAAO,aAAA;kBACA;gBAAA;kBAAA/Q,EAAA;kBAAAyQ,MAAA;kBAAAjB,CAAA;kBAAAC,CAAA;kBAAAmB,IAAA;kBAAAC,IAAA;kBAAA9Q,OAAA;kBAAAqB,SAAA;gBAAA;cACA;YAAA;YACAyM,UAAA,EAAA1L,IAAA,CAAAqP,SAAA,CAAAP,aAAA,GACAlM,KAAA,CAAAC,OAAA,CAAA7C,IAAA,CAAAqP,SAAA,CAAAP,aAAA,IAAA9O,IAAA,CAAAqP,SAAA,CAAAP,aAAA,CAAA/O,GAAA,WAAAgP,CAAA;cAAA;gBACAC,WAAA,EAAAD,CAAA,CAAAC,WAAA;gBACArD,KAAA,EAAAoD,CAAA,CAAAE,QAAA;gBACArD,QAAA,EAAAmD,CAAA,CAAAG,OAAA;gBACArD,QAAA,EAAAkD,CAAA,CAAAI,WAAA;cACA;YAAA;UACA;YAAA1U,IAAA;YAAAqT,mBAAA;YAAAC,mBAAA;YAAArC,UAAA;UAAA;UACA4D,YAAA,EAAAtP,IAAA,CAAAuP,oBAAA;YACAP,WAAA,EAAAhP,IAAA,CAAAuP,oBAAA,CAAAP,WAAA;YACApS,MAAA,EAAAoD,IAAA,CAAAuP,oBAAA,CAAA3S,MAAA;YACA+O,KAAA,EAAA3L,IAAA,CAAAuP,oBAAA,CAAAN,QAAA;YACArD,QAAA,EAAA5L,IAAA,CAAAuP,oBAAA,CAAAL,OAAA;UACA;YAAAF,WAAA;YAAApS,MAAA;YAAA+O,KAAA;YAAAC,QAAA;UAAA;UACAvO,QAAA;UACA4P,MAAA,EAAAA;QACA;QACA;QACAE,OAAA,CAAA9P,QAAA,GAAA2C,IAAA,CAAA3C,QAAA,GAAA2P,MAAA,CAAA3F,cAAA,CAAArH,IAAA,CAAA3C,QAAA,EAAA8P,OAAA;QACA,OAAAA,OAAA;MACA;IACA;IACAqC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAA1N,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAyN,SAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAhL,eAAA,EAAAzJ,YAAA,EAAA0U,qBAAA,EAAAC,uBAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAjL,YAAA,EAAAkL,OAAA,EAAAC,QAAA;QAAA,WAAApO,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAiO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/N,IAAA,GAAA+N,SAAA,CAAA9N,IAAA;YAAA;cAAA8N,SAAA,CAAA/N,IAAA;cAEAkN,MAAA,CAAA9Q,UAAA;cACA;cACAmG,eAAA,GAAA2K,MAAA,CAAAtU,YAAA,CAAAwK,IAAA,WAAAC,IAAA;gBAAA,OAAAlC,MAAA,CAAAkC,IAAA,CAAA/H,EAAA,MAAA4R,MAAA,CAAArU,UAAA;cAAA;cACAC,YAAA,GAAAyJ,eAAA,GAAAA,eAAA,CAAAzJ,YAAA,SAEA;cACA0U,qBAAA,GAAAN,MAAA,CAAA3T,YAAA,GAAA2T,MAAA,CAAA3T,YAAA,CAAA+B,EAAA;cACAmS,uBAAA,GAAAP,MAAA,CAAA3T,YAAA,GAAA2T,MAAA,CAAA3T,YAAA,CAAArB,IAAA,SAEA;cACAwV,UAAA;gBACAM,UAAA,EAAAd,MAAA,CAAArU,UAAA;gBACAC,YAAA,EAAAA,YAAA;gBAAA;gBACAyK,iBAAA,EAAA2J,MAAA,CAAAlU,IAAA,CAAAuK,iBAAA;gBACAtK,SAAA,EAAAiU,MAAA,CAAAlU,IAAA,CAAAC,SAAA;gBACAC,QAAA,EAAAgU,MAAA,CAAAlU,IAAA,CAAAE,QAAA;gBACAyK,OAAA,EAAAuJ,MAAA,CAAAnU,aAAA,GAAAmU,MAAA,CAAAnU,aAAA,CAAA4K,OAAA;gBACArJ,oBAAA,EAAA4S,MAAA,CAAAlU,IAAA,CAAAG,QAAA;gBACAoB,iBAAA,EAAA2S,MAAA,CAAAlU,IAAA,CAAAI,SAAA;gBACAC,mBAAA,EAAA6T,MAAA,CAAAlU,IAAA,CAAAK,mBAAA;gBACA4U,qBAAA;kBACApR,gBAAA,GAAAuQ,qBAAA,GAAAF,MAAA,CAAAxR,kBAAA,CAAAwR,MAAA,CAAArU,UAAA,eAAAuU,qBAAA,eAAAA,qBAAA,CAAAvQ,gBAAA,IAAAwD,KAAA,CAAAC,OAAA,CAAA4M,MAAA,CAAAxR,kBAAA,CAAAwR,MAAA,CAAArU,UAAA,EAAAgE,gBAAA,IACAqQ,MAAA,CAAAxR,kBAAA,CAAAwR,MAAA,CAAArU,UAAA,EAAAgE,gBAAA,CAAAW,GAAA,WAAA0Q,IAAA;oBAAA;sBACA5S,EAAA,EAAA4S,IAAA,CAAA5S,EAAA;sBACAoF,IAAA;sBACAiL,GAAA,EAAAuC,IAAA,CAAAvC,GAAA;sBACAK,MAAA,EAAAkC,IAAA,CAAAlC,MAAA;sBACAC,MAAA,EAAAiC,IAAA,CAAAjC,MAAA;sBACAC,IAAA,EAAAgC,IAAA,CAAAhC,IAAA;sBACAC,IAAA,EAAA+B,IAAA,CAAA/B,IAAA;sBACA7R,oBAAA,EAAA4T,IAAA,CAAA5T,oBAAA;sBACAC,iBAAA,EAAA2T,IAAA,CAAA3T,iBAAA;sBACAF,MAAA;sBACA+Q,UAAA;oBACA;kBAAA;kBACAtO,kBAAA;oBACAzC,MAAA,IAAAgT,sBAAA,GAAAH,MAAA,CAAAxR,kBAAA,CAAAwR,MAAA,CAAArU,UAAA,eAAAwU,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAAvQ,kBAAA,cAAAuQ,sBAAA,uBAAAA,sBAAA,CAAAhT,MAAA;oBACAE,iBAAA,IAAA+S,sBAAA,GAAAJ,MAAA,CAAAxR,kBAAA,CAAAwR,MAAA,CAAArU,UAAA,eAAAyU,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAAxQ,kBAAA,cAAAwQ,sBAAA,uBAAAA,sBAAA,CAAA/S,iBAAA;oBACAE,oBAAA,GAAA8S,sBAAA,GAAAL,MAAA,CAAAxR,kBAAA,CAAAwR,MAAA,CAAArU,UAAA,eAAA0U,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAAzQ,kBAAA,cAAAyQ,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAA9S,oBAAA,cAAA8S,sBAAA,eAAAA,sBAAA,CAAAhR,MAAA,GACA2Q,MAAA,CAAAxR,kBAAA,CAAAwR,MAAA,CAAArU,UAAA,EAAAiE,kBAAA,CAAArC,oBAAA,CAAA+C,GAAA,WAAApC,GAAA;sBAAA;wBACAoB,IAAA,EAAApB,GAAA,CAAAoB,IAAA;wBACAE,SAAA,EAAAtB,GAAA,CAAAsB,SAAA;wBACAD,SAAA,EAAArB,GAAA,CAAAqB,SAAA;sBACA;oBAAA;kBACA;gBACA;gBACAsH,wBAAA,EAAAmJ,MAAA,CAAA/S,UAAA,CAAAqD,GAAA,WAAA2Q,GAAA;kBACA,IAAAC,UAAA;oBACA9S,EAAA,EAAA6S,GAAA,CAAA7S,EAAA;oBACA0S,UAAA,EAAAd,MAAA,CAAArU,UAAA;oBACAX,IAAA,EAAAiW,GAAA,CAAAjW,IAAA;oBACAgM,OAAA,EAAAiK,GAAA,CAAAlK,GAAA;oBACAG,QAAA,EAAA+J,GAAA,CAAAhK,OAAA;oBACAL,MAAA,EAAAqK,GAAA,CAAArK,MAAA,IAAAqK,GAAA,CAAAjW,IAAA;oBACAuM,aAAA;kBACA;kBAEA,IAAA0J,GAAA,CAAAlK,GAAA;oBACA,IAAAoK,kBAAA,GAAAnB,MAAA,CAAAoB,qBAAA,CAAApB,MAAA,CAAA5T,eAAA;oBACA8U,UAAA,CAAA1J,mBAAA;sBACA5H,kBAAA;wBACAzC,MAAA,EAAA6S,MAAA,CAAA1S,gBAAA,CAAAH,MAAA;wBACAE,iBAAA,EAAA2S,MAAA,CAAA1S,gBAAA,CAAAD,iBAAA;wBACAE,oBAAA,EAAAyS,MAAA,CAAA1S,gBAAA,CAAAC,oBAAA,CAAA8B,MAAA,GAAA2Q,MAAA,CAAA1S,gBAAA,CAAAC,oBAAA;sBACA;sBACAoK,WAAA,EAAAwJ;oBACA;kBACA;oBACAD,UAAA,CAAA1J,mBAAA;kBACA;kBAEA,OAAA0J,UAAA;gBACA;gBACAG,kBAAA,EAAArB,MAAA,CAAAvR,oBAAA,CAAAuR,MAAA,CAAArU,UAAA,KAAAwH,KAAA,CAAAC,OAAA,CAAA4M,MAAA,CAAAvR,oBAAA,CAAAuR,MAAA,CAAArU,UAAA,KACAqU,MAAA,CAAAvR,oBAAA,CAAAuR,MAAA,CAAArU,UAAA,EAAA2E,GAAA,WAAA3C,KAAA;kBAAA;oBACAS,EAAA,EAAAT,KAAA,CAAAS,EAAA;oBACA8P,UAAA,EAAAvQ,KAAA,CAAAuQ,UAAA;oBACA1K,IAAA;oBACAiL,GAAA,EAAA9Q,KAAA,CAAA8Q,GAAA;oBACArR,oBAAA,EAAAO,KAAA,CAAAP,oBAAA;oBACAC,iBAAA,EAAAM,KAAA,CAAAN,iBAAA;kBACA;gBAAA;gBACAiU,aAAA,EAAAtB,MAAA,CAAAtR,cAAA,CAAAsR,MAAA,CAAArU,UAAA,KAAAwH,KAAA,CAAAC,OAAA,CAAA4M,MAAA,CAAAtR,cAAA,CAAAsR,MAAA,CAAArU,UAAA,KACAqU,MAAA,CAAAtR,cAAA,CAAAsR,MAAA,CAAArU,UAAA,EAAA2E,GAAA,WAAAiR,EAAA;kBAAA;oBACAnT,EAAA,EAAAmT,EAAA,CAAAnT,EAAA;oBACA0S,UAAA,EAAAS,EAAA,CAAAT,UAAA,IAAAd,MAAA,CAAArU,UAAA;oBACA6H,IAAA,EAAA+N,EAAA,CAAA/N,IAAA;oBACA0K,UAAA,EAAAqD,EAAA,CAAArD,UAAA;oBACAlT,IAAA,EAAAuW,EAAA,CAAAvW,IAAA;oBACAwW,OAAA,EAAAD,EAAA,CAAAC,OAAA;kBACA;gBAAA;cACA;cAAAX,SAAA,CAAA9N,IAAA;cAAA,OAEA,IAAA0O,uBAAA,EAAAjB,UAAA;YAAA;cAAAC,QAAA,GAAAI,SAAA,CAAA5N,IAAA;cACA+M,MAAA,CAAA5G,MAAA,CAAAsI,UAAA;;cAEA;cAAAb,SAAA,CAAA9N,IAAA;cAAA,OACAiN,MAAA,CAAA1L,YAAA,CAAA0L,MAAA,CAAArU,UAAA;YAAA;cAEA,IAAA2U,qBAAA,IAAAN,MAAA,CAAA5T,eAAA,CAAAiD,MAAA;gBACA;gBACAmG,YAAA,GAAAwK,MAAA,CAAAnI,YAAA,CAAAmI,MAAA,CAAA5T,eAAA,EAAAkU,qBAAA;gBAEA,IAAA9K,YAAA;kBACA;kBACAkL,OAAA;kBACAC,QAAA,YAAAA,SAAAtQ,KAAA,EAAAsR,QAAA;oBAAA,IAAAC,WAAA,GAAA5M,SAAA,CAAA3F,MAAA,QAAA2F,SAAA,QAAAY,SAAA,GAAAZ,SAAA;oBAAA,IAAA6M,UAAA,OAAAtG,2BAAA,CAAA1K,OAAA,EACAR,KAAA;sBAAAyR,MAAA;oBAAA;sBAAA,KAAAD,UAAA,CAAApG,CAAA,MAAAqG,MAAA,GAAAD,UAAA,CAAAnG,CAAA,IAAAC,IAAA;wBAAA,IAAApL,IAAA,GAAAuR,MAAA,CAAAnU,KAAA;wBACA,IAAAoU,OAAA,MAAA/P,MAAA,KAAAgC,mBAAA,CAAAnD,OAAA,EAAA+Q,WAAA,IAAArR,IAAA,CAAAnC,EAAA;wBACA,IAAAmC,IAAA,CAAAnC,EAAA,KAAAuT,QAAA;0BACAjB,OAAA,CAAA5M,IAAA,CAAAC,KAAA,CAAA2M,OAAA,MAAA1M,mBAAA,CAAAnD,OAAA,EAAAkR,OAAA;0BACA;wBACA;wBACA,IAAAxR,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA;0BACA,IAAAsR,QAAA,CAAApQ,IAAA,CAAA3C,QAAA,EAAA+T,QAAA,EAAAI,OAAA;4BACA;0BACA;wBACA;sBACA;oBAAA,SAAAlG,GAAA;sBAAAgG,UAAA,CAAA/F,CAAA,CAAAD,GAAA;oBAAA;sBAAAgG,UAAA,CAAA9F,CAAA;oBAAA;oBACA;kBACA;kBAEA4E,QAAA,CAAAX,MAAA,CAAA5T,eAAA,EAAAkU,qBAAA;kBACAN,MAAA,CAAAlR,gBAAA,GAAA4R,OAAA,CAAAsB,KAAA;;kBAEA;kBACAhC,MAAA,CAAA3T,YAAA,GAAAmJ,YAAA;;kBAEA;kBACAwK,MAAA,CAAA9L,SAAA;oBACA8L,MAAA,CAAA9L,SAAA;sBACA;sBACA8L,MAAA,CAAA3C,oBAAA,CAAA7H,YAAA;oBACA;kBACA;gBACA;cACA;cAEAvD,OAAA,CAAAC,GAAA,UAAAsO,UAAA;cAAAK,SAAA,CAAA9N,IAAA;cAAA;YAAA;cAAA8N,SAAA,CAAA/N,IAAA;cAAA+N,SAAA,CAAA5I,EAAA,GAAA4I,SAAA;cAEA5O,OAAA,CAAAiG,KAAA,UAAA2I,SAAA,CAAA5I,EAAA;cACA+H,MAAA,CAAA7H,QAAA,CAAAD,KAAA;YAAA;cAAA2I,SAAA,CAAA/N,IAAA;cAEAkN,MAAA,CAAA9Q,UAAA;cAAA,OAAA2R,SAAA,CAAAzI,MAAA;YAAA;YAAA;cAAA,OAAAyI,SAAA,CAAAtM,IAAA;UAAA;QAAA,GAAA0L,QAAA;MAAA;IAEA;IACAgC,eAAA,WAAAA,gBAAA;MACA;MACA,UAAA3U,gBAAA,CAAAC,oBAAA,SAAAD,gBAAA,CAAAC,oBAAA,CAAA8B,MAAA;QACA,KAAA/B,gBAAA,CAAAC,oBAAA;UAAA+B,IAAA;UAAAnB,OAAA;UAAAoB,SAAA;UAAAC,SAAA;QAAA;MACA;MACA,KAAAlC,gBAAA,CAAAC,oBAAA,CAAAuG,IAAA;QAAAxE,IAAA;QAAAnB,OAAA;QAAAoB,SAAA;QAAAC,SAAA;MAAA;IACA;IACA0S,kBAAA,WAAAA,mBAAA5F,GAAA;MACA,KAAAhP,gBAAA,CAAAC,oBAAA,CAAAoL,MAAA,CAAA2D,GAAA;IACA;IACA;IACA6F,kBAAA,WAAAA,mBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,OAAAD,IAAA,CAAA9R,GAAA,WAAA6F,IAAA;QACA;QACA,IAAA5F,IAAA,OAAAK,cAAA,CAAAC,OAAA,MAAAsF,IAAA;;QAEA;QACA,IAAA5F,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA;UACAkB,IAAA,CAAA3C,QAAA,GAAAyU,MAAA,CAAAF,kBAAA,CAAA5R,IAAA,CAAA3C,QAAA;QACA;UACA;UACA,OAAA2C,IAAA,CAAA3C,QAAA;QACA;QAEA,OAAA2C,IAAA;MACA;IACA;IACA0F,oBAAA,WAAAA,qBAAA7H,EAAA;MAAA,IAAAkU,MAAA;MAAA,WAAAhQ,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA+P,SAAA;QAAA,IAAAlN,eAAA,EAAAzJ,YAAA,EAAA8G,GAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAA6P,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3P,IAAA,GAAA2P,SAAA,CAAA1P,IAAA;YAAA;cAAA0P,SAAA,CAAA3P,IAAA;cAEA;cACAuC,eAAA,GAAAiN,MAAA,CAAA5W,YAAA,CAAAwK,IAAA,WAAAC,IAAA;gBAAA,OAAAlC,MAAA,CAAAkC,IAAA,CAAA/H,EAAA,MAAAA,EAAA;cAAA;cACAxC,YAAA,GAAAyJ,eAAA,GAAAA,eAAA,CAAAzJ,YAAA;cAAA6W,SAAA,CAAA1P,IAAA;cAAA,OAEA,IAAA2P,0BAAA;gBAAA9W,YAAA,EAAAA;cAAA;YAAA;cAAA8G,GAAA,GAAA+P,SAAA,CAAAxP,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAC,KAAA,CAAAC,OAAA,CAAAV,GAAA,CAAAnH,IAAA;gBACA+W,MAAA,CAAA9U,gBAAA,GAAA8U,MAAA,CAAAH,kBAAA,CAAAzP,GAAA,CAAAnH,IAAA;cACA;cAAAkX,SAAA,CAAA1P,IAAA;cAAA;YAAA;cAAA0P,SAAA,CAAA3P,IAAA;cAAA2P,SAAA,CAAAxK,EAAA,GAAAwK,SAAA;cAEAxQ,OAAA,CAAAiG,KAAA,aAAAuK,SAAA,CAAAxK,EAAA;YAAA;YAAA;cAAA,OAAAwK,SAAA,CAAAlO,IAAA;UAAA;QAAA,GAAAgO,QAAA;MAAA;IAEA;IACAI,yBAAA,WAAAA,0BAAA1S,GAAA,EAAAqM,GAAA;MACA;MACA,UAAAhP,gBAAA,CAAAC,oBAAA,UAAAD,gBAAA,CAAAC,oBAAA,CAAA+O,GAAA;QACA;MACA;MAEA,IAAAsG,SAAA,YAAAA,UAAAR,IAAA,EAAAhU,EAAA;QAAA,IAAAyU,UAAA,OAAAtH,2BAAA,CAAA1K,OAAA,EACAuR,IAAA;UAAAU,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAApH,CAAA,MAAAqH,MAAA,GAAAD,UAAA,CAAAnH,CAAA,IAAAC,IAAA;YAAA,IAAApL,KAAA,GAAAuS,MAAA,CAAAnV,KAAA;YACA,IAAA4C,KAAA,CAAAnC,EAAA,KAAAA,EAAA,SAAAmC,KAAA;YACA,IAAAA,KAAA,CAAA3C,QAAA,IAAA2C,KAAA,CAAA3C,QAAA,CAAAyB,MAAA;cACA,IAAAuM,KAAA,GAAAgH,SAAA,CAAArS,KAAA,CAAA3C,QAAA,EAAAQ,EAAA;cACA,IAAAwN,KAAA,SAAAA,KAAA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAgH,UAAA,CAAA/G,CAAA,CAAAD,GAAA;QAAA;UAAAgH,UAAA,CAAA9G,CAAA;QAAA;QACA;MACA;MACA,IAAAxL,IAAA,GAAAqS,SAAA,MAAApV,gBAAA,EAAAyC,GAAA;MACA,IAAAM,IAAA;QACA;QACA,KAAAjD,gBAAA,CAAAC,oBAAA,CAAA+O,GAAA,EAAAnO,OAAA,GAAA8B,GAAA;QACA,KAAA3C,gBAAA,CAAAC,oBAAA,CAAA+O,GAAA,EAAA/M,SAAA,GAAAgB,IAAA,CAAAhB,SAAA;QACA,KAAAjC,gBAAA,CAAAC,oBAAA,CAAA+O,GAAA,EAAA9M,SAAA,GAAAe,IAAA,CAAAf,SAAA;MACA;IACA;IACAuT,eAAA,WAAAA,gBAAA3U,EAAA,EAAA4U,UAAA;MACA;MACA,YAAA1V,gBAAA,CAAAC,oBAAA,CAAAU,IAAA,WAAAC,GAAA,EAAAoO,GAAA;QAAA,OAAAA,GAAA,KAAA0G,UAAA,IAAA9U,GAAA,CAAAC,OAAA,KAAAC,EAAA;MAAA;IACA;IACA;IACAgT,qBAAA,WAAAA,sBAAA6B,SAAA;MAAA,IAAAC,OAAA;MACAjR,OAAA,CAAAC,GAAA,WAAA+Q,SAAA;MACA,OAAAA,SAAA,CAAA3S,GAAA,WAAAC,IAAA;QAAA;UACAoN,WAAA,EAAApN,IAAA,CAAAoN,WAAA;UACAxP,OAAA,EAAAoC,IAAA,CAAAnC,EAAA;UACA+U,OAAA,EAAA5S,IAAA,CAAAiN,MAAA,GAAAjN,IAAA,CAAAiN,MAAA,CAAApP,EAAA;UACAmB,SAAA,EAAAgB,IAAA,CAAAvF,IAAA;UACAwE,SAAA,EAAAe,IAAA,CAAA2C,IAAA;UACA0K,CAAA,EAAArN,IAAA,CAAAqN,CAAA;UACAC,CAAA,EAAAtN,IAAA,CAAAsN,CAAA;UACArK,IAAA,EAAAjD,IAAA,CAAAiD,IAAA;UACArG,MAAA,EAAAoD,IAAA,CAAApD,MAAA;UACA2Q,QAAA,EAAAvN,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA,OAAAkB,IAAA,CAAAuN,QAAA;UACAC,eAAA,EAAAxN,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA,OAAAkB,IAAA,CAAAwN,eAAA;UACAC,kBAAA,EAAAzN,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA,OAAAkB,IAAA,CAAAyN,kBAAA;UACAC,gBAAA,EAAA1N,IAAA,CAAA0N,gBAAA;YACA7P,EAAA,EAAAmC,IAAA,CAAA0N,gBAAA,CAAA7P,EAAA;YACAoF,IAAA,EAAAjD,IAAA,CAAA0N,gBAAA,CAAAzK,IAAA;YACA0K,UAAA,EAAA3N,IAAA,CAAA0N,gBAAA,CAAAC,UAAA;YACA/Q,MAAA,EAAAoD,IAAA,CAAA0N,gBAAA,CAAA9Q,MAAA;YACAC,oBAAA,EAAAmD,IAAA,CAAA0N,gBAAA,CAAA7Q,oBAAA;YACAC,iBAAA,EAAAkD,IAAA,CAAA0N,gBAAA,CAAA5Q,iBAAA;UACA;UACA+Q,gBAAA,EAAA7N,IAAA,CAAA4N,SAAA;YACAnT,IAAA,EAAAuF,IAAA,CAAA4N,SAAA,CAAAnT,IAAA;YACAqT,mBAAA,EAAA9N,IAAA,CAAA4N,SAAA,CAAAE,mBAAA;YACAE,cAAA,EAAAhO,IAAA,CAAA4N,SAAA,CAAAG,mBAAA,IAAA/N,IAAA,CAAA4N,SAAA,CAAAG,mBAAA,CAAAjP,MAAA,GACAkB,IAAA,CAAA4N,SAAA,CAAAG,mBAAA,CAAAhO,GAAA,WAAA8S,QAAA;cAAA;gBACAhV,EAAA,EAAAgV,QAAA,CAAAhV,EAAA;gBACAqQ,GAAA,EAAA2E,QAAA,CAAA1V,KAAA;gBACAsR,IAAA,EAAAoE,QAAA,CAAApE,IAAA;gBACAC,IAAA,EAAAmE,QAAA,CAAAnE,IAAA;gBACA9R,MAAA,EAAAiW,QAAA,CAAAjW,MAAA;gBACAqG,IAAA;gBACA0K,UAAA,EAAAkF,QAAA,CAAAlF,UAAA;gBACA9Q,oBAAA,EAAAgW,QAAA,CAAAhE,KAAA;gBACA/R,iBAAA,EAAA+V,QAAA,CAAApU,MAAA;gBACA2P,gBAAA,EAAAyE,QAAA,CAAA1E,WAAA,IAAA0E,QAAA,CAAA1E,WAAA,CAAArP,MAAA,GACA+T,QAAA,CAAA1E,WAAA,CAAApO,GAAA,WAAA+S,KAAA;kBAAA;oBACAjV,EAAA,EAAAiV,KAAA,CAAAjV,EAAA;oBACAyQ,MAAA,EAAAwE,KAAA,CAAAxE,MAAA;oBACAC,MAAA,EAAAuE,KAAA,CAAAzF,CAAA,KAAAhI,SAAA,IAAAyN,KAAA,CAAAzF,CAAA,YAAAyF,KAAA,CAAAzF,CAAA,eAAAyF,KAAA,CAAAzF,CAAA;oBACAmB,MAAA,EAAAsE,KAAA,CAAAxF,CAAA,KAAAjI,SAAA,IAAAyN,KAAA,CAAAxF,CAAA,YAAAwF,KAAA,CAAAxF,CAAA,eAAAwF,KAAA,CAAAxF,CAAA;oBACAmB,IAAA,EAAAqE,KAAA,CAAArE,IAAA,KAAApJ,SAAA,IAAAyN,KAAA,CAAArE,IAAA,YAAAqE,KAAA,CAAArE,IAAA,WAAAqE,KAAA,CAAArE,IAAA,cAAAqE,KAAA,CAAArE,IAAA;oBACAC,IAAA,EAAAoE,KAAA,CAAApE,IAAA,KAAArJ,SAAA,IAAAyN,KAAA,CAAApE,IAAA,YAAAoE,KAAA,CAAApE,IAAA,WAAAoE,KAAA,CAAApE,IAAA,cAAAoE,KAAA,CAAApE,IAAA;oBACAC,MAAA,EAAAmE,KAAA,CAAAnE,MAAA,KAAAtJ,SAAA,IAAAyN,KAAA,CAAAnE,MAAA,YAAAmE,KAAA,CAAAnE,MAAA,eAAAmE,KAAA,CAAAnE,MAAA;oBACAC,aAAA,EAAAkE,KAAA,CAAA7T,SAAA,KAAAoG,SAAA,IAAAyN,KAAA,CAAA7T,SAAA,YAAA6T,KAAA,CAAA7T,SAAA,eAAA6T,KAAA,CAAA7T,SAAA;kBACA;gBAAA;cACA;YAAA;YACA6P,aAAA,EAAA9O,IAAA,CAAA4N,SAAA,CAAAlC,UAAA,IAAA1L,IAAA,CAAA4N,SAAA,CAAAlC,UAAA,CAAA5M,MAAA,GACAkB,IAAA,CAAA4N,SAAA,CAAAlC,UAAA,CAAA3L,GAAA,WAAAgT,IAAA;cAAA;gBACA/D,WAAA,EAAA+D,IAAA,CAAA/D,WAAA;gBACAC,QAAA,EAAA8D,IAAA,CAAApH,KAAA;gBACAuD,OAAA,EAAA6D,IAAA,CAAAnH,QAAA;gBACAuD,WAAA,EAAA4D,IAAA,CAAAlH,QAAA;cACA;YAAA;UACA;UACAwD,SAAA,EAAArP,IAAA,CAAAoP,QAAA;YACA3U,IAAA,EAAAuF,IAAA,CAAAoP,QAAA,CAAA3U,IAAA;YACAqT,mBAAA,EAAA9N,IAAA,CAAAoP,QAAA,CAAAtB,mBAAA;YACAE,cAAA,EAAAhO,IAAA,CAAAoP,QAAA,CAAArB,mBAAA,IAAA/N,IAAA,CAAAoP,QAAA,CAAArB,mBAAA,CAAAjP,MAAA,GACAkB,IAAA,CAAAoP,QAAA,CAAArB,mBAAA,CAAAhO,GAAA,WAAA8S,QAAA;cAAA;gBACAhV,EAAA,EAAAgV,QAAA,CAAAhV,EAAA;gBACAqQ,GAAA,EAAA2E,QAAA,CAAA3E,GAAA;gBACAtR,MAAA,EAAAiW,QAAA,CAAAjW,MAAA;gBACAqG,IAAA;gBACA0K,UAAA,EAAAkF,QAAA,CAAAlF,UAAA;gBACA9Q,oBAAA,EAAAgW,QAAA,CAAAhE,KAAA;gBACA/R,iBAAA,EAAA+V,QAAA,CAAApU,MAAA;gBACA2P,gBAAA,EAAAyE,QAAA,CAAA1E,WAAA,IAAA0E,QAAA,CAAA1E,WAAA,CAAArP,MAAA,GACA+T,QAAA,CAAA1E,WAAA,CAAApO,GAAA,WAAA+S,KAAA;kBAAA;oBACAjV,EAAA,EAAAiV,KAAA,CAAAjV,EAAA;oBACAyQ,MAAA,EAAAwE,KAAA,CAAAxE,MAAA;oBACAC,MAAA,EAAAuE,KAAA,CAAAzF,CAAA,KAAAhI,SAAA,IAAAyN,KAAA,CAAAzF,CAAA,YAAAyF,KAAA,CAAAzF,CAAA,eAAAyF,KAAA,CAAAzF,CAAA;oBACAmB,MAAA,EAAAsE,KAAA,CAAAxF,CAAA,KAAAjI,SAAA,IAAAyN,KAAA,CAAAxF,CAAA,YAAAwF,KAAA,CAAAxF,CAAA,eAAAwF,KAAA,CAAAxF,CAAA;oBACAmB,IAAA,EAAAqE,KAAA,CAAArE,IAAA,KAAApJ,SAAA,IAAAyN,KAAA,CAAArE,IAAA,YAAAqE,KAAA,CAAArE,IAAA,WAAAqE,KAAA,CAAArE,IAAA,cAAAqE,KAAA,CAAArE,IAAA;oBACAC,IAAA,EAAAoE,KAAA,CAAApE,IAAA,KAAArJ,SAAA,IAAAyN,KAAA,CAAApE,IAAA,YAAAoE,KAAA,CAAApE,IAAA,WAAAoE,KAAA,CAAApE,IAAA,cAAAoE,KAAA,CAAApE,IAAA;oBACAC,MAAA,EAAAmE,KAAA,CAAAnE,MAAA,KAAAtJ,SAAA,IAAAyN,KAAA,CAAAnE,MAAA,YAAAmE,KAAA,CAAAnE,MAAA,eAAAmE,KAAA,CAAAnE,MAAA;oBACAC,aAAA,EAAAkE,KAAA,CAAA7T,SAAA,KAAAoG,SAAA,IAAAyN,KAAA,CAAA7T,SAAA,YAAA6T,KAAA,CAAA7T,SAAA,eAAA6T,KAAA,CAAA7T,SAAA;kBACA;gBAAA;cACA;YAAA;YACA6P,aAAA,EAAA9O,IAAA,CAAAoP,QAAA,CAAA1D,UAAA,IAAA1L,IAAA,CAAAoP,QAAA,CAAA1D,UAAA,CAAA5M,MAAA,GACAkB,IAAA,CAAAoP,QAAA,CAAA1D,UAAA,CAAA3L,GAAA,WAAAgT,IAAA;cAAA;gBACA/D,WAAA,EAAA+D,IAAA,CAAA/D,WAAA;gBACAC,QAAA,EAAA8D,IAAA,CAAApH,KAAA;gBACAuD,OAAA,EAAA6D,IAAA,CAAAnH,QAAA;gBACAuD,WAAA,EAAA4D,IAAA,CAAAlH,QAAA;cACA;YAAA;UACA;UACA0D,oBAAA,EAAAvP,IAAA,CAAAsP,YAAA;YACAN,WAAA,EAAAhP,IAAA,CAAAsP,YAAA,CAAAN,WAAA;YACApS,MAAA,EAAAoD,IAAA,CAAAsP,YAAA,CAAA1S,MAAA;YACAqS,QAAA,EAAAjP,IAAA,CAAAsP,YAAA,CAAA3D,KAAA;YACAuD,OAAA,EAAAlP,IAAA,CAAAsP,YAAA,CAAA1D,QAAA,IAAA5L,IAAA,CAAAsP,YAAA,CAAA1D,QAAA,CAAA9M,MAAA,GAAAkB,IAAA,CAAAsP,YAAA,CAAA1D,QAAA;UACA;UACAvO,QAAA,EAAA2C,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA,GAAA6T,OAAA,CAAA9B,qBAAA,CAAA7Q,IAAA,CAAA3C,QAAA;QACA;MAAA;IACA;IACA2V,gBAAA,WAAAA,iBAAAtT,GAAA,EAAAqM,GAAA;MACA;MACA,UAAAhP,gBAAA,CAAAC,oBAAA,SAAAD,gBAAA,CAAAC,oBAAA,CAAA8B,MAAA;QACA,KAAA/B,gBAAA,CAAAC,oBAAA;UAAA+B,IAAA;UAAAnB,OAAA;UAAAoB,SAAA;UAAAC,SAAA;QAAA;MACA;MACA;MACA,SAAAlC,gBAAA,CAAAC,oBAAA,CAAA+O,GAAA;QACA,KAAAhP,gBAAA,CAAAC,oBAAA,CAAA+O,GAAA,EAAAhN,IAAA,GAAAW,GAAA;MACA;IACA;IACA;IACAwH,iBAAA,WAAAA,kBAAAjI,SAAA;MACA,KAAAA,SAAA,UAAAhC,gBAAA;MAEA,IAAAgW,UAAA,YAAAA,WAAApB,IAAA;QAAA,IAAAqB,UAAA,OAAAlI,2BAAA,CAAA1K,OAAA,EACAuR,IAAA;UAAAsB,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAhI,CAAA,MAAAiI,MAAA,GAAAD,UAAA,CAAA/H,CAAA,IAAAC,IAAA;YAAA,IAAApL,IAAA,GAAAmT,MAAA,CAAA/V,KAAA;YACA,IAAA4C,IAAA,CAAAf,SAAA,KAAAA,SAAA;cACA,OAAAe,IAAA,CAAAnC,EAAA;YACA;YACA,IAAAmC,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA;cACA,IAAAuM,KAAA,GAAA4H,UAAA,CAAAjT,IAAA,CAAA3C,QAAA;cACA,IAAAgO,KAAA,SAAAA,KAAA;YACA;UACA;QAAA,SAAAC,GAAA;UAAA4H,UAAA,CAAA3H,CAAA,CAAAD,GAAA;QAAA;UAAA4H,UAAA,CAAA1H,CAAA;QAAA;QACA;MACA;MAEA,OAAAyH,UAAA,MAAAhW,gBAAA;IACA;IACA;IACAmW,kBAAA,WAAAA,mBAAArL,IAAA,EAAAsL,QAAA;MACA,KAAA9X,IAAA,CAAAI,SAAA;MACA,KAAAJ,IAAA,CAAAG,QAAA;MACA,KAAAoC,UAAA;MACA,KAAA8J,QAAA,CAAAsB,OAAA;IACA;IACA;IACAnD,gBAAA,WAAAA,iBAAA;MACA,SAAAxK,IAAA,CAAAI,SAAA;QACA,IAAA6N,QAAA,QAAAjO,IAAA,CAAAI,SAAA,CAAAmO,KAAA,MAAAC,GAAA;QACA,KAAAjM,UAAA;UACArD,IAAA,EAAA+O,QAAA;UACAQ,GAAA,OAAAzO,IAAA,CAAAI,SAAA;UACAsO,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAArM,UAAA;MACA;IACA;IACA;IACAwV,8BAAA,WAAAA,+BAAAvL,IAAA,EAAAsL,QAAA;MACA,KAAA1W,cAAA,CAAAG,iBAAA;MACA,KAAAH,cAAA,CAAAE,oBAAA;MACA,KAAAyN,sBAAA;MACA,KAAA1C,QAAA,CAAAsB,OAAA;IACA;IACA;IACAqK,4BAAA,WAAAA,6BAAA;MACA,SAAA5W,cAAA,CAAAG,iBAAA;QACA,IAAA0M,QAAA,QAAA7M,cAAA,CAAAG,iBAAA,CAAAgN,KAAA,MAAAC,GAAA;QACA,KAAAO,sBAAA;UACA7P,IAAA,EAAA+O,QAAA;UACAQ,GAAA,OAAArN,cAAA,CAAAG,iBAAA;UACAmN,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAAG,sBAAA;MACA;IACA;IACA;IACAkJ,gCAAA,WAAAA,iCAAAzL,IAAA,EAAAsL,QAAA;MACA,KAAAtW,gBAAA,CAAAD,iBAAA;MACA,KAAAiB,wBAAA;MACA,KAAA6J,QAAA,CAAAsB,OAAA;IACA;IACA;IACA/B,8BAAA,WAAAA,+BAAA;MACA,SAAApK,gBAAA,CAAAD,iBAAA;QACA,IAAA0M,QAAA,QAAAzM,gBAAA,CAAAD,iBAAA,CAAAgN,KAAA,MAAAC,GAAA;QACA,KAAAhM,wBAAA;UACAtD,IAAA,EAAA+O,QAAA;UACAQ,GAAA,OAAAjN,gBAAA,CAAAD,iBAAA;UACAmN,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAApM,wBAAA;MACA;IACA;IACA;IACA0V,mBAAA,WAAAA,oBAAA1L,IAAA,EAAAsL,QAAA;MACA,KAAA9X,IAAA,CAAAK,mBAAA;MACA,KAAAoC,WAAA;MACA,KAAA4J,QAAA,CAAAsB,OAAA;IACA;IAEA;IACAlD,iBAAA,WAAAA,kBAAA;MACA,SAAAzK,IAAA,CAAAK,mBAAA;QACA,IAAA4N,QAAA,QAAAjO,IAAA,CAAAK,mBAAA,CAAAkO,KAAA,MAAAC,GAAA;QACA,KAAA/L,WAAA;UACAvD,IAAA,EAAA+O,QAAA;UACAQ,GAAA,OAAAzO,IAAA,CAAAK,mBAAA;UACAqO,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAAnM,WAAA;MACA;IACA;IACA;IACA0V,YAAA,WAAAA,aAAA1J,GAAA;MACA,IAAAA,GAAA;QACA,KAAA3L,eAAA,GAAA2L,GAAA;QACA,KAAA5L,cAAA;MACA;IACA;IACAuV,YAAA,WAAAA,aAAA;MACA,KAAAvV,cAAA;MACA,KAAAC,eAAA;IACA;IACA;IACAuV,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA/Q,IAAA;MACA,GAAAgR,IAAA;QACAJ,OAAA,CAAAtY,IAAA,CAAAG,QAAA;QACAmY,OAAA,CAAAjM,QAAA,CAAAsB,OAAA;MACA,GAAAgL,KAAA;IACA;IACAC,mBAAA,WAAAA,oBAAApM,IAAA;MAAA,IAAAqM,OAAA;MAAA,WAAArS,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAoS,SAAA;QAAA,IAAA7L,QAAA,EAAArG,GAAA,EAAAqH,QAAA;QAAA,WAAAxH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAkS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhS,IAAA,GAAAgS,SAAA,CAAA/R,IAAA;YAAA;cAAA,IAEAuF,IAAA,CAAAtN,IAAA,CAAA0F,WAAA,GAAAkK,QAAA;gBAAAkK,SAAA,CAAA/R,IAAA;gBAAA;cAAA;cACA4R,OAAA,CAAAxM,QAAA,CAAAD,KAAA;cAAA,OAAA4M,SAAA,CAAA3L,MAAA,WACA;YAAA;cAAA,MAIAb,IAAA,CAAAyM,IAAA;gBAAAD,SAAA,CAAA/R,IAAA;gBAAA;cAAA;cACA4R,OAAA,CAAAxM,QAAA,CAAAD,KAAA;cAAA,OAAA4M,SAAA,CAAA3L,MAAA,WACA;YAAA;cAAA2L,SAAA,CAAAhS,IAAA;cAIA6R,OAAA,CAAAvL,MAAA,CAAA9M,OAAA;cACAyM,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,SAAAhB,IAAA;cACAS,QAAA,CAAAO,MAAA,iBAAAqL,OAAA,CAAA/Y,YAAA;cAAAkZ,SAAA,CAAA/R,IAAA;cAAA,OAEA,IAAAwG,0BAAA,EAAAR,QAAA;YAAA;cAAArG,GAAA,GAAAoS,SAAA,CAAA7R,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAR,GAAA,CAAAnH,IAAA;gBACA;gBACAoZ,OAAA,CAAA7Y,IAAA,CAAAK,mBAAA,GAAAuG,GAAA,CAAAnH,IAAA,CAAAiO,OAAA;;gBAEA;gBACAO,QAAA,GAAArH,GAAA,CAAAnH,IAAA,CAAAiO,OAAA,CAAAa,KAAA,MAAAC,GAAA;gBACAqK,OAAA,CAAApW,WAAA;kBACAvD,IAAA,EAAA+O,QAAA;kBACAQ,GAAA,EAAA7H,GAAA,CAAAnH,IAAA,CAAAiO,OAAA;kBACAgB,GAAA,EAAAC,IAAA,CAAAC,GAAA;gBACA;gBAEAiK,OAAA,CAAAxM,QAAA,CAAAsB,OAAA;cACA;gBACAkL,OAAA,CAAAxM,QAAA,CAAAD,KAAA,CAAAxF,GAAA,CAAAgH,GAAA;cACA;cAAAoL,SAAA,CAAA/R,IAAA;cAAA;YAAA;cAAA+R,SAAA,CAAAhS,IAAA;cAAAgS,SAAA,CAAA7M,EAAA,GAAA6M,SAAA;cAEAH,OAAA,CAAAxM,QAAA,CAAAD,KAAA;YAAA;cAAA4M,SAAA,CAAAhS,IAAA;cAEA6R,OAAA,CAAAvL,MAAA,CAAAO,YAAA;cAAA,OAAAmL,SAAA,CAAA1M,MAAA;YAAA;cAAA,OAAA0M,SAAA,CAAA3L,MAAA,WAEA;YAAA;YAAA;cAAA,OAAA2L,SAAA,CAAAvQ,IAAA;UAAA;QAAA,GAAAqQ,QAAA;MAAA;IACA;IACA;IACAI,aAAA,WAAAA,cAAAC,KAAA;MACA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAC,cAAA;QACA,KAAApZ,IAAA,CAAAG,QAAA,GAAAgZ,KAAA,CAAAC,cAAA;MACA;IACA;IACA;IACAC,0BAAA,WAAAA,2BAAAzG,WAAA;MACA,KAAAA,WAAA,KAAAvL,KAAA,CAAAC,OAAA,CAAAsL,WAAA;QACA;UAAAI,MAAA;UAAAC,MAAA;QAAA;MACA;MAEA,IAAAqG,OAAA,GAAA1G,WAAA,CAAApO,GAAA,WAAA+S,KAAA;QAAA,OAAAA,KAAA,CAAAzF,CAAA;MAAA,GAAAyH,IAAA;MACA,IAAAC,OAAA,GAAA5G,WAAA,CAAApO,GAAA,WAAA+S,KAAA;QAAA,OAAAA,KAAA,CAAAxF,CAAA;MAAA,GAAAwH,IAAA;MAEA;QACAvG,MAAA,EAAAsG,OAAA;QACArG,MAAA,EAAAuG;MACA;IACA;IACA;IACAC,uBAAA,WAAAA,wBAAAzG,MAAA,EAAAC,MAAA;MACA,IAAAyG,MAAA,GAAA1G,MAAA,GAAAA,MAAA,CAAAzE,KAAA;MACA,IAAAoL,MAAA,GAAA1G,MAAA,GAAAA,MAAA,CAAA1E,KAAA;;MAEA;MACA,IAAAqL,SAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAJ,MAAA,CAAAnW,MAAA,EAAAoW,MAAA,CAAApW,MAAA;MACA,IAAAqP,WAAA;MAEA,SAAAmH,CAAA,MAAAA,CAAA,GAAAH,SAAA,EAAAG,CAAA;QACAnH,WAAA,CAAA5K,IAAA;UACA8J,CAAA,EAAA4H,MAAA,CAAAK,CAAA;UACAhI,CAAA,EAAA4H,MAAA,CAAAI,CAAA;QACA;MACA;;MAEA;MACA,OAAAnH,WAAA,CAAArP,MAAA,OAAAqP,WAAA;QAAAd,CAAA;QAAAC,CAAA;MAAA;IACA;IACA;IACAiI,cAAA,WAAAA,eAAApN,KAAA;MAAA,IAAAqN,OAAA;MACA,IAAAC,QAAA,QAAA/Y,UAAA,CAAAyL,KAAA;MACAsN,QAAA,CAAA7O,OAAA;MACA6O,QAAA,CAAA5O,WAAA,GAAA4O,QAAA,CAAAhb,IAAA;;MAEA;MACA,KAAAkJ,SAAA;QACA;QACA,IAAA+R,QAAA,GAAAF,OAAA,CAAA5R,KAAA,eAAAnC,MAAA,CAAA0G,KAAA;QACA,IAAAuN,QAAA,IAAAA,QAAA;UACAA,QAAA,IAAAC,KAAA;UACAD,QAAA,IAAAE,MAAA;QACA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA1N,KAAA;MACA,IAAAsN,QAAA,QAAA/Y,UAAA,CAAAyL,KAAA;MACA,IAAAsN,QAAA,CAAA5O,WAAA,IAAA4O,QAAA,CAAA5O,WAAA,CAAAiP,IAAA;QACAL,QAAA,CAAAhb,IAAA,GAAAgb,QAAA,CAAA5O,WAAA,CAAAiP,IAAA;MACA;MACAL,QAAA,CAAA7O,OAAA;MACA6O,QAAA,CAAA5O,WAAA;IACA;IAEA;IACAkP,eAAA,WAAAA,gBAAA5N,KAAA;MACA,IAAAsN,QAAA,QAAA/Y,UAAA,CAAAyL,KAAA;MACAsN,QAAA,CAAA7O,OAAA;MACA6O,QAAA,CAAA5O,WAAA;IACA;IACA;IACAmP,aAAA,WAAAA,cAAA/S,IAAA,EAAAgT,IAAA;MACA,KAAA1W,IAAA,MAAAf,WAAA,EAAAyE,IAAA,EAAAgT,IAAA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAA9Y,KAAA;MACA,KAAAU,UAAA;MACA,IAAAV,KAAA;QACA,IAAAoM,QAAA,GAAApM,KAAA,CAAA0M,KAAA,MAAAC,GAAA;QACA,KAAAjM,UAAA;UACArD,IAAA,EAAA+O,QAAA;UACAQ,GAAA,EAAA5M,KAAA;UACA6M,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACAgM,8BAAA,WAAAA,+BAAA/Y,KAAA;MACA,KAAAW,wBAAA;MACA,IAAAX,KAAA;QACA,IAAAoM,QAAA,GAAApM,KAAA,CAAA0M,KAAA,MAAAC,GAAA;QACA,KAAAhM,wBAAA;UACAtD,IAAA,EAAA+O,QAAA;UACAQ,GAAA,EAAA5M,KAAA;UACA6M,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACAiM,4BAAA,WAAAA,6BAAAhZ,KAAA;MACA,KAAAkN,sBAAA;MACA,IAAAlN,KAAA;QACA,IAAAoM,QAAA,GAAApM,KAAA,CAAA0M,KAAA,MAAAC,GAAA;QACA,KAAAO,sBAAA;UACA7P,IAAA,EAAA+O,QAAA;UACAQ,GAAA,EAAA5M,KAAA;UACA6M,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACAkM,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAvU,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAsU,SAAA;QAAA,IAAA/N,QAAA,EAAArG,GAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAoU,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlU,IAAA,GAAAkU,SAAA,CAAAjU,IAAA;YAAA;cAAA,IACA8T,OAAA,CAAA/a,IAAA,CAAAuK,iBAAA;gBAAA2Q,SAAA,CAAAjU,IAAA;gBAAA;cAAA;cACA8T,OAAA,CAAA1O,QAAA,CAAA8O,OAAA;cAAA,OAAAD,SAAA,CAAA7N,MAAA;YAAA;cAAA6N,SAAA,CAAAlU,IAAA;cAKA+T,OAAA,CAAA5X,aAAA;cACA4X,OAAA,CAAAzN,MAAA,CAAA9M,OAAA;;cAEA;cACAyM,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,iBAAAuN,OAAA,CAAA/a,IAAA,CAAAuK,iBAAA;cAAA2Q,SAAA,CAAAjU,IAAA;cAAA,OAEA,IAAAmU,8BAAA,EAAAnO,QAAA;YAAA;cAAArG,GAAA,GAAAsU,SAAA,CAAA/T,IAAA;cAEA,IAAAP,GAAA,CAAAQ,IAAA;gBACA2T,OAAA,CAAA1O,QAAA,CAAAsB,OAAA,CAAA/G,GAAA,CAAAgH,GAAA;cACA;gBACAmN,OAAA,CAAA1O,QAAA,CAAAD,KAAA,CAAAxF,GAAA,CAAAgH,GAAA;cACA;cAAAsN,SAAA,CAAAjU,IAAA;cAAA;YAAA;cAAAiU,SAAA,CAAAlU,IAAA;cAAAkU,SAAA,CAAA/O,EAAA,GAAA+O,SAAA;cAEA/U,OAAA,CAAAiG,KAAA,YAAA8O,SAAA,CAAA/O,EAAA;cACA4O,OAAA,CAAA1O,QAAA,CAAAD,KAAA;YAAA;cAAA8O,SAAA,CAAAlU,IAAA;cAEA+T,OAAA,CAAA5X,aAAA;cACA4X,OAAA,CAAAzN,MAAA,CAAAO,YAAA;cAAA,OAAAqN,SAAA,CAAA5O,MAAA;YAAA;YAAA;cAAA,OAAA4O,SAAA,CAAAzS,IAAA;UAAA;QAAA,GAAAuS,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}