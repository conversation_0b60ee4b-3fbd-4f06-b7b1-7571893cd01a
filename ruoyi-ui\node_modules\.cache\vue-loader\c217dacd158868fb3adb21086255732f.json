{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue", "mtime": 1754893059597}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1743599728056}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyB1cGxvYWRTY2VuZUZpbGUsIGJhY2tncm91bmRGaWxlRGVsLCBmaWxlQmluZERlbCB9IGZyb20gJ0AvYXBpL3ZpZXcvc2NlbmVWaWV3Jw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTY2VuZUNvbmZpZ05vZGUnLA0KICBwcm9wczogew0KICAgIG5vZGU6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICByb290VHJlZTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgc2NlbmVUcmVlT3B0aW9uczogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgbGVmdFRyZWVJbmR1c3RyeUNvZGU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICdub2RlLnN0YXR1cycodmFsKSB7DQogICAgICBpZiAodmFsID09PSAnMCcpIHsNCiAgICAgICAgdGhpcy5maW5kQW5kT3BlblBhcmVudCh0aGlzLm5vZGUuaWQsIHRoaXMucm9vdFRyZWUpDQogICAgICB9DQogICAgfSwNCiAgICBub2RlOiB7DQogICAgICBoYW5kbGVyKG5ld05vZGUsIG9sZE5vZGUpIHsNCiAgICAgICAgaWYgKG5ld05vZGUgJiYgbmV3Tm9kZSAhPT0gb2xkTm9kZSkgew0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIC8vIOWIneWni+WMluiKgueCueaVsOaNrg0KICAgICAgICAgICAgdGhpcy5pbml0Tm9kZURhdGEoKQ0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDnoa7kv51pbnRyb2R1Y2VWaWRlb1Zv5a2Y5Zyo5LiU5pyJ5a6M5pW05pWw5o2u5pe25omN5aSE55CGDQogICAgICAgICAgICBpZiAobmV3Tm9kZS5pbnRyb2R1Y2VWaWRlb1ZvICYmIG5ld05vZGUuaW50cm9kdWNlVmlkZW9Wby5oYXNPd25Qcm9wZXJ0eSgnc3RhdHVzJykpIHsNCiAgICAgICAgICAgICAgLy8g5pWw5o2u5bey5a2Y5Zyo77yM5LiN6ZyA6KaB5Yid5aeL5YyW77yM55u05o6l5pu05paw5paH5Lu25YiX6KGoDQogICAgICAgICAgICAgIHRoaXMudXBkYXRlSW50cm9kdWNlVmlkZW9GaWxlTGlzdCgpDQogICAgICAgICAgICB9IGVsc2UgaWYgKCFuZXdOb2RlLmludHJvZHVjZVZpZGVvVm8pIHsNCiAgICAgICAgICAgICAgLy8g5pWw5o2u5LiN5a2Y5Zyo5pe25omN5Yid5aeL5YyWDQogICAgICAgICAgICAgIHRoaXMuaW5pdEludHJvZHVjZVZpZGVvKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC8vIOmHjeaWsOWIneWni+WMluaWh+S7tuWIl+ihqO+8jOa4hemZpOWPr+iDveeahOe7p+aJv+mXrumimA0KICAgICAgICAgICAgdGhpcy5pbml0RmlsZUxpc3RzKCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgaW1tZWRpYXRlOiB0cnVlLA0KICAgICAgZGVlcDogdHJ1ZQ0KICAgIH0sDQogICAgLy8g55uR5ZCsaW50cm9kdWNlVmlkZW9Wb+aVtOS4quWvueixoeeahOWPmOWMlg0KICAgICdub2RlLmludHJvZHVjZVZpZGVvVm8nOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgICAgICBpZiAobmV3VmFsICYmIG5ld1ZhbC5zdGF0dXMgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgIHRoaXMudXBkYXRlSW50cm9kdWNlVmlkZW9GaWxlTGlzdCgpDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUsDQogICAgICBkZWVwOiB0cnVlDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBmaWxlTGlzdHM6IHsNCiAgICAgICAgdHJhZGl0aW9uOiB7fSwNCiAgICAgICAgd2lzZG9tNWc6IHt9LA0KICAgICAgICBpbnRyb2R1Y2VWaWRlbzogW10NCiAgICAgIH0sDQogICAgICB1cGxvYWRNb2Rlczogew0KICAgICAgICB0cmFkaXRpb246IHt9LA0KICAgICAgICB3aXNkb201Zzoge30sDQogICAgICAgIGludHJvZHVjZVZpZGVvOiAndXBsb2FkJw0KICAgICAgfSwNCiAgICAgIC8vIOa3u+WKoOS7i+e7jeinhumikeaWh+S7tuWIl+ihqOe8k+WtmA0KICAgICAgaW50cm9kdWNlVmlkZW9GaWxlTGlzdDogW10sDQogICAgICAvLyDlm77niYfpooTop4gNCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHByZXZpZXdJbWFnZVVybDogJycsDQogICAgICAvLyDlnLrmma/nuqfogZTpgInmi6nlmajphY3nva4NCiAgICAgIHNjZW5lQ2FzY2FkZXJQcm9wczogew0KICAgICAgICBsYWJlbDogJ3NjZW5lTmFtZScsDQogICAgICAgIHZhbHVlOiAnaWQnLA0KICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywNCiAgICAgICAgZW1pdFBhdGg6IGZhbHNlLA0KICAgICAgICBjaGVja1N0cmljdGx5OiB0cnVlDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaW5pdEludHJvZHVjZVZpZGVvKCkNCiAgICB0aGlzLmluaXROb2RlRGF0YSgpDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0RGF0YSgpDQogICAgdGhpcy5pbml0RmlsZUxpc3RzKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXREYXRhKCkgew0KICAgICAgLy8g56Gu5L+dNUfmmbrmhafmqKHlnZfmnInpu5jorqTnu5PmnoTlubbmt7Hmi7fotJ3pgb/lhY3lvJXnlKjlhbHkuqsNCiAgICAgIGlmICghdGhpcy5ub2RlLndpc2RvbTVnKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLm5vZGUsICd3aXNkb201ZycsIHsNCiAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sS2V5OiAnJywNCiAgICAgICAgICBiYWNrZ3JvdW5kUmVzb3VyY2VzOiBbXSwNCiAgICAgICAgICBwYWluUG9pbnRzOiBbXQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5rex5ou36LSd546w5pyJ55qENUfmmbrmhafmlbDmja7vvIzpgb/lhY3lpJrkuKrlnLrmma/lhbHkuqvlvJXnlKgNCiAgICAgICAgY29uc3Qgd2lzZG9tNWdDb3B5ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLm5vZGUud2lzZG9tNWcpKQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAnd2lzZG9tNWcnLCB3aXNkb201Z0NvcHkpDQogICAgICAgIA0KICAgICAgICAvLyDkv67mraPlrZfmrrXmmKDlsIQNCiAgICAgICAgaWYgKHRoaXMubm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzKSB7DQogICAgICAgICAgdGhpcy5ub2RlLndpc2RvbTVnLmJhY2tncm91bmRSZXNvdXJjZXMuZm9yRWFjaChyZXNvdXJjZSA9PiB7DQogICAgICAgICAgICBpZiAocmVzb3VyY2UuYmFja2dyb3VuZEltZ0ZpbGVVcmwgJiYgIXJlc291cmNlLmJnSW1nKSB7DQogICAgICAgICAgICAgIHJlc291cmNlLmJnSW1nID0gcmVzb3VyY2UuYmFja2dyb3VuZEltZ0ZpbGVVcmwNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmIChyZXNvdXJjZS5iYWNrZ3JvdW5kRmlsZVVybCAmJiAhcmVzb3VyY2UuYmdGaWxlKSB7DQogICAgICAgICAgICAgIHJlc291cmNlLmJnRmlsZSA9IHJlc291cmNlLmJhY2tncm91bmRGaWxlVXJsDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAocmVzb3VyY2UudGFnICYmICFyZXNvdXJjZS5sYWJlbCkgew0KICAgICAgICAgICAgICByZXNvdXJjZS5sYWJlbCA9IHJlc291cmNlLnRhZw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5ZCM5qC35aSE55CG5Lyg57uf5qih5Z2XDQogICAgICBpZiAoIXRoaXMubm9kZS50cmFkaXRpb24pIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubm9kZSwgJ3RyYWRpdGlvbicsIHsNCiAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sS2V5OiAnJywNCiAgICAgICAgICBiYWNrZ3JvdW5kUmVzb3VyY2VzOiBbXSwNCiAgICAgICAgICBwYWluUG9pbnRzOiBbXQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5rex5ou36LSd5Lyg57uf5qih5Z2X5pWw5o2uDQogICAgICAgIGNvbnN0IHRyYWRpdGlvbkNvcHkgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMubm9kZS50cmFkaXRpb24pKQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAndHJhZGl0aW9uJywgdHJhZGl0aW9uQ29weSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWIneWni+WMluaWh+S7tuWIl+ihqA0KICAgIGluaXRGaWxlTGlzdHMoKSB7DQogICAgICAvLyDmuIXnqbrmiYDmnInmlofku7bliJfooajvvIzpgb/lhY3nu6fmib/pl67popgNCiAgICAgIHRoaXMuZmlsZUxpc3RzID0gew0KICAgICAgICB0cmFkaXRpb246IHt9LA0KICAgICAgICB3aXNkb201Zzoge30sDQogICAgICAgIGludHJvZHVjZVZpZGVvOiBbXQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDliJ3lp4vljJbkvKDnu5/mqKHlnZfnmoTmlofku7bliJfooagNCiAgICAgIGlmICh0aGlzLm5vZGUudHJhZGl0aW9uICYmIHRoaXMubm9kZS50cmFkaXRpb24uYmFja2dyb3VuZFJlc291cmNlcykgew0KICAgICAgICB0aGlzLm5vZGUudHJhZGl0aW9uLmJhY2tncm91bmRSZXNvdXJjZXMuZm9yRWFjaCgocmVzb3VyY2UsIGlkeCkgPT4gew0KICAgICAgICAgIC8vIOWPquacieW9k+i1hOa6kOehruWunuacieaWh+S7tuaXtuaJjeWIm+W7uuaWh+S7tuWIl+ihqA0KICAgICAgICAgIGlmIChyZXNvdXJjZS5iZ0ZpbGUgJiYgcmVzb3VyY2UuYmdGaWxlLnRyaW0oKSkgew0KICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSByZXNvdXJjZS5iZ0ZpbGUuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZmlsZUxpc3RzLnRyYWRpdGlvbiwgaWR4LCBbew0KICAgICAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICAgICAgdXJsOiByZXNvdXJjZS5iZ0ZpbGUsDQogICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKSArIGlkeA0KICAgICAgICAgICAgfV0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaYjuehruiuvue9ruS4uuepuuaVsOe7hA0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZmlsZUxpc3RzLnRyYWRpdGlvbiwgaWR4LCBbXSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWIneWni+WMljVH5pm65oWn5qih5Z2X55qE5paH5Lu25YiX6KGoDQogICAgICBpZiAodGhpcy5ub2RlLndpc2RvbTVnICYmIHRoaXMubm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzKSB7DQogICAgICAgIHRoaXMubm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzLmZvckVhY2goKHJlc291cmNlLCBpZHgpID0+IHsNCiAgICAgICAgICAvLyDlj6rmnInlvZPotYTmupDnoa7lrp7mnInmlofku7bml7bmiY3liJvlu7rmlofku7bliJfooagNCiAgICAgICAgICBpZiAocmVzb3VyY2UuYmdGaWxlICYmIHJlc291cmNlLmJnRmlsZS50cmltKCkpIHsNCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzb3VyY2UuYmdGaWxlLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZpbGVMaXN0cy53aXNkb201ZywgaWR4LCBbew0KICAgICAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICAgICAgdXJsOiByZXNvdXJjZS5iZ0ZpbGUsDQogICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKSArIGlkeCArIDEwMDANCiAgICAgICAgICAgIH1dKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDmmI7noa7orr7nva7kuLrnqbrmlbDnu4QNCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZpbGVMaXN0cy53aXNkb201ZywgaWR4LCBbXSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDliJ3lp4vljJbku4vnu43op4bpopHlr7nosaENCiAgICBpbml0SW50cm9kdWNlVmlkZW8oKSB7DQogICAgICBpZiAoIXRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLm5vZGUsICdpbnRyb2R1Y2VWaWRlb1ZvJywgew0KICAgICAgICAgIGlkOiAnJywNCiAgICAgICAgICB0eXBlOiAnJywNCiAgICAgICAgICB2aWV3SW5mb0lkOiAnJywNCiAgICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogJycsDQogICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6ICcnDQogICAgICAgIH0pDQogICAgICB9DQogICAgICAvLyDlrozlhajliKDpmaRzdGF0dXPnmoTph43mlrDorr7nva7vvIzkv53mjIHmjqXlj6Pov5Tlm57nmoTljp/lp4vlgLwNCiAgICAgIHRoaXMudXBkYXRlSW50cm9kdWNlVmlkZW9GaWxlTGlzdCgpDQogICAgfSwNCiAgICAvLyDnoa7kv53lhbbku5bmlbDmja7nu5PmnoTkuZ/mnInpu5jorqTlgLwNCiAgICBpbml0Tm9kZURhdGEoKSB7DQogICAgICAvLyDnoa7kv53ln7rnoYDlrZfmrrXmnInpu5jorqTlgLwgLSDlj6rlnKjnnJ/mraPmsqHmnInlgLzml7bmiY3orr7nva7pu5jorqTlgLwNCiAgICAgIGlmICh0aGlzLm5vZGUuaXNVbmZvbGQgPT09IHVuZGVmaW5lZCB8fCB0aGlzLm5vZGUuaXNVbmZvbGQgPT09IG51bGwgfHwgdGhpcy5ub2RlLmlzVW5mb2xkID09PSAnJykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAnaXNVbmZvbGQnLCAnMScpDQogICAgICB9DQogICAgICBpZiAodGhpcy5ub2RlLmRpc3BsYXlMb2NhdGlvbiA9PT0gdW5kZWZpbmVkIHx8IHRoaXMubm9kZS5kaXNwbGF5TG9jYXRpb24gPT09IG51bGwgfHwgdGhpcy5ub2RlLmRpc3BsYXlMb2NhdGlvbiA9PT0gJycpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubm9kZSwgJ2Rpc3BsYXlMb2NhdGlvbicsICcwJykNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLm5vZGUudHJlZUNsYXNzaWZpY2F0aW9uID09PSB1bmRlZmluZWQgfHwgdGhpcy5ub2RlLnRyZWVDbGFzc2lmaWNhdGlvbiA9PT0gbnVsbCB8fCB0aGlzLm5vZGUudHJlZUNsYXNzaWZpY2F0aW9uID09PSAnJykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAndHJlZUNsYXNzaWZpY2F0aW9uJywgJzMnKQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDnoa7kv53kvKDnu5/mqKHlnZfmnInpu5jorqTnu5PmnoQNCiAgICAgIGlmICghdGhpcy5ub2RlLnRyYWRpdGlvbikgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAndHJhZGl0aW9uJywgew0KICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxLZXk6ICcnLA0KICAgICAgICAgIGJhY2tncm91bmRSZXNvdXJjZXM6IFtdLA0KICAgICAgICAgIHBhaW5Qb2ludHM6IFtdDQogICAgICAgIH0pDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOehruS/nTVH5pm65oWn5qih5Z2X5pyJ6buY6K6k57uT5p6E5bm25L+u5q2j5a2X5q615pig5bCEDQogICAgICBpZiAoIXRoaXMubm9kZS53aXNkb201Zykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLCAnd2lzZG9tNWcnLCB7DQogICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgcGFub3JhbWljVmlld1htbEtleTogJycsDQogICAgICAgICAgYmFja2dyb3VuZFJlc291cmNlczogW10sDQogICAgICAgICAgcGFpblBvaW50czogW10NCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOS/ruatozVH5pm65oWn5qih5Z2X55qE5a2X5q615pig5bCEDQogICAgICAgIGlmICh0aGlzLm5vZGUud2lzZG9tNWcuYmFja2dyb3VuZFJlc291cmNlcykgew0KICAgICAgICAgIHRoaXMubm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzLmZvckVhY2gocmVzb3VyY2UgPT4gew0KICAgICAgICAgICAgLy8g56Gu5L+d5a2X5q615ZCN56ew5q2j56GuDQogICAgICAgICAgICBpZiAocmVzb3VyY2UuYmFja2dyb3VuZEltZ0ZpbGVVcmwgJiYgIXJlc291cmNlLmJnSW1nKSB7DQogICAgICAgICAgICAgIHJlc291cmNlLmJnSW1nID0gcmVzb3VyY2UuYmFja2dyb3VuZEltZ0ZpbGVVcmwNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmIChyZXNvdXJjZS5iYWNrZ3JvdW5kRmlsZVVybCAmJiAhcmVzb3VyY2UuYmdGaWxlKSB7DQogICAgICAgICAgICAgIHJlc291cmNlLmJnRmlsZSA9IHJlc291cmNlLmJhY2tncm91bmRGaWxlVXJsDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAocmVzb3VyY2UudGFnICYmICFyZXNvdXJjZS5sYWJlbCkgew0KICAgICAgICAgICAgICByZXNvdXJjZS5sYWJlbCA9IHJlc291cmNlLnRhZw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g56Gu5L+d5oiQ5pys6aKE5Lyw5pyJ6buY6K6k57uT5p6EDQogICAgICBpZiAoIXRoaXMubm9kZS5jb3N0RXN0aW1hdGUpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubm9kZSwgJ2Nvc3RFc3RpbWF0ZScsIHsNCiAgICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgICB0aXRsZTogJycsDQogICAgICAgICAgY29udGVudHM6IFtdDQogICAgICAgIH0pDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOehruS/neaIkOacrOmihOS8sOaciXN0YXR1c+Wtl+autQ0KICAgICAgaWYgKHRoaXMubm9kZS5jb3N0RXN0aW1hdGUgJiYgdGhpcy5ub2RlLmNvc3RFc3RpbWF0ZS5zdGF0dXMgPT09IHVuZGVmaW5lZCkgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlLmNvc3RFc3RpbWF0ZSwgJ3N0YXR1cycsICcwJykNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOW8gOWFs+iBlOWKqA0KICAgIG9uU3RhdHVzQ2hhbmdlKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PT0gJzAnKSB7DQogICAgICAgIC8vIOW8gOWQr+aXtumAkuW9kuW8gOWQr+aJgOacieeItue6pw0KICAgICAgICBsZXQgcCA9IHRoaXMucGFyZW50DQogICAgICAgIHdoaWxlIChwKSB7DQogICAgICAgICAgaWYgKHAuc3RhdHVzICE9PSAnMCcpIHAuc3RhdHVzID0gJzAnDQogICAgICAgICAgcCA9IHAucGFyZW50DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWFs+mXreaXtumAkuW9kuWFs+mXreaJgOacieWtkOe6pw0KICAgICAgICBmdW5jdGlvbiBjbG9zZUNoaWxkcmVuKG5vZGUpIHsNCiAgICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICAgICAgbm9kZS5jaGlsZHJlbi5mb3JFYWNoKGNoaWxkID0+IHsNCiAgICAgICAgICAgICAgY2hpbGQuc3RhdHVzID0gJzEnDQogICAgICAgICAgICAgIGNsb3NlQ2hpbGRyZW4oY2hpbGQpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBjbG9zZUNoaWxkcmVuKHRoaXMubm9kZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOmAkuW9kuafpeaJvuW5tuW8gOWQr+aJgOacieeItue6pw0KICAgIGZpbmRBbmRPcGVuUGFyZW50KGlkLCB0cmVlKSB7DQogICAgICBmdW5jdGlvbiBoZWxwZXIobm9kZXMsIHBhcmVudCkgew0KICAgICAgICBmb3IgKGxldCBub2RlIG9mIG5vZGVzKSB7DQogICAgICAgICAgaWYgKG5vZGUuaWQgPT09IGlkKSB7DQogICAgICAgICAgICBpZiAocGFyZW50KSBwYXJlbnQuc3RhdHVzID0gJzAnDQogICAgICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICAgICAgaWYgKGhlbHBlcihub2RlLmNoaWxkcmVuLCBub2RlKSkgew0KICAgICAgICAgICAgICBpZiAocGFyZW50KSBwYXJlbnQuc3RhdHVzID0gJzAnDQogICAgICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgaGVscGVyKHRyZWUsIG51bGwpDQogICAgfSwNCiAgICAvLyDmlrDlop7og4zmma/otYTmupDnrqHnkIbmlrnms5UNCiAgICBhZGRCYWNrZ3JvdW5kUmVzb3VyY2UodHlwZSkgew0KICAgICAgaWYgKCF0aGlzLm5vZGVbdHlwZV0uYmFja2dyb3VuZFJlc291cmNlcykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlW3R5cGVdLCAnYmFja2dyb3VuZFJlc291cmNlcycsIFtdKQ0KICAgICAgfQ0KICAgICAgDQogICAgICBjb25zdCBuZXdJZHggPSB0aGlzLm5vZGVbdHlwZV0uYmFja2dyb3VuZFJlc291cmNlcy5sZW5ndGgNCiAgICAgIA0KICAgICAgLy8g5Yib5bu65a6M5YWo54us56uL55qE5paw6LWE5rqQ5a+56LGh77yM5LiN5YyF5ZCr6buY6K6k5Z2Q5qCH57uEDQogICAgICBjb25zdCBuZXdSZXNvdXJjZSA9IHsgDQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICB0YWc6ICcnLCANCiAgICAgICAgc3RhdHVzOiAnJywNCiAgICAgICAgdHlwZTogJycsDQogICAgICAgIHZpZXdJbmZvSWQ6ICcnLA0KICAgICAgICBiZ0ltZzogJycsIA0KICAgICAgICBiZ0ZpbGU6ICcnLA0KICAgICAgICBjb29yZGluYXRlczogW10gLy8g5pS55Li656m65pWw57uE77yM5LiN6buY6K6k5re75Yqg5Z2Q5qCH57uEDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOS9v+eUqOa3seaLt+i0neehruS/neWvueixoeWujOWFqOeLrOeriw0KICAgICAgY29uc3QgaW5kZXBlbmRlbnRSZXNvdXJjZSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkobmV3UmVzb3VyY2UpKQ0KICAgICAgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXMucHVzaChpbmRlcGVuZGVudFJlc291cmNlKQ0KICAgICAgDQogICAgICAvLyDliJ3lp4vljJblr7nlupTnmoTni6znq4vmlofku7bliJfooagNCiAgICAgIHRoaXMuJHNldCh0aGlzLmZpbGVMaXN0c1t0eXBlXSwgbmV3SWR4LCBbXSkNCiAgICAgIGNvbnNvbGUubG9nKCfmt7vliqDmlrDog4zmma/otYTmupA6JywgaW5kZXBlbmRlbnRSZXNvdXJjZSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOWIoOmZpOiDjOaZr+i1hOa6kA0KICAgIGFzeW5jIHJlbW92ZUJhY2tncm91bmRSZXNvdXJjZSh0eXBlLCBpZHgpIHsNCiAgICAgIGNvbnN0IHJlc291cmNlID0gdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbaWR4XQ0KICAgICAgDQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTmraTog4zmma/otYTmupDlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oYXN5bmMgKCkgPT4gew0KICAgICAgICAvLyDlpoLmnpzog4zmma/otYTmupDmnIlJRO+8jOiwg+eUqOWIoOmZpOaOpeWPow0KICAgICAgICBpZiAocmVzb3VyY2UuaWQpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5Yig6Zmk6IOM5pmv6LWE5rqQ77yM6K+356iN5YCZLi4uIikNCiAgICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGJhY2tncm91bmRGaWxlRGVsKHsgaWQ6IHJlc291cmNlLmlkIH0pDQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXMuc3BsaWNlKGlkeCwgMSkNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfliKDpmaTlpLHotKUnKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlpLHotKUnKQ0KICAgICAgICAgIH0gZmluYWxseSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmsqHmnIlJROeahOaWsOiDjOaZr+i1hOa6kO+8jOebtOaOpeS7juaVsOe7hOS4reenu+mZpA0KICAgICAgICAgIHRoaXMubm9kZVt0eXBlXS5iYWNrZ3JvdW5kUmVzb3VyY2VzLnNwbGljZShpZHgsIDEpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOa3u+WKoOWdkOagh+e7hA0KICAgIGFkZENvb3JkaW5hdGUodHlwZSwgcmVzb3VyY2VJZHgpIHsNCiAgICAgIGNvbnN0IHJlc291cmNlID0gdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbcmVzb3VyY2VJZHhdDQogICAgICBpZiAoIXJlc291cmNlLmNvb3JkaW5hdGVzKSB7DQogICAgICAgIHRoaXMuJHNldChyZXNvdXJjZSwgJ2Nvb3JkaW5hdGVzJywgW10pDQogICAgICB9DQogICAgICByZXNvdXJjZS5jb29yZGluYXRlcy5wdXNoKHsgDQogICAgICAgIGlkOiAwLA0KICAgICAgICBmaWxlSWQ6IDAsDQogICAgICAgIHg6ICcnLCANCiAgICAgICAgeTogJycsIA0KICAgICAgICB3aWRlOiAnJywgDQogICAgICAgIGhpZ2g6ICcnLCANCiAgICAgICAgc2NlbmVJZDogJycsIA0KICAgICAgICBzY2VuZUNvZGU6ICcnLA0KICAgICAgICB4bWxLZXk6ICcnDQogICAgICB9KQ0KICAgIH0sDQogICAgDQogICAgLy8g5Yig6Zmk5Z2Q5qCH57uEDQogICAgYXN5bmMgcmVtb3ZlQ29vcmRpbmF0ZSh0eXBlLCByZXNvdXJjZUlkeCwgY29vcmRJZHgpIHsNCiAgICAgIGNvbnN0IGNvb3JkID0gdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbcmVzb3VyY2VJZHhdLmNvb3JkaW5hdGVzW2Nvb3JkSWR4XQ0KICAgICAgDQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTmraTlnZDmoIfnu4TlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oYXN5bmMgKCkgPT4gew0KICAgICAgICAvLyDlpoLmnpzlnZDmoIfnu4TmnIlJRO+8jOiwg+eUqOWIoOmZpOaOpeWPow0KICAgICAgICBpZiAoY29vcmQuaWQpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5Yig6Zmk5Z2Q5qCH57uE77yM6K+356iN5YCZLi4uIikNCiAgICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZpbGVCaW5kRGVsKHsgaWQ6IGNvb3JkLmlkIH0pDQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbcmVzb3VyY2VJZHhdLmNvb3JkaW5hdGVzLnNwbGljZShjb29yZElkeCwgMSkNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfliKDpmaTlpLHotKUnKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlpLHotKUnKQ0KICAgICAgICAgIH0gZmluYWxseSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmsqHmnIlJROeahOaWsOWdkOagh+e7hO+8jOebtOaOpeS7juaVsOe7hOS4reenu+mZpA0KICAgICAgICAgIHRoaXMubm9kZVt0eXBlXS5iYWNrZ3JvdW5kUmVzb3VyY2VzW3Jlc291cmNlSWR4XS5jb29yZGluYXRlcy5zcGxpY2UoY29vcmRJZHgsIDEpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOWcuuaZr+e6p+iBlOmAieaLqeWZqOWPmOWMluWkhOeQhg0KICAgIGhhbmRsZVNjZW5lQ2FzY2FkZXJDaGFuZ2UodmFsLCB0eXBlLCByZXNvdXJjZUlkeCwgY29vcmRJZHgpIHsNCiAgICAgIGNvbnN0IHNjZW5lID0gdGhpcy5maW5kU2NlbmVCeUlkKHRoaXMuc2NlbmVUcmVlT3B0aW9ucywgdmFsKQ0KICAgICAgaWYgKHNjZW5lKSB7DQogICAgICAgIGNvbnN0IGNvb3JkID0gdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbcmVzb3VyY2VJZHhdLmNvb3JkaW5hdGVzW2Nvb3JkSWR4XQ0KICAgICAgICBjb29yZC5zY2VuZUNvZGUgPSBzY2VuZS5zY2VuZUNvZGUgfHwgJycNCiAgICAgICAgY29uc29sZS5sb2coJ+mAieaLqeeahOWcuuaZrzonLCBzY2VuZSwgJ+iuvue9rnNjZW5lQ29kZTonLCBjb29yZC5zY2VuZUNvZGUpDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDmoLnmja5JROafpeaJvuWcuuaZrw0KICAgIGZpbmRTY2VuZUJ5SWQodHJlZSwgaWQpIHsNCiAgICAgIGlmICghdHJlZSB8fCAhQXJyYXkuaXNBcnJheSh0cmVlKSkgcmV0dXJuIG51bGwNCiAgICAgIA0KICAgICAgZm9yIChjb25zdCBub2RlIG9mIHRyZWUpIHsNCiAgICAgICAgaWYgKG5vZGUuaWQgPT09IGlkKSB7DQogICAgICAgICAgcmV0dXJuIG5vZGUNCiAgICAgICAgfQ0KICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICAgIGNvbnN0IGZvdW5kID0gdGhpcy5maW5kU2NlbmVCeUlkKG5vZGUuY2hpbGRyZW4sIGlkKQ0KICAgICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBudWxsDQogICAgfSwNCiAgICANCiAgICAvLyDmoLzlvI/ljJblnZDmoIfmlbDmja7nlKjkuo7mj5DkuqQNCiAgICBmb3JtYXRDb29yZGluYXRlc0ZvclN1Ym1pdChjb29yZGluYXRlcykgew0KICAgICAgaWYgKCFjb29yZGluYXRlcyB8fCAhQXJyYXkuaXNBcnJheShjb29yZGluYXRlcykpIHsNCiAgICAgICAgcmV0dXJuIHsgY2xpY2tYOiAnJywgY2xpY2tZOiAnJyB9DQogICAgICB9DQogICAgICANCiAgICAgIGNvbnN0IHhWYWx1ZXMgPSBjb29yZGluYXRlcy5tYXAoY29vcmQgPT4gY29vcmQueCB8fCAnMCcpLmpvaW4oJywnKQ0KICAgICAgY29uc3QgeVZhbHVlcyA9IGNvb3JkaW5hdGVzLm1hcChjb29yZCA9PiBjb29yZC55IHx8ICcwJykuam9pbignLCcpDQogICAgICANCiAgICAgIHJldHVybiB7DQogICAgICAgIGNsaWNrWDogeFZhbHVlcywNCiAgICAgICAgY2xpY2tZOiB5VmFsdWVzDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDkv67mlLnnjrDmnInnmoTkuIrkvKDmlrnms5UNCiAgICBhc3luYyBiZWZvcmVVcGxvYWRTY2VuZUNvbmZpZ0ltZyhmaWxlLCBub2RlLCB0eXBlLCBhcnJheUtleU9yS2V5LCBpbmRleE9yVW5kZWZpbmVkLCBrZXlPclVuZGVmaW5lZCkgew0KICAgICAgaWYgKCFmaWxlLnR5cGUuc3RhcnRzV2l0aCgnaW1hZ2UvJykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5Lyg5Zu+54mH5paH5Lu277yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4iuS8oOWbvueJh++8jOivt+eojeWAmS4uLiIpDQogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdpbmR1c3RyeUNvZGUnLCB0aGlzLmxlZnRUcmVlSW5kdXN0cnlDb2RlKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ3NjZW5lQ29kZScsIG5vZGUuY29kZSkNCg0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCB1cGxvYWRTY2VuZUZpbGUoZm9ybURhdGEpDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCAmJiByZXMuZGF0YSkgew0KICAgICAgICAgIC8vIOWNleeLrOS4iuS8oOiDjOaZr+WbvueJh+mmluW4p+aXtu+8jOS9v+eUqCBmaWxlVXJsDQogICAgICAgICAgY29uc3QgaW1hZ2VVcmwgPSByZXMuZGF0YS5maWxlVXJsDQogICAgICAgICAgDQogICAgICAgICAgLy8g5Yik5pat5piv5ZCm5Li65pWw57uE5b2i5byP55qE5LiK5LygDQogICAgICAgICAgaWYgKHR5cGVvZiBpbmRleE9yVW5kZWZpbmVkID09PSAnbnVtYmVyJyAmJiBrZXlPclVuZGVmaW5lZCkgew0KICAgICAgICAgICAgLy8g5pWw57uE5b2i5byP77yabm9kZVt0eXBlXVthcnJheUtleV1baW5kZXhdW2tleV0NCiAgICAgICAgICAgIHRoaXMuJHNldChub2RlW3R5cGVdW2FycmF5S2V5T3JLZXldW2luZGV4T3JVbmRlZmluZWRdLCBrZXlPclVuZGVmaW5lZCwgaW1hZ2VVcmwpDQogICAgICAgICAgICBjb25zb2xlLmxvZygn5LiK5Lyg5oiQ5Yqf77yM6K6+572u5Zu+54mHVVJMOicsIGltYWdlVXJsKQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ+W9k+WJjXJlc291cmNlOicsIG5vZGVbdHlwZV1bYXJyYXlLZXlPcktleV1baW5kZXhPclVuZGVmaW5lZF0pDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWNleS4quWtl+auteW9ouW8j++8mm5vZGVbdHlwZV1ba2V5XQ0KICAgICAgICAgICAgdGhpcy4kc2V0KG5vZGVbdHlwZV0sIGFycmF5S2V5T3JLZXksIGltYWdlVXJsKQ0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip8nKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+S4iuS8oOWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S4iuS8oOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIA0KICAgIGFzeW5jIGJlZm9yZVVwbG9hZFNjZW5lQ29uZmlnRmlsZShmaWxlLCBub2RlLCB0eXBlLCBhcnJheUtleU9yS2V5LCBpbmRleE9yVW5kZWZpbmVkLCBrZXlPclVuZGVmaW5lZCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5LiK5Lyg5paH5Lu277yM6K+356iN5YCZLi4uIikNCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2luZHVzdHJ5Q29kZScsIHRoaXMubGVmdFRyZWVJbmR1c3RyeUNvZGUpDQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnc2NlbmVDb2RlJywgbm9kZS5jb2RlKQ0KDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZFNjZW5lRmlsZShmb3JtRGF0YSkNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgLy8g5Yik5pat5piv5ZCm5Li65pWw57uE5b2i5byP55qE5LiK5LygDQogICAgICAgICAgaWYgKHR5cGVvZiBpbmRleE9yVW5kZWZpbmVkID09PSAnbnVtYmVyJyAmJiBrZXlPclVuZGVmaW5lZCkgew0KICAgICAgICAgICAgLy8g5pWw57uE5b2i5byP5aSE55CGLi4uDQogICAgICAgICAgICB0aGlzLiRzZXQobm9kZVt0eXBlXVthcnJheUtleU9yS2V5XVtpbmRleE9yVW5kZWZpbmVkXSwga2V5T3JVbmRlZmluZWQsIHJlcy5kYXRhLmZpbGVVcmwpDQogICAgICAgICAgICANCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzLmRhdGEuZmlsZVVybC5zcGxpdCgnLycpLnBvcCgpDQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5maWxlTGlzdHNbdHlwZV0sIGluZGV4T3JVbmRlZmluZWQsIFt7DQogICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICB1cmw6IHJlcy5kYXRhLmZpbGVVcmwsDQogICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICAgICAgfV0pDQogICAgICAgICAgICANCiAgICAgICAgICAgIGlmIChmaWxlLnR5cGUgPT09ICd2aWRlby9tcDQnICYmIHJlcy5kYXRhLmltZ1VybCkgew0KICAgICAgICAgICAgICB0aGlzLiRzZXQobm9kZVt0eXBlXVthcnJheUtleU9yS2V5XVtpbmRleE9yVW5kZWZpbmVkXSwgJ2JnSW1nJywgcmVzLmRhdGEuaW1nVXJsKQ0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKn++8jOW3suiHquWKqOeUn+aIkOiDjOaZr+WbvueJh+mmluW4pycpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWNleS4quWtl+auteW9ouW8j++8mm5vZGVbdHlwZV1ba2V5XQ0KICAgICAgICAgICAgdGhpcy4kc2V0KG5vZGVbdHlwZV0sIGFycmF5S2V5T3JLZXksIHJlcy5kYXRhLmZpbGVVcmwpDQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOWmguaenOaYr+S7i+e7jeinhumikeS4iuS8oA0KICAgICAgICAgICAgaWYgKHR5cGUgPT09ICdpbnRyb2R1Y2VWaWRlb1ZvJyAmJiBhcnJheUtleU9yS2V5ID09PSAnYmFja2dyb3VuZEZpbGVVcmwnKSB7DQogICAgICAgICAgICAgIC8vIOabtOaWsOS7i+e7jeinhumikeaWh+S7tuWIl+ihqA0KICAgICAgICAgICAgICB0aGlzLnVwZGF0ZUludHJvZHVjZVZpZGVvRmlsZUxpc3QoKQ0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgaWYgKGZpbGUudHlwZSA9PT0gJ3ZpZGVvL21wNCcgJiYgcmVzLmRhdGEuaW1nVXJsKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kc2V0KG5vZGVbdHlwZV0sICdiYWNrZ3JvdW5kSW1nRmlsZVVybCcsIHJlcy5kYXRhLmltZ1VybCkNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKn++8jOW3suiHquWKqOeUn+aIkOS7i+e7jeinhumikemmluW4pycpDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip8nKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5LiK5Lyg5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIGFkZFBhaW5Qb2ludCh0eXBlKSB7DQogICAgICB0aGlzLm5vZGVbdHlwZV0ucGFpblBvaW50cyA9IHRoaXMubm9kZVt0eXBlXS5wYWluUG9pbnRzIHx8IFtdDQogICAgICB0aGlzLm5vZGVbdHlwZV0ucGFpblBvaW50cy5wdXNoKHsgdGl0bGU6ICcnLCBjb250ZW50czogWycnXSwgc2hvd1RpbWU6ICcnIH0pDQogICAgfSwNCiAgICByZW1vdmVQYWluUG9pbnQodHlwZSwgaWR4KSB7DQogICAgICB0aGlzLm5vZGVbdHlwZV0ucGFpblBvaW50cy5zcGxpY2UoaWR4LCAxKQ0KICAgIH0sDQogICAgYWRkUGFpbkNvbnRlbnQodHlwZSwgaWR4KSB7DQogICAgICB0aGlzLm5vZGVbdHlwZV0ucGFpblBvaW50c1tpZHhdLmNvbnRlbnRzLnB1c2goJycpDQogICAgfSwNCiAgICByZW1vdmVQYWluQ29udGVudCh0eXBlLCBpZHgsIGNpZHgpIHsNCiAgICAgIHRoaXMubm9kZVt0eXBlXS5wYWluUG9pbnRzW2lkeF0uY29udGVudHMuc3BsaWNlKGNpZHgsIDEpDQogICAgfSwNCiAgICBhZGRDb3N0Q29udGVudCgpIHsNCiAgICAgIHRoaXMubm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMucHVzaCgnJykNCiAgICB9LA0KICAgIHJlbW92ZUNvc3RDb250ZW50KGNpZHgpIHsNCiAgICAgIHRoaXMubm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMuc3BsaWNlKGNpZHgsIDEpDQogICAgfSwNCiAgICAvLyDojrflj5bog4zmma/mlofku7bliJfooagNCiAgICBnZXRCYWNrZ3JvdW5kRmlsZUxpc3Qobm9kZSwgdHlwZSwgaWR4KSB7DQogICAgICBjb25zdCByZXNvdXJjZSA9IG5vZGVbdHlwZV0uYmFja2dyb3VuZFJlc291cmNlc1tpZHhdDQogICAgICBpZiAocmVzb3VyY2UgJiYgcmVzb3VyY2UuYmdGaWxlKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzb3VyY2UuYmdGaWxlLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgcmV0dXJuIFt7DQogICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgdXJsOiByZXNvdXJjZS5iZ0ZpbGUsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpICsgaWR4DQogICAgICAgIH1dDQogICAgICB9DQogICAgICByZXR1cm4gW10NCiAgICB9LA0KICAgIC8vIOWkhOeQhuiDjOaZr+aWh+S7tuWIoOmZpA0KICAgIGhhbmRsZVJlbW92ZUJhY2tncm91bmRGaWxlKG5vZGUsIHR5cGUsIGlkeCkgew0KICAgICAgY29uc3QgcmVzb3VyY2UgPSBub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbaWR4XQ0KICAgICAgcmVzb3VyY2UuYmdGaWxlID0gJycNCiAgICAgIHJlc291cmNlLmJnSW1nID0gJycgLy8g5ZCM5pe25riF56m66IOM5pmv5Zu+54mH6aaW5binDQogICAgICB0aGlzLiRzZXQodGhpcy5maWxlTGlzdHNbdHlwZV0sIGlkeCwgW10pDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuW3suWIoOmZpCcpDQogICAgfSwNCiAgICAvLyDojrflj5bmlofku7bliJfooaggLSDnoa7kv53ov5Tlm57mraPnoa7nmoTmlofku7bliJfooagNCiAgICBnZXRGaWxlTGlzdCh0eXBlLCBpZHgpIHsNCiAgICAgIC8vIOajgOafpeWvueW6lOeahOi1hOa6kOaYr+WQpuecn+eahOacieaWh+S7tg0KICAgICAgY29uc3QgcmVzb3VyY2UgPSB0aGlzLm5vZGVbdHlwZV0gJiYgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXMgJiYgdGhpcy5ub2RlW3R5cGVdLmJhY2tncm91bmRSZXNvdXJjZXNbaWR4XQ0KICAgICAgDQogICAgICBpZiAoIXJlc291cmNlIHx8ICFyZXNvdXJjZS5iZ0ZpbGUgfHwgIXJlc291cmNlLmJnRmlsZS50cmltKCkpIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5paH5Lu277yM6L+U5Zue56m65pWw57uEDQogICAgICAgIHJldHVybiBbXQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlpoLmnpzmlofku7bliJfooajkuK3mnInmlbDmja7kuJTkuI7otYTmupDmlofku7bljLnphY3vvIzov5Tlm57mlofku7bliJfooagNCiAgICAgIGlmICh0aGlzLmZpbGVMaXN0c1t0eXBlXSAmJiB0aGlzLmZpbGVMaXN0c1t0eXBlXVtpZHhdICYmIHRoaXMuZmlsZUxpc3RzW3R5cGVdW2lkeF0ubGVuZ3RoID4gMCkgew0KICAgICAgICBjb25zdCBmaWxlTGlzdCA9IHRoaXMuZmlsZUxpc3RzW3R5cGVdW2lkeF0NCiAgICAgICAgLy8g6aqM6K+B5paH5Lu25YiX6KGo5Lit55qEVVJM5piv5ZCm5LiO6LWE5rqQ5Lit55qE5paH5Lu2VVJM5Yy56YWNDQogICAgICAgIGlmIChmaWxlTGlzdFswXS51cmwgPT09IHJlc291cmNlLmJnRmlsZSkgew0KICAgICAgICAgIHJldHVybiBmaWxlTGlzdA0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWmguaenOaWh+S7tuWIl+ihqOS4jeWMuemFjeaIluS4uuepuu+8jOS9hui1hOa6kOacieaWh+S7tu+8jOmHjeaWsOWIm+W7uuaWh+S7tuWIl+ihqA0KICAgICAgaWYgKHJlc291cmNlLmJnRmlsZSAmJiByZXNvdXJjZS5iZ0ZpbGUudHJpbSgpKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzb3VyY2UuYmdGaWxlLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgY29uc3QgbmV3RmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogcmVzb3VyY2UuYmdGaWxlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5maWxlTGlzdHNbdHlwZV0sIGlkeCwgbmV3RmlsZUxpc3QpDQogICAgICAgIHJldHVybiBuZXdGaWxlTGlzdA0KICAgICAgfQ0KICAgICAgDQogICAgICByZXR1cm4gW10NCiAgICB9LA0KICAgIGdldFVwbG9hZE1vZGUodHlwZSwgaWR4KSB7DQogICAgICBpZiAoIXRoaXMudXBsb2FkTW9kZXNbdHlwZV1baWR4XSkgew0KICAgICAgICAvLyDpu5jorqTmoLnmja7mmK/lkKblt7LmnInmlofku7ZVUkzmnaXliKTmlq3mqKHlvI8NCiAgICAgICAgY29uc3QgcmVzb3VyY2UgPSB0aGlzLm5vZGVbdHlwZV0uYmFja2dyb3VuZFJlc291cmNlc1tpZHhdDQogICAgICAgIGNvbnN0IGhhc0ZpbGUgPSByZXNvdXJjZSAmJiByZXNvdXJjZS5iZ0ZpbGUNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMudXBsb2FkTW9kZXNbdHlwZV0sIGlkeCwgaGFzRmlsZSA/ICd1cGxvYWQnIDogJ3VwbG9hZCcpDQogICAgICB9DQogICAgICByZXR1cm4gdGhpcy51cGxvYWRNb2Rlc1t0eXBlXVtpZHhdDQogICAgfSwNCiAgICBzZXRVcGxvYWRNb2RlKHR5cGUsIGlkeCwgdmFsdWUpIHsNCiAgICAgIGlmICh0eXBlb2YgaWR4ID09PSAnc3RyaW5nJykgew0KICAgICAgICAvLyDku4vnu43op4bpopHmqKHlvI/vvJp0eXBlPSdpbnRyb2R1Y2VWaWRlbycsIGlkeD0ndXBsb2FkJ+aIlid1cmwnDQogICAgICAgIHRoaXMuJHNldCh0aGlzLnVwbG9hZE1vZGVzLCB0eXBlLCBpZHgpDQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDog4zmma/otYTmupDmqKHlvI/vvJp0eXBlPSd0cmFkaXRpb24n5oiWJ3dpc2RvbTVnJywgaWR4PeaVsOWtl+e0ouW8lSwgdmFsdWU9J3VwbG9hZCfmiJYndXJsJw0KICAgICAgICB0aGlzLiRzZXQodGhpcy51cGxvYWRNb2Rlc1t0eXBlXSwgaWR4LCB2YWx1ZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVVybElucHV0KHR5cGUsIGlkeCwgdmFsdWUpIHsNCiAgICAgIC8vIOa4heepuuaWh+S7tuWIl+ihqA0KICAgICAgdGhpcy4kc2V0KHRoaXMuZmlsZUxpc3RzW3R5cGVdLCBpZHgsIFtdKQ0KICAgICAgDQogICAgICAvLyDlpoLmnpzovpPlhaXkuobpk77mjqXvvIzliJvlu7rlr7nlupTnmoTmlofku7bliJfooajpobkNCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZmlsZUxpc3RzW3R5cGVdLCBpZHgsIFt7DQogICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgdXJsOiB2YWx1ZSwNCiAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgfV0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmm7TmlrDku4vnu43op4bpopHmlofku7bliJfooagNCiAgICB1cGRhdGVJbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0KCkgew0KICAgICAgaWYgKHRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvICYmIHRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmJhY2tncm91bmRGaWxlVXJsKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gdGhpcy5ub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEZpbGVVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdGhpcy5ub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEZpbGVVcmwsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0SW50cm9kdWNlVmlkZW9GaWxlTGlzdCgpIHsNCiAgICAgIHJldHVybiB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QNCiAgICB9LA0KICAgIGhhbmRsZVJlbW92ZUludHJvZHVjZVZpZGVvKCkgew0KICAgICAgaWYgKHRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvKSB7DQogICAgICAgIHRoaXMubm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmJhY2tncm91bmRGaWxlVXJsID0gJycNCiAgICAgICAgdGhpcy5ub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEltZ0ZpbGVVcmwgPSAnJw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S7i+e7jeinhumikeW3suWIoOmZpCcpDQogICAgICAgIC8vIOWQjOaXtuabtOaWsOaWh+S7tuWIl+ihqA0KICAgICAgICB0aGlzLnVwZGF0ZUludHJvZHVjZVZpZGVvRmlsZUxpc3QoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5LuL57uN6KeG6aKR5byA5YWz5Y+Y5YyWDQogICAgb25JbnRyb2R1Y2VWaWRlb1N0YXR1c0NoYW5nZSh2YWwpIHsNCiAgICAgIC8vIOeugOWNleWkhOeQhu+8jOS4jemcgOimgeWkjeadgumAu+i+kQ0KICAgIH0sDQogICAgDQogICAgLy8g5Zu+54mH6aKE6KeIDQogICAgcHJldmlld0ltYWdlKHVybCkgew0KICAgICAgaWYgKHVybCkgew0KICAgICAgICB0aGlzLnByZXZpZXdJbWFnZVVybCA9IHVybA0KICAgICAgICB0aGlzLnByZXZpZXdWaXNpYmxlID0gdHJ1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5Yig6Zmk5LuL57uN6KeG6aKR5Zu+54mHDQogICAgZGVsZXRlSW50cm9kdWNlVmlkZW9JbWcoKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTmraTlm77niYflkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLm5vZGUuaW50cm9kdWNlVmlkZW9Wby5iYWNrZ3JvdW5kSW1nRmlsZVVybCA9ICcnDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Zu+54mH5bey5Yig6ZmkJykNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQ0KICAgIH0sDQogICAgDQogICAgLy8g5Yig6Zmk6IOM5pmv6LWE5rqQ5Zu+54mHDQogICAgZGVsZXRlQmFja2dyb3VuZEltZyh0eXBlLCBpZHgpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuWIoOmZpOatpOWbvueJh+WQl++8nycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMubm9kZVt0eXBlXS5iYWNrZ3JvdW5kUmVzb3VyY2VzW2lkeF0uYmdJbWcgPSAnJw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WbvueJh+W3suWIoOmZpCcpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIC8vIOmqjOivgVhNTCBLZXnovpPlhaXvvIjlj6rlhYHorrjoi7HmlofjgIHmlbDlrZflkozkuIvliJLnur/vvIkNCiAgICB2YWxpZGF0ZVhtbEtleSh2YWx1ZSwgdHlwZSkgew0KICAgICAgLy8g5Y+q5L+d55WZ6Iux5paH5a2X5q+N44CB5pWw5a2X5ZKM5LiL5YiS57q/DQogICAgICBjb25zdCBmaWx0ZXJlZFZhbHVlID0gdmFsdWUucmVwbGFjZSgvW15hLXpBLVowLTlfXS9nLCAnJykNCiAgICAgIHRoaXMubm9kZVt0eXBlXS5wYW5vcmFtaWNWaWV3WG1sS2V5ID0gZmlsdGVyZWRWYWx1ZQ0KICAgIH0sDQogICAgaGFuZGxlSW50cm9kdWNlVmlkZW9VcmxJbnB1dCh2YWx1ZSkgew0KICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW10NCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHZhbHVlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgICAgdGhpcy51cGRhdGVJbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0KCkNCiAgICB9LA0KICAgIC8vIOWkhOeQhuWdkOagh+aVsOWtl+i+k+WFpQ0KICAgIGhhbmRsZUNvb3JkTnVtYmVySW5wdXQodmFsLCBjb29yZCwgZmllbGQpIHsNCiAgICAgIC8vIOWmguaenHZhbOS4um51bGzmiJZ1bmRlZmluZWTvvIzorr7nva7kuLrnqbrlrZfnrKbkuLINCiAgICAgIGlmICh2YWwgPT09IG51bGwgfHwgdmFsID09PSB1bmRlZmluZWQpIHsNCiAgICAgICAgY29vcmRbZmllbGRdID0gJycNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvb3JkW2ZpZWxkXSA9IHZhbA0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDliKTmlq3lvZPliY3oioLngrnmmK/lkKbmnInlrZDoioLngrkNCiAgICBoYXNDaGlsZHJlbigpIHsNCiAgICAgIHJldHVybiB0aGlzLm5vZGUgJiYgdGhpcy5ub2RlLmNoaWxkcmVuICYmIHRoaXMubm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwDQogICAgfQ0KICB9LA0KICBjb21wb25lbnRzOiB7DQogICAgU2NlbmVDb25maWdOb2RlOiBudWxsIC8vIOmAkuW9kuazqOWGjO+8jOS4u+mhtemdomltcG9ydOaXtuihpeWFqA0KICB9DQp9DQo="}, null]}