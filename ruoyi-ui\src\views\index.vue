<template>
  <div class="page-container">
    <!-- ✅ 左侧菜单区域 -->
    <div class="menu-panel">
      <!-- 搜索框 -->
      <div class="menu-search">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索菜单..."
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
        />
      </div>
      
      <el-tree
        ref="menuTree"
        :data="filteredMenuData"
        :props="{ label: 'name', children: 'children' }"
        node-key="id"
        :current-node-key="activeMenu"
        @node-click="handleTreeNodeClick"
        highlight-current
        :expand-on-click-node="false"
        :default-expanded-keys="menuData.map(item => item.id)"
        class="menu-tree"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span v-html="highlightText(data.name)"></span>
        </span>
      </el-tree>
    </div>

    <!-- ✅ 右侧内容区域 -->
    <div class="content-panel" :class="{ 'loading-no-scroll': switchingIndustry }" v-loading="switchingIndustry" element-loading-text="正在切换行业...">
      <el-form :model="form" ref="sceneForm" label-width="120px">
        <el-row :gutter="20">
          <!-- 左侧：基本信息 -->
          <el-col :span="12">
            <el-card class="mini-block" shadow="never">
              <div slot="header">基本信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="主标题" required>
                    <el-input v-model="form.mainTitle" placeholder="请输入主标题" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="副标题" required>
                    <el-input v-model="form.subTitle" placeholder="请输入副标题" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="背景图片首帧">
                    <el-upload
                      class="upload image-upload"
                      action="#"
                      :show-file-list="false"
                      list-type="picture-card"
                      accept="image/*"
                      :before-upload="beforeUploadIntroduceImg"
                      :http-request="() => {}"
                    >
                      <div v-if="form.bgImgUrl" class="image-preview-container">
                        <img :src="form.bgImgUrl" class="upload-image" />
                        <div class="image-overlay">
                          <i class="el-icon-zoom-in preview-icon" @click.stop="previewImage(form.bgImgUrl)" title="预览"></i>
                          <i class="el-icon-delete delete-icon" @click.stop="deleteBgImage" title="删除"></i>
                        </div>
                      </div>
                      <i v-else class="el-icon-plus"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="主题选择">
                    <theme-selection-dialog 
                      v-model="selectedTheme"
                      @change="onThemeChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="背景文件">
                    <div style="margin-bottom: 8px;">
                      <el-radio-group v-model="uploadModes.bgFile || 'upload'" @input="value => setUploadMode('bgFile', value)" size="small">
                        <el-radio-button label="upload">上传文件</el-radio-button>
                        <el-radio-button label="url">填写链接</el-radio-button>
                      </el-radio-group>
                    </div>
                    
                    <!-- 上传模式 -->
                    <el-upload
                      v-if="(uploadModes.bgFile || 'upload') === 'upload'"
                      class="upload"
                      action="#"
                      :show-file-list="true"
                      :file-list="bgFileList"
                      :before-upload="file => beforeUploadIntroduceVideo(file)"
                      :http-request="() => {}"
                      :on-remove="handleRemoveBgFile"
                    >
                      <el-button type="primary">上传背景文件</el-button>
                    </el-upload>
                    
                    <!-- 链接模式 -->
                    <el-input
                      v-else
                      v-model="form.bgFileUrl"
                      placeholder="请输入文件链接"
                      @input="handleBgFileUrlInput"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="XML文件">
                    <el-upload
                      class="upload"
                      action="#"
                      :show-file-list="true"
                      :file-list="xmlFileList"
                      accept=".xml"
                      :before-upload="beforeUploadXmlFile"
                      :http-request="() => {}"
                      :on-remove="handleRemoveXmlFile"
                    >
                      <el-button type="primary">上传XML文件</el-button>
                      <div slot="tip" class="el-upload__tip">只能上传xml文件，且不超过50MB</div>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
          
          <!-- 右侧：视频讲解 -->
          <el-col :span="12">
            <el-card class="mini-block video-card" shadow="never">
              <div slot="header" class="video-header-row">
                <span>视频讲解</span>
                <el-switch v-model="videoExplanation.status" :active-value="'0'" :inactive-value="'1'" style="float:right;" />
              </div>
              <div v-show="videoExplanation.status === '0'">
              <el-collapse-transition>
                <div class="video-card-yu">
                  <el-form-item label="讲解视频">
                    <div style="margin-bottom: 8px;">
                      <el-radio-group v-model="uploadModes.videoExplanation || 'upload'" @input="value => setUploadMode('videoExplanation', value)" size="small">
                        <el-radio-button label="upload">上传文件</el-radio-button>
                        <el-radio-button label="url">填写链接</el-radio-button>
                      </el-radio-group>
                    </div>
                    
                    <!-- 上传模式 -->
                    <el-upload
                      v-if="(uploadModes.videoExplanation || 'upload') === 'upload'"
                      action="#"
                      :show-file-list="true"
                      :file-list="videoExplanationFileList"
                      accept=".mp4"
                      :before-upload="file => beforeUploadIntroduceVideo(file, 'videoExplanation', 'backgroundFileUrl')"
                      :http-request="() => {}"
                      :on-remove="handleRemoveVideoExplanationFile"
                    >
                      <el-button size="small" type="primary">点击上传</el-button>
                      <div slot="tip" class="el-upload__tip">只能上传mp4文件</div>
                    </el-upload>
                    
                    <!-- 链接模式 -->
                    <el-input
                      v-else
                      v-model="videoExplanation.backgroundFileUrl"
                      placeholder="请输入视频链接"
                      @input="handleVideoExplanationUrlInput"
                    />
                  </el-form-item>
                  <el-form-item label="视频分段说明">
                    <div class="segment-scroll">
                      <div v-for="(seg, idx) in videoSegmentedList" :key="idx" style="display:flex;align-items:center;margin-bottom:8px;">
                        <el-input-number
                          v-model="seg.time"
                          :min="0"
                          :max="999999"
                          placeholder="时间"
                          style="width: 120px; margin-right: 10px;"
                          @change="val => handleTimeChange(val, idx)"
                        />
                        <span style="margin-right: 10px; color: #606266;">秒</span>
                        <el-cascader
                          v-model="seg.sceneId"
                          :options="sceneTreeOptions"
                          :props="sceneCascaderProps"
                          filterable
                          check-strictly
                          placeholder="所属场景"
                          style="width: 200px; margin-right: 10px;"
                          @change="val => handleSceneCascaderChange(val, idx)"
                        />
                        <el-button type="danger" icon="el-icon-delete" circle @click="removeVideoSegment(idx)" />
                      </div>
                    </div>
                    <el-button type="primary" plain @click="addVideoSegment" style="margin-top: 8px;">增加分段</el-button>
                  </el-form-item>
                </div>
              </el-collapse-transition>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form>

      <!-- ✅ 分类区域（支持折叠） -->
      <div
        v-for="(category, index) in categories"
        :key="category.key"
        class="category-block"
      >
        <div class="category-header">
          <div class="category-title" @dblclick="startEditTitle(index)">
            <el-input
              v-if="category.editing"
              v-model="category.editingName"
              @blur="finishEditTitle(index)"
              @keyup.enter="finishEditTitle(index)"
              @keyup.esc="cancelEditTitle(index)"
              :data-edit-index="index"
              size="small"
              style="width: 200px;"
              placeholder="请输入标题"
            />
            <span v-else>{{ category.name }}</span>
          </div>
          <el-switch
            v-model="category.enabled"
            active-color="#13ce66"
            inactive-color="#ccc"
          />
        </div>

        <div v-show="category.enabled" class="category-body">
          <div v-if="category.key === 'default_scene'">
            <el-form label-width="120px">
              <el-row :gutter="20">
                <el-col :span="24">
                  <!-- 场景配置分块 -->
                  <el-card class="mini-block scene-config-container" shadow="never" style="margin-top: 20px;">
                    <div slot="header">场景配置</div>
                    <div>
                      <el-row :gutter="20">
                        <el-col :span="6">
                          <el-tree
                            ref="sceneTree"
                            :data="sceneConfigTree"
                            node-key="id"
                            :props="{ label: 'name', children: 'children' }"
                            @node-click="handleSceneNodeClick"
                            highlight-current
                            :expand-on-click-node="false"
                            :default-expanded-keys="treeExpandedKeys.length > 0 ? treeExpandedKeys : (sceneConfigTree.length ? [sceneConfigTree[0].id] : [])"
                            :current-node-key="selectedNode ? selectedNode.id : null"
                            :sort="false"
                          />
                        </el-col>
                        <el-col :span="18">
                          <SceneConfigNode 
                            v-if="selectedNode" 
                            :node="selectedNode" 
                            :root-tree="sceneConfigTree"
                            :scene-tree-options="sceneTreeOptions"
                            :left-tree-industry-code="industryCode"
                          />
                        </el-col>
                      </el-row>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 其他分类的占位内容 -->
          <div v-else-if="category.key === 'default_plan'">
            <network-plan-config v-model="networkPlanData" :left-tree-industry-code="industryCode" />
          </div>
          <div v-else-if="category.key === 'default_value'">
            <business-value-config v-model="businessValueData" :left-tree-industry-code="industryCode" />
          </div>
          <div v-else-if="category.key === 'default_vr'">
            <vr-scene-config v-model="vrSceneData" />
          </div>
          <div v-else>
            <p>这里是 <strong>{{ category.name }}</strong> 分类的内容区域。</p>
          </div>
        </div>
      </div>
      <div class="submit-footer">
      <div class="form-actions">
        <!-- 同步文件按钮加 tooltip -->
        <el-tooltip 
          effect="dark" 
          content="链接填写完成后，点击【提交】后再点击【同步】按钮" 
          placement="top"
        >
          <el-button 
            type="success" 
            @click="handleSynchronizeFile" 
            :disabled="!form.sceneViewConfigId"
            :loading="synchronizing"
          >
            {{ synchronizing ? '同步中...' : '同步文件' }}
          </el-button>
        </el-tooltip>

        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting" 
          style="margin-left: 30px;"
        >
          {{ submitting ? '提交中...' : '提交' }}
        </el-button>
      </div>
    </div>

    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      title="图片预览"
      width="60%"
      append-to-body
      @close="closePreview"
    >
      <div class="preview-container">
        <img :src="previewImageUrl" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getIndustryList, getSceneTreeList } from '@/api/view/industry'
import SceneConfigNode from './SceneConfigNode.vue'
import { getSceneViewConfig, sceneViewUpd, uploadSceneFile, synchronizationFile } from '@/api/view/sceneView'
import NetworkPlanConfig from './NetworkPlanConfig.vue'
import BusinessValueConfig from './BusinessValueConfig.vue'
import VrSceneConfig from './VrSceneConfig.vue'
import ThemeSelectionDialog from './ThemeSelectionDialog.vue'
import axios from 'axios'

export default {
  name: 'IndustryScenePage',
  components: {
    SceneConfigNode,
    NetworkPlanConfig,
    BusinessValueConfig,
    VrSceneConfig,
    ThemeSelectionDialog
  },
  data() {
    return {
      menuData: [], // 原始菜单数据
      flatMenuData: [], // 扁平化的行业数据，用于搜索和业务逻辑
      activeMenu: '',
      industryCode: '',
      selectedTheme: null, // 当前选择的主题
      form: {
        mainTitle: '',
        subTitle: '',
        bgImgUrl: '',
        bgFileUrl: '',
        panoramicViewXmlUrl: ''
      },
      sceneConfigTree: [],
      selectedNode: null,
      loading: false, // 页面加载状态
      switchingIndustry: false, // 新增：切换行业的loading状态
      rules: {
        introduceVideoImgUrl: [
          { required: true, message: '请上传介绍视频首帧', trigger: 'change' }
        ],
        introduceVideoFileUrl: [
          { required: true, message: '请上传介绍视频', trigger: 'change' }
        ],
        videoExplanationFileUrl: [
          { required: true, message: '请上传讲解视频', trigger: 'change' }
        ]
      },
      uploadingType: '',
      uploadingKey: '',
      categories: [], // 改为空数组，从后端动态获取
      introduceVideo: {
        status: '0',
        backgroundImgFileUrl: '',
        backgroundFileUrl: ''
      },
      videoExplanation: {
        status: '0',
        backgroundFileUrl: '',
        videoSegmentedVoList: []
      },
      sceneTreeOptions: [],
      sceneCascaderProps: {
        label: 'sceneName',
        value: 'id',
        children: 'children',
        emitPath: false,
        checkStrictly: true,
        disabled: (data) => {
          // 允许所有节点可选，只要没有被其他分段选中
          const isSelected = this.videoExplanation && this.videoExplanation.videoSegmentedVoList
            ? this.videoExplanation.videoSegmentedVoList.some(seg => seg.sceneId === data.id)
            : false
          return isSelected
        }
      },
      bgFileList: [], // 背景文件列表
      videoExplanationFileList: [], // 讲解视频文件列表
      xmlFileList: [], // XML文件列表
      networkPlanDataMap: {}, // 改为对象，按菜单ID存储
      businessValueDataMap: {}, // 商业价值数据映射
      vrSceneDataMap: {}, // VR看现场数据映射
      // 图片预览
      previewVisible: false,
      previewImageUrl: '',
      searchKeyword: '',
      treeExpandedKeys: [], // 新增：保存树的展开状态
      uploadModes: {
        bgFile: 'upload',
        videoExplanation: 'upload',
        introduceVideo: 'upload'
      },
      synchronizing: false,
      submitting: false // 添加这个属性
    }
  },
  computed: {
    videoSegmentedList() {
      // 如果没有数据，默认返回一行空数据
      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {
        return [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]
      }
      return this.videoExplanation.videoSegmentedVoList
    },
    networkPlanData: {
      get() {
        const menuData = this.networkPlanDataMap[this.activeMenu]
        if (!menuData) {
          return {
            networkVideoList: [],
            videoExplanationVo: {
              status: '0',
              backgroundFileUrl: '',
              videoSegmentedVoList: []
            }
          }
        }
        return menuData
      },
      set(value) {
        this.$set(this.networkPlanDataMap, this.activeMenu, value)
      }
    },
    businessValueData: {
      get() {
        return this.businessValueDataMap[this.activeMenu] || []
      },
      set(value) {
        this.$set(this.businessValueDataMap, this.activeMenu, value)
      }
    },
    vrSceneData: {
      get() {
        return this.vrSceneDataMap[this.activeMenu] || []
      },
      set(val) {
        this.$set(this.vrSceneDataMap, this.activeMenu, val)
      }
    },
    filteredMenuData() {
      if (!this.searchKeyword) {
        return this.menuData
      }
      
      // 递归过滤树形数据
      const filterTree = (nodes) => {
        return nodes.map(node => {
          const filteredChildren = node.children ? filterTree(node.children) : []
          const matchesSearch = node.name && node.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
          
          if (matchesSearch || filteredChildren.length > 0) {
            return {
              ...node,
              children: filteredChildren
            }
          }
          return null
        }).filter(Boolean)
      }
      
      return filterTree(this.menuData)
    }
  },
  created() {
    // 从URL获取token并设置
    this.initTokenFromUrl()
    this.loadIndustryMenu()
  },
  methods: {
    // 从URL获取token并设置
    initTokenFromUrl() {
      const urlParams = new URLSearchParams(window.location.search)
  const token = urlParams.get('token')
  
  if (token) {
    localStorage.setItem('external-token', token)
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    console.log('从URL获取到token:', token)
  } else {
    const storedToken = localStorage.getItem('external-token')
    if (storedToken) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
    }
  }
    },
    async loadIndustryMenu() {
      const res = await getIndustryList()
      if (res.code === 0 && Array.isArray(res.data)) {
        // 转换数据结构为树形菜单
        this.menuData = res.data.map(plate => ({
          id: `plate_${plate.plateKey}`,
          name: plate.plateName,
          type: 'plate',
          children: plate.industryTreeListVos ? plate.industryTreeListVos.map(industry => ({
            id: industry.id,
            name: industry.industryName,
            industryCode: industry.industryCode,
            plate: industry.plate,
            type: 'industry'
          })) : []
        }))
        
        // 创建扁平化的行业数据，用于业务逻辑
        this.flatMenuData = []
        res.data.forEach(plate => {
          if (plate.industryTreeListVos) {
            this.flatMenuData.push(...plate.industryTreeListVos)
          }
        })
        
        // 默认选中第一个行业
        if (this.flatMenuData.length) {
          this.activeMenu = String(this.flatMenuData[0].id)
          this.industryCode = this.flatMenuData[0].industryCode
          // 等待DOM更新后设置树组件的当前节点
          this.$nextTick(() => {
            // 确保树组件已渲染并设置当前选中节点
            if (this.$refs.menuTree && this.$refs.menuTree.setCurrentKey) {
              this.$refs.menuTree.setCurrentKey(this.activeMenu)
            }
          })
          
          // 加载第一个行业的数据
          await this.handleSelect(this.activeMenu)
        }
      }
    },
    
    handleTreeNodeClick(data) {
      // 只有点击行业节点才处理
      if (data.type === 'industry') {
        this.handleSelect(String(data.id))
        this.industryCode = data.industryCode;
      }
    },

    handleSearch(value) {
      this.searchKeyword = value
    },
    highlightText(text) {
      if (!this.searchKeyword) return text
      const regex = new RegExp(`(${this.searchKeyword})`, 'gi')
      return text.replace(regex, '<span class="highlight">$1</span>')
    },
    async handleSelect(id, keepSelectedNode = false, showLoading = true) {
      try {
        // 开启切换行业的loading
        if (showLoading) {
          this.switchingIndustry = true
          // 禁用页面滚动
          document.body.style.overflow = 'hidden'

          // 右侧编辑栏回到顶部
          this.$nextTick(() => {
            const contentPanel = document.querySelector('.content-panel')
            if (contentPanel) {
              contentPanel.scrollTop = 0
            }
          })
        }
        
        this.activeMenu = id
        await this.loadSceneTreeOptions(this.activeMenu)
        
        // 保存当前选中的节点
        const currentSelectedNode = keepSelectedNode ? this.selectedNode : null
        
        // 重置主题选择
        this.selectedTheme = null
        
        // 从扁平化菜单数据中获取当前行业的 industryCode
        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)
        const industryCode = currentIndustry ? currentIndustry.industryCode : null
        
        const res = await getSceneViewConfig({ industryCode: industryCode })
        if (res.code === 0 && res.data) {
          
          // 同步主标题、副标题、背景图片、XML文件等
          this.form.sceneViewConfigId = res.data.sceneViewConfigId || ''
          this.form.mainTitle = res.data.mainTitle || ''
          this.form.subTitle = res.data.subTitle || ''
          this.form.bgImgUrl = res.data.backgroundImgFileUrl || ''
          this.form.bgFileUrl = res.data.backgroundFileUrl || ''
          this.form.panoramicViewXmlUrl = res.data.panoramicViewXmlUrl || ''
          
          // 更新背景文件列表
          this.updateBgFileList()
          
          // 更新XML文件列表
          this.updateXmlFileList()
          
          // 回显主题选择
          if (res.data.themeInfoVo) {
            this.selectedTheme = {
              themeId: res.data.themeInfoVo.themeId,
              themeName: res.data.themeInfoVo.themeName,
              themeEffectImg: res.data.themeInfoVo.themeEffectImg,
              remark: res.data.themeInfoVo.remark
            }
          } else {
            this.selectedTheme = null
          }
          
          // 处理 sceneDefaultConfigVoList，动态生成 categories
          if (res.data.sceneDefaultConfigVoList && Array.isArray(res.data.sceneDefaultConfigVoList)) {
            this.categories = res.data.sceneDefaultConfigVoList.map(configItem => ({
              id: configItem.id,
              key: configItem.keyName,
              name: configItem.name,
              enabled: configItem.keyValue === '0', // keyValue为'0'表示启用
              editing: false,
              editingName: '',
              originalName: configItem.name,
              remark: configItem.remark,
              classification: configItem.classification,
              defaultStatus: configItem.defaultStatus
            }))
            
            // 查找场景配置分类
            const sceneCategory = res.data.sceneDefaultConfigVoList.find(item => item.keyName === 'default_scene')
            
            // 处理视频讲解数据
            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.videoExplanationVo) {
              const videoData = sceneCategory.industrySceneInfoVo.videoExplanationVo
              this.videoExplanation = {
                status: videoData.status || '0',
                backgroundFileUrl: videoData.backgroundFileUrl || '',
                videoSegmentedVoList: videoData.videoSegmentedVoList ? videoData.videoSegmentedVoList.map(seg => ({
                  time: seg.time,
                  sceneCode: seg.sceneCode,
                  sceneName: seg.sceneName,
                  sceneId: this.findSceneIdByCode(seg.sceneCode) // 根据sceneCode查找sceneId
                })) : []
              }
            } else {
              this.videoExplanation = {
                status: '0',
                backgroundFileUrl: '',
                videoSegmentedVoList: []
              }
            }
            
            // 更新视频讲解文件列表
            this.updateVideoExplanationFileList()
            
            // 处理场景配置树
            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.sceneListVo) {
              this.sceneConfigTree = this.adaptSceneTree(sceneCategory.industrySceneInfoVo.sceneListVo)
              
              // 如果需要保持选中节点
              if (keepSelectedNode && currentSelectedNode) {
                const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNode.id)
                if (nodeToSelect) {
                  this.selectedNode = nodeToSelect
                } else {
                  this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null
                }
              } else {
                // 默认选择第一个节点
                this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null
              }
            } else {
              // 没有场景数据时清空
              this.sceneConfigTree = []
              this.selectedNode = null
            }
          }
          
          // 处理网络方案数据
          if (res.data.networkSolutionVo) {
            const networkData = {
              networkVideoList: res.data.networkSolutionVo.networkVideoList || [],
              videoExplanationVo: res.data.networkSolutionVo.videoExplanationVo || {
                status: '0',
                backgroundFileUrl: '',
                videoSegmentedVoList: []
              }
            }
            this.$set(this.networkPlanDataMap, this.activeMenu, networkData)
          }

          // 处理商业价值数据
          if (res.data.commercialValueListVo) {
            this.$set(this.businessValueDataMap, this.activeMenu, res.data.commercialValueListVo)
          }

          // 处理VR看现场数据
          if (res.data.vrInfoListVo) {
            this.$set(this.vrSceneDataMap, this.activeMenu, res.data.vrInfoListVo)
          }
          
          // 其他数据处理逻辑保持不变...
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        // 关闭切换行业的loading
        if (showLoading) {
          this.switchingIndustry = false
          // 恢复页面滚动
          document.body.style.overflow = ''
        }
      }
    },
    handleBeforeUpload(file) {
      return false // 拦截默认上传行为
    },
    addSegment() {
      this.form.videoSegmentedVoList.push({ time: '', scene: '' })
    },
    removeSegment(index) {
      if (this.form.videoSegmentedVoList.length >= 1) {
        this.form.videoSegmentedVoList.splice(index, 1)
      }
    },
    async beforeUploadIntroduceImg(file, type, key) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传图片，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.industryCode)
        
        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          if (type && key) {
            // 针对介绍视频和视频讲解的上传，单独上传图片时使用 fileUrl
            this[type][key] = res.data.fileUrl
          } else {
            // 针对主背景图片的上传
            this.form.bgImgUrl = res.data.fileUrl
          }
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    async beforeUploadIntroduceVideo(file, type, key) {
      // 如果是主背景文件上传（没有type和key参数）
      if (!type && !key) {
        try {
          this.$modal.loading("正在上传文件，请稍候...")
          const formData = new FormData()
          formData.append('file', file)
          formData.append('industryCode', this.industryCode)
          
          const res = await uploadSceneFile(formData)
          if (res.code === 0 && res.data) {
            // 设置背景文件URL
            this.form.bgFileUrl = res.data.fileUrl
            
            // 直接覆盖背景文件列表
            const fileName = res.data.fileUrl.split('/').pop()
            this.bgFileList = [{
              name: fileName,
              url: res.data.fileUrl,
              uid: Date.now()
            }]
            
            // 如果是MP4文件且返回了imgUrl，自动设置背景图片首帧
            if (file.type === 'video/mp4' && res.data.imgUrl) {
              this.form.bgImgUrl = res.data.imgUrl
              this.$message.success('上传成功，已自动生成背景图片首帧')
            } else {
              this.$message.success('上传成功')
            }
          } else {
            this.$message.error(res.msg || '上传失败')
          }
        } catch (error) {
          this.$message.error('上传失败')
        } finally {
          this.$modal.closeLoading()
        }
        return false
      }
      
      // 其他视频上传逻辑（介绍视频、讲解视频等）
      if (!file.type.startsWith('video/') && !file.name.endsWith('.mp4')) {
        this.$message.error('只能上传MP4视频文件！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传视频，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.industryCode)
        
        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          if (type && key) {
            // 针对介绍视频和视频讲解的上传
            this[type][key] = res.data.fileUrl
            
            // 直接覆盖对应的文件列表
            const fileName = res.data.fileUrl.split('/').pop()
            if (type === 'introduceVideo' && key === 'backgroundFileUrl') {
              this.introduceVideoFileList = [{
                name: fileName,
                url: res.data.fileUrl,
                uid: Date.now()
              }]
            } else if (type === 'videoExplanation' && key === 'backgroundFileUrl') {
              this.videoExplanationFileList = [{
                name: fileName,
                url: res.data.fileUrl,
                uid: Date.now()
              }]
            }
            
            // 如果是介绍视频上传，且返回了imgUrl，自动设置介绍视频首帧
            if (type === 'introduceVideo' && key === 'backgroundFileUrl' && res.data.imgUrl) {
              this.introduceVideo.backgroundImgFileUrl = res.data.imgUrl
              this.$message.success('上传成功，已自动生成介绍视频首帧')
            } else {
              this.$message.success('上传成功')
            }
          }
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    beforeUploadExplanationVideo(file) {
      this.uploadingType = 'mp4'
      this.uploadingKey = 'videoExplanationFileUrl'
      return this.handleBeforeUpload(file)
    },
    // 新增方法：添加场景配置节点
    addSceneConfigNode(parentId = null) {
      const newNode = {
        id: Date.now(), // 生成唯一ID
        name: '新场景',
        type: 'scene', // 类型为场景
        enabled: true,
        children: [],
        parentId: parentId
      }
      if (parentId) {
        const parentNode = this.findNodeById(this.sceneConfigTree, parentId)
        if (parentNode) {
          parentNode.children.push(newNode)
        }
      } else {
        this.sceneConfigTree.push(newNode)
      }
      return newNode.id
    },
    // 新增方法：删除场景配置节点
    removeSceneConfigNode(nodeId) {
      this.sceneConfigTree = this.sceneConfigTree.filter(node => node.id !== nodeId)
    },
    // 新增方法：查找节点
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id)
          if (found) {
            return found
          }
        }
      }
      return null
    },
    // 新增方法：添加场景的痛点价值
    addScenePainPoint(nodeId) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'scene') {
        node.painPoints = node.painPoints || []
        node.painPoints.push({ title: '', contents: [''], showTime: '' })
      }
    },
    // 新增方法：删除场景的痛点价值
    removeScenePainPoint(nodeId, idx) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'scene') {
        node.painPoints = node.painPoints || []
        node.painPoints.splice(idx, 1)
      }
    },
    // 新增方法：添加场景痛点内容的项
    addScenePainContent(nodeId, idx) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'scene') {
        node.painPoints = node.painPoints || []
        node.painPoints[idx].contents.push('')
      }
    },
    // 新增方法：删除场景痛点内容的项
    removeScenePainContent(nodeId, idx, cidx) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'scene') {
        node.painPoints = node.painPoints || []
        node.painPoints[idx].contents.splice(cidx, 1)
      }
    },
    // 新增方法：添加场景的成本预估内容
    addSceneCostContent(nodeId) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'costEstimate') {
        node.contents = node.contents || []
        node.contents.push('')
      }
    },
    // 新增方法：删除场景的成本预估内容
    removeSceneCostContent(nodeId, cidx) {
      const node = this.findNodeById(this.sceneConfigTree, nodeId)
      if (node && node.type === 'costEstimate') {
        node.contents = node.contents || []
        node.contents.splice(cidx, 1)
      }
    },
    // 新增方法：上传场景配置图片
    beforeUploadSceneConfigImg(file, type, key) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件！')
        return false
      }
      const reader = new FileReader()
      reader.onload = e => {
        this.findNodeById(this.sceneConfigTree, type)[key] = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },
    // 新增方法：上传场景配置文件
    beforeUploadSceneConfigFile(file, type, key) {
      // 这里只做文件名回显
      this.findNodeById(this.sceneConfigTree, type)[key] = file.name
      return false
    },
    handleSceneNodeClick(node) {
      this.selectedNode = node
    },
    // 适配函数移到methods中，供接口数据适配使用
    adaptSceneTree(rawTree, parent = null) {
      if (!rawTree) return []
      const arr = Array.isArray(rawTree) ? rawTree : [rawTree]
      return arr.map((node) => {
        const adapted = {
          id: node.sceneId,
          sceneInfoId: node.sceneInfoId || null,
          name: node.sceneName,
          code: node.sceneCode,
          x: node.x,
          y: node.y,
          type: node.type,
          status: node.status !== null ? node.status : '0',
          isUnfold: node.isUnfold !== null && node.isUnfold !== undefined ? node.isUnfold : '1',
          displayLocation: node.displayLocation !== null && node.displayLocation !== undefined ? node.displayLocation : '0',
          treeClassification: node.treeClassification !== null && node.treeClassification !== undefined ? node.treeClassification : '3',
          introduceVideoVo: node.introduceVideoVo ? {
            id: node.introduceVideoVo.id || '',
            type: node.introduceVideoVo.type || '',
            viewInfoId: node.introduceVideoVo.viewInfoId || '',
            status: node.introduceVideoVo.status || '0',
            backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || '',
            backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || ''
          } : { id: '', type: '', viewInfoId: '', status: '0', backgroundImgFileUrl: '', backgroundFileUrl: '' },
          tradition: node.sceneTraditionVo ? {
            name: node.sceneTraditionVo.name || '',
            panoramicViewXmlKey: node.sceneTraditionVo.panoramicViewXmlKey || '',
            backgroundResources: node.sceneTraditionVo.sceneVideoList ? 
              node.sceneTraditionVo.sceneVideoList.map(v => ({
                id: v.id || null,
                label: v.tag || '',
                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({
                  id: rel.id || null,
                  fileId: rel.fileId || null,
                  x: rel.clickX || '',
                  y: rel.clickY || '',
                  wide: rel.wide || '',
                  high: rel.high || '',
                  xmlKey: rel.xmlKey || '',
                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),
                  sceneCode: rel.bindSceneCode || ''
                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }],
                wide: v.wide || 0,
                high: v.high || 0,
                bgImg: v.backgroundImgFileUrl || '',
                bgFile: v.backgroundFileUrl || '',
                status: v.status || '',
                type: v.type || '',
                viewInfoId: v.viewInfoId || ''
              })) : [],
            painPoints: node.sceneTraditionVo.painPointList ?
              (Array.isArray(node.sceneTraditionVo.painPointList) ? node.sceneTraditionVo.painPointList.map(p => ({
                painPointId: p.painPointId || null,
                title: p.bigTitle || '',
                contents: p.content || [],
                showTime: p.displayTime || ''
              })) : []) : []
          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },
          wisdom5g: node.scene5gVo ? {
            name: node.scene5gVo.name || '',
            panoramicViewXmlKey: node.scene5gVo.panoramicViewXmlKey || '',
            backgroundResources: node.scene5gVo.sceneVideoList ? 
              node.scene5gVo.sceneVideoList.map(v => ({
                id: v.id || null,
                tag: v.tag || '',
                status: v.status || '',
                type: v.type || '',
                viewInfoId: v.viewInfoId || '',
                backgroundImgFileUrl: v.backgroundImgFileUrl || '',
                backgroundFileUrl: v.backgroundFileUrl || '',
                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({
                  id: rel.id || null,
                  fileId: rel.fileId || null,
                  x: rel.clickX || '',
                  y: rel.clickY || '',
                  wide: rel.wide || '',
                  high: rel.high || '',
                  xmlKey: rel.xmlKey || '',
                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),
                  sceneCode: rel.bindSceneCode || ''
                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }]
              })) : [],
            painPoints: node.scene5gVo.painPointList ? 
              (Array.isArray(node.scene5gVo.painPointList) ? node.scene5gVo.painPointList.map(p => ({
                painPointId: p.painPointId || null,
                title: p.bigTitle || '',
                contents: p.content || [],
                showTime: p.displayTime || ''
              })) : []) : []
          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },
          costEstimate: node.costEstimationInfoVo ? {
            painPointId: node.costEstimationInfoVo.painPointId || null,
            status: node.costEstimationInfoVo.status || '0',
            title: node.costEstimationInfoVo.bigTitle || '',
            contents: node.costEstimationInfoVo.content || []
          } : { painPointId: null, status: '0', title: '', contents: [] },
          children: [],
          parent
        }
        // 递归处理子节点，保持后端返回的原始顺序
        adapted.children = node.children ? this.adaptSceneTree(node.children, adapted) : []
        return adapted
      })
    },
    async handleSubmit() {
      try {
        this.submitting = true
        // 从扁平化菜单数据中获取当前行业的 industryCode
        const currentIndustry = this.flatMenuData.find(item => String(item.id) === this.activeMenu)
        const industryCode = currentIndustry ? currentIndustry.industryCode : null
        
        // 保存当前选中的节点的完整信息
        const currentSelectedNodeId = this.selectedNode ? this.selectedNode.id : null
        const currentSelectedNodeName = this.selectedNode ? this.selectedNode.name : null
        
        // 构建提交数据
        const submitData = {
          industryId: this.activeMenu,
          industryCode: industryCode, // 新增参数
          sceneViewConfigId: this.form.sceneViewConfigId || null,
          mainTitle: this.form.mainTitle || null,
          subTitle: this.form.subTitle || null,
          themeId: this.selectedTheme ? this.selectedTheme.themeId : null,
          backgroundImgFileUrl: this.form.bgImgUrl || null,
          backgroundFileUrl: this.form.bgFileUrl || null,
          panoramicViewXmlUrl: this.form.panoramicViewXmlUrl || null,
          networkSolutionInfoVo: {
            networkVideoList: (this.networkPlanDataMap[this.activeMenu]?.networkVideoList && Array.isArray(this.networkPlanDataMap[this.activeMenu].networkVideoList)) ? 
              this.networkPlanDataMap[this.activeMenu].networkVideoList.map(plan => ({
                id: plan.id || null,
                type: 4,
                tag: plan.tag || null,
                clickX: plan.clickX || null,
                clickY: plan.clickY || null,
                wide: plan.wide || null,
                high: plan.high || null,
                backgroundImgFileUrl: plan.backgroundImgFileUrl || null,
                backgroundFileUrl: plan.backgroundFileUrl || null,
                status: null,
                viewInfoId: null
              })) : [],
            videoExplanationVo: {
              status: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.status || '0',
              backgroundFileUrl: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.backgroundFileUrl || null,
              videoSegmentedVoList: (this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.videoSegmentedVoList?.length) ? 
                this.networkPlanDataMap[this.activeMenu].videoExplanationVo.videoSegmentedVoList.map(seg => ({
                  time: seg.time || null,
                  sceneCode: seg.sceneCode || null,
                  sceneName: seg.sceneName || null
                })) : null
            }
          },
          sceneDefaultConfigVoList: this.categories.map(cat => {
            const baseConfig = {
              id: cat.id || null,
              industryId: this.activeMenu || null,
              name: cat.name,
              keyName: cat.key,
              keyValue: cat.enabled ? '0' : '1',
              remark: cat.remark || cat.name,
              defaultStatus: '0'
            }

            if (cat.key === 'default_scene') {
              const convertedSceneList = this.convertSceneTreeToApi(this.sceneConfigTree)     
              baseConfig.industrySceneInfoVo = {
                videoExplanationVo: {
                  status: this.videoExplanation.status,
                  backgroundFileUrl: this.videoExplanation.backgroundFileUrl || null,
                  videoSegmentedVoList: this.videoExplanation.videoSegmentedVoList.length ? this.videoExplanation.videoSegmentedVoList : null
                },
                sceneListVo: convertedSceneList
              }
            } else {
              baseConfig.industrySceneInfoVo = null
            }
            
            return baseConfig
          }),
          commercialValueDTO: (this.businessValueDataMap[this.activeMenu] && Array.isArray(this.businessValueDataMap[this.activeMenu])) ? 
            this.businessValueDataMap[this.activeMenu].map(value => ({
              id: value.id || null,
              viewInfoId: value.viewInfoId || null,
              type: 5,
              tag: value.tag || null,
              backgroundImgFileUrl: value.backgroundImgFileUrl || null,
              backgroundFileUrl: value.backgroundFileUrl || null
            })) : [],
          vrInfoDtoList: (this.vrSceneDataMap[this.activeMenu] && Array.isArray(this.vrSceneDataMap[this.activeMenu])) ? 
            this.vrSceneDataMap[this.activeMenu]. map(vr => ({
              id: vr.id || null,
              industryId: vr.industryId || this.activeMenu,
              type: vr.type || 6,
              viewInfoId: vr.viewInfoId || null,
              name: vr.name || '',
              address: vr.address || ''
            })) : [],
        }
      
        const response = await sceneViewUpd(submitData)
        this.$modal.msgSuccess("修改成功");

        // 重新加载数据（不显示全局loading，避免与按钮loading冲突）
        await this.handleSelect(this.activeMenu, false, false)

        if (currentSelectedNodeId && this.sceneConfigTree.length > 0) {
          // 强制查找并设置选中节点
          const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNodeId)

          if (nodeToSelect) {
            // 计算并设置展开路径
            const pathIds = []
            const findPath = (nodes, targetId, currentPath = []) => {
              for (const node of nodes) {
                const newPath = [...currentPath, node.id]
                if (node.id === targetId) {
                  pathIds.push(...newPath)
                  return true
                }
                if (node.children && node.children.length > 0) {
                  if (findPath(node.children, targetId, newPath)) {
                    return true
                  }
                }
              }
              return false
            }

            findPath(this.sceneConfigTree, currentSelectedNodeId)
            this.treeExpandedKeys = pathIds.slice(0, -1)

            // 先设置选中节点
            this.selectedNode = nodeToSelect

            // 强制更新树组件的选中状态
            this.$nextTick(() => {
              this.$nextTick(() => {
                // 模拟点击节点来强制更新选中状态
                this.handleSceneNodeClick(nodeToSelect)
              })
            })
          }
        }
        
        console.log('提交内容:', submitData)
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitting = false
      }
    },
    addVideoSegment() {
      // 如果原数组为空，先初始化
      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {
        this.videoExplanation.videoSegmentedVoList = [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]
      }
      this.videoExplanation.videoSegmentedVoList.push({ time: '', sceneId: '', sceneName: '', sceneCode: '' })
    },
    removeVideoSegment(idx) {
      this.videoExplanation.videoSegmentedVoList.splice(idx, 1)
    },
    //递归重构结构
    getDeepTreeOptions(tree) {
    return tree.map(item => {
      // 复制当前节点的基础属性
      const node = { ...item };
      
      // 如果存在 children 且不为空，则递归处理
      if (node.children && node.children.length > 0) {
        node.children = this.getDeepTreeOptions(node.children);
      } else {
        // 当 children 为空或不存在时，删除 children 属性（可选）
        delete node.children;
      }
      
      return node;
    });
  },
    async loadSceneTreeOptions(id) {
      try {
        // 从扁平化菜单数据中获取当前行业的 industryCode
        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)
        const industryCode = currentIndustry ? currentIndustry.industryCode : null
        
        const res = await getSceneTreeList({ industryCode: industryCode })
        if (res.code === 0 && Array.isArray(res.data)) {
          this.sceneTreeOptions = this.getDeepTreeOptions(res.data)
        }
      } catch (error) {
        console.error('加载场景树失败:', error)
      }
    },
    handleSceneCascaderChange(val, idx) {
      // 确保数组和索引位置的对象存在
      if (!this.videoExplanation.videoSegmentedVoList || !this.videoExplanation.videoSegmentedVoList[idx]) {
        return
      }
      
      const findScene = (tree, id) => {
        for (const node of tree) {
          if (node.id === id) return node
          if (node.children && node.children.length) {
            const found = findScene(node.children, id)
            if (found) return found
          }
        }
        return null
      }
      const node = findScene(this.sceneTreeOptions, val)
      if (node) {
        // 设置场景ID和相关信息
        this.videoExplanation.videoSegmentedVoList[idx].sceneId = val
        this.videoExplanation.videoSegmentedVoList[idx].sceneName = node.sceneName
        this.videoExplanation.videoSegmentedVoList[idx].sceneCode = node.sceneCode
      }
    },
    isSceneDisabled(id, currentIdx) {
      // 除当前分段外，其他分段已选的id
      return this.videoExplanation.videoSegmentedVoList.some((seg, idx) => idx !== currentIdx && seg.sceneId === id)
    },
    // 将场景树转换为接口格式
    convertSceneTreeToApi(sceneTree) {
      console.log("提交的数据:", sceneTree);
      return sceneTree.map(node => ({
        sceneInfoId: node.sceneInfoId || null,
        sceneId: node.id,
        paramId: node.parent ? node.parent.id : null,
        sceneName: node.name,
        sceneCode: node.code,
        x: node.x || null,
        y: node.y || null,
        type: node.type || null,
        status: node.status,
        isUnfold: (node.children && node.children.length > 0) ? (node.isUnfold || '1') : null,
        displayLocation: (node.children && node.children.length > 0) ? (node.displayLocation || '0') : null,
        treeClassification: (node.children && node.children.length > 0) ? (node.treeClassification || '3') : null,
        introduceVideoVo: node.introduceVideoVo ? {
          id: node.introduceVideoVo.id || null,
          type: node.introduceVideoVo.type || null,
          viewInfoId: node.introduceVideoVo.viewInfoId || null,
          status: node.introduceVideoVo.status || null,
          backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || null,
          backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || null
        } : null,
        sceneTraditionVo: node.tradition ? {
          name: node.tradition.name || null,
          panoramicViewXmlKey: node.tradition.panoramicViewXmlKey || null,
          sceneVideoList: node.tradition.backgroundResources && node.tradition.backgroundResources.length ? 
            node.tradition.backgroundResources.map(resource => ({
              id: resource.id || null,
              tag: resource.label || null,
              wide: resource.wide || null,
              high: resource.high || null,
              status: resource.status || null,
              type: 1,
              viewInfoId: resource.viewInfoId || null,
              backgroundImgFileUrl: resource.bgImg || '',
              backgroundFileUrl: resource.bgFile || '',
              sceneFileRelList: resource.coordinates && resource.coordinates.length ? 
                resource.coordinates.map(coord => ({
                  id: coord.id || null,
                  fileId: coord.fileId || null,
                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,
                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,
                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,
                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,
                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,
                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null
                })) : []
            })) : null,
          painPointList: node.tradition.painPoints && node.tradition.painPoints.length ? 
            node.tradition.painPoints.map(pain => ({
              painPointId: pain.painPointId || null,
              bigTitle: pain.title || null,
              content: pain.contents || [],
              displayTime: pain.showTime || null
            })) : null
        } : null,
        scene5gVo: node.wisdom5g ? {
          name: node.wisdom5g.name || null,
          panoramicViewXmlKey: node.wisdom5g.panoramicViewXmlKey || null,
          sceneVideoList: node.wisdom5g.backgroundResources && node.wisdom5g.backgroundResources.length ? 
            node.wisdom5g.backgroundResources.map(resource => ({
              id: resource.id || null,
              tag: resource.tag || null,
              status: resource.status || null,
              type: 2,
              viewInfoId: resource.viewInfoId || null,
              backgroundImgFileUrl: resource.bgImg || '',
              backgroundFileUrl: resource.bgFile || '',
              sceneFileRelList: resource.coordinates && resource.coordinates.length ? 
                resource.coordinates.map(coord => ({
                  id: coord.id || null,
                  fileId: coord.fileId || null,
                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,
                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,
                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,
                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,
                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,
                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null
                })) : []
            })) : null,
          painPointList: node.wisdom5g.painPoints && node.wisdom5g.painPoints.length ? 
            node.wisdom5g.painPoints.map(pain => ({
              painPointId: pain.painPointId || null,
              bigTitle: pain.title || null,
              content: pain.contents || [],
              displayTime: pain.showTime || null
            })) : null
        } : null,
        costEstimationInfoVo: node.costEstimate ? {
          painPointId: node.costEstimate.painPointId || null,
          status: node.costEstimate.status || '0',
          bigTitle: node.costEstimate.title || null,
          content: node.costEstimate.contents && node.costEstimate.contents.length ? node.costEstimate.contents : null
        } : null,
        children: node.children && node.children.length ? this.convertSceneTreeToApi(node.children) : []
      }))
    },
    handleTimeChange(val, idx) {
      // 确保数组已初始化
      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {
        this.videoExplanation.videoSegmentedVoList = [{ time: 0, sceneId: '', sceneName: '', sceneCode: '' }]
      }
      // 更新对应位置的时间值
      if (this.videoExplanation.videoSegmentedVoList[idx]) {
        this.videoExplanation.videoSegmentedVoList[idx].time = val
      }
    },
    // 根据sceneCode查找对应的sceneId
    findSceneIdByCode(sceneCode) {
      if (!sceneCode || !this.sceneTreeOptions) return ''
      
      const findInTree = (tree) => {
        for (const node of tree) {
          if (node.sceneCode === sceneCode) {
            return node.id
          }
          if (node.children && node.children.length) {
            const found = findInTree(node.children)
            if (found) return found
          }
        }
        return null
      }
      
      return findInTree(this.sceneTreeOptions) || ''
    },
    // 处理背景文件删除
    handleRemoveBgFile(file, fileList) {
      this.form.bgFileUrl = ''
      this.form.bgImgUrl = '' // 同时清空背景图片首帧
      this.bgFileList = []
      this.$message.success('文件已删除')
    },
    // 更新背景文件列表
    updateBgFileList() {
      if (this.form.bgFileUrl) {
        const fileName = this.form.bgFileUrl.split('/').pop()
        this.bgFileList = [{
          name: fileName,
          url: this.form.bgFileUrl,
          uid: Date.now()
        }]
      } else {
        this.bgFileList = []
      }
    },
    // 处理介绍视频文件删除
    handleRemoveIntroduceVideoFile(file, fileList) {
      this.introduceVideo.backgroundFileUrl = ''
      this.introduceVideo.backgroundImgFileUrl = '' // 同时清空首帧图片
      this.introduceVideoFileList = []
      this.$message.success('介绍视频已删除')
    },
    // 更新介绍视频文件列表
    updateIntroduceVideoFileList() {
      if (this.introduceVideo.backgroundFileUrl) {
        const fileName = this.introduceVideo.backgroundFileUrl.split('/').pop()
        this.introduceVideoFileList = [{
          name: fileName,
          url: this.introduceVideo.backgroundFileUrl,
          uid: Date.now()
        }]
      } else {
        this.introduceVideoFileList = []
      }
    },
    // 处理讲解视频文件删除
    handleRemoveVideoExplanationFile(file, fileList) {
      this.videoExplanation.backgroundFileUrl = ''
      this.videoExplanationFileList = []
      this.$message.success('讲解视频已删除')
    },
    // 更新讲解视频文件列表
    updateVideoExplanationFileList() {
      if (this.videoExplanation.backgroundFileUrl) {
        const fileName = this.videoExplanation.backgroundFileUrl.split('/').pop()
        this.videoExplanationFileList = [{
          name: fileName,
          url: this.videoExplanation.backgroundFileUrl,
          uid: Date.now()
        }]
      } else {
        this.videoExplanationFileList = []
      }
    },
    // 处理XML文件删除
    handleRemoveXmlFile(file, fileList) {
      this.form.panoramicViewXmlUrl = ''
      this.xmlFileList = []
      this.$message.success('XML文件已删除')
    },
    
    // 更新XML文件列表
    updateXmlFileList() {
      if (this.form.panoramicViewXmlUrl) {
        const fileName = this.form.panoramicViewXmlUrl.split('/').pop()
        this.xmlFileList = [{
          name: fileName,
          url: this.form.panoramicViewXmlUrl,
          uid: Date.now()
        }]
      } else {
        this.xmlFileList = []
      }
    },
    // 图片预览
    previewImage(url) {
      if (url) {
        this.previewImageUrl = url
        this.previewVisible = true
      }
    },
    closePreview() {
      this.previewVisible = false
      this.previewImageUrl = ''
    },
    // 删除背景图片
    deleteBgImage() {
      this.$confirm('确定删除此图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.bgImgUrl = ''
        this.$message.success('图片已删除')
      }).catch(() => {})
    },
    async beforeUploadXmlFile(file) {
      // 检查文件类型
      if (!file.name.toLowerCase().endsWith('.xml')) {
        this.$message.error('只能上传XML文件！')
        return false
      }
      
      // 检查文件大小（50MB）
      if (file.size > 50 * 1024 * 1024) {
        this.$message.error('文件大小不能超过50MB！')
        return false
      }
      
      try {
        this.$modal.loading("正在上传XML文件，请稍候...")
        const formData = new FormData()
        formData.append('file', file)
        formData.append('industryCode', this.industryCode)
        
        const res = await uploadSceneFile(formData)
        if (res.code === 0 && res.data) {
          // 设置XML文件URL
          this.form.panoramicViewXmlUrl = res.data.fileUrl
          
          // 直接覆盖XML文件列表
          const fileName = res.data.fileUrl.split('/').pop()
          this.xmlFileList = [{
            name: fileName,
            url: res.data.fileUrl,
            uid: Date.now()
          }]
          
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.msg || '上传失败')
        }
      } catch (error) {
        this.$message.error('上传失败')
      } finally {
        this.$modal.closeLoading()
      }
      return false
    },
    // 主题变更回调
    onThemeChange(theme) {
      // 如果主题有默认背景图，可以自动设置
      if (theme && theme.defaultBgImage) {
        this.form.bgImgUrl = theme.defaultBgImage
      }
    },
    // 格式化坐标数据用于提交
    formatCoordinatesForSubmit(coordinates) {
      if (!coordinates || !Array.isArray(coordinates)) {
        return { clickX: '', clickY: '' }
      }
      
      const xValues = coordinates.map(coord => coord.x || '0').join(',')
      const yValues = coordinates.map(coord => coord.y || '0').join(',')
      
      return {
        clickX: xValues,
        clickY: yValues
      }
    },
    // 解析坐标字符串为坐标数组
    parseCoordinatesFromApi(clickX, clickY) {
      const xArray = clickX ? clickX.split(',') : ['']
      const yArray = clickY ? clickY.split(',') : ['']
      
      // 取较长的数组长度，确保坐标对齐
      const maxLength = Math.max(xArray.length, yArray.length)
      const coordinates = []
      
      for (let i = 0; i < maxLength; i++) {
        coordinates.push({
          x: xArray[i] || '',
          y: yArray[i] || ''
        })
      }
      
      // 至少保证有一个坐标组
      return coordinates.length > 0 ? coordinates : [{ x: '', y: '' }]
    },
    // 开始编辑标题
    startEditTitle(index) {
      const category = this.categories[index]
      category.editing = true
      category.editingName = category.name
      
      // 下一帧聚焦输入框
      this.$nextTick(() => {
        // 使用动态ref名称
        const inputRef = this.$refs[`titleInput_${index}`]
        if (inputRef && inputRef[0]) {
          inputRef[0].focus()
          inputRef[0].select()
        }
      })
    },

    // 完成编辑标题
    finishEditTitle(index) {
      const category = this.categories[index]
      if (category.editingName && category.editingName.trim()) {
        category.name = category.editingName.trim()
      }
      category.editing = false
      category.editingName = ''
    },

    // 取消编辑标题
    cancelEditTitle(index) {
      const category = this.categories[index]
      category.editing = false
      category.editingName = ''
    },
    // 设置上传模式
    setUploadMode(type, mode) {
      this.$set(this.uploadModes, type, mode)
    },
    // 背景文件链接输入处理
    handleBgFileUrlInput(value) {
      this.bgFileList = []
      if (value) {
        const fileName = value.split('/').pop() || '外部链接文件'
        this.bgFileList = [{
          name: fileName,
          url: value,
          uid: Date.now()
        }]
      }
    },
    // 视频讲解链接输入处理
    handleVideoExplanationUrlInput(value) {
      this.videoExplanationFileList = []
      if (value) {
        const fileName = value.split('/').pop() || '外部链接文件'
        this.videoExplanationFileList = [{
          name: fileName,
          url: value,
          uid: Date.now()
        }]
      }
    },
    // 介绍视频链接输入处理
    handleIntroduceVideoUrlInput(value) {
      this.introduceVideoFileList = []
      if (value) {
        const fileName = value.split('/').pop() || '外部链接文件'
        this.introduceVideoFileList = [{
          name: fileName,
          url: value,
          uid: Date.now()
        }]
      }
    },
    // 同步文件
    async handleSynchronizeFile() {
      if (!this.form.sceneViewConfigId) {
        this.$message.warning('请先保存配置后再同步文件')
        return
      }
      
      try {
        this.synchronizing = true
        this.$modal.loading("正在同步文件，请稍候...")
        
        // 使用FormData或URLSearchParams传递表单参数
        const formData = new FormData()
        formData.append('viewConfigId', this.form.sceneViewConfigId)
        
        const res = await synchronizationFile(formData)
        
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg || '文件同步失败')
        }
      } catch (error) {
        console.error('同步文件失败:', error)
        this.$message.error('文件同步失败')
      } finally {
        this.synchronizing = false
        this.$modal.closeLoading()
      }
    }
  }
}
</script>

<style scoped>
.page-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.menu-panel {
  width: 250px;
  background-color: #f5f7fa;
  border-right: 1px solid #e4e7ed;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.menu-search {
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
  background-color: #f5f7fa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.menu-tree {
  background-color: #f5f7fa;
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.menu-tree ::v-deep .el-tree-node__content {
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
}

.menu-tree ::v-deep .el-tree-node__content:hover {
  background-color: #e6f7ff;
}

.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background-color: #409EFF;
  color: white;
}

/* 自定义滚动条样式 */
.menu-tree::-webkit-scrollbar {
  width: 6px;
}

.menu-tree::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  border-radius: 3px;
}

.menu-tree::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}

.menu-tree::-webkit-scrollbar-thumb:hover {
  background-color: #a0a0a0;
}

.content-panel {
  flex: 1;
  padding: 20px 20px 80px 20px;
  overflow-y: auto;
  background-color: #fff;
  position: relative;
}

/* 在切换行业loading期间禁止滚动 */
.content-panel.loading-no-scroll {
  overflow: hidden;
}
.mini-block {
  margin-bottom: 20px;
}

.category-block {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 15px;
  background-color: #f9f9f9;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.category-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  cursor: pointer;
  user-select: none;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.category-title:hover {
  background-color: #f0f0f0;
}

.category-title span {
  display: inline-block;
  min-width: 100px;
}

.category-body {
  padding: 12px;
  background: #ffffff;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.sub-category-block {
  margin-bottom: 15px;
}
.sub-category-block:last-child {
  margin-bottom: 0;
}

.sub-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

.sub-category-title {
  font-weight: 500;
}

.sub-category-body {
  padding: 15px;
}

.segment-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.pain-point-block {
  margin-bottom: 15px;
}

.pain-point-block:last-child {
  margin-bottom: 0;
}

.mini-block {
  margin-bottom: 20px;
  min-height: 450px; /* 设置统一的最小高度 */
}

.mini-block:last-child {
  margin-bottom: 0;
}

/* 确保卡片内容区域也有合适的高度 */
.mini-block .el-card__body {
  min-height: 450px;
  display: flex;
  flex-direction: column;
}

/* 视频卡片保持原有样式 */
.video-card {
  display: flex;
  flex-direction: column;
  min-height: 450px;
}

.video-card .el-card__body {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 450px;
}
.video-card-yu{
  min-height: 300px;
}
.video-card .el-card__body {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.segment-scroll {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
  background: #fafbfc;
}
.scene-config-container {
  margin-top: 20px;
}

/* 限制上传图片的显示大小 */
.image-upload .el-upload--picture-card {
  width: 148px;
  height: 148px;
  border-radius: 8px;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 6px;
}

/* 介绍视频首帧图片大小控制 */
.image-upload .el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

/* 上传框也添加圆角 */
.image-upload .el-upload--picture-card {
  border-radius: 8px;
}

/* 图片预览样式 */
.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

/* 图片悬停操作样式 */
.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 6px;
}

.image-preview-container:hover .image-overlay {
  opacity: 1;
}

.preview-icon,
.delete-icon {
  color: white;
  font-size: 20px;
  margin: 0 10px;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-icon:hover,
.delete-icon:hover {
  transform: scale(1.2);
}

.submit-footer {
  position: fixed;
  bottom: 0;
  right: 0;
  left: 250px;
  height: 60px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.submit-footer .el-button {
  min-width: 100px;
}

.menu-search {
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
}

.menu-search .el-input {
  border-radius: 20px;
}

.menu-search .el-input__inner {
  border-radius: 20px;
  background-color: #fff;
}

.highlight {
  background-color: #ffeb3b;
  color: #333;
  font-weight: bold;
}

.menu-list {
  border-right: none;
}

.menu-tree {
  background-color: #f5f7fa;
}

.menu-tree ::v-deep .el-tree-node__content {
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
}

.menu-tree ::v-deep .el-tree-node__content:hover {
  background-color: #e6f7ff;
}

.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background-color: #409EFF;
  color: white;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.highlight {
  background-color: yellow;
  font-weight: bold;
}
</style>
  justify-content: flex-end;
  padding: 0 20px;
}
</style>
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.submit-footer .el-button {
  min-width: 100px;
}

.menu-search {
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
}

.menu-search .el-input {
  border-radius: 20px;
}

.menu-search .el-input__inner {
  border-radius: 20px;
  background-color: #fff;
}

.highlight {
  background-color: #ffeb3b;
  color: #333;
  font-weight: bold;
}

.menu-list {
  border-right: none;
}

.menu-tree {
  background
