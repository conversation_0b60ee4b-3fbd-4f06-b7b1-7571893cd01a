{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754893848755}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRJbmR1c3RyeUxpc3QsIGdldFNjZW5lVHJlZUxpc3QgfSBmcm9tICdAL2FwaS92aWV3L2luZHVzdHJ5Jw0KaW1wb3J0IFNjZW5lQ29uZmlnTm9kZSBmcm9tICcuL1NjZW5lQ29uZmlnTm9kZS52dWUnDQppbXBvcnQgeyBnZXRTY2VuZVZpZXdDb25maWcsIHNjZW5lVmlld1VwZCwgdXBsb2FkU2NlbmVGaWxlLCBzeW5jaHJvbml6YXRpb25GaWxlIH0gZnJvbSAnQC9hcGkvdmlldy9zY2VuZVZpZXcnDQppbXBvcnQgTmV0d29ya1BsYW5Db25maWcgZnJvbSAnLi9OZXR3b3JrUGxhbkNvbmZpZy52dWUnDQppbXBvcnQgQnVzaW5lc3NWYWx1ZUNvbmZpZyBmcm9tICcuL0J1c2luZXNzVmFsdWVDb25maWcudnVlJw0KaW1wb3J0IFZyU2NlbmVDb25maWcgZnJvbSAnLi9WclNjZW5lQ29uZmlnLnZ1ZScNCmltcG9ydCBUaGVtZVNlbGVjdGlvbkRpYWxvZyBmcm9tICcuL1RoZW1lU2VsZWN0aW9uRGlhbG9nLnZ1ZScNCmltcG9ydCBheGlvcyBmcm9tICdheGlvcycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnSW5kdXN0cnlTY2VuZVBhZ2UnLA0KICBjb21wb25lbnRzOiB7DQogICAgU2NlbmVDb25maWdOb2RlLA0KICAgIE5ldHdvcmtQbGFuQ29uZmlnLA0KICAgIEJ1c2luZXNzVmFsdWVDb25maWcsDQogICAgVnJTY2VuZUNvbmZpZywNCiAgICBUaGVtZVNlbGVjdGlvbkRpYWxvZw0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBtZW51RGF0YTogW10sIC8vIOWOn+Wni+iPnOWNleaVsOaNrg0KICAgICAgZmxhdE1lbnVEYXRhOiBbXSwgLy8g5omB5bmz5YyW55qE6KGM5Lia5pWw5o2u77yM55So5LqO5pCc57Si5ZKM5Lia5Yqh6YC76L6RDQogICAgICBhY3RpdmVNZW51OiAnJywNCiAgICAgIGluZHVzdHJ5Q29kZTogJycsDQogICAgICBzZWxlY3RlZFRoZW1lOiBudWxsLCAvLyDlvZPliY3pgInmi6nnmoTkuLvpopgNCiAgICAgIGZvcm06IHsNCiAgICAgICAgbWFpblRpdGxlOiAnJywNCiAgICAgICAgc3ViVGl0bGU6ICcnLA0KICAgICAgICBiZ0ltZ1VybDogJycsDQogICAgICAgIGJnRmlsZVVybDogJycsDQogICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxVcmw6ICcnDQogICAgICB9LA0KICAgICAgc2NlbmVDb25maWdUcmVlOiBbXSwNCiAgICAgIHNlbGVjdGVkTm9kZTogbnVsbCwNCiAgICAgIGxvYWRpbmc6IGZhbHNlLCAvLyDpobXpnaLliqDovb3nirbmgIENCiAgICAgIHN3aXRjaGluZ0luZHVzdHJ5OiBmYWxzZSwgLy8g5paw5aKe77ya5YiH5o2i6KGM5Lia55qEbG9hZGluZ+eKtuaAgQ0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgaW50cm9kdWNlVmlkZW9JbWdVcmw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35LiK5Lyg5LuL57uN6KeG6aKR6aaW5binJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICBpbnRyb2R1Y2VWaWRlb0ZpbGVVcmw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35LiK5Lyg5LuL57uN6KeG6aKRJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICB2aWRlb0V4cGxhbmF0aW9uRmlsZVVybDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fkuIrkvKDorrLop6Pop4bpopEnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICB1cGxvYWRpbmdUeXBlOiAnJywNCiAgICAgIHVwbG9hZGluZ0tleTogJycsDQogICAgICBjYXRlZ29yaWVzOiBbXSwgLy8g5pS55Li656m65pWw57uE77yM5LuO5ZCO56uv5Yqo5oCB6I635Y+WDQogICAgICBpbnRyb2R1Y2VWaWRlbzogew0KICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6ICcnLA0KICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycNCiAgICAgIH0sDQogICAgICB2aWRlb0V4cGxhbmF0aW9uOiB7DQogICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycsDQogICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHNjZW5lVHJlZU9wdGlvbnM6IFtdLA0KICAgICAgc2NlbmVDYXNjYWRlclByb3BzOiB7DQogICAgICAgIGxhYmVsOiAnc2NlbmVOYW1lJywNCiAgICAgICAgdmFsdWU6ICdpZCcsDQogICAgICAgIGNoaWxkcmVuOiAnY2hpbGRyZW4nLA0KICAgICAgICBlbWl0UGF0aDogZmFsc2UsDQogICAgICAgIGNoZWNrU3RyaWN0bHk6IHRydWUsDQogICAgICAgIGRpc2FibGVkOiAoZGF0YSkgPT4gew0KICAgICAgICAgIC8vIOWFgeiuuOaJgOacieiKgueCueWPr+mAie+8jOWPquimgeayoeacieiiq+WFtuS7luWIhuautemAieS4rQ0KICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSB0aGlzLnZpZGVvRXhwbGFuYXRpb24gJiYgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0DQogICAgICAgICAgICA/IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5zb21lKHNlZyA9PiBzZWcuc2NlbmVJZCA9PT0gZGF0YS5pZCkNCiAgICAgICAgICAgIDogZmFsc2UNCiAgICAgICAgICByZXR1cm4gaXNTZWxlY3RlZA0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgYmdGaWxlTGlzdDogW10sIC8vIOiDjOaZr+aWh+S7tuWIl+ihqA0KICAgICAgdmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0OiBbXSwgLy8g6K6y6Kej6KeG6aKR5paH5Lu25YiX6KGoDQogICAgICB4bWxGaWxlTGlzdDogW10sIC8vIFhNTOaWh+S7tuWIl+ihqA0KICAgICAgbmV0d29ya1BsYW5EYXRhTWFwOiB7fSwgLy8g5pS55Li65a+56LGh77yM5oyJ6I+c5Y2VSUTlrZjlgqgNCiAgICAgIGJ1c2luZXNzVmFsdWVEYXRhTWFwOiB7fSwgLy8g5ZWG5Lia5Lu35YC85pWw5o2u5pig5bCEDQogICAgICB2clNjZW5lRGF0YU1hcDoge30sIC8vIFZS55yL546w5Zy65pWw5o2u5pig5bCEDQogICAgICAvLyDlm77niYfpooTop4gNCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHByZXZpZXdJbWFnZVVybDogJycsDQogICAgICBzZWFyY2hLZXl3b3JkOiAnJywNCiAgICAgIHRyZWVFeHBhbmRlZEtleXM6IFtdLCAvLyDmlrDlop7vvJrkv53lrZjmoJHnmoTlsZXlvIDnirbmgIENCiAgICAgIHVwbG9hZE1vZGVzOiB7DQogICAgICAgIGJnRmlsZTogJ3VwbG9hZCcsDQogICAgICAgIHZpZGVvRXhwbGFuYXRpb246ICd1cGxvYWQnLA0KICAgICAgICBpbnRyb2R1Y2VWaWRlbzogJ3VwbG9hZCcNCiAgICAgIH0sDQogICAgICBzeW5jaHJvbml6aW5nOiBmYWxzZSwNCiAgICAgIHN1Ym1pdHRpbmc6IGZhbHNlIC8vIOa3u+WKoOi/meS4quWxnuaApw0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICB2aWRlb1NlZ21lbnRlZExpc3QoKSB7DQogICAgICAvLyDlpoLmnpzmsqHmnInmlbDmja7vvIzpu5jorqTov5Tlm57kuIDooYznqbrmlbDmja4NCiAgICAgIGlmICghdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0IHx8IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIFt7IHRpbWU6ICcnLCBzY2VuZUlkOiAnJywgc2NlbmVOYW1lOiAnJywgc2NlbmVDb2RlOiAnJyB9XQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdA0KICAgIH0sDQogICAgbmV0d29ya1BsYW5EYXRhOiB7DQogICAgICBnZXQoKSB7DQogICAgICAgIGNvbnN0IG1lbnVEYXRhID0gdGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XQ0KICAgICAgICBpZiAoIW1lbnVEYXRhKSB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIG5ldHdvcmtWaWRlb0xpc3Q6IFtdLA0KICAgICAgICAgICAgdmlkZW9FeHBsYW5hdGlvblZvOiB7DQogICAgICAgICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycsDQogICAgICAgICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiBbXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gbWVudURhdGENCiAgICAgIH0sDQogICAgICBzZXQodmFsdWUpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubmV0d29ya1BsYW5EYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIHZhbHVlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYnVzaW5lc3NWYWx1ZURhdGE6IHsNCiAgICAgIGdldCgpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuYnVzaW5lc3NWYWx1ZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XSB8fCBbXQ0KICAgICAgfSwNCiAgICAgIHNldCh2YWx1ZSkgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5idXNpbmVzc1ZhbHVlRGF0YU1hcCwgdGhpcy5hY3RpdmVNZW51LCB2YWx1ZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIHZyU2NlbmVEYXRhOiB7DQogICAgICBnZXQoKSB7DQogICAgICAgIHJldHVybiB0aGlzLnZyU2NlbmVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0gfHwgW10NCiAgICAgIH0sDQogICAgICBzZXQodmFsKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLnZyU2NlbmVEYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIHZhbCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGZpbHRlcmVkTWVudURhdGEoKSB7DQogICAgICBpZiAoIXRoaXMuc2VhcmNoS2V5d29yZCkgew0KICAgICAgICByZXR1cm4gdGhpcy5tZW51RGF0YQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDpgJLlvZLov4fmu6TmoJHlvaLmlbDmja4NCiAgICAgIGNvbnN0IGZpbHRlclRyZWUgPSAobm9kZXMpID0+IHsNCiAgICAgICAgcmV0dXJuIG5vZGVzLm1hcChub2RlID0+IHsNCiAgICAgICAgICBjb25zdCBmaWx0ZXJlZENoaWxkcmVuID0gbm9kZS5jaGlsZHJlbiA/IGZpbHRlclRyZWUobm9kZS5jaGlsZHJlbikgOiBbXQ0KICAgICAgICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBub2RlLm5hbWUgJiYgbm9kZS5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXModGhpcy5zZWFyY2hLZXl3b3JkLnRvTG93ZXJDYXNlKCkpDQogICAgICAgICAgDQogICAgICAgICAgaWYgKG1hdGNoZXNTZWFyY2ggfHwgZmlsdGVyZWRDaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAuLi5ub2RlLA0KICAgICAgICAgICAgICBjaGlsZHJlbjogZmlsdGVyZWRDaGlsZHJlbg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gbnVsbA0KICAgICAgICB9KS5maWx0ZXIoQm9vbGVhbikNCiAgICAgIH0NCiAgICAgIA0KICAgICAgcmV0dXJuIGZpbHRlclRyZWUodGhpcy5tZW51RGF0YSkNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8g5LuOVVJM6I635Y+WdG9rZW7lubborr7nva4NCiAgICB0aGlzLmluaXRUb2tlbkZyb21VcmwoKQ0KICAgIHRoaXMubG9hZEluZHVzdHJ5TWVudSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDku45VUkzojrflj5Z0b2tlbuW5tuiuvue9rg0KICAgIGluaXRUb2tlbkZyb21VcmwoKSB7DQogICAgICBjb25zdCB1cmxQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHdpbmRvdy5sb2NhdGlvbi5zZWFyY2gpDQogIGNvbnN0IHRva2VuID0gdXJsUGFyYW1zLmdldCgndG9rZW4nKQ0KICANCiAgaWYgKHRva2VuKSB7DQogICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2V4dGVybmFsLXRva2VuJywgdG9rZW4pDQogICAgYXhpb3MuZGVmYXVsdHMuaGVhZGVycy5jb21tb25bJ0F1dGhvcml6YXRpb24nXSA9IGBCZWFyZXIgJHt0b2tlbn1gDQogICAgY29uc29sZS5sb2coJ+S7jlVSTOiOt+WPluWIsHRva2VuOicsIHRva2VuKQ0KICB9IGVsc2Ugew0KICAgIGNvbnN0IHN0b3JlZFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2V4dGVybmFsLXRva2VuJykNCiAgICBpZiAoc3RvcmVkVG9rZW4pIHsNCiAgICAgIGF4aW9zLmRlZmF1bHRzLmhlYWRlcnMuY29tbW9uWydBdXRob3JpemF0aW9uJ10gPSBgQmVhcmVyICR7c3RvcmVkVG9rZW59YA0KICAgIH0NCiAgfQ0KICAgIH0sDQogICAgYXN5bmMgbG9hZEluZHVzdHJ5TWVudSgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldEluZHVzdHJ5TGlzdCgpDQogICAgICBpZiAocmVzLmNvZGUgPT09IDAgJiYgQXJyYXkuaXNBcnJheShyZXMuZGF0YSkpIHsNCiAgICAgICAgLy8g6L2s5o2i5pWw5o2u57uT5p6E5Li65qCR5b2i6I+c5Y2VDQogICAgICAgIHRoaXMubWVudURhdGEgPSByZXMuZGF0YS5tYXAocGxhdGUgPT4gKHsNCiAgICAgICAgICBpZDogYHBsYXRlXyR7cGxhdGUucGxhdGVLZXl9YCwNCiAgICAgICAgICBuYW1lOiBwbGF0ZS5wbGF0ZU5hbWUsDQogICAgICAgICAgdHlwZTogJ3BsYXRlJywNCiAgICAgICAgICBjaGlsZHJlbjogcGxhdGUuaW5kdXN0cnlUcmVlTGlzdFZvcyA/IHBsYXRlLmluZHVzdHJ5VHJlZUxpc3RWb3MubWFwKGluZHVzdHJ5ID0+ICh7DQogICAgICAgICAgICBpZDogaW5kdXN0cnkuaWQsDQogICAgICAgICAgICBuYW1lOiBpbmR1c3RyeS5pbmR1c3RyeU5hbWUsDQogICAgICAgICAgICBpbmR1c3RyeUNvZGU6IGluZHVzdHJ5LmluZHVzdHJ5Q29kZSwNCiAgICAgICAgICAgIHBsYXRlOiBpbmR1c3RyeS5wbGF0ZSwNCiAgICAgICAgICAgIHR5cGU6ICdpbmR1c3RyeScNCiAgICAgICAgICB9KSkgOiBbXQ0KICAgICAgICB9KSkNCiAgICAgICAgDQogICAgICAgIC8vIOWIm+W7uuaJgeW5s+WMlueahOihjOS4muaVsOaNru+8jOeUqOS6juS4muWKoemAu+i+kQ0KICAgICAgICB0aGlzLmZsYXRNZW51RGF0YSA9IFtdDQogICAgICAgIHJlcy5kYXRhLmZvckVhY2gocGxhdGUgPT4gew0KICAgICAgICAgIGlmIChwbGF0ZS5pbmR1c3RyeVRyZWVMaXN0Vm9zKSB7DQogICAgICAgICAgICB0aGlzLmZsYXRNZW51RGF0YS5wdXNoKC4uLnBsYXRlLmluZHVzdHJ5VHJlZUxpc3RWb3MpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICANCiAgICAgICAgLy8g6buY6K6k6YCJ5Lit56ys5LiA5Liq6KGM5LiaDQogICAgICAgIGlmICh0aGlzLmZsYXRNZW51RGF0YS5sZW5ndGgpIHsNCiAgICAgICAgICB0aGlzLmFjdGl2ZU1lbnUgPSBTdHJpbmcodGhpcy5mbGF0TWVudURhdGFbMF0uaWQpDQogICAgICAgICAgdGhpcy5pbmR1c3RyeUNvZGUgPSB0aGlzLmZsYXRNZW51RGF0YVswXS5pbmR1c3RyeUNvZGUNCiAgICAgICAgICAvLyDnrYnlvoVET03mm7TmlrDlkI7orr7nva7moJHnu4Tku7bnmoTlvZPliY3oioLngrkNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAvLyDnoa7kv53moJHnu4Tku7blt7LmuLLmn5Plubborr7nva7lvZPliY3pgInkuK3oioLngrkNCiAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLm1lbnVUcmVlICYmIHRoaXMuJHJlZnMubWVudVRyZWUuc2V0Q3VycmVudEtleSkgew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLm1lbnVUcmVlLnNldEN1cnJlbnRLZXkodGhpcy5hY3RpdmVNZW51KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgDQogICAgICAgICAgLy8g5Yqg6L2956ys5LiA5Liq6KGM5Lia55qE5pWw5o2uDQogICAgICAgICAgYXdhaXQgdGhpcy5oYW5kbGVTZWxlY3QodGhpcy5hY3RpdmVNZW51KQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICBoYW5kbGVUcmVlTm9kZUNsaWNrKGRhdGEpIHsNCiAgICAgIC8vIOWPquacieeCueWHu+ihjOS4muiKgueCueaJjeWkhOeQhg0KICAgICAgaWYgKGRhdGEudHlwZSA9PT0gJ2luZHVzdHJ5Jykgew0KICAgICAgICB0aGlzLmhhbmRsZVNlbGVjdChTdHJpbmcoZGF0YS5pZCkpDQogICAgICAgIHRoaXMuaW5kdXN0cnlDb2RlID0gZGF0YS5pbmR1c3RyeUNvZGU7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZVNlYXJjaCh2YWx1ZSkgew0KICAgICAgdGhpcy5zZWFyY2hLZXl3b3JkID0gdmFsdWUNCiAgICB9LA0KICAgIGhpZ2hsaWdodFRleHQodGV4dCkgew0KICAgICAgaWYgKCF0aGlzLnNlYXJjaEtleXdvcmQpIHJldHVybiB0ZXh0DQogICAgICBjb25zdCByZWdleCA9IG5ldyBSZWdFeHAoYCgke3RoaXMuc2VhcmNoS2V5d29yZH0pYCwgJ2dpJykNCiAgICAgIHJldHVybiB0ZXh0LnJlcGxhY2UocmVnZXgsICc8c3BhbiBjbGFzcz0iaGlnaGxpZ2h0Ij4kMTwvc3Bhbj4nKQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlU2VsZWN0KGlkLCBrZWVwU2VsZWN0ZWROb2RlID0gZmFsc2UsIHNob3dMb2FkaW5nID0gdHJ1ZSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5byA5ZCv5YiH5o2i6KGM5Lia55qEbG9hZGluZw0KICAgICAgICBpZiAoc2hvd0xvYWRpbmcpIHsNCiAgICAgICAgICB0aGlzLnN3aXRjaGluZ0luZHVzdHJ5ID0gdHJ1ZQ0KICAgICAgICAgIC8vIOemgeeUqOmhtemdoua7muWKqA0KICAgICAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnaGlkZGVuJw0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgICB0aGlzLmFjdGl2ZU1lbnUgPSBpZA0KICAgICAgICBhd2FpdCB0aGlzLmxvYWRTY2VuZVRyZWVPcHRpb25zKHRoaXMuYWN0aXZlTWVudSkNCiAgICAgICAgDQogICAgICAgIC8vIOS/neWtmOW9k+WJjemAieS4reeahOiKgueCuQ0KICAgICAgICBjb25zdCBjdXJyZW50U2VsZWN0ZWROb2RlID0ga2VlcFNlbGVjdGVkTm9kZSA/IHRoaXMuc2VsZWN0ZWROb2RlIDogbnVsbA0KICAgICAgICANCiAgICAgICAgLy8g6YeN572u5Li76aKY6YCJ5oupDQogICAgICAgIHRoaXMuc2VsZWN0ZWRUaGVtZSA9IG51bGwNCiAgICAgICAgDQogICAgICAgIC8vIOS7juaJgeW5s+WMluiPnOWNleaVsOaNruS4reiOt+WPluW9k+WJjeihjOS4mueahCBpbmR1c3RyeUNvZGUNCiAgICAgICAgY29uc3QgY3VycmVudEluZHVzdHJ5ID0gdGhpcy5mbGF0TWVudURhdGEuZmluZChpdGVtID0+IFN0cmluZyhpdGVtLmlkKSA9PT0gaWQpDQogICAgICAgIGNvbnN0IGluZHVzdHJ5Q29kZSA9IGN1cnJlbnRJbmR1c3RyeSA/IGN1cnJlbnRJbmR1c3RyeS5pbmR1c3RyeUNvZGUgOiBudWxsDQogICAgICAgIA0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBnZXRTY2VuZVZpZXdDb25maWcoeyBpbmR1c3RyeUNvZGU6IGluZHVzdHJ5Q29kZSB9KQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDAgJiYgcmVzLmRhdGEpIHsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDlkIzmraXkuLvmoIfpopjjgIHlia/moIfpopjjgIHog4zmma/lm77niYfjgIFYTUzmlofku7bnrYkNCiAgICAgICAgICB0aGlzLmZvcm0uc2NlbmVWaWV3Q29uZmlnSWQgPSByZXMuZGF0YS5zY2VuZVZpZXdDb25maWdJZCB8fCAnJw0KICAgICAgICAgIHRoaXMuZm9ybS5tYWluVGl0bGUgPSByZXMuZGF0YS5tYWluVGl0bGUgfHwgJycNCiAgICAgICAgICB0aGlzLmZvcm0uc3ViVGl0bGUgPSByZXMuZGF0YS5zdWJUaXRsZSB8fCAnJw0KICAgICAgICAgIHRoaXMuZm9ybS5iZ0ltZ1VybCA9IHJlcy5kYXRhLmJhY2tncm91bmRJbWdGaWxlVXJsIHx8ICcnDQogICAgICAgICAgdGhpcy5mb3JtLmJnRmlsZVVybCA9IHJlcy5kYXRhLmJhY2tncm91bmRGaWxlVXJsIHx8ICcnDQogICAgICAgICAgdGhpcy5mb3JtLnBhbm9yYW1pY1ZpZXdYbWxVcmwgPSByZXMuZGF0YS5wYW5vcmFtaWNWaWV3WG1sVXJsIHx8ICcnDQogICAgICAgICAgDQogICAgICAgICAgLy8g5pu05paw6IOM5pmv5paH5Lu25YiX6KGoDQogICAgICAgICAgdGhpcy51cGRhdGVCZ0ZpbGVMaXN0KCkNCiAgICAgICAgICANCiAgICAgICAgICAvLyDmm7TmlrBYTUzmlofku7bliJfooagNCiAgICAgICAgICB0aGlzLnVwZGF0ZVhtbEZpbGVMaXN0KCkNCiAgICAgICAgICANCiAgICAgICAgICAvLyDlm57mmL7kuLvpopjpgInmi6kNCiAgICAgICAgICBpZiAocmVzLmRhdGEudGhlbWVJbmZvVm8pIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRUaGVtZSA9IHsNCiAgICAgICAgICAgICAgdGhlbWVJZDogcmVzLmRhdGEudGhlbWVJbmZvVm8udGhlbWVJZCwNCiAgICAgICAgICAgICAgdGhlbWVOYW1lOiByZXMuZGF0YS50aGVtZUluZm9Wby50aGVtZU5hbWUsDQogICAgICAgICAgICAgIHRoZW1lRWZmZWN0SW1nOiByZXMuZGF0YS50aGVtZUluZm9Wby50aGVtZUVmZmVjdEltZywNCiAgICAgICAgICAgICAgcmVtYXJrOiByZXMuZGF0YS50aGVtZUluZm9Wby5yZW1hcmsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFRoZW1lID0gbnVsbA0KICAgICAgICAgIH0NCiAgICAgICAgICANCiAgICAgICAgICAvLyDlpITnkIYgc2NlbmVEZWZhdWx0Q29uZmlnVm9MaXN077yM5Yqo5oCB55Sf5oiQIGNhdGVnb3JpZXMNCiAgICAgICAgICBpZiAocmVzLmRhdGEuc2NlbmVEZWZhdWx0Q29uZmlnVm9MaXN0ICYmIEFycmF5LmlzQXJyYXkocmVzLmRhdGEuc2NlbmVEZWZhdWx0Q29uZmlnVm9MaXN0KSkgew0KICAgICAgICAgICAgdGhpcy5jYXRlZ29yaWVzID0gcmVzLmRhdGEuc2NlbmVEZWZhdWx0Q29uZmlnVm9MaXN0Lm1hcChjb25maWdJdGVtID0+ICh7DQogICAgICAgICAgICAgIGlkOiBjb25maWdJdGVtLmlkLA0KICAgICAgICAgICAgICBrZXk6IGNvbmZpZ0l0ZW0ua2V5TmFtZSwNCiAgICAgICAgICAgICAgbmFtZTogY29uZmlnSXRlbS5uYW1lLA0KICAgICAgICAgICAgICBlbmFibGVkOiBjb25maWdJdGVtLmtleVZhbHVlID09PSAnMCcsIC8vIGtleVZhbHVl5Li6JzAn6KGo56S65ZCv55SoDQogICAgICAgICAgICAgIGVkaXRpbmc6IGZhbHNlLA0KICAgICAgICAgICAgICBlZGl0aW5nTmFtZTogJycsDQogICAgICAgICAgICAgIG9yaWdpbmFsTmFtZTogY29uZmlnSXRlbS5uYW1lLA0KICAgICAgICAgICAgICByZW1hcms6IGNvbmZpZ0l0ZW0ucmVtYXJrLA0KICAgICAgICAgICAgICBjbGFzc2lmaWNhdGlvbjogY29uZmlnSXRlbS5jbGFzc2lmaWNhdGlvbiwNCiAgICAgICAgICAgICAgZGVmYXVsdFN0YXR1czogY29uZmlnSXRlbS5kZWZhdWx0U3RhdHVzDQogICAgICAgICAgICB9KSkNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5p+l5om+5Zy65pmv6YWN572u5YiG57G7DQogICAgICAgICAgICBjb25zdCBzY2VuZUNhdGVnb3J5ID0gcmVzLmRhdGEuc2NlbmVEZWZhdWx0Q29uZmlnVm9MaXN0LmZpbmQoaXRlbSA9PiBpdGVtLmtleU5hbWUgPT09ICdkZWZhdWx0X3NjZW5lJykNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5aSE55CG6KeG6aKR6K6y6Kej5pWw5o2uDQogICAgICAgICAgICBpZiAoc2NlbmVDYXRlZ29yeSAmJiBzY2VuZUNhdGVnb3J5LmluZHVzdHJ5U2NlbmVJbmZvVm8gJiYgc2NlbmVDYXRlZ29yeS5pbmR1c3RyeVNjZW5lSW5mb1ZvLnZpZGVvRXhwbGFuYXRpb25Wbykgew0KICAgICAgICAgICAgICBjb25zdCB2aWRlb0RhdGEgPSBzY2VuZUNhdGVnb3J5LmluZHVzdHJ5U2NlbmVJbmZvVm8udmlkZW9FeHBsYW5hdGlvblZvDQogICAgICAgICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbiA9IHsNCiAgICAgICAgICAgICAgICBzdGF0dXM6IHZpZGVvRGF0YS5zdGF0dXMgfHwgJzAnLA0KICAgICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiB2aWRlb0RhdGEuYmFja2dyb3VuZEZpbGVVcmwgfHwgJycsDQogICAgICAgICAgICAgICAgdmlkZW9TZWdtZW50ZWRWb0xpc3Q6IHZpZGVvRGF0YS52aWRlb1NlZ21lbnRlZFZvTGlzdCA/IHZpZGVvRGF0YS52aWRlb1NlZ21lbnRlZFZvTGlzdC5tYXAoc2VnID0+ICh7DQogICAgICAgICAgICAgICAgICB0aW1lOiBzZWcudGltZSwNCiAgICAgICAgICAgICAgICAgIHNjZW5lQ29kZTogc2VnLnNjZW5lQ29kZSwNCiAgICAgICAgICAgICAgICAgIHNjZW5lTmFtZTogc2VnLnNjZW5lTmFtZSwNCiAgICAgICAgICAgICAgICAgIHNjZW5lSWQ6IHRoaXMuZmluZFNjZW5lSWRCeUNvZGUoc2VnLnNjZW5lQ29kZSkgLy8g5qC55o2uc2NlbmVDb2Rl5p+l5om+c2NlbmVJZA0KICAgICAgICAgICAgICAgIH0pKSA6IFtdDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbiA9IHsNCiAgICAgICAgICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycsDQogICAgICAgICAgICAgICAgdmlkZW9TZWdtZW50ZWRWb0xpc3Q6IFtdDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5pu05paw6KeG6aKR6K6y6Kej5paH5Lu25YiX6KGoDQogICAgICAgICAgICB0aGlzLnVwZGF0ZVZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCgpDQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOWkhOeQhuWcuuaZr+mFjee9ruagkQ0KICAgICAgICAgICAgaWYgKHNjZW5lQ2F0ZWdvcnkgJiYgc2NlbmVDYXRlZ29yeS5pbmR1c3RyeVNjZW5lSW5mb1ZvICYmIHNjZW5lQ2F0ZWdvcnkuaW5kdXN0cnlTY2VuZUluZm9Wby5zY2VuZUxpc3RWbykgew0KICAgICAgICAgICAgICB0aGlzLnNjZW5lQ29uZmlnVHJlZSA9IHRoaXMuYWRhcHRTY2VuZVRyZWUoc2NlbmVDYXRlZ29yeS5pbmR1c3RyeVNjZW5lSW5mb1ZvLnNjZW5lTGlzdFZvKQ0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgLy8g5aaC5p6c6ZyA6KaB5L+d5oyB6YCJ5Lit6IqC54K5DQogICAgICAgICAgICAgIGlmIChrZWVwU2VsZWN0ZWROb2RlICYmIGN1cnJlbnRTZWxlY3RlZE5vZGUpIHsNCiAgICAgICAgICAgICAgICBjb25zdCBub2RlVG9TZWxlY3QgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgY3VycmVudFNlbGVjdGVkTm9kZS5pZCkNCiAgICAgICAgICAgICAgICBpZiAobm9kZVRvU2VsZWN0KSB7DQogICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkTm9kZSA9IG5vZGVUb1NlbGVjdA0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkTm9kZSA9IHRoaXMuc2NlbmVDb25maWdUcmVlLmxlbmd0aCA+IDAgPyB0aGlzLnNjZW5lQ29uZmlnVHJlZVswXSA6IG51bGwNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8g6buY6K6k6YCJ5oup56ys5LiA5Liq6IqC54K5DQogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSB0aGlzLnNjZW5lQ29uZmlnVHJlZS5sZW5ndGggPiAwID8gdGhpcy5zY2VuZUNvbmZpZ1RyZWVbMF0gOiBudWxsDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIC8vIOayoeacieWcuuaZr+aVsOaNruaXtua4heepug0KICAgICAgICAgICAgICB0aGlzLnNjZW5lQ29uZmlnVHJlZSA9IFtdDQogICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWROb2RlID0gbnVsbA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICANCiAgICAgICAgICAvLyDlpITnkIbnvZHnu5zmlrnmoYjmlbDmja4NCiAgICAgICAgICBpZiAocmVzLmRhdGEubmV0d29ya1NvbHV0aW9uVm8pIHsNCiAgICAgICAgICAgIGNvbnN0IG5ldHdvcmtEYXRhID0gew0KICAgICAgICAgICAgICBuZXR3b3JrVmlkZW9MaXN0OiByZXMuZGF0YS5uZXR3b3JrU29sdXRpb25Wby5uZXR3b3JrVmlkZW9MaXN0IHx8IFtdLA0KICAgICAgICAgICAgICB2aWRlb0V4cGxhbmF0aW9uVm86IHJlcy5kYXRhLm5ldHdvcmtTb2x1dGlvblZvLnZpZGVvRXhwbGFuYXRpb25WbyB8fCB7DQogICAgICAgICAgICAgICAgc3RhdHVzOiAnMCcsDQogICAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6ICcnLA0KICAgICAgICAgICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiBbXQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5uZXR3b3JrUGxhbkRhdGFNYXAsIHRoaXMuYWN0aXZlTWVudSwgbmV0d29ya0RhdGEpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5aSE55CG5ZWG5Lia5Lu35YC85pWw5o2uDQogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbW1lcmNpYWxWYWx1ZUxpc3RWbykgew0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuYnVzaW5lc3NWYWx1ZURhdGFNYXAsIHRoaXMuYWN0aXZlTWVudSwgcmVzLmRhdGEuY29tbWVyY2lhbFZhbHVlTGlzdFZvKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWkhOeQhlZS55yL546w5Zy65pWw5o2uDQogICAgICAgICAgaWYgKHJlcy5kYXRhLnZySW5mb0xpc3RWbykgew0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMudnJTY2VuZURhdGFNYXAsIHRoaXMuYWN0aXZlTWVudSwgcmVzLmRhdGEudnJJbmZvTGlzdFZvKQ0KICAgICAgICAgIH0NCiAgICAgICAgICANCiAgICAgICAgICAvLyDlhbbku5bmlbDmja7lpITnkIbpgLvovpHkv53mjIHkuI3lj5guLi4NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295pWw5o2u5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3mlbDmja7lpLHotKUnKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgLy8g5YWz6Zet5YiH5o2i6KGM5Lia55qEbG9hZGluZw0KICAgICAgICBpZiAoc2hvd0xvYWRpbmcpIHsNCiAgICAgICAgICB0aGlzLnN3aXRjaGluZ0luZHVzdHJ5ID0gZmFsc2UNCiAgICAgICAgICAvLyDmgaLlpI3pobXpnaLmu5rliqgNCiAgICAgICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJycNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIHJldHVybiBmYWxzZSAvLyDmi6bmiKrpu5jorqTkuIrkvKDooYzkuLoNCiAgICB9LA0KICAgIGFkZFNlZ21lbnQoKSB7DQogICAgICB0aGlzLmZvcm0udmlkZW9TZWdtZW50ZWRWb0xpc3QucHVzaCh7IHRpbWU6ICcnLCBzY2VuZTogJycgfSkNCiAgICB9LA0KICAgIHJlbW92ZVNlZ21lbnQoaW5kZXgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0udmlkZW9TZWdtZW50ZWRWb0xpc3QubGVuZ3RoID49IDEpIHsNCiAgICAgICAgdGhpcy5mb3JtLnZpZGVvU2VnbWVudGVkVm9MaXN0LnNwbGljZShpbmRleCwgMSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGJlZm9yZVVwbG9hZEludHJvZHVjZUltZyhmaWxlLCB0eXBlLCBrZXkpIHsNCiAgICAgIGlmICghZmlsZS50eXBlLnN0YXJ0c1dpdGgoJ2ltYWdlLycpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oOWbvueJh+aWh+S7tu+8gScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgDQogICAgICB0cnkgew0KICAgICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjkuIrkvKDlm77niYfvvIzor7fnqI3lgJkuLi4iKQ0KICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZScsIGZpbGUpDQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnaW5kdXN0cnlDb2RlJywgdGhpcy5pbmR1c3RyeUNvZGUpDQogICAgICAgIA0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCB1cGxvYWRTY2VuZUZpbGUoZm9ybURhdGEpDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCAmJiByZXMuZGF0YSkgew0KICAgICAgICAgIGlmICh0eXBlICYmIGtleSkgew0KICAgICAgICAgICAgLy8g6ZKI5a+55LuL57uN6KeG6aKR5ZKM6KeG6aKR6K6y6Kej55qE5LiK5Lyg77yM5Y2V54us5LiK5Lyg5Zu+54mH5pe25L2/55SoIGZpbGVVcmwNCiAgICAgICAgICAgIHRoaXNbdHlwZV1ba2V5XSA9IHJlcy5kYXRhLmZpbGVVcmwNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g6ZKI5a+55Li76IOM5pmv5Zu+54mH55qE5LiK5LygDQogICAgICAgICAgICB0aGlzLmZvcm0uYmdJbWdVcmwgPSByZXMuZGF0YS5maWxlVXJsDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiK5Lyg5oiQ5YqfJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+S4iuS8oOWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZhbHNlDQogICAgfSwNCiAgICBhc3luYyBiZWZvcmVVcGxvYWRJbnRyb2R1Y2VWaWRlbyhmaWxlLCB0eXBlLCBrZXkpIHsNCiAgICAgIC8vIOWmguaenOaYr+S4u+iDjOaZr+aWh+S7tuS4iuS8oO+8iOayoeaciXR5cGXlkoxrZXnlj4LmlbDvvIkNCiAgICAgIGlmICghdHlwZSAmJiAha2V5KSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5LiK5Lyg5paH5Lu277yM6K+356iN5YCZLi4uIikNCiAgICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQogICAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSkNCiAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2luZHVzdHJ5Q29kZScsIHRoaXMuaW5kdXN0cnlDb2RlKQ0KICAgICAgICAgIA0KICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZFNjZW5lRmlsZShmb3JtRGF0YSkNCiAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDAgJiYgcmVzLmRhdGEpIHsNCiAgICAgICAgICAgIC8vIOiuvue9ruiDjOaZr+aWh+S7tlVSTA0KICAgICAgICAgICAgdGhpcy5mb3JtLmJnRmlsZVVybCA9IHJlcy5kYXRhLmZpbGVVcmwNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g55u05o6l6KaG55uW6IOM5pmv5paH5Lu25YiX6KGoDQogICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHJlcy5kYXRhLmZpbGVVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICAgICAgdGhpcy5iZ0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgICAgIHVybDogcmVzLmRhdGEuZmlsZVVybCwNCiAgICAgICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgICAgICB9XQ0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDlpoLmnpzmmK9NUDTmlofku7bkuJTov5Tlm57kuoZpbWdVcmzvvIzoh6rliqjorr7nva7og4zmma/lm77niYfpppbluKcNCiAgICAgICAgICAgIGlmIChmaWxlLnR5cGUgPT09ICd2aWRlby9tcDQnICYmIHJlcy5kYXRhLmltZ1VybCkgew0KICAgICAgICAgICAgICB0aGlzLmZvcm0uYmdJbWdVcmwgPSByZXMuZGF0YS5pbWdVcmwNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip/vvIzlt7Loh6rliqjnlJ/miJDog4zmma/lm77niYfpppbluKcnKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip8nKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+S4iuS8oOWksei0pScpDQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOWksei0pScpDQogICAgICAgIH0gZmluYWxseSB7DQogICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCkNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5YW25LuW6KeG6aKR5LiK5Lyg6YC76L6R77yI5LuL57uN6KeG6aKR44CB6K6y6Kej6KeG6aKR562J77yJDQogICAgICBpZiAoIWZpbGUudHlwZS5zdGFydHNXaXRoKCd2aWRlby8nKSAmJiAhZmlsZS5uYW1lLmVuZHNXaXRoKCcubXA0JykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5LygTVA06KeG6aKR5paH5Lu277yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4iuS8oOinhumike+8jOivt+eojeWAmS4uLiIpDQogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdpbmR1c3RyeUNvZGUnLCB0aGlzLmluZHVzdHJ5Q29kZSkNCiAgICAgICAgDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZFNjZW5lRmlsZShmb3JtRGF0YSkNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgaWYgKHR5cGUgJiYga2V5KSB7DQogICAgICAgICAgICAvLyDpkojlr7nku4vnu43op4bpopHlkozop4bpopHorrLop6PnmoTkuIrkvKANCiAgICAgICAgICAgIHRoaXNbdHlwZV1ba2V5XSA9IHJlcy5kYXRhLmZpbGVVcmwNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g55u05o6l6KaG55uW5a+55bqU55qE5paH5Lu25YiX6KGoDQogICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHJlcy5kYXRhLmZpbGVVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICAgICAgaWYgKHR5cGUgPT09ICdpbnRyb2R1Y2VWaWRlbycgJiYga2V5ID09PSAnYmFja2dyb3VuZEZpbGVVcmwnKSB7DQogICAgICAgICAgICAgIHRoaXMuaW50cm9kdWNlVmlkZW9GaWxlTGlzdCA9IFt7DQogICAgICAgICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgICAgICAgdXJsOiByZXMuZGF0YS5maWxlVXJsLA0KICAgICAgICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICAgICAgICB9XQ0KICAgICAgICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAndmlkZW9FeHBsYW5hdGlvbicgJiYga2V5ID09PSAnYmFja2dyb3VuZEZpbGVVcmwnKSB7DQogICAgICAgICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0ID0gW3sNCiAgICAgICAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICAgICAgICB1cmw6IHJlcy5kYXRhLmZpbGVVcmwsDQogICAgICAgICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgICAgICAgIH1dDQogICAgICAgICAgICB9DQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOWmguaenOaYr+S7i+e7jeinhumikeS4iuS8oO+8jOS4lOi/lOWbnuS6hmltZ1VybO+8jOiHquWKqOiuvue9ruS7i+e7jeinhumikemmluW4pw0KICAgICAgICAgICAgaWYgKHR5cGUgPT09ICdpbnRyb2R1Y2VWaWRlbycgJiYga2V5ID09PSAnYmFja2dyb3VuZEZpbGVVcmwnICYmIHJlcy5kYXRhLmltZ1VybCkgew0KICAgICAgICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvLmJhY2tncm91bmRJbWdGaWxlVXJsID0gcmVzLmRhdGEuaW1nVXJsDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiK5Lyg5oiQ5Yqf77yM5bey6Ieq5Yqo55Sf5oiQ5LuL57uN6KeG6aKR6aaW5binJykNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiK5Lyg5oiQ5YqfJykNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfkuIrkvKDlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDlpLHotKUnKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCkNCiAgICAgIH0NCiAgICAgIHJldHVybiBmYWxzZQ0KICAgIH0sDQogICAgYmVmb3JlVXBsb2FkRXhwbGFuYXRpb25WaWRlbyhmaWxlKSB7DQogICAgICB0aGlzLnVwbG9hZGluZ1R5cGUgPSAnbXA0Jw0KICAgICAgdGhpcy51cGxvYWRpbmdLZXkgPSAndmlkZW9FeHBsYW5hdGlvbkZpbGVVcmwnDQogICAgICByZXR1cm4gdGhpcy5oYW5kbGVCZWZvcmVVcGxvYWQoZmlsZSkNCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8mua3u+WKoOWcuuaZr+mFjee9ruiKgueCuQ0KICAgIGFkZFNjZW5lQ29uZmlnTm9kZShwYXJlbnRJZCA9IG51bGwpIHsNCiAgICAgIGNvbnN0IG5ld05vZGUgPSB7DQogICAgICAgIGlkOiBEYXRlLm5vdygpLCAvLyDnlJ/miJDllK/kuIBJRA0KICAgICAgICBuYW1lOiAn5paw5Zy65pmvJywNCiAgICAgICAgdHlwZTogJ3NjZW5lJywgLy8g57G75Z6L5Li65Zy65pmvDQogICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgIGNoaWxkcmVuOiBbXSwNCiAgICAgICAgcGFyZW50SWQ6IHBhcmVudElkDQogICAgICB9DQogICAgICBpZiAocGFyZW50SWQpIHsNCiAgICAgICAgY29uc3QgcGFyZW50Tm9kZSA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBwYXJlbnRJZCkNCiAgICAgICAgaWYgKHBhcmVudE5vZGUpIHsNCiAgICAgICAgICBwYXJlbnROb2RlLmNoaWxkcmVuLnB1c2gobmV3Tm9kZSkNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zY2VuZUNvbmZpZ1RyZWUucHVzaChuZXdOb2RlKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIG5ld05vZGUuaWQNCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8muWIoOmZpOWcuuaZr+mFjee9ruiKgueCuQ0KICAgIHJlbW92ZVNjZW5lQ29uZmlnTm9kZShub2RlSWQpIHsNCiAgICAgIHRoaXMuc2NlbmVDb25maWdUcmVlID0gdGhpcy5zY2VuZUNvbmZpZ1RyZWUuZmlsdGVyKG5vZGUgPT4gbm9kZS5pZCAhPT0gbm9kZUlkKQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5p+l5om+6IqC54K5DQogICAgZmluZE5vZGVCeUlkKG5vZGVzLCBpZCkgew0KICAgICAgZm9yIChjb25zdCBub2RlIG9mIG5vZGVzKSB7DQogICAgICAgIGlmIChub2RlLmlkID09PSBpZCkgew0KICAgICAgICAgIHJldHVybiBub2RlDQogICAgICAgIH0NCiAgICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgY29uc3QgZm91bmQgPSB0aGlzLmZpbmROb2RlQnlJZChub2RlLmNoaWxkcmVuLCBpZCkNCiAgICAgICAgICBpZiAoZm91bmQpIHsNCiAgICAgICAgICAgIHJldHVybiBmb3VuZA0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIG51bGwNCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8mua3u+WKoOWcuuaZr+eahOeXm+eCueS7t+WAvA0KICAgIGFkZFNjZW5lUGFpblBvaW50KG5vZGVJZCkgew0KICAgICAgY29uc3Qgbm9kZSA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBub2RlSWQpDQogICAgICBpZiAobm9kZSAmJiBub2RlLnR5cGUgPT09ICdzY2VuZScpIHsNCiAgICAgICAgbm9kZS5wYWluUG9pbnRzID0gbm9kZS5wYWluUG9pbnRzIHx8IFtdDQogICAgICAgIG5vZGUucGFpblBvaW50cy5wdXNoKHsgdGl0bGU6ICcnLCBjb250ZW50czogWycnXSwgc2hvd1RpbWU6ICcnIH0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrliKDpmaTlnLrmma/nmoTnl5vngrnku7flgLwNCiAgICByZW1vdmVTY2VuZVBhaW5Qb2ludChub2RlSWQsIGlkeCkgew0KICAgICAgY29uc3Qgbm9kZSA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBub2RlSWQpDQogICAgICBpZiAobm9kZSAmJiBub2RlLnR5cGUgPT09ICdzY2VuZScpIHsNCiAgICAgICAgbm9kZS5wYWluUG9pbnRzID0gbm9kZS5wYWluUG9pbnRzIHx8IFtdDQogICAgICAgIG5vZGUucGFpblBvaW50cy5zcGxpY2UoaWR4LCAxKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5re75Yqg5Zy65pmv55eb54K55YaF5a6555qE6aG5DQogICAgYWRkU2NlbmVQYWluQ29udGVudChub2RlSWQsIGlkeCkgew0KICAgICAgY29uc3Qgbm9kZSA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBub2RlSWQpDQogICAgICBpZiAobm9kZSAmJiBub2RlLnR5cGUgPT09ICdzY2VuZScpIHsNCiAgICAgICAgbm9kZS5wYWluUG9pbnRzID0gbm9kZS5wYWluUG9pbnRzIHx8IFtdDQogICAgICAgIG5vZGUucGFpblBvaW50c1tpZHhdLmNvbnRlbnRzLnB1c2goJycpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrliKDpmaTlnLrmma/nl5vngrnlhoXlrrnnmoTpobkNCiAgICByZW1vdmVTY2VuZVBhaW5Db250ZW50KG5vZGVJZCwgaWR4LCBjaWR4KSB7DQogICAgICBjb25zdCBub2RlID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIG5vZGVJZCkNCiAgICAgIGlmIChub2RlICYmIG5vZGUudHlwZSA9PT0gJ3NjZW5lJykgew0KICAgICAgICBub2RlLnBhaW5Qb2ludHMgPSBub2RlLnBhaW5Qb2ludHMgfHwgW10NCiAgICAgICAgbm9kZS5wYWluUG9pbnRzW2lkeF0uY29udGVudHMuc3BsaWNlKGNpZHgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrmt7vliqDlnLrmma/nmoTmiJDmnKzpooTkvLDlhoXlrrkNCiAgICBhZGRTY2VuZUNvc3RDb250ZW50KG5vZGVJZCkgew0KICAgICAgY29uc3Qgbm9kZSA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBub2RlSWQpDQogICAgICBpZiAobm9kZSAmJiBub2RlLnR5cGUgPT09ICdjb3N0RXN0aW1hdGUnKSB7DQogICAgICAgIG5vZGUuY29udGVudHMgPSBub2RlLmNvbnRlbnRzIHx8IFtdDQogICAgICAgIG5vZGUuY29udGVudHMucHVzaCgnJykNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8muWIoOmZpOWcuuaZr+eahOaIkOacrOmihOS8sOWGheWuuQ0KICAgIHJlbW92ZVNjZW5lQ29zdENvbnRlbnQobm9kZUlkLCBjaWR4KSB7DQogICAgICBjb25zdCBub2RlID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIG5vZGVJZCkNCiAgICAgIGlmIChub2RlICYmIG5vZGUudHlwZSA9PT0gJ2Nvc3RFc3RpbWF0ZScpIHsNCiAgICAgICAgbm9kZS5jb250ZW50cyA9IG5vZGUuY29udGVudHMgfHwgW10NCiAgICAgICAgbm9kZS5jb250ZW50cy5zcGxpY2UoY2lkeCwgMSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8muS4iuS8oOWcuuaZr+mFjee9ruWbvueJhw0KICAgIGJlZm9yZVVwbG9hZFNjZW5lQ29uZmlnSW1nKGZpbGUsIHR5cGUsIGtleSkgew0KICAgICAgaWYgKCFmaWxlLnR5cGUuc3RhcnRzV2l0aCgnaW1hZ2UvJykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5Lyg5Zu+54mH5paH5Lu277yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICBjb25zdCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpDQogICAgICByZWFkZXIub25sb2FkID0gZSA9PiB7DQogICAgICAgIHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCB0eXBlKVtrZXldID0gZS50YXJnZXQucmVzdWx0DQogICAgICB9DQogICAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlKQ0KICAgICAgcmV0dXJuIGZhbHNlDQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrkuIrkvKDlnLrmma/phY3nva7mlofku7YNCiAgICBiZWZvcmVVcGxvYWRTY2VuZUNvbmZpZ0ZpbGUoZmlsZSwgdHlwZSwga2V5KSB7DQogICAgICAvLyDov5nph4zlj6rlgZrmlofku7blkI3lm57mmL4NCiAgICAgIHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCB0eXBlKVtrZXldID0gZmlsZS5uYW1lDQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIGhhbmRsZVNjZW5lTm9kZUNsaWNrKG5vZGUpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWROb2RlID0gbm9kZQ0KICAgIH0sDQogICAgLy8g6YCC6YWN5Ye95pWw56e75YiwbWV0aG9kc+S4re+8jOS+m+aOpeWPo+aVsOaNrumAgumFjeS9v+eUqA0KICAgIGFkYXB0U2NlbmVUcmVlKHJhd1RyZWUsIHBhcmVudCA9IG51bGwpIHsNCiAgICAgIGlmICghcmF3VHJlZSkgcmV0dXJuIFtdDQogICAgICBjb25zdCBhcnIgPSBBcnJheS5pc0FycmF5KHJhd1RyZWUpID8gcmF3VHJlZSA6IFtyYXdUcmVlXQ0KICAgICAgcmV0dXJuIGFyci5tYXAoKG5vZGUpID0+IHsNCiAgICAgICAgY29uc3QgYWRhcHRlZCA9IHsNCiAgICAgICAgICBpZDogbm9kZS5zY2VuZUlkLA0KICAgICAgICAgIHNjZW5lSW5mb0lkOiBub2RlLnNjZW5lSW5mb0lkIHx8IG51bGwsDQogICAgICAgICAgbmFtZTogbm9kZS5zY2VuZU5hbWUsDQogICAgICAgICAgY29kZTogbm9kZS5zY2VuZUNvZGUsDQogICAgICAgICAgeDogbm9kZS54LA0KICAgICAgICAgIHk6IG5vZGUueSwNCiAgICAgICAgICB0eXBlOiBub2RlLnR5cGUsDQogICAgICAgICAgc3RhdHVzOiBub2RlLnN0YXR1cyAhPT0gbnVsbCA/IG5vZGUuc3RhdHVzIDogJzAnLA0KICAgICAgICAgIGlzVW5mb2xkOiBub2RlLmlzVW5mb2xkICE9PSBudWxsICYmIG5vZGUuaXNVbmZvbGQgIT09IHVuZGVmaW5lZCA/IG5vZGUuaXNVbmZvbGQgOiAnMScsDQogICAgICAgICAgZGlzcGxheUxvY2F0aW9uOiBub2RlLmRpc3BsYXlMb2NhdGlvbiAhPT0gbnVsbCAmJiBub2RlLmRpc3BsYXlMb2NhdGlvbiAhPT0gdW5kZWZpbmVkID8gbm9kZS5kaXNwbGF5TG9jYXRpb24gOiAnMCcsDQogICAgICAgICAgdHJlZUNsYXNzaWZpY2F0aW9uOiBub2RlLnRyZWVDbGFzc2lmaWNhdGlvbiAhPT0gbnVsbCAmJiBub2RlLnRyZWVDbGFzc2lmaWNhdGlvbiAhPT0gdW5kZWZpbmVkID8gbm9kZS50cmVlQ2xhc3NpZmljYXRpb24gOiAnMycsDQogICAgICAgICAgaW50cm9kdWNlVmlkZW9Wbzogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvID8gew0KICAgICAgICAgICAgaWQ6IG5vZGUuaW50cm9kdWNlVmlkZW9Wby5pZCB8fCAnJywNCiAgICAgICAgICAgIHR5cGU6IG5vZGUuaW50cm9kdWNlVmlkZW9Wby50eXBlIHx8ICcnLA0KICAgICAgICAgICAgdmlld0luZm9JZDogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLnZpZXdJbmZvSWQgfHwgJycsDQogICAgICAgICAgICBzdGF0dXM6IG5vZGUuaW50cm9kdWNlVmlkZW9Wby5zdGF0dXMgfHwgJzAnLA0KICAgICAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6IG5vZGUuaW50cm9kdWNlVmlkZW9Wby5iYWNrZ3JvdW5kSW1nRmlsZVVybCB8fCAnJywNCiAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiBub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEZpbGVVcmwgfHwgJycNCiAgICAgICAgICB9IDogeyBpZDogJycsIHR5cGU6ICcnLCB2aWV3SW5mb0lkOiAnJywgc3RhdHVzOiAnMCcsIGJhY2tncm91bmRJbWdGaWxlVXJsOiAnJywgYmFja2dyb3VuZEZpbGVVcmw6ICcnIH0sDQogICAgICAgICAgdHJhZGl0aW9uOiBub2RlLnNjZW5lVHJhZGl0aW9uVm8gPyB7DQogICAgICAgICAgICBuYW1lOiBub2RlLnNjZW5lVHJhZGl0aW9uVm8ubmFtZSB8fCAnJywNCiAgICAgICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxLZXk6IG5vZGUuc2NlbmVUcmFkaXRpb25Wby5wYW5vcmFtaWNWaWV3WG1sS2V5IHx8ICcnLA0KICAgICAgICAgICAgYmFja2dyb3VuZFJlc291cmNlczogbm9kZS5zY2VuZVRyYWRpdGlvblZvLnNjZW5lVmlkZW9MaXN0ID8gDQogICAgICAgICAgICAgIG5vZGUuc2NlbmVUcmFkaXRpb25Wby5zY2VuZVZpZGVvTGlzdC5tYXAodiA9PiAoew0KICAgICAgICAgICAgICAgIGlkOiB2LmlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgbGFiZWw6IHYudGFnIHx8ICcnLA0KICAgICAgICAgICAgICAgIGNvb3JkaW5hdGVzOiB2LnNjZW5lRmlsZVJlbExpc3QgPyB2LnNjZW5lRmlsZVJlbExpc3QubWFwKHJlbCA9PiAoew0KICAgICAgICAgICAgICAgICAgaWQ6IHJlbC5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgZmlsZUlkOiByZWwuZmlsZUlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICB4OiByZWwuY2xpY2tYIHx8ICcnLA0KICAgICAgICAgICAgICAgICAgeTogcmVsLmNsaWNrWSB8fCAnJywNCiAgICAgICAgICAgICAgICAgIHdpZGU6IHJlbC53aWRlIHx8ICcnLA0KICAgICAgICAgICAgICAgICAgaGlnaDogcmVsLmhpZ2ggfHwgJycsDQogICAgICAgICAgICAgICAgICB4bWxLZXk6IHJlbC54bWxLZXkgfHwgJycsDQogICAgICAgICAgICAgICAgICBzY2VuZUlkOiB0aGlzLmZpbmRTY2VuZUlkQnlDb2RlKHJlbC5iaW5kU2NlbmVDb2RlKSwNCiAgICAgICAgICAgICAgICAgIHNjZW5lQ29kZTogcmVsLmJpbmRTY2VuZUNvZGUgfHwgJycNCiAgICAgICAgICAgICAgICB9KSkgOiBbeyBpZDogbnVsbCwgZmlsZUlkOiBudWxsLCB4OiAnJywgeTogJycsIHdpZGU6ICcnLCBoaWdoOiAnJywgc2NlbmVJZDogJycsIHNjZW5lQ29kZTogJycgfV0sDQogICAgICAgICAgICAgICAgd2lkZTogdi53aWRlIHx8IDAsDQogICAgICAgICAgICAgICAgaGlnaDogdi5oaWdoIHx8IDAsDQogICAgICAgICAgICAgICAgYmdJbWc6IHYuYmFja2dyb3VuZEltZ0ZpbGVVcmwgfHwgJycsDQogICAgICAgICAgICAgICAgYmdGaWxlOiB2LmJhY2tncm91bmRGaWxlVXJsIHx8ICcnLA0KICAgICAgICAgICAgICAgIHN0YXR1czogdi5zdGF0dXMgfHwgJycsDQogICAgICAgICAgICAgICAgdHlwZTogdi50eXBlIHx8ICcnLA0KICAgICAgICAgICAgICAgIHZpZXdJbmZvSWQ6IHYudmlld0luZm9JZCB8fCAnJw0KICAgICAgICAgICAgICB9KSkgOiBbXSwNCiAgICAgICAgICAgIHBhaW5Qb2ludHM6IG5vZGUuc2NlbmVUcmFkaXRpb25Wby5wYWluUG9pbnRMaXN0ID8NCiAgICAgICAgICAgICAgKEFycmF5LmlzQXJyYXkobm9kZS5zY2VuZVRyYWRpdGlvblZvLnBhaW5Qb2ludExpc3QpID8gbm9kZS5zY2VuZVRyYWRpdGlvblZvLnBhaW5Qb2ludExpc3QubWFwKHAgPT4gKHsNCiAgICAgICAgICAgICAgICBwYWluUG9pbnRJZDogcC5wYWluUG9pbnRJZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIHRpdGxlOiBwLmJpZ1RpdGxlIHx8ICcnLA0KICAgICAgICAgICAgICAgIGNvbnRlbnRzOiBwLmNvbnRlbnQgfHwgW10sDQogICAgICAgICAgICAgICAgc2hvd1RpbWU6IHAuZGlzcGxheVRpbWUgfHwgJycNCiAgICAgICAgICAgICAgfSkpIDogW10pIDogW10NCiAgICAgICAgICB9IDogeyBuYW1lOiAnJywgcGFub3JhbWljVmlld1htbEtleTogJycsIGJhY2tncm91bmRSZXNvdXJjZXM6IFtdLCBwYWluUG9pbnRzOiBbXSB9LA0KICAgICAgICAgIHdpc2RvbTVnOiBub2RlLnNjZW5lNWdWbyA/IHsNCiAgICAgICAgICAgIG5hbWU6IG5vZGUuc2NlbmU1Z1ZvLm5hbWUgfHwgJycsDQogICAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sS2V5OiBub2RlLnNjZW5lNWdWby5wYW5vcmFtaWNWaWV3WG1sS2V5IHx8ICcnLA0KICAgICAgICAgICAgYmFja2dyb3VuZFJlc291cmNlczogbm9kZS5zY2VuZTVnVm8uc2NlbmVWaWRlb0xpc3QgPyANCiAgICAgICAgICAgICAgbm9kZS5zY2VuZTVnVm8uc2NlbmVWaWRlb0xpc3QubWFwKHYgPT4gKHsNCiAgICAgICAgICAgICAgICBpZDogdi5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIHRhZzogdi50YWcgfHwgJycsDQogICAgICAgICAgICAgICAgc3RhdHVzOiB2LnN0YXR1cyB8fCAnJywNCiAgICAgICAgICAgICAgICB0eXBlOiB2LnR5cGUgfHwgJycsDQogICAgICAgICAgICAgICAgdmlld0luZm9JZDogdi52aWV3SW5mb0lkIHx8ICcnLA0KICAgICAgICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiB2LmJhY2tncm91bmRJbWdGaWxlVXJsIHx8ICcnLA0KICAgICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiB2LmJhY2tncm91bmRGaWxlVXJsIHx8ICcnLA0KICAgICAgICAgICAgICAgIGNvb3JkaW5hdGVzOiB2LnNjZW5lRmlsZVJlbExpc3QgPyB2LnNjZW5lRmlsZVJlbExpc3QubWFwKHJlbCA9PiAoew0KICAgICAgICAgICAgICAgICAgaWQ6IHJlbC5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgZmlsZUlkOiByZWwuZmlsZUlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICB4OiByZWwuY2xpY2tYIHx8ICcnLA0KICAgICAgICAgICAgICAgICAgeTogcmVsLmNsaWNrWSB8fCAnJywNCiAgICAgICAgICAgICAgICAgIHdpZGU6IHJlbC53aWRlIHx8ICcnLA0KICAgICAgICAgICAgICAgICAgaGlnaDogcmVsLmhpZ2ggfHwgJycsDQogICAgICAgICAgICAgICAgICB4bWxLZXk6IHJlbC54bWxLZXkgfHwgJycsDQogICAgICAgICAgICAgICAgICBzY2VuZUlkOiB0aGlzLmZpbmRTY2VuZUlkQnlDb2RlKHJlbC5iaW5kU2NlbmVDb2RlKSwNCiAgICAgICAgICAgICAgICAgIHNjZW5lQ29kZTogcmVsLmJpbmRTY2VuZUNvZGUgfHwgJycNCiAgICAgICAgICAgICAgICB9KSkgOiBbeyBpZDogbnVsbCwgZmlsZUlkOiBudWxsLCB4OiAnJywgeTogJycsIHdpZGU6ICcnLCBoaWdoOiAnJywgc2NlbmVJZDogJycsIHNjZW5lQ29kZTogJycgfV0NCiAgICAgICAgICAgICAgfSkpIDogW10sDQogICAgICAgICAgICBwYWluUG9pbnRzOiBub2RlLnNjZW5lNWdWby5wYWluUG9pbnRMaXN0ID8gDQogICAgICAgICAgICAgIChBcnJheS5pc0FycmF5KG5vZGUuc2NlbmU1Z1ZvLnBhaW5Qb2ludExpc3QpID8gbm9kZS5zY2VuZTVnVm8ucGFpblBvaW50TGlzdC5tYXAocCA9PiAoew0KICAgICAgICAgICAgICAgIHBhaW5Qb2ludElkOiBwLnBhaW5Qb2ludElkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgdGl0bGU6IHAuYmlnVGl0bGUgfHwgJycsDQogICAgICAgICAgICAgICAgY29udGVudHM6IHAuY29udGVudCB8fCBbXSwNCiAgICAgICAgICAgICAgICBzaG93VGltZTogcC5kaXNwbGF5VGltZSB8fCAnJw0KICAgICAgICAgICAgICB9KSkgOiBbXSkgOiBbXQ0KICAgICAgICAgIH0gOiB7IG5hbWU6ICcnLCBwYW5vcmFtaWNWaWV3WG1sS2V5OiAnJywgYmFja2dyb3VuZFJlc291cmNlczogW10sIHBhaW5Qb2ludHM6IFtdIH0sDQogICAgICAgICAgY29zdEVzdGltYXRlOiBub2RlLmNvc3RFc3RpbWF0aW9uSW5mb1ZvID8gew0KICAgICAgICAgICAgcGFpblBvaW50SWQ6IG5vZGUuY29zdEVzdGltYXRpb25JbmZvVm8ucGFpblBvaW50SWQgfHwgbnVsbCwNCiAgICAgICAgICAgIHN0YXR1czogbm9kZS5jb3N0RXN0aW1hdGlvbkluZm9Wby5zdGF0dXMgfHwgJzAnLA0KICAgICAgICAgICAgdGl0bGU6IG5vZGUuY29zdEVzdGltYXRpb25JbmZvVm8uYmlnVGl0bGUgfHwgJycsDQogICAgICAgICAgICBjb250ZW50czogbm9kZS5jb3N0RXN0aW1hdGlvbkluZm9Wby5jb250ZW50IHx8IFtdDQogICAgICAgICAgfSA6IHsgcGFpblBvaW50SWQ6IG51bGwsIHN0YXR1czogJzAnLCB0aXRsZTogJycsIGNvbnRlbnRzOiBbXSB9LA0KICAgICAgICAgIGNoaWxkcmVuOiBbXSwNCiAgICAgICAgICBwYXJlbnQNCiAgICAgICAgfQ0KICAgICAgICAvLyDpgJLlvZLlpITnkIblrZDoioLngrnvvIzkv53mjIHlkI7nq6/ov5Tlm57nmoTljp/lp4vpobrluo8NCiAgICAgICAgYWRhcHRlZC5jaGlsZHJlbiA9IG5vZGUuY2hpbGRyZW4gPyB0aGlzLmFkYXB0U2NlbmVUcmVlKG5vZGUuY2hpbGRyZW4sIGFkYXB0ZWQpIDogW10NCiAgICAgICAgcmV0dXJuIGFkYXB0ZWQNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVTdWJtaXQoKSB7DQogICAgICB0cnkgew0KICAgICAgICB0aGlzLnN1Ym1pdHRpbmcgPSB0cnVlDQogICAgICAgIC8vIOS7juaJgeW5s+WMluiPnOWNleaVsOaNruS4reiOt+WPluW9k+WJjeihjOS4mueahCBpbmR1c3RyeUNvZGUNCiAgICAgICAgY29uc3QgY3VycmVudEluZHVzdHJ5ID0gdGhpcy5mbGF0TWVudURhdGEuZmluZChpdGVtID0+IFN0cmluZyhpdGVtLmlkKSA9PT0gdGhpcy5hY3RpdmVNZW51KQ0KICAgICAgICBjb25zdCBpbmR1c3RyeUNvZGUgPSBjdXJyZW50SW5kdXN0cnkgPyBjdXJyZW50SW5kdXN0cnkuaW5kdXN0cnlDb2RlIDogbnVsbA0KICAgICAgICANCiAgICAgICAgLy8g5L+d5a2Y5b2T5YmN6YCJ5Lit55qE6IqC54K555qE5a6M5pW05L+h5oGvDQogICAgICAgIGNvbnN0IGN1cnJlbnRTZWxlY3RlZE5vZGVJZCA9IHRoaXMuc2VsZWN0ZWROb2RlID8gdGhpcy5zZWxlY3RlZE5vZGUuaWQgOiBudWxsDQogICAgICAgIGNvbnN0IGN1cnJlbnRTZWxlY3RlZE5vZGVOYW1lID0gdGhpcy5zZWxlY3RlZE5vZGUgPyB0aGlzLnNlbGVjdGVkTm9kZS5uYW1lIDogbnVsbA0KICAgICAgICANCiAgICAgICAgLy8g5p6E5bu65o+Q5Lqk5pWw5o2uDQogICAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB7DQogICAgICAgICAgaW5kdXN0cnlJZDogdGhpcy5hY3RpdmVNZW51LA0KICAgICAgICAgIGluZHVzdHJ5Q29kZTogaW5kdXN0cnlDb2RlLCAvLyDmlrDlop7lj4LmlbANCiAgICAgICAgICBzY2VuZVZpZXdDb25maWdJZDogdGhpcy5mb3JtLnNjZW5lVmlld0NvbmZpZ0lkIHx8IG51bGwsDQogICAgICAgICAgbWFpblRpdGxlOiB0aGlzLmZvcm0ubWFpblRpdGxlIHx8IG51bGwsDQogICAgICAgICAgc3ViVGl0bGU6IHRoaXMuZm9ybS5zdWJUaXRsZSB8fCBudWxsLA0KICAgICAgICAgIHRoZW1lSWQ6IHRoaXMuc2VsZWN0ZWRUaGVtZSA/IHRoaXMuc2VsZWN0ZWRUaGVtZS50aGVtZUlkIDogbnVsbCwNCiAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogdGhpcy5mb3JtLmJnSW1nVXJsIHx8IG51bGwsDQogICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHRoaXMuZm9ybS5iZ0ZpbGVVcmwgfHwgbnVsbCwNCiAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sVXJsOiB0aGlzLmZvcm0ucGFub3JhbWljVmlld1htbFVybCB8fCBudWxsLA0KICAgICAgICAgIG5ldHdvcmtTb2x1dGlvbkluZm9Wbzogew0KICAgICAgICAgICAgbmV0d29ya1ZpZGVvTGlzdDogKHRoaXMubmV0d29ya1BsYW5EYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0/Lm5ldHdvcmtWaWRlb0xpc3QgJiYgQXJyYXkuaXNBcnJheSh0aGlzLm5ldHdvcmtQbGFuRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdLm5ldHdvcmtWaWRlb0xpc3QpKSA/IA0KICAgICAgICAgICAgICB0aGlzLm5ldHdvcmtQbGFuRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdLm5ldHdvcmtWaWRlb0xpc3QubWFwKHBsYW4gPT4gKHsNCiAgICAgICAgICAgICAgICBpZDogcGxhbi5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIHR5cGU6IDQsDQogICAgICAgICAgICAgICAgdGFnOiBwbGFuLnRhZyB8fCBudWxsLA0KICAgICAgICAgICAgICAgIGNsaWNrWDogcGxhbi5jbGlja1ggfHwgbnVsbCwNCiAgICAgICAgICAgICAgICBjbGlja1k6IHBsYW4uY2xpY2tZIHx8IG51bGwsDQogICAgICAgICAgICAgICAgd2lkZTogcGxhbi53aWRlIHx8IG51bGwsDQogICAgICAgICAgICAgICAgaGlnaDogcGxhbi5oaWdoIHx8IG51bGwsDQogICAgICAgICAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6IHBsYW4uYmFja2dyb3VuZEltZ0ZpbGVVcmwgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogcGxhbi5iYWNrZ3JvdW5kRmlsZVVybCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIHN0YXR1czogbnVsbCwNCiAgICAgICAgICAgICAgICB2aWV3SW5mb0lkOiBudWxsDQogICAgICAgICAgICAgIH0pKSA6IFtdLA0KICAgICAgICAgICAgdmlkZW9FeHBsYW5hdGlvblZvOiB7DQogICAgICAgICAgICAgIHN0YXR1czogdGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XT8udmlkZW9FeHBsYW5hdGlvblZvPy5zdGF0dXMgfHwgJzAnLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogdGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XT8udmlkZW9FeHBsYW5hdGlvblZvPy5iYWNrZ3JvdW5kRmlsZVVybCB8fCBudWxsLA0KICAgICAgICAgICAgICB2aWRlb1NlZ21lbnRlZFZvTGlzdDogKHRoaXMubmV0d29ya1BsYW5EYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0/LnZpZGVvRXhwbGFuYXRpb25Wbz8udmlkZW9TZWdtZW50ZWRWb0xpc3Q/Lmxlbmd0aCkgPyANCiAgICAgICAgICAgICAgICB0aGlzLm5ldHdvcmtQbGFuRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdLnZpZGVvRXhwbGFuYXRpb25Wby52aWRlb1NlZ21lbnRlZFZvTGlzdC5tYXAoc2VnID0+ICh7DQogICAgICAgICAgICAgICAgICB0aW1lOiBzZWcudGltZSB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgc2NlbmVDb2RlOiBzZWcuc2NlbmVDb2RlIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBzY2VuZU5hbWU6IHNlZy5zY2VuZU5hbWUgfHwgbnVsbA0KICAgICAgICAgICAgICAgIH0pKSA6IG51bGwNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNjZW5lRGVmYXVsdENvbmZpZ1ZvTGlzdDogdGhpcy5jYXRlZ29yaWVzLm1hcChjYXQgPT4gew0KICAgICAgICAgICAgY29uc3QgYmFzZUNvbmZpZyA9IHsNCiAgICAgICAgICAgICAgaWQ6IGNhdC5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICBpbmR1c3RyeUlkOiB0aGlzLmFjdGl2ZU1lbnUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgbmFtZTogY2F0Lm5hbWUsDQogICAgICAgICAgICAgIGtleU5hbWU6IGNhdC5rZXksDQogICAgICAgICAgICAgIGtleVZhbHVlOiBjYXQuZW5hYmxlZCA/ICcwJyA6ICcxJywNCiAgICAgICAgICAgICAgcmVtYXJrOiBjYXQucmVtYXJrIHx8IGNhdC5uYW1lLA0KICAgICAgICAgICAgICBkZWZhdWx0U3RhdHVzOiAnMCcNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgaWYgKGNhdC5rZXkgPT09ICdkZWZhdWx0X3NjZW5lJykgew0KICAgICAgICAgICAgICBjb25zdCBjb252ZXJ0ZWRTY2VuZUxpc3QgPSB0aGlzLmNvbnZlcnRTY2VuZVRyZWVUb0FwaSh0aGlzLnNjZW5lQ29uZmlnVHJlZSkgICAgIA0KICAgICAgICAgICAgICBiYXNlQ29uZmlnLmluZHVzdHJ5U2NlbmVJbmZvVm8gPSB7DQogICAgICAgICAgICAgICAgdmlkZW9FeHBsYW5hdGlvblZvOiB7DQogICAgICAgICAgICAgICAgICBzdGF0dXM6IHRoaXMudmlkZW9FeHBsYW5hdGlvbi5zdGF0dXMsDQogICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogdGhpcy52aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICB2aWRlb1NlZ21lbnRlZFZvTGlzdDogdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0Lmxlbmd0aCA/IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCA6IG51bGwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHNjZW5lTGlzdFZvOiBjb252ZXJ0ZWRTY2VuZUxpc3QNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgYmFzZUNvbmZpZy5pbmR1c3RyeVNjZW5lSW5mb1ZvID0gbnVsbA0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgDQogICAgICAgICAgICByZXR1cm4gYmFzZUNvbmZpZw0KICAgICAgICAgIH0pLA0KICAgICAgICAgIGNvbW1lcmNpYWxWYWx1ZURUTzogKHRoaXMuYnVzaW5lc3NWYWx1ZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XSAmJiBBcnJheS5pc0FycmF5KHRoaXMuYnVzaW5lc3NWYWx1ZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XSkpID8gDQogICAgICAgICAgICB0aGlzLmJ1c2luZXNzVmFsdWVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0ubWFwKHZhbHVlID0+ICh7DQogICAgICAgICAgICAgIGlkOiB2YWx1ZS5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICB2aWV3SW5mb0lkOiB2YWx1ZS52aWV3SW5mb0lkIHx8IG51bGwsDQogICAgICAgICAgICAgIHR5cGU6IDUsDQogICAgICAgICAgICAgIHRhZzogdmFsdWUudGFnIHx8IG51bGwsDQogICAgICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiB2YWx1ZS5iYWNrZ3JvdW5kSW1nRmlsZVVybCB8fCBudWxsLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogdmFsdWUuYmFja2dyb3VuZEZpbGVVcmwgfHwgbnVsbA0KICAgICAgICAgICAgfSkpIDogW10sDQogICAgICAgICAgdnJJbmZvRHRvTGlzdDogKHRoaXMudnJTY2VuZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XSAmJiBBcnJheS5pc0FycmF5KHRoaXMudnJTY2VuZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XSkpID8gDQogICAgICAgICAgICB0aGlzLnZyU2NlbmVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0uIG1hcCh2ciA9PiAoew0KICAgICAgICAgICAgICBpZDogdnIuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgaW5kdXN0cnlJZDogdnIuaW5kdXN0cnlJZCB8fCB0aGlzLmFjdGl2ZU1lbnUsDQogICAgICAgICAgICAgIHR5cGU6IHZyLnR5cGUgfHwgNiwNCiAgICAgICAgICAgICAgdmlld0luZm9JZDogdnIudmlld0luZm9JZCB8fCBudWxsLA0KICAgICAgICAgICAgICBuYW1lOiB2ci5uYW1lIHx8ICcnLA0KICAgICAgICAgICAgICBhZGRyZXNzOiB2ci5hZGRyZXNzIHx8ICcnDQogICAgICAgICAgICB9KSkgOiBbXSwNCiAgICAgICAgfQ0KICAgICAgDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc2NlbmVWaWV3VXBkKHN1Ym1pdERhdGEpDQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KDQogICAgICAgIC8vIOmHjeaWsOWKoOi9veaVsOaNru+8iOS4jeaYvuekuuWFqOWxgGxvYWRpbmfvvIzpgb/lhY3kuI7mjInpkq5sb2FkaW5n5Yay56qB77yJDQogICAgICAgIGF3YWl0IHRoaXMuaGFuZGxlU2VsZWN0KHRoaXMuYWN0aXZlTWVudSwgZmFsc2UsIGZhbHNlKQ0KDQogICAgICAgIGlmIChjdXJyZW50U2VsZWN0ZWROb2RlSWQgJiYgdGhpcy5zY2VuZUNvbmZpZ1RyZWUubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOW8uuWItuafpeaJvuW5tuiuvue9rumAieS4reiKgueCuQ0KICAgICAgICAgIGNvbnN0IG5vZGVUb1NlbGVjdCA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBjdXJyZW50U2VsZWN0ZWROb2RlSWQpDQoNCiAgICAgICAgICBpZiAobm9kZVRvU2VsZWN0KSB7DQogICAgICAgICAgICAvLyDorqHnrpflubborr7nva7lsZXlvIDot6/lvoQNCiAgICAgICAgICAgIGNvbnN0IHBhdGhJZHMgPSBbXQ0KICAgICAgICAgICAgY29uc3QgZmluZFBhdGggPSAobm9kZXMsIHRhcmdldElkLCBjdXJyZW50UGF0aCA9IFtdKSA9PiB7DQogICAgICAgICAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiBub2Rlcykgew0KICAgICAgICAgICAgICAgIGNvbnN0IG5ld1BhdGggPSBbLi4uY3VycmVudFBhdGgsIG5vZGUuaWRdDQogICAgICAgICAgICAgICAgaWYgKG5vZGUuaWQgPT09IHRhcmdldElkKSB7DQogICAgICAgICAgICAgICAgICBwYXRoSWRzLnB1c2goLi4ubmV3UGF0aCkNCiAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgICAgaWYgKGZpbmRQYXRoKG5vZGUuY2hpbGRyZW4sIHRhcmdldElkLCBuZXdQYXRoKSkgew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgZmluZFBhdGgodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIGN1cnJlbnRTZWxlY3RlZE5vZGVJZCkNCiAgICAgICAgICAgIHRoaXMudHJlZUV4cGFuZGVkS2V5cyA9IHBhdGhJZHMuc2xpY2UoMCwgLTEpDQoNCiAgICAgICAgICAgIC8vIOWFiOiuvue9rumAieS4reiKgueCuQ0KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSBub2RlVG9TZWxlY3QNCg0KICAgICAgICAgICAgLy8g5by65Yi25pu05paw5qCR57uE5Lu255qE6YCJ5Lit54q25oCBDQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgICAvLyDmqKHmi5/ngrnlh7voioLngrnmnaXlvLrliLbmm7TmlrDpgInkuK3nirbmgIENCiAgICAgICAgICAgICAgICB0aGlzLmhhbmRsZVNjZW5lTm9kZUNsaWNrKG5vZGVUb1NlbGVjdCkNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk5YaF5a65OicsIHN1Ym1pdERhdGEpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmj5DkuqTlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aPkOS6pOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLnN1Ym1pdHRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgYWRkVmlkZW9TZWdtZW50KCkgew0KICAgICAgLy8g5aaC5p6c5Y6f5pWw57uE5Li656m677yM5YWI5Yid5aeL5YyWDQogICAgICBpZiAoIXRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCB8fCB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCA9IFt7IHRpbWU6ICcnLCBzY2VuZUlkOiAnJywgc2NlbmVOYW1lOiAnJywgc2NlbmVDb2RlOiAnJyB9XQ0KICAgICAgfQ0KICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0LnB1c2goeyB0aW1lOiAnJywgc2NlbmVJZDogJycsIHNjZW5lTmFtZTogJycsIHNjZW5lQ29kZTogJycgfSkNCiAgICB9LA0KICAgIHJlbW92ZVZpZGVvU2VnbWVudChpZHgpIHsNCiAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5zcGxpY2UoaWR4LCAxKQ0KICAgIH0sDQogICAgLy/pgJLlvZLph43mnoTnu5PmnoQNCiAgICBnZXREZWVwVHJlZU9wdGlvbnModHJlZSkgew0KICAgIHJldHVybiB0cmVlLm1hcChpdGVtID0+IHsNCiAgICAgIC8vIOWkjeWItuW9k+WJjeiKgueCueeahOWfuuehgOWxnuaApw0KICAgICAgY29uc3Qgbm9kZSA9IHsgLi4uaXRlbSB9Ow0KICAgICAgDQogICAgICAvLyDlpoLmnpzlrZjlnKggY2hpbGRyZW4g5LiU5LiN5Li656m677yM5YiZ6YCS5b2S5aSE55CGDQogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbm9kZS5jaGlsZHJlbiA9IHRoaXMuZ2V0RGVlcFRyZWVPcHRpb25zKG5vZGUuY2hpbGRyZW4pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5b2TIGNoaWxkcmVuIOS4uuepuuaIluS4jeWtmOWcqOaXtu+8jOWIoOmZpCBjaGlsZHJlbiDlsZ7mgKfvvIjlj6/pgInvvIkNCiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW47DQogICAgICB9DQogICAgICANCiAgICAgIHJldHVybiBub2RlOw0KICAgIH0pOw0KICB9LA0KICAgIGFzeW5jIGxvYWRTY2VuZVRyZWVPcHRpb25zKGlkKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDku47miYHlubPljJboj5zljZXmlbDmja7kuK3ojrflj5blvZPliY3ooYzkuJrnmoQgaW5kdXN0cnlDb2RlDQogICAgICAgIGNvbnN0IGN1cnJlbnRJbmR1c3RyeSA9IHRoaXMuZmxhdE1lbnVEYXRhLmZpbmQoaXRlbSA9PiBTdHJpbmcoaXRlbS5pZCkgPT09IGlkKQ0KICAgICAgICBjb25zdCBpbmR1c3RyeUNvZGUgPSBjdXJyZW50SW5kdXN0cnkgPyBjdXJyZW50SW5kdXN0cnkuaW5kdXN0cnlDb2RlIDogbnVsbA0KICAgICAgICANCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0U2NlbmVUcmVlTGlzdCh7IGluZHVzdHJ5Q29kZTogaW5kdXN0cnlDb2RlIH0pDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCAmJiBBcnJheS5pc0FycmF5KHJlcy5kYXRhKSkgew0KICAgICAgICAgIHRoaXMuc2NlbmVUcmVlT3B0aW9ucyA9IHRoaXMuZ2V0RGVlcFRyZWVPcHRpb25zKHJlcy5kYXRhKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3lnLrmma/moJHlpLHotKU6JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTY2VuZUNhc2NhZGVyQ2hhbmdlKHZhbCwgaWR4KSB7DQogICAgICAvLyDnoa7kv53mlbDnu4TlkozntKLlvJXkvY3nva7nmoTlr7nosaHlrZjlnKgNCiAgICAgIGlmICghdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0IHx8ICF0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XSkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgY29uc3QgZmluZFNjZW5lID0gKHRyZWUsIGlkKSA9PiB7DQogICAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiB0cmVlKSB7DQogICAgICAgICAgaWYgKG5vZGUuaWQgPT09IGlkKSByZXR1cm4gbm9kZQ0KICAgICAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgICBjb25zdCBmb3VuZCA9IGZpbmRTY2VuZShub2RlLmNoaWxkcmVuLCBpZCkNCiAgICAgICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBudWxsDQogICAgICB9DQogICAgICBjb25zdCBub2RlID0gZmluZFNjZW5lKHRoaXMuc2NlbmVUcmVlT3B0aW9ucywgdmFsKQ0KICAgICAgaWYgKG5vZGUpIHsNCiAgICAgICAgLy8g6K6+572u5Zy65pmvSUTlkoznm7jlhbPkv6Hmga8NCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0W2lkeF0uc2NlbmVJZCA9IHZhbA0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XS5zY2VuZU5hbWUgPSBub2RlLnNjZW5lTmFtZQ0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XS5zY2VuZUNvZGUgPSBub2RlLnNjZW5lQ29kZQ0KICAgICAgfQ0KICAgIH0sDQogICAgaXNTY2VuZURpc2FibGVkKGlkLCBjdXJyZW50SWR4KSB7DQogICAgICAvLyDpmaTlvZPliY3liIbmrrXlpJbvvIzlhbbku5bliIbmrrXlt7LpgInnmoRpZA0KICAgICAgcmV0dXJuIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5zb21lKChzZWcsIGlkeCkgPT4gaWR4ICE9PSBjdXJyZW50SWR4ICYmIHNlZy5zY2VuZUlkID09PSBpZCkNCiAgICB9LA0KICAgIC8vIOWwhuWcuuaZr+agkei9rOaNouS4uuaOpeWPo+agvOW8jw0KICAgIGNvbnZlcnRTY2VuZVRyZWVUb0FwaShzY2VuZVRyZWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKCLmj5DkuqTnmoTmlbDmja46Iiwgc2NlbmVUcmVlKTsNCiAgICAgIHJldHVybiBzY2VuZVRyZWUubWFwKG5vZGUgPT4gKHsNCiAgICAgICAgc2NlbmVJbmZvSWQ6IG5vZGUuc2NlbmVJbmZvSWQgfHwgbnVsbCwNCiAgICAgICAgc2NlbmVJZDogbm9kZS5pZCwNCiAgICAgICAgcGFyYW1JZDogbm9kZS5wYXJlbnQgPyBub2RlLnBhcmVudC5pZCA6IG51bGwsDQogICAgICAgIHNjZW5lTmFtZTogbm9kZS5uYW1lLA0KICAgICAgICBzY2VuZUNvZGU6IG5vZGUuY29kZSwNCiAgICAgICAgeDogbm9kZS54IHx8IG51bGwsDQogICAgICAgIHk6IG5vZGUueSB8fCBudWxsLA0KICAgICAgICB0eXBlOiBub2RlLnR5cGUgfHwgbnVsbCwNCiAgICAgICAgc3RhdHVzOiBub2RlLnN0YXR1cywNCiAgICAgICAgaXNVbmZvbGQ6IChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgPyAobm9kZS5pc1VuZm9sZCB8fCAnMScpIDogbnVsbCwNCiAgICAgICAgZGlzcGxheUxvY2F0aW9uOiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApID8gKG5vZGUuZGlzcGxheUxvY2F0aW9uIHx8ICcwJykgOiBudWxsLA0KICAgICAgICB0cmVlQ2xhc3NpZmljYXRpb246IChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgPyAobm9kZS50cmVlQ2xhc3NpZmljYXRpb24gfHwgJzMnKSA6IG51bGwsDQogICAgICAgIGludHJvZHVjZVZpZGVvVm86IG5vZGUuaW50cm9kdWNlVmlkZW9WbyA/IHsNCiAgICAgICAgICBpZDogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmlkIHx8IG51bGwsDQogICAgICAgICAgdHlwZTogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLnR5cGUgfHwgbnVsbCwNCiAgICAgICAgICB2aWV3SW5mb0lkOiBub2RlLmludHJvZHVjZVZpZGVvVm8udmlld0luZm9JZCB8fCBudWxsLA0KICAgICAgICAgIHN0YXR1czogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLnN0YXR1cyB8fCBudWxsLA0KICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiBub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEltZ0ZpbGVVcmwgfHwgbnVsbCwNCiAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmJhY2tncm91bmRGaWxlVXJsIHx8IG51bGwNCiAgICAgICAgfSA6IG51bGwsDQogICAgICAgIHNjZW5lVHJhZGl0aW9uVm86IG5vZGUudHJhZGl0aW9uID8gew0KICAgICAgICAgIG5hbWU6IG5vZGUudHJhZGl0aW9uLm5hbWUgfHwgbnVsbCwNCiAgICAgICAgICBwYW5vcmFtaWNWaWV3WG1sS2V5OiBub2RlLnRyYWRpdGlvbi5wYW5vcmFtaWNWaWV3WG1sS2V5IHx8IG51bGwsDQogICAgICAgICAgc2NlbmVWaWRlb0xpc3Q6IG5vZGUudHJhZGl0aW9uLmJhY2tncm91bmRSZXNvdXJjZXMgJiYgbm9kZS50cmFkaXRpb24uYmFja2dyb3VuZFJlc291cmNlcy5sZW5ndGggPyANCiAgICAgICAgICAgIG5vZGUudHJhZGl0aW9uLmJhY2tncm91bmRSZXNvdXJjZXMubWFwKHJlc291cmNlID0+ICh7DQogICAgICAgICAgICAgIGlkOiByZXNvdXJjZS5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICB0YWc6IHJlc291cmNlLmxhYmVsIHx8IG51bGwsDQogICAgICAgICAgICAgIHdpZGU6IHJlc291cmNlLndpZGUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgaGlnaDogcmVzb3VyY2UuaGlnaCB8fCBudWxsLA0KICAgICAgICAgICAgICBzdGF0dXM6IHJlc291cmNlLnN0YXR1cyB8fCBudWxsLA0KICAgICAgICAgICAgICB0eXBlOiAxLA0KICAgICAgICAgICAgICB2aWV3SW5mb0lkOiByZXNvdXJjZS52aWV3SW5mb0lkIHx8IG51bGwsDQogICAgICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiByZXNvdXJjZS5iZ0ltZyB8fCAnJywNCiAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHJlc291cmNlLmJnRmlsZSB8fCAnJywNCiAgICAgICAgICAgICAgc2NlbmVGaWxlUmVsTGlzdDogcmVzb3VyY2UuY29vcmRpbmF0ZXMgJiYgcmVzb3VyY2UuY29vcmRpbmF0ZXMubGVuZ3RoID8gDQogICAgICAgICAgICAgICAgcmVzb3VyY2UuY29vcmRpbmF0ZXMubWFwKGNvb3JkID0+ICh7DQogICAgICAgICAgICAgICAgICBpZDogY29vcmQuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIGZpbGVJZDogY29vcmQuZmlsZUlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBjbGlja1g6IGNvb3JkLnggIT09IHVuZGVmaW5lZCAmJiBjb29yZC54ICE9PSBudWxsID8gKGNvb3JkLnggPT09ICcnID8gJycgOiBjb29yZC54KSA6IG51bGwsDQogICAgICAgICAgICAgICAgICBjbGlja1k6IGNvb3JkLnkgIT09IHVuZGVmaW5lZCAmJiBjb29yZC55ICE9PSBudWxsID8gKGNvb3JkLnkgPT09ICcnID8gJycgOiBjb29yZC55KSA6IG51bGwsDQogICAgICAgICAgICAgICAgICB3aWRlOiBjb29yZC53aWRlICE9PSB1bmRlZmluZWQgJiYgY29vcmQud2lkZSAhPT0gbnVsbCA/IChjb29yZC53aWRlID09PSAnJyB8fCBjb29yZC53aWRlID09PSAwID8gJycgOiBjb29yZC53aWRlKSA6IG51bGwsDQogICAgICAgICAgICAgICAgICBoaWdoOiBjb29yZC5oaWdoICE9PSB1bmRlZmluZWQgJiYgY29vcmQuaGlnaCAhPT0gbnVsbCA/IChjb29yZC5oaWdoID09PSAnJyB8fCBjb29yZC5oaWdoID09PSAwID8gJycgOiBjb29yZC5oaWdoKSA6IG51bGwsDQogICAgICAgICAgICAgICAgICB4bWxLZXk6IGNvb3JkLnhtbEtleSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLnhtbEtleSAhPT0gbnVsbCA/IChjb29yZC54bWxLZXkgPT09ICcnID8gJycgOiBjb29yZC54bWxLZXkpIDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIGJpbmRTY2VuZUNvZGU6IGNvb3JkLnNjZW5lQ29kZSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLnNjZW5lQ29kZSAhPT0gbnVsbCA/IChjb29yZC5zY2VuZUNvZGUgPT09ICcnID8gJycgOiBjb29yZC5zY2VuZUNvZGUpIDogbnVsbA0KICAgICAgICAgICAgICAgIH0pKSA6IFtdDQogICAgICAgICAgICB9KSkgOiBudWxsLA0KICAgICAgICAgIHBhaW5Qb2ludExpc3Q6IG5vZGUudHJhZGl0aW9uLnBhaW5Qb2ludHMgJiYgbm9kZS50cmFkaXRpb24ucGFpblBvaW50cy5sZW5ndGggPyANCiAgICAgICAgICAgIG5vZGUudHJhZGl0aW9uLnBhaW5Qb2ludHMubWFwKHBhaW4gPT4gKHsNCiAgICAgICAgICAgICAgcGFpblBvaW50SWQ6IHBhaW4ucGFpblBvaW50SWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgYmlnVGl0bGU6IHBhaW4udGl0bGUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgY29udGVudDogcGFpbi5jb250ZW50cyB8fCBbXSwNCiAgICAgICAgICAgICAgZGlzcGxheVRpbWU6IHBhaW4uc2hvd1RpbWUgfHwgbnVsbA0KICAgICAgICAgICAgfSkpIDogbnVsbA0KICAgICAgICB9IDogbnVsbCwNCiAgICAgICAgc2NlbmU1Z1ZvOiBub2RlLndpc2RvbTVnID8gew0KICAgICAgICAgIG5hbWU6IG5vZGUud2lzZG9tNWcubmFtZSB8fCBudWxsLA0KICAgICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxLZXk6IG5vZGUud2lzZG9tNWcucGFub3JhbWljVmlld1htbEtleSB8fCBudWxsLA0KICAgICAgICAgIHNjZW5lVmlkZW9MaXN0OiBub2RlLndpc2RvbTVnLmJhY2tncm91bmRSZXNvdXJjZXMgJiYgbm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzLmxlbmd0aCA/IA0KICAgICAgICAgICAgbm9kZS53aXNkb201Zy5iYWNrZ3JvdW5kUmVzb3VyY2VzLm1hcChyZXNvdXJjZSA9PiAoew0KICAgICAgICAgICAgICBpZDogcmVzb3VyY2UuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgdGFnOiByZXNvdXJjZS50YWcgfHwgbnVsbCwNCiAgICAgICAgICAgICAgc3RhdHVzOiByZXNvdXJjZS5zdGF0dXMgfHwgbnVsbCwNCiAgICAgICAgICAgICAgdHlwZTogMiwNCiAgICAgICAgICAgICAgdmlld0luZm9JZDogcmVzb3VyY2Uudmlld0luZm9JZCB8fCBudWxsLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogcmVzb3VyY2UuYmdJbWcgfHwgJycsDQogICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiByZXNvdXJjZS5iZ0ZpbGUgfHwgJycsDQogICAgICAgICAgICAgIHNjZW5lRmlsZVJlbExpc3Q6IHJlc291cmNlLmNvb3JkaW5hdGVzICYmIHJlc291cmNlLmNvb3JkaW5hdGVzLmxlbmd0aCA/IA0KICAgICAgICAgICAgICAgIHJlc291cmNlLmNvb3JkaW5hdGVzLm1hcChjb29yZCA9PiAoew0KICAgICAgICAgICAgICAgICAgaWQ6IGNvb3JkLmlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBmaWxlSWQ6IGNvb3JkLmZpbGVJZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgY2xpY2tYOiBjb29yZC54ICE9PSB1bmRlZmluZWQgJiYgY29vcmQueCAhPT0gbnVsbCA/IChjb29yZC54ID09PSAnJyA/ICcnIDogY29vcmQueCkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgY2xpY2tZOiBjb29yZC55ICE9PSB1bmRlZmluZWQgJiYgY29vcmQueSAhPT0gbnVsbCA/IChjb29yZC55ID09PSAnJyA/ICcnIDogY29vcmQueSkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgd2lkZTogY29vcmQud2lkZSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLndpZGUgIT09IG51bGwgPyAoY29vcmQud2lkZSA9PT0gJycgfHwgY29vcmQud2lkZSA9PT0gMCA/ICcnIDogY29vcmQud2lkZSkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgaGlnaDogY29vcmQuaGlnaCAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLmhpZ2ggIT09IG51bGwgPyAoY29vcmQuaGlnaCA9PT0gJycgfHwgY29vcmQuaGlnaCA9PT0gMCA/ICcnIDogY29vcmQuaGlnaCkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgeG1sS2V5OiBjb29yZC54bWxLZXkgIT09IHVuZGVmaW5lZCAmJiBjb29yZC54bWxLZXkgIT09IG51bGwgPyAoY29vcmQueG1sS2V5ID09PSAnJyA/ICcnIDogY29vcmQueG1sS2V5KSA6IG51bGwsDQogICAgICAgICAgICAgICAgICBiaW5kU2NlbmVDb2RlOiBjb29yZC5zY2VuZUNvZGUgIT09IHVuZGVmaW5lZCAmJiBjb29yZC5zY2VuZUNvZGUgIT09IG51bGwgPyAoY29vcmQuc2NlbmVDb2RlID09PSAnJyA/ICcnIDogY29vcmQuc2NlbmVDb2RlKSA6IG51bGwNCiAgICAgICAgICAgICAgICB9KSkgOiBbXQ0KICAgICAgICAgICAgfSkpIDogbnVsbCwNCiAgICAgICAgICBwYWluUG9pbnRMaXN0OiBub2RlLndpc2RvbTVnLnBhaW5Qb2ludHMgJiYgbm9kZS53aXNkb201Zy5wYWluUG9pbnRzLmxlbmd0aCA/IA0KICAgICAgICAgICAgbm9kZS53aXNkb201Zy5wYWluUG9pbnRzLm1hcChwYWluID0+ICh7DQogICAgICAgICAgICAgIHBhaW5Qb2ludElkOiBwYWluLnBhaW5Qb2ludElkIHx8IG51bGwsDQogICAgICAgICAgICAgIGJpZ1RpdGxlOiBwYWluLnRpdGxlIHx8IG51bGwsDQogICAgICAgICAgICAgIGNvbnRlbnQ6IHBhaW4uY29udGVudHMgfHwgW10sDQogICAgICAgICAgICAgIGRpc3BsYXlUaW1lOiBwYWluLnNob3dUaW1lIHx8IG51bGwNCiAgICAgICAgICAgIH0pKSA6IG51bGwNCiAgICAgICAgfSA6IG51bGwsDQogICAgICAgIGNvc3RFc3RpbWF0aW9uSW5mb1ZvOiBub2RlLmNvc3RFc3RpbWF0ZSA/IHsNCiAgICAgICAgICBwYWluUG9pbnRJZDogbm9kZS5jb3N0RXN0aW1hdGUucGFpblBvaW50SWQgfHwgbnVsbCwNCiAgICAgICAgICBzdGF0dXM6IG5vZGUuY29zdEVzdGltYXRlLnN0YXR1cyB8fCAnMCcsDQogICAgICAgICAgYmlnVGl0bGU6IG5vZGUuY29zdEVzdGltYXRlLnRpdGxlIHx8IG51bGwsDQogICAgICAgICAgY29udGVudDogbm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMgJiYgbm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMubGVuZ3RoID8gbm9kZS5jb3N0RXN0aW1hdGUuY29udGVudHMgOiBudWxsDQogICAgICAgIH0gOiBudWxsLA0KICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA/IHRoaXMuY29udmVydFNjZW5lVHJlZVRvQXBpKG5vZGUuY2hpbGRyZW4pIDogW10NCiAgICAgIH0pKQ0KICAgIH0sDQogICAgaGFuZGxlVGltZUNoYW5nZSh2YWwsIGlkeCkgew0KICAgICAgLy8g56Gu5L+d5pWw57uE5bey5Yid5aeL5YyWDQogICAgICBpZiAoIXRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCB8fCB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdCA9IFt7IHRpbWU6IDAsIHNjZW5lSWQ6ICcnLCBzY2VuZU5hbWU6ICcnLCBzY2VuZUNvZGU6ICcnIH1dDQogICAgICB9DQogICAgICAvLyDmm7TmlrDlr7nlupTkvY3nva7nmoTml7bpl7TlgLwNCiAgICAgIGlmICh0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XSkgew0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XS50aW1lID0gdmFsDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmoLnmja5zY2VuZUNvZGXmn6Xmib7lr7nlupTnmoRzY2VuZUlkDQogICAgZmluZFNjZW5lSWRCeUNvZGUoc2NlbmVDb2RlKSB7DQogICAgICBpZiAoIXNjZW5lQ29kZSB8fCAhdGhpcy5zY2VuZVRyZWVPcHRpb25zKSByZXR1cm4gJycNCiAgICAgIA0KICAgICAgY29uc3QgZmluZEluVHJlZSA9ICh0cmVlKSA9PiB7DQogICAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiB0cmVlKSB7DQogICAgICAgICAgaWYgKG5vZGUuc2NlbmVDb2RlID09PSBzY2VuZUNvZGUpIHsNCiAgICAgICAgICAgIHJldHVybiBub2RlLmlkDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgICBjb25zdCBmb3VuZCA9IGZpbmRJblRyZWUobm9kZS5jaGlsZHJlbikNCiAgICAgICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBudWxsDQogICAgICB9DQogICAgICANCiAgICAgIHJldHVybiBmaW5kSW5UcmVlKHRoaXMuc2NlbmVUcmVlT3B0aW9ucykgfHwgJycNCiAgICB9LA0KICAgIC8vIOWkhOeQhuiDjOaZr+aWh+S7tuWIoOmZpA0KICAgIGhhbmRsZVJlbW92ZUJnRmlsZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLmJnRmlsZVVybCA9ICcnDQogICAgICB0aGlzLmZvcm0uYmdJbWdVcmwgPSAnJyAvLyDlkIzml7bmuIXnqbrog4zmma/lm77niYfpppbluKcNCiAgICAgIHRoaXMuYmdGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuW3suWIoOmZpCcpDQogICAgfSwNCiAgICAvLyDmm7TmlrDog4zmma/mlofku7bliJfooagNCiAgICB1cGRhdGVCZ0ZpbGVMaXN0KCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5iZ0ZpbGVVcmwpIHsNCiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB0aGlzLmZvcm0uYmdGaWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgdGhpcy5iZ0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHRoaXMuZm9ybS5iZ0ZpbGVVcmwsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmJnRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5aSE55CG5LuL57uN6KeG6aKR5paH5Lu25Yig6ZmkDQogICAgaGFuZGxlUmVtb3ZlSW50cm9kdWNlVmlkZW9GaWxlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmludHJvZHVjZVZpZGVvLmJhY2tncm91bmRGaWxlVXJsID0gJycNCiAgICAgIHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEltZ0ZpbGVVcmwgPSAnJyAvLyDlkIzml7bmuIXnqbrpppbluKflm77niYcNCiAgICAgIHRoaXMuaW50cm9kdWNlVmlkZW9GaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S7i+e7jeinhumikeW3suWIoOmZpCcpDQogICAgfSwNCiAgICAvLyDmm7TmlrDku4vnu43op4bpopHmlofku7bliJfooagNCiAgICB1cGRhdGVJbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0KCkgew0KICAgICAgaWYgKHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEZpbGVVcmwpIHsNCiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB0aGlzLmludHJvZHVjZVZpZGVvLmJhY2tncm91bmRGaWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEZpbGVVcmwsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5aSE55CG6K6y6Kej6KeG6aKR5paH5Lu25Yig6ZmkDQogICAgaGFuZGxlUmVtb3ZlVmlkZW9FeHBsYW5hdGlvbkZpbGUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi5iYWNrZ3JvdW5kRmlsZVVybCA9ICcnDQogICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+iusuino+inhumikeW3suWIoOmZpCcpDQogICAgfSwNCiAgICAvLyDmm7TmlrDorrLop6Pop4bpopHmlofku7bliJfooagNCiAgICB1cGRhdGVWaWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QoKSB7DQogICAgICBpZiAodGhpcy52aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gdGhpcy52aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdGhpcy52aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5aSE55CGWE1M5paH5Lu25Yig6ZmkDQogICAgaGFuZGxlUmVtb3ZlWG1sRmlsZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLnBhbm9yYW1pY1ZpZXdYbWxVcmwgPSAnJw0KICAgICAgdGhpcy54bWxGaWxlTGlzdCA9IFtdDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ1hNTOaWh+S7tuW3suWIoOmZpCcpDQogICAgfSwNCiAgICANCiAgICAvLyDmm7TmlrBYTUzmlofku7bliJfooagNCiAgICB1cGRhdGVYbWxGaWxlTGlzdCgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucGFub3JhbWljVmlld1htbFVybCkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHRoaXMuZm9ybS5wYW5vcmFtaWNWaWV3WG1sVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgdGhpcy54bWxGaWxlTGlzdCA9IFt7DQogICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgdXJsOiB0aGlzLmZvcm0ucGFub3JhbWljVmlld1htbFVybCwNCiAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgfV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMueG1sRmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5Zu+54mH6aKE6KeIDQogICAgcHJldmlld0ltYWdlKHVybCkgew0KICAgICAgaWYgKHVybCkgew0KICAgICAgICB0aGlzLnByZXZpZXdJbWFnZVVybCA9IHVybA0KICAgICAgICB0aGlzLnByZXZpZXdWaXNpYmxlID0gdHJ1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgY2xvc2VQcmV2aWV3KCkgew0KICAgICAgdGhpcy5wcmV2aWV3VmlzaWJsZSA9IGZhbHNlDQogICAgICB0aGlzLnByZXZpZXdJbWFnZVVybCA9ICcnDQogICAgfSwNCiAgICAvLyDliKDpmaTog4zmma/lm77niYcNCiAgICBkZWxldGVCZ0ltYWdlKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a5Yig6Zmk5q2k5Zu+54mH5ZCX77yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtLmJnSW1nVXJsID0gJycNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflm77niYflt7LliKDpmaQnKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICBhc3luYyBiZWZvcmVVcGxvYWRYbWxGaWxlKGZpbGUpIHsNCiAgICAgIC8vIOajgOafpeaWh+S7tuexu+Weiw0KICAgICAgaWYgKCFmaWxlLm5hbWUudG9Mb3dlckNhc2UoKS5lbmRzV2l0aCgnLnhtbCcpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oFhNTOaWh+S7tu+8gScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDmo4Dmn6Xmlofku7blpKflsI/vvIg1ME1C77yJDQogICAgICBpZiAoZmlsZS5zaXplID4gNTAgKiAxMDI0ICogMTAyNCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blpKflsI/kuI3og73otoXov4c1ME1C77yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4iuS8oFhNTOaWh+S7tu+8jOivt+eojeWAmS4uLiIpDQogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdpbmR1c3RyeUNvZGUnLCB0aGlzLmluZHVzdHJ5Q29kZSkNCiAgICAgICAgDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZFNjZW5lRmlsZShmb3JtRGF0YSkNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgLy8g6K6+572uWE1M5paH5Lu2VVJMDQogICAgICAgICAgdGhpcy5mb3JtLnBhbm9yYW1pY1ZpZXdYbWxVcmwgPSByZXMuZGF0YS5maWxlVXJsDQogICAgICAgICAgDQogICAgICAgICAgLy8g55u05o6l6KaG55uWWE1M5paH5Lu25YiX6KGoDQogICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSByZXMuZGF0YS5maWxlVXJsLnNwbGl0KCcvJykucG9wKCkNCiAgICAgICAgICB0aGlzLnhtbEZpbGVMaXN0ID0gW3sNCiAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgdXJsOiByZXMuZGF0YS5maWxlVXJsLA0KICAgICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgICAgfV0NCiAgICAgICAgICANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfkuIrkvKDlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDlpLHotKUnKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCkNCiAgICAgIH0NCiAgICAgIHJldHVybiBmYWxzZQ0KICAgIH0sDQogICAgLy8g5Li76aKY5Y+Y5pu05Zue6LCDDQogICAgb25UaGVtZUNoYW5nZSh0aGVtZSkgew0KICAgICAgLy8g5aaC5p6c5Li76aKY5pyJ6buY6K6k6IOM5pmv5Zu+77yM5Y+v5Lul6Ieq5Yqo6K6+572uDQogICAgICBpZiAodGhlbWUgJiYgdGhlbWUuZGVmYXVsdEJnSW1hZ2UpIHsNCiAgICAgICAgdGhpcy5mb3JtLmJnSW1nVXJsID0gdGhlbWUuZGVmYXVsdEJnSW1hZ2UNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOagvOW8j+WMluWdkOagh+aVsOaNrueUqOS6juaPkOS6pA0KICAgIGZvcm1hdENvb3JkaW5hdGVzRm9yU3VibWl0KGNvb3JkaW5hdGVzKSB7DQogICAgICBpZiAoIWNvb3JkaW5hdGVzIHx8ICFBcnJheS5pc0FycmF5KGNvb3JkaW5hdGVzKSkgew0KICAgICAgICByZXR1cm4geyBjbGlja1g6ICcnLCBjbGlja1k6ICcnIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgY29uc3QgeFZhbHVlcyA9IGNvb3JkaW5hdGVzLm1hcChjb29yZCA9PiBjb29yZC54IHx8ICcwJykuam9pbignLCcpDQogICAgICBjb25zdCB5VmFsdWVzID0gY29vcmRpbmF0ZXMubWFwKGNvb3JkID0+IGNvb3JkLnkgfHwgJzAnKS5qb2luKCcsJykNCiAgICAgIA0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgY2xpY2tYOiB4VmFsdWVzLA0KICAgICAgICBjbGlja1k6IHlWYWx1ZXMNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOino+aekOWdkOagh+Wtl+espuS4suS4uuWdkOagh+aVsOe7hA0KICAgIHBhcnNlQ29vcmRpbmF0ZXNGcm9tQXBpKGNsaWNrWCwgY2xpY2tZKSB7DQogICAgICBjb25zdCB4QXJyYXkgPSBjbGlja1ggPyBjbGlja1guc3BsaXQoJywnKSA6IFsnJ10NCiAgICAgIGNvbnN0IHlBcnJheSA9IGNsaWNrWSA/IGNsaWNrWS5zcGxpdCgnLCcpIDogWycnXQ0KICAgICAgDQogICAgICAvLyDlj5bovoPplb/nmoTmlbDnu4Tplb/luqbvvIznoa7kv53lnZDmoIflr7npvZANCiAgICAgIGNvbnN0IG1heExlbmd0aCA9IE1hdGgubWF4KHhBcnJheS5sZW5ndGgsIHlBcnJheS5sZW5ndGgpDQogICAgICBjb25zdCBjb29yZGluYXRlcyA9IFtdDQogICAgICANCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbWF4TGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29vcmRpbmF0ZXMucHVzaCh7DQogICAgICAgICAgeDogeEFycmF5W2ldIHx8ICcnLA0KICAgICAgICAgIHk6IHlBcnJheVtpXSB8fCAnJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDoh7PlsJHkv53or4HmnInkuIDkuKrlnZDmoIfnu4QNCiAgICAgIHJldHVybiBjb29yZGluYXRlcy5sZW5ndGggPiAwID8gY29vcmRpbmF0ZXMgOiBbeyB4OiAnJywgeTogJycgfV0NCiAgICB9LA0KICAgIC8vIOW8gOWni+e8lui+keagh+mimA0KICAgIHN0YXJ0RWRpdFRpdGxlKGluZGV4KSB7DQogICAgICBjb25zdCBjYXRlZ29yeSA9IHRoaXMuY2F0ZWdvcmllc1tpbmRleF0NCiAgICAgIGNhdGVnb3J5LmVkaXRpbmcgPSB0cnVlDQogICAgICBjYXRlZ29yeS5lZGl0aW5nTmFtZSA9IGNhdGVnb3J5Lm5hbWUNCiAgICAgIA0KICAgICAgLy8g5LiL5LiA5bin6IGa54Sm6L6T5YWl5qGGDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIC8vIOS9v+eUqOWKqOaAgXJlZuWQjeensA0KICAgICAgICBjb25zdCBpbnB1dFJlZiA9IHRoaXMuJHJlZnNbYHRpdGxlSW5wdXRfJHtpbmRleH1gXQ0KICAgICAgICBpZiAoaW5wdXRSZWYgJiYgaW5wdXRSZWZbMF0pIHsNCiAgICAgICAgICBpbnB1dFJlZlswXS5mb2N1cygpDQogICAgICAgICAgaW5wdXRSZWZbMF0uc2VsZWN0KCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5a6M5oiQ57yW6L6R5qCH6aKYDQogICAgZmluaXNoRWRpdFRpdGxlKGluZGV4KSB7DQogICAgICBjb25zdCBjYXRlZ29yeSA9IHRoaXMuY2F0ZWdvcmllc1tpbmRleF0NCiAgICAgIGlmIChjYXRlZ29yeS5lZGl0aW5nTmFtZSAmJiBjYXRlZ29yeS5lZGl0aW5nTmFtZS50cmltKCkpIHsNCiAgICAgICAgY2F0ZWdvcnkubmFtZSA9IGNhdGVnb3J5LmVkaXRpbmdOYW1lLnRyaW0oKQ0KICAgICAgfQ0KICAgICAgY2F0ZWdvcnkuZWRpdGluZyA9IGZhbHNlDQogICAgICBjYXRlZ29yeS5lZGl0aW5nTmFtZSA9ICcnDQogICAgfSwNCg0KICAgIC8vIOWPlua2iOe8lui+keagh+mimA0KICAgIGNhbmNlbEVkaXRUaXRsZShpbmRleCkgew0KICAgICAgY29uc3QgY2F0ZWdvcnkgPSB0aGlzLmNhdGVnb3JpZXNbaW5kZXhdDQogICAgICBjYXRlZ29yeS5lZGl0aW5nID0gZmFsc2UNCiAgICAgIGNhdGVnb3J5LmVkaXRpbmdOYW1lID0gJycNCiAgICB9LA0KICAgIC8vIOiuvue9ruS4iuS8oOaooeW8jw0KICAgIHNldFVwbG9hZE1vZGUodHlwZSwgbW9kZSkgew0KICAgICAgdGhpcy4kc2V0KHRoaXMudXBsb2FkTW9kZXMsIHR5cGUsIG1vZGUpDQogICAgfSwNCiAgICAvLyDog4zmma/mlofku7bpk77mjqXovpPlhaXlpITnkIYNCiAgICBoYW5kbGVCZ0ZpbGVVcmxJbnB1dCh2YWx1ZSkgew0KICAgICAgdGhpcy5iZ0ZpbGVMaXN0ID0gW10NCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy5iZ0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHZhbHVlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6KeG6aKR6K6y6Kej6ZO+5o6l6L6T5YWl5aSE55CGDQogICAgaGFuZGxlVmlkZW9FeHBsYW5hdGlvblVybElucHV0KHZhbHVlKSB7DQogICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCA9IFtdDQogICAgICBpZiAodmFsdWUpIHsNCiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB2YWx1ZS5zcGxpdCgnLycpLnBvcCgpIHx8ICflpJbpg6jpk77mjqXmlofku7YnDQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHZhbHVlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5LuL57uN6KeG6aKR6ZO+5o6l6L6T5YWl5aSE55CGDQogICAgaGFuZGxlSW50cm9kdWNlVmlkZW9VcmxJbnB1dCh2YWx1ZSkgew0KICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW10NCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHZhbHVlLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5ZCM5q2l5paH5Lu2DQogICAgYXN5bmMgaGFuZGxlU3luY2hyb25pemVGaWxlKCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0uc2NlbmVWaWV3Q29uZmlnSWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjkv53lrZjphY3nva7lkI7lho3lkIzmraXmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5zeW5jaHJvbml6aW5nID0gdHJ1ZQ0KICAgICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjlkIzmraXmlofku7bvvIzor7fnqI3lgJkuLi4iKQ0KICAgICAgICANCiAgICAgICAgLy8g5L2/55SoRm9ybURhdGHmiJZVUkxTZWFyY2hQYXJhbXPkvKDpgJLooajljZXlj4LmlbANCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ3ZpZXdDb25maWdJZCcsIHRoaXMuZm9ybS5zY2VuZVZpZXdDb25maWdJZCkNCiAgICAgICAgDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHN5bmNocm9uaXphdGlvbkZpbGUoZm9ybURhdGEpDQogICAgICAgIA0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzLm1zZykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+aWh+S7tuWQjOatpeWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WQjOatpeaWh+S7tuWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25ZCM5q2l5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuc3luY2hyb25pemluZyA9IGZhbHNlDQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0VA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <!-- ✅ 左侧菜单区域 -->\r\n    <div class=\"menu-panel\">\r\n      <!-- 搜索框 -->\r\n      <div class=\"menu-search\">\r\n        <el-input\r\n          v-model=\"searchKeyword\"\r\n          placeholder=\"搜索菜单...\"\r\n          prefix-icon=\"el-icon-search\"\r\n          clearable\r\n          @input=\"handleSearch\"\r\n        />\r\n      </div>\r\n      \r\n      <el-tree\r\n        ref=\"menuTree\"\r\n        :data=\"filteredMenuData\"\r\n        :props=\"{ label: 'name', children: 'children' }\"\r\n        node-key=\"id\"\r\n        :current-node-key=\"activeMenu\"\r\n        @node-click=\"handleTreeNodeClick\"\r\n        highlight-current\r\n        :expand-on-click-node=\"false\"\r\n        :default-expanded-keys=\"menuData.map(item => item.id)\"\r\n        class=\"menu-tree\"\r\n      >\r\n        <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\r\n          <span v-html=\"highlightText(data.name)\"></span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n\r\n    <!-- ✅ 右侧内容区域 -->\r\n    <div class=\"content-panel\" v-loading=\"switchingIndustry\" element-loading-text=\"正在切换行业...\">\r\n      <el-form :model=\"form\" ref=\"sceneForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <!-- 左侧：基本信息 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"mini-block\" shadow=\"never\">\r\n              <div slot=\"header\">基本信息</div>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"主标题\" required>\r\n                    <el-input v-model=\"form.mainTitle\" placeholder=\"请输入主标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"副标题\" required>\r\n                    <el-input v-model=\"form.subTitle\" placeholder=\"请输入副标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"beforeUploadIntroduceImg\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"form.bgImgUrl\" class=\"image-preview-container\">\r\n                        <img :src=\"form.bgImgUrl\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(form.bgImgUrl)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBgImage\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"主题选择\">\r\n                    <theme-selection-dialog \r\n                      v-model=\"selectedTheme\"\r\n                      @change=\"onThemeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\">\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.bgFile || 'upload'\" @input=\"value => setUploadMode('bgFile', value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.bgFile || 'upload') === 'upload'\"\r\n                      class=\"upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"bgFileList\"\r\n                      :before-upload=\"file => beforeUploadIntroduceVideo(file)\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveBgFile\"\r\n                    >\r\n                      <el-button type=\"primary\">上传背景文件</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"form.bgFileUrl\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"handleBgFileUrlInput\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"XML文件\">\r\n                    <el-upload\r\n                      class=\"upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"xmlFileList\"\r\n                      accept=\".xml\"\r\n                      :before-upload=\"beforeUploadXmlFile\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveXmlFile\"\r\n                    >\r\n                      <el-button type=\"primary\">上传XML文件</el-button>\r\n                      <div slot=\"tip\" class=\"el-upload__tip\">只能上传xml文件，且不超过50MB</div>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-card>\r\n          </el-col>\r\n          \r\n          <!-- 右侧：视频讲解 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"mini-block video-card\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"video-header-row\">\r\n                <span>视频讲解</span>\r\n                <el-switch v-model=\"videoExplanation.status\" :active-value=\"'0'\" :inactive-value=\"'1'\" style=\"float:right;\" />\r\n              </div>\r\n              <div v-show=\"videoExplanation.status === '0'\">\r\n              <el-collapse-transition>\r\n                <div class=\"video-card-yu\">\r\n                  <el-form-item label=\"讲解视频\">\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.videoExplanation || 'upload'\" @input=\"value => setUploadMode('videoExplanation', value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.videoExplanation || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"videoExplanationFileList\"\r\n                      accept=\".mp4\"\r\n                      :before-upload=\"file => beforeUploadIntroduceVideo(file, 'videoExplanation', 'backgroundFileUrl')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveVideoExplanationFile\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                      <div slot=\"tip\" class=\"el-upload__tip\">只能上传mp4文件</div>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"videoExplanation.backgroundFileUrl\"\r\n                      placeholder=\"请输入视频链接\"\r\n                      @input=\"handleVideoExplanationUrlInput\"\r\n                    />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"视频分段说明\">\r\n                    <div class=\"segment-scroll\">\r\n                      <div v-for=\"(seg, idx) in videoSegmentedList\" :key=\"idx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                        <el-input-number\r\n                          v-model=\"seg.time\"\r\n                          :min=\"0\"\r\n                          :max=\"999999\"\r\n                          placeholder=\"时间\"\r\n                          style=\"width: 120px; margin-right: 10px;\"\r\n                          @change=\"val => handleTimeChange(val, idx)\"\r\n                        />\r\n                        <span style=\"margin-right: 10px; color: #606266;\">秒</span>\r\n                        <el-cascader\r\n                          v-model=\"seg.sceneId\"\r\n                          :options=\"sceneTreeOptions\"\r\n                          :props=\"sceneCascaderProps\"\r\n                          filterable\r\n                          check-strictly\r\n                          placeholder=\"所属场景\"\r\n                          style=\"width: 200px; margin-right: 10px;\"\r\n                          @change=\"val => handleSceneCascaderChange(val, idx)\"\r\n                        />\r\n                        <el-button type=\"danger\" icon=\"el-icon-delete\" circle @click=\"removeVideoSegment(idx)\" />\r\n                      </div>\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addVideoSegment\" style=\"margin-top: 8px;\">增加分段</el-button>\r\n                  </el-form-item>\r\n                </div>\r\n              </el-collapse-transition>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <!-- ✅ 分类区域（支持折叠） -->\r\n      <div\r\n        v-for=\"(category, index) in categories\"\r\n        :key=\"category.key\"\r\n        class=\"category-block\"\r\n      >\r\n        <div class=\"category-header\">\r\n          <div class=\"category-title\" @dblclick=\"startEditTitle(index)\">\r\n            <el-input\r\n              v-if=\"category.editing\"\r\n              v-model=\"category.editingName\"\r\n              @blur=\"finishEditTitle(index)\"\r\n              @keyup.enter=\"finishEditTitle(index)\"\r\n              @keyup.esc=\"cancelEditTitle(index)\"\r\n              :data-edit-index=\"index\"\r\n              size=\"small\"\r\n              style=\"width: 200px;\"\r\n              placeholder=\"请输入标题\"\r\n            />\r\n            <span v-else>{{ category.name }}</span>\r\n          </div>\r\n          <el-switch\r\n            v-model=\"category.enabled\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ccc\"\r\n          />\r\n        </div>\r\n\r\n        <div v-show=\"category.enabled\" class=\"category-body\">\r\n          <div v-if=\"category.key === 'default_scene'\">\r\n            <el-form label-width=\"120px\">\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"24\">\r\n                  <!-- 场景配置分块 -->\r\n                  <el-card class=\"mini-block scene-config-container\" shadow=\"never\" style=\"margin-top: 20px;\">\r\n                    <div slot=\"header\">场景配置</div>\r\n                    <div>\r\n                      <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                          <el-tree\r\n                            ref=\"sceneTree\"\r\n                            :data=\"sceneConfigTree\"\r\n                            node-key=\"id\"\r\n                            :props=\"{ label: 'name', children: 'children' }\"\r\n                            @node-click=\"handleSceneNodeClick\"\r\n                            highlight-current\r\n                            :expand-on-click-node=\"false\"\r\n                            :default-expanded-keys=\"treeExpandedKeys.length > 0 ? treeExpandedKeys : (sceneConfigTree.length ? [sceneConfigTree[0].id] : [])\"\r\n                            :current-node-key=\"selectedNode ? selectedNode.id : null\"\r\n                            :sort=\"false\"\r\n                          />\r\n                        </el-col>\r\n                        <el-col :span=\"18\">\r\n                          <SceneConfigNode \r\n                            v-if=\"selectedNode\" \r\n                            :node=\"selectedNode\" \r\n                            :root-tree=\"sceneConfigTree\"\r\n                            :scene-tree-options=\"sceneTreeOptions\"\r\n                            :left-tree-industry-code=\"industryCode\"\r\n                          />\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                  </el-card>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n\r\n          <!-- 其他分类的占位内容 -->\r\n          <div v-else-if=\"category.key === 'default_plan'\">\r\n            <network-plan-config v-model=\"networkPlanData\" :left-tree-industry-code=\"industryCode\" />\r\n          </div>\r\n          <div v-else-if=\"category.key === 'default_value'\">\r\n            <business-value-config v-model=\"businessValueData\" :left-tree-industry-code=\"industryCode\" />\r\n          </div>\r\n          <div v-else-if=\"category.key === 'default_vr'\">\r\n            <vr-scene-config v-model=\"vrSceneData\" />\r\n          </div>\r\n          <div v-else>\r\n            <p>这里是 <strong>{{ category.name }}</strong> 分类的内容区域。</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit-footer\">\r\n      <div class=\"form-actions\">\r\n        <!-- 同步文件按钮加 tooltip -->\r\n        <el-tooltip \r\n          effect=\"dark\" \r\n          content=\"链接填写完成后，点击【提交】后再点击【同步】按钮\" \r\n          placement=\"top\"\r\n        >\r\n          <el-button \r\n            type=\"success\" \r\n            @click=\"handleSynchronizeFile\" \r\n            :disabled=\"!form.sceneViewConfigId\"\r\n            :loading=\"synchronizing\"\r\n          >\r\n            {{ synchronizing ? '同步中...' : '同步文件' }}\r\n          </el-button>\r\n        </el-tooltip>\r\n\r\n        <el-button \r\n          type=\"primary\" \r\n          @click=\"handleSubmit\" \r\n          :loading=\"submitting\" \r\n          style=\"margin-left: 30px;\"\r\n        >\r\n          {{ submitting ? '提交中...' : '提交' }}\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    </div>\r\n    \r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"previewVisible\"\r\n      title=\"图片预览\"\r\n      width=\"60%\"\r\n      append-to-body\r\n      @close=\"closePreview\"\r\n    >\r\n      <div class=\"preview-container\">\r\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getIndustryList, getSceneTreeList } from '@/api/view/industry'\r\nimport SceneConfigNode from './SceneConfigNode.vue'\r\nimport { getSceneViewConfig, sceneViewUpd, uploadSceneFile, synchronizationFile } from '@/api/view/sceneView'\r\nimport NetworkPlanConfig from './NetworkPlanConfig.vue'\r\nimport BusinessValueConfig from './BusinessValueConfig.vue'\r\nimport VrSceneConfig from './VrSceneConfig.vue'\r\nimport ThemeSelectionDialog from './ThemeSelectionDialog.vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'IndustryScenePage',\r\n  components: {\r\n    SceneConfigNode,\r\n    NetworkPlanConfig,\r\n    BusinessValueConfig,\r\n    VrSceneConfig,\r\n    ThemeSelectionDialog\r\n  },\r\n  data() {\r\n    return {\r\n      menuData: [], // 原始菜单数据\r\n      flatMenuData: [], // 扁平化的行业数据，用于搜索和业务逻辑\r\n      activeMenu: '',\r\n      industryCode: '',\r\n      selectedTheme: null, // 当前选择的主题\r\n      form: {\r\n        mainTitle: '',\r\n        subTitle: '',\r\n        bgImgUrl: '',\r\n        bgFileUrl: '',\r\n        panoramicViewXmlUrl: ''\r\n      },\r\n      sceneConfigTree: [],\r\n      selectedNode: null,\r\n      loading: false, // 页面加载状态\r\n      switchingIndustry: false, // 新增：切换行业的loading状态\r\n      rules: {\r\n        introduceVideoImgUrl: [\r\n          { required: true, message: '请上传介绍视频首帧', trigger: 'change' }\r\n        ],\r\n        introduceVideoFileUrl: [\r\n          { required: true, message: '请上传介绍视频', trigger: 'change' }\r\n        ],\r\n        videoExplanationFileUrl: [\r\n          { required: true, message: '请上传讲解视频', trigger: 'change' }\r\n        ]\r\n      },\r\n      uploadingType: '',\r\n      uploadingKey: '',\r\n      categories: [], // 改为空数组，从后端动态获取\r\n      introduceVideo: {\r\n        status: '0',\r\n        backgroundImgFileUrl: '',\r\n        backgroundFileUrl: ''\r\n      },\r\n      videoExplanation: {\r\n        status: '0',\r\n        backgroundFileUrl: '',\r\n        videoSegmentedVoList: []\r\n      },\r\n      sceneTreeOptions: [],\r\n      sceneCascaderProps: {\r\n        label: 'sceneName',\r\n        value: 'id',\r\n        children: 'children',\r\n        emitPath: false,\r\n        checkStrictly: true,\r\n        disabled: (data) => {\r\n          // 允许所有节点可选，只要没有被其他分段选中\r\n          const isSelected = this.videoExplanation && this.videoExplanation.videoSegmentedVoList\r\n            ? this.videoExplanation.videoSegmentedVoList.some(seg => seg.sceneId === data.id)\r\n            : false\r\n          return isSelected\r\n        }\r\n      },\r\n      bgFileList: [], // 背景文件列表\r\n      videoExplanationFileList: [], // 讲解视频文件列表\r\n      xmlFileList: [], // XML文件列表\r\n      networkPlanDataMap: {}, // 改为对象，按菜单ID存储\r\n      businessValueDataMap: {}, // 商业价值数据映射\r\n      vrSceneDataMap: {}, // VR看现场数据映射\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      searchKeyword: '',\r\n      treeExpandedKeys: [], // 新增：保存树的展开状态\r\n      uploadModes: {\r\n        bgFile: 'upload',\r\n        videoExplanation: 'upload',\r\n        introduceVideo: 'upload'\r\n      },\r\n      synchronizing: false,\r\n      submitting: false // 添加这个属性\r\n    }\r\n  },\r\n  computed: {\r\n    videoSegmentedList() {\r\n      // 如果没有数据，默认返回一行空数据\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        return [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      return this.videoExplanation.videoSegmentedVoList\r\n    },\r\n    networkPlanData: {\r\n      get() {\r\n        const menuData = this.networkPlanDataMap[this.activeMenu]\r\n        if (!menuData) {\r\n          return {\r\n            networkVideoList: [],\r\n            videoExplanationVo: {\r\n              status: '0',\r\n              backgroundFileUrl: '',\r\n              videoSegmentedVoList: []\r\n            }\r\n          }\r\n        }\r\n        return menuData\r\n      },\r\n      set(value) {\r\n        this.$set(this.networkPlanDataMap, this.activeMenu, value)\r\n      }\r\n    },\r\n    businessValueData: {\r\n      get() {\r\n        return this.businessValueDataMap[this.activeMenu] || []\r\n      },\r\n      set(value) {\r\n        this.$set(this.businessValueDataMap, this.activeMenu, value)\r\n      }\r\n    },\r\n    vrSceneData: {\r\n      get() {\r\n        return this.vrSceneDataMap[this.activeMenu] || []\r\n      },\r\n      set(val) {\r\n        this.$set(this.vrSceneDataMap, this.activeMenu, val)\r\n      }\r\n    },\r\n    filteredMenuData() {\r\n      if (!this.searchKeyword) {\r\n        return this.menuData\r\n      }\r\n      \r\n      // 递归过滤树形数据\r\n      const filterTree = (nodes) => {\r\n        return nodes.map(node => {\r\n          const filteredChildren = node.children ? filterTree(node.children) : []\r\n          const matchesSearch = node.name && node.name.toLowerCase().includes(this.searchKeyword.toLowerCase())\r\n          \r\n          if (matchesSearch || filteredChildren.length > 0) {\r\n            return {\r\n              ...node,\r\n              children: filteredChildren\r\n            }\r\n          }\r\n          return null\r\n        }).filter(Boolean)\r\n      }\r\n      \r\n      return filterTree(this.menuData)\r\n    }\r\n  },\r\n  created() {\r\n    // 从URL获取token并设置\r\n    this.initTokenFromUrl()\r\n    this.loadIndustryMenu()\r\n  },\r\n  methods: {\r\n    // 从URL获取token并设置\r\n    initTokenFromUrl() {\r\n      const urlParams = new URLSearchParams(window.location.search)\r\n  const token = urlParams.get('token')\r\n  \r\n  if (token) {\r\n    localStorage.setItem('external-token', token)\r\n    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    console.log('从URL获取到token:', token)\r\n  } else {\r\n    const storedToken = localStorage.getItem('external-token')\r\n    if (storedToken) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`\r\n    }\r\n  }\r\n    },\r\n    async loadIndustryMenu() {\r\n      const res = await getIndustryList()\r\n      if (res.code === 0 && Array.isArray(res.data)) {\r\n        // 转换数据结构为树形菜单\r\n        this.menuData = res.data.map(plate => ({\r\n          id: `plate_${plate.plateKey}`,\r\n          name: plate.plateName,\r\n          type: 'plate',\r\n          children: plate.industryTreeListVos ? plate.industryTreeListVos.map(industry => ({\r\n            id: industry.id,\r\n            name: industry.industryName,\r\n            industryCode: industry.industryCode,\r\n            plate: industry.plate,\r\n            type: 'industry'\r\n          })) : []\r\n        }))\r\n        \r\n        // 创建扁平化的行业数据，用于业务逻辑\r\n        this.flatMenuData = []\r\n        res.data.forEach(plate => {\r\n          if (plate.industryTreeListVos) {\r\n            this.flatMenuData.push(...plate.industryTreeListVos)\r\n          }\r\n        })\r\n        \r\n        // 默认选中第一个行业\r\n        if (this.flatMenuData.length) {\r\n          this.activeMenu = String(this.flatMenuData[0].id)\r\n          this.industryCode = this.flatMenuData[0].industryCode\r\n          // 等待DOM更新后设置树组件的当前节点\r\n          this.$nextTick(() => {\r\n            // 确保树组件已渲染并设置当前选中节点\r\n            if (this.$refs.menuTree && this.$refs.menuTree.setCurrentKey) {\r\n              this.$refs.menuTree.setCurrentKey(this.activeMenu)\r\n            }\r\n          })\r\n          \r\n          // 加载第一个行业的数据\r\n          await this.handleSelect(this.activeMenu)\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleTreeNodeClick(data) {\r\n      // 只有点击行业节点才处理\r\n      if (data.type === 'industry') {\r\n        this.handleSelect(String(data.id))\r\n        this.industryCode = data.industryCode;\r\n      }\r\n    },\r\n\r\n    handleSearch(value) {\r\n      this.searchKeyword = value\r\n    },\r\n    highlightText(text) {\r\n      if (!this.searchKeyword) return text\r\n      const regex = new RegExp(`(${this.searchKeyword})`, 'gi')\r\n      return text.replace(regex, '<span class=\"highlight\">$1</span>')\r\n    },\r\n    async handleSelect(id, keepSelectedNode = false, showLoading = true) {\r\n      try {\r\n        // 开启切换行业的loading\r\n        if (showLoading) {\r\n          this.switchingIndustry = true\r\n          // 禁用页面滚动\r\n          document.body.style.overflow = 'hidden'\r\n        }\r\n        \r\n        this.activeMenu = id\r\n        await this.loadSceneTreeOptions(this.activeMenu)\r\n        \r\n        // 保存当前选中的节点\r\n        const currentSelectedNode = keepSelectedNode ? this.selectedNode : null\r\n        \r\n        // 重置主题选择\r\n        this.selectedTheme = null\r\n        \r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        const res = await getSceneViewConfig({ industryCode: industryCode })\r\n        if (res.code === 0 && res.data) {\r\n          \r\n          // 同步主标题、副标题、背景图片、XML文件等\r\n          this.form.sceneViewConfigId = res.data.sceneViewConfigId || ''\r\n          this.form.mainTitle = res.data.mainTitle || ''\r\n          this.form.subTitle = res.data.subTitle || ''\r\n          this.form.bgImgUrl = res.data.backgroundImgFileUrl || ''\r\n          this.form.bgFileUrl = res.data.backgroundFileUrl || ''\r\n          this.form.panoramicViewXmlUrl = res.data.panoramicViewXmlUrl || ''\r\n          \r\n          // 更新背景文件列表\r\n          this.updateBgFileList()\r\n          \r\n          // 更新XML文件列表\r\n          this.updateXmlFileList()\r\n          \r\n          // 回显主题选择\r\n          if (res.data.themeInfoVo) {\r\n            this.selectedTheme = {\r\n              themeId: res.data.themeInfoVo.themeId,\r\n              themeName: res.data.themeInfoVo.themeName,\r\n              themeEffectImg: res.data.themeInfoVo.themeEffectImg,\r\n              remark: res.data.themeInfoVo.remark\r\n            }\r\n          } else {\r\n            this.selectedTheme = null\r\n          }\r\n          \r\n          // 处理 sceneDefaultConfigVoList，动态生成 categories\r\n          if (res.data.sceneDefaultConfigVoList && Array.isArray(res.data.sceneDefaultConfigVoList)) {\r\n            this.categories = res.data.sceneDefaultConfigVoList.map(configItem => ({\r\n              id: configItem.id,\r\n              key: configItem.keyName,\r\n              name: configItem.name,\r\n              enabled: configItem.keyValue === '0', // keyValue为'0'表示启用\r\n              editing: false,\r\n              editingName: '',\r\n              originalName: configItem.name,\r\n              remark: configItem.remark,\r\n              classification: configItem.classification,\r\n              defaultStatus: configItem.defaultStatus\r\n            }))\r\n            \r\n            // 查找场景配置分类\r\n            const sceneCategory = res.data.sceneDefaultConfigVoList.find(item => item.keyName === 'default_scene')\r\n            \r\n            // 处理视频讲解数据\r\n            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.videoExplanationVo) {\r\n              const videoData = sceneCategory.industrySceneInfoVo.videoExplanationVo\r\n              this.videoExplanation = {\r\n                status: videoData.status || '0',\r\n                backgroundFileUrl: videoData.backgroundFileUrl || '',\r\n                videoSegmentedVoList: videoData.videoSegmentedVoList ? videoData.videoSegmentedVoList.map(seg => ({\r\n                  time: seg.time,\r\n                  sceneCode: seg.sceneCode,\r\n                  sceneName: seg.sceneName,\r\n                  sceneId: this.findSceneIdByCode(seg.sceneCode) // 根据sceneCode查找sceneId\r\n                })) : []\r\n              }\r\n            } else {\r\n              this.videoExplanation = {\r\n                status: '0',\r\n                backgroundFileUrl: '',\r\n                videoSegmentedVoList: []\r\n              }\r\n            }\r\n            \r\n            // 更新视频讲解文件列表\r\n            this.updateVideoExplanationFileList()\r\n            \r\n            // 处理场景配置树\r\n            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.sceneListVo) {\r\n              this.sceneConfigTree = this.adaptSceneTree(sceneCategory.industrySceneInfoVo.sceneListVo)\r\n              \r\n              // 如果需要保持选中节点\r\n              if (keepSelectedNode && currentSelectedNode) {\r\n                const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNode.id)\r\n                if (nodeToSelect) {\r\n                  this.selectedNode = nodeToSelect\r\n                } else {\r\n                  this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null\r\n                }\r\n              } else {\r\n                // 默认选择第一个节点\r\n                this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null\r\n              }\r\n            } else {\r\n              // 没有场景数据时清空\r\n              this.sceneConfigTree = []\r\n              this.selectedNode = null\r\n            }\r\n          }\r\n          \r\n          // 处理网络方案数据\r\n          if (res.data.networkSolutionVo) {\r\n            const networkData = {\r\n              networkVideoList: res.data.networkSolutionVo.networkVideoList || [],\r\n              videoExplanationVo: res.data.networkSolutionVo.videoExplanationVo || {\r\n                status: '0',\r\n                backgroundFileUrl: '',\r\n                videoSegmentedVoList: []\r\n              }\r\n            }\r\n            this.$set(this.networkPlanDataMap, this.activeMenu, networkData)\r\n          }\r\n\r\n          // 处理商业价值数据\r\n          if (res.data.commercialValueListVo) {\r\n            this.$set(this.businessValueDataMap, this.activeMenu, res.data.commercialValueListVo)\r\n          }\r\n\r\n          // 处理VR看现场数据\r\n          if (res.data.vrInfoListVo) {\r\n            this.$set(this.vrSceneDataMap, this.activeMenu, res.data.vrInfoListVo)\r\n          }\r\n          \r\n          // 其他数据处理逻辑保持不变...\r\n        }\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n        this.$message.error('加载数据失败')\r\n      } finally {\r\n        // 关闭切换行业的loading\r\n        if (showLoading) {\r\n          this.switchingIndustry = false\r\n          // 恢复页面滚动\r\n          document.body.style.overflow = ''\r\n        }\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n      return false // 拦截默认上传行为\r\n    },\r\n    addSegment() {\r\n      this.form.videoSegmentedVoList.push({ time: '', scene: '' })\r\n    },\r\n    removeSegment(index) {\r\n      if (this.form.videoSegmentedVoList.length >= 1) {\r\n        this.form.videoSegmentedVoList.splice(index, 1)\r\n      }\r\n    },\r\n    async beforeUploadIntroduceImg(file, type, key) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传图片，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          if (type && key) {\r\n            // 针对介绍视频和视频讲解的上传，单独上传图片时使用 fileUrl\r\n            this[type][key] = res.data.fileUrl\r\n          } else {\r\n            // 针对主背景图片的上传\r\n            this.form.bgImgUrl = res.data.fileUrl\r\n          }\r\n          this.$message.success('上传成功')\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    async beforeUploadIntroduceVideo(file, type, key) {\r\n      // 如果是主背景文件上传（没有type和key参数）\r\n      if (!type && !key) {\r\n        try {\r\n          this.$modal.loading(\"正在上传文件，请稍候...\")\r\n          const formData = new FormData()\r\n          formData.append('file', file)\r\n          formData.append('industryCode', this.industryCode)\r\n          \r\n          const res = await uploadSceneFile(formData)\r\n          if (res.code === 0 && res.data) {\r\n            // 设置背景文件URL\r\n            this.form.bgFileUrl = res.data.fileUrl\r\n            \r\n            // 直接覆盖背景文件列表\r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            this.bgFileList = [{\r\n              name: fileName,\r\n              url: res.data.fileUrl,\r\n              uid: Date.now()\r\n            }]\r\n            \r\n            // 如果是MP4文件且返回了imgUrl，自动设置背景图片首帧\r\n            if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n              this.form.bgImgUrl = res.data.imgUrl\r\n              this.$message.success('上传成功，已自动生成背景图片首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || '上传失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('上传失败')\r\n        } finally {\r\n          this.$modal.closeLoading()\r\n        }\r\n        return false\r\n      }\r\n      \r\n      // 其他视频上传逻辑（介绍视频、讲解视频等）\r\n      if (!file.type.startsWith('video/') && !file.name.endsWith('.mp4')) {\r\n        this.$message.error('只能上传MP4视频文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传视频，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          if (type && key) {\r\n            // 针对介绍视频和视频讲解的上传\r\n            this[type][key] = res.data.fileUrl\r\n            \r\n            // 直接覆盖对应的文件列表\r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            if (type === 'introduceVideo' && key === 'backgroundFileUrl') {\r\n              this.introduceVideoFileList = [{\r\n                name: fileName,\r\n                url: res.data.fileUrl,\r\n                uid: Date.now()\r\n              }]\r\n            } else if (type === 'videoExplanation' && key === 'backgroundFileUrl') {\r\n              this.videoExplanationFileList = [{\r\n                name: fileName,\r\n                url: res.data.fileUrl,\r\n                uid: Date.now()\r\n              }]\r\n            }\r\n            \r\n            // 如果是介绍视频上传，且返回了imgUrl，自动设置介绍视频首帧\r\n            if (type === 'introduceVideo' && key === 'backgroundFileUrl' && res.data.imgUrl) {\r\n              this.introduceVideo.backgroundImgFileUrl = res.data.imgUrl\r\n              this.$message.success('上传成功，已自动生成介绍视频首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    beforeUploadExplanationVideo(file) {\r\n      this.uploadingType = 'mp4'\r\n      this.uploadingKey = 'videoExplanationFileUrl'\r\n      return this.handleBeforeUpload(file)\r\n    },\r\n    // 新增方法：添加场景配置节点\r\n    addSceneConfigNode(parentId = null) {\r\n      const newNode = {\r\n        id: Date.now(), // 生成唯一ID\r\n        name: '新场景',\r\n        type: 'scene', // 类型为场景\r\n        enabled: true,\r\n        children: [],\r\n        parentId: parentId\r\n      }\r\n      if (parentId) {\r\n        const parentNode = this.findNodeById(this.sceneConfigTree, parentId)\r\n        if (parentNode) {\r\n          parentNode.children.push(newNode)\r\n        }\r\n      } else {\r\n        this.sceneConfigTree.push(newNode)\r\n      }\r\n      return newNode.id\r\n    },\r\n    // 新增方法：删除场景配置节点\r\n    removeSceneConfigNode(nodeId) {\r\n      this.sceneConfigTree = this.sceneConfigTree.filter(node => node.id !== nodeId)\r\n    },\r\n    // 新增方法：查找节点\r\n    findNodeById(nodes, id) {\r\n      for (const node of nodes) {\r\n        if (node.id === id) {\r\n          return node\r\n        }\r\n        if (node.children && node.children.length > 0) {\r\n          const found = this.findNodeById(node.children, id)\r\n          if (found) {\r\n            return found\r\n          }\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    // 新增方法：添加场景的痛点价值\r\n    addScenePainPoint(nodeId) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints.push({ title: '', contents: [''], showTime: '' })\r\n      }\r\n    },\r\n    // 新增方法：删除场景的痛点价值\r\n    removeScenePainPoint(nodeId, idx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints.splice(idx, 1)\r\n      }\r\n    },\r\n    // 新增方法：添加场景痛点内容的项\r\n    addScenePainContent(nodeId, idx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints[idx].contents.push('')\r\n      }\r\n    },\r\n    // 新增方法：删除场景痛点内容的项\r\n    removeScenePainContent(nodeId, idx, cidx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints[idx].contents.splice(cidx, 1)\r\n      }\r\n    },\r\n    // 新增方法：添加场景的成本预估内容\r\n    addSceneCostContent(nodeId) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'costEstimate') {\r\n        node.contents = node.contents || []\r\n        node.contents.push('')\r\n      }\r\n    },\r\n    // 新增方法：删除场景的成本预估内容\r\n    removeSceneCostContent(nodeId, cidx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'costEstimate') {\r\n        node.contents = node.contents || []\r\n        node.contents.splice(cidx, 1)\r\n      }\r\n    },\r\n    // 新增方法：上传场景配置图片\r\n    beforeUploadSceneConfigImg(file, type, key) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      const reader = new FileReader()\r\n      reader.onload = e => {\r\n        this.findNodeById(this.sceneConfigTree, type)[key] = e.target.result\r\n      }\r\n      reader.readAsDataURL(file)\r\n      return false\r\n    },\r\n    // 新增方法：上传场景配置文件\r\n    beforeUploadSceneConfigFile(file, type, key) {\r\n      // 这里只做文件名回显\r\n      this.findNodeById(this.sceneConfigTree, type)[key] = file.name\r\n      return false\r\n    },\r\n    handleSceneNodeClick(node) {\r\n      this.selectedNode = node\r\n    },\r\n    // 适配函数移到methods中，供接口数据适配使用\r\n    adaptSceneTree(rawTree, parent = null) {\r\n      if (!rawTree) return []\r\n      const arr = Array.isArray(rawTree) ? rawTree : [rawTree]\r\n      return arr.map((node) => {\r\n        const adapted = {\r\n          id: node.sceneId,\r\n          sceneInfoId: node.sceneInfoId || null,\r\n          name: node.sceneName,\r\n          code: node.sceneCode,\r\n          x: node.x,\r\n          y: node.y,\r\n          type: node.type,\r\n          status: node.status !== null ? node.status : '0',\r\n          isUnfold: node.isUnfold !== null && node.isUnfold !== undefined ? node.isUnfold : '1',\r\n          displayLocation: node.displayLocation !== null && node.displayLocation !== undefined ? node.displayLocation : '0',\r\n          treeClassification: node.treeClassification !== null && node.treeClassification !== undefined ? node.treeClassification : '3',\r\n          introduceVideoVo: node.introduceVideoVo ? {\r\n            id: node.introduceVideoVo.id || '',\r\n            type: node.introduceVideoVo.type || '',\r\n            viewInfoId: node.introduceVideoVo.viewInfoId || '',\r\n            status: node.introduceVideoVo.status || '0',\r\n            backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || '',\r\n            backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || ''\r\n          } : { id: '', type: '', viewInfoId: '', status: '0', backgroundImgFileUrl: '', backgroundFileUrl: '' },\r\n          tradition: node.sceneTraditionVo ? {\r\n            name: node.sceneTraditionVo.name || '',\r\n            panoramicViewXmlKey: node.sceneTraditionVo.panoramicViewXmlKey || '',\r\n            backgroundResources: node.sceneTraditionVo.sceneVideoList ? \r\n              node.sceneTraditionVo.sceneVideoList.map(v => ({\r\n                id: v.id || null,\r\n                label: v.tag || '',\r\n                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({\r\n                  id: rel.id || null,\r\n                  fileId: rel.fileId || null,\r\n                  x: rel.clickX || '',\r\n                  y: rel.clickY || '',\r\n                  wide: rel.wide || '',\r\n                  high: rel.high || '',\r\n                  xmlKey: rel.xmlKey || '',\r\n                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),\r\n                  sceneCode: rel.bindSceneCode || ''\r\n                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }],\r\n                wide: v.wide || 0,\r\n                high: v.high || 0,\r\n                bgImg: v.backgroundImgFileUrl || '',\r\n                bgFile: v.backgroundFileUrl || '',\r\n                status: v.status || '',\r\n                type: v.type || '',\r\n                viewInfoId: v.viewInfoId || ''\r\n              })) : [],\r\n            painPoints: node.sceneTraditionVo.painPointList ?\r\n              (Array.isArray(node.sceneTraditionVo.painPointList) ? node.sceneTraditionVo.painPointList.map(p => ({\r\n                painPointId: p.painPointId || null,\r\n                title: p.bigTitle || '',\r\n                contents: p.content || [],\r\n                showTime: p.displayTime || ''\r\n              })) : []) : []\r\n          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },\r\n          wisdom5g: node.scene5gVo ? {\r\n            name: node.scene5gVo.name || '',\r\n            panoramicViewXmlKey: node.scene5gVo.panoramicViewXmlKey || '',\r\n            backgroundResources: node.scene5gVo.sceneVideoList ? \r\n              node.scene5gVo.sceneVideoList.map(v => ({\r\n                id: v.id || null,\r\n                tag: v.tag || '',\r\n                status: v.status || '',\r\n                type: v.type || '',\r\n                viewInfoId: v.viewInfoId || '',\r\n                backgroundImgFileUrl: v.backgroundImgFileUrl || '',\r\n                backgroundFileUrl: v.backgroundFileUrl || '',\r\n                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({\r\n                  id: rel.id || null,\r\n                  fileId: rel.fileId || null,\r\n                  x: rel.clickX || '',\r\n                  y: rel.clickY || '',\r\n                  wide: rel.wide || '',\r\n                  high: rel.high || '',\r\n                  xmlKey: rel.xmlKey || '',\r\n                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),\r\n                  sceneCode: rel.bindSceneCode || ''\r\n                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }]\r\n              })) : [],\r\n            painPoints: node.scene5gVo.painPointList ? \r\n              (Array.isArray(node.scene5gVo.painPointList) ? node.scene5gVo.painPointList.map(p => ({\r\n                painPointId: p.painPointId || null,\r\n                title: p.bigTitle || '',\r\n                contents: p.content || [],\r\n                showTime: p.displayTime || ''\r\n              })) : []) : []\r\n          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },\r\n          costEstimate: node.costEstimationInfoVo ? {\r\n            painPointId: node.costEstimationInfoVo.painPointId || null,\r\n            status: node.costEstimationInfoVo.status || '0',\r\n            title: node.costEstimationInfoVo.bigTitle || '',\r\n            contents: node.costEstimationInfoVo.content || []\r\n          } : { painPointId: null, status: '0', title: '', contents: [] },\r\n          children: [],\r\n          parent\r\n        }\r\n        // 递归处理子节点，保持后端返回的原始顺序\r\n        adapted.children = node.children ? this.adaptSceneTree(node.children, adapted) : []\r\n        return adapted\r\n      })\r\n    },\r\n    async handleSubmit() {\r\n      try {\r\n        this.submitting = true\r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === this.activeMenu)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        // 保存当前选中的节点的完整信息\r\n        const currentSelectedNodeId = this.selectedNode ? this.selectedNode.id : null\r\n        const currentSelectedNodeName = this.selectedNode ? this.selectedNode.name : null\r\n        \r\n        // 构建提交数据\r\n        const submitData = {\r\n          industryId: this.activeMenu,\r\n          industryCode: industryCode, // 新增参数\r\n          sceneViewConfigId: this.form.sceneViewConfigId || null,\r\n          mainTitle: this.form.mainTitle || null,\r\n          subTitle: this.form.subTitle || null,\r\n          themeId: this.selectedTheme ? this.selectedTheme.themeId : null,\r\n          backgroundImgFileUrl: this.form.bgImgUrl || null,\r\n          backgroundFileUrl: this.form.bgFileUrl || null,\r\n          panoramicViewXmlUrl: this.form.panoramicViewXmlUrl || null,\r\n          networkSolutionInfoVo: {\r\n            networkVideoList: (this.networkPlanDataMap[this.activeMenu]?.networkVideoList && Array.isArray(this.networkPlanDataMap[this.activeMenu].networkVideoList)) ? \r\n              this.networkPlanDataMap[this.activeMenu].networkVideoList.map(plan => ({\r\n                id: plan.id || null,\r\n                type: 4,\r\n                tag: plan.tag || null,\r\n                clickX: plan.clickX || null,\r\n                clickY: plan.clickY || null,\r\n                wide: plan.wide || null,\r\n                high: plan.high || null,\r\n                backgroundImgFileUrl: plan.backgroundImgFileUrl || null,\r\n                backgroundFileUrl: plan.backgroundFileUrl || null,\r\n                status: null,\r\n                viewInfoId: null\r\n              })) : [],\r\n            videoExplanationVo: {\r\n              status: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.status || '0',\r\n              backgroundFileUrl: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.backgroundFileUrl || null,\r\n              videoSegmentedVoList: (this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.videoSegmentedVoList?.length) ? \r\n                this.networkPlanDataMap[this.activeMenu].videoExplanationVo.videoSegmentedVoList.map(seg => ({\r\n                  time: seg.time || null,\r\n                  sceneCode: seg.sceneCode || null,\r\n                  sceneName: seg.sceneName || null\r\n                })) : null\r\n            }\r\n          },\r\n          sceneDefaultConfigVoList: this.categories.map(cat => {\r\n            const baseConfig = {\r\n              id: cat.id || null,\r\n              industryId: this.activeMenu || null,\r\n              name: cat.name,\r\n              keyName: cat.key,\r\n              keyValue: cat.enabled ? '0' : '1',\r\n              remark: cat.remark || cat.name,\r\n              defaultStatus: '0'\r\n            }\r\n\r\n            if (cat.key === 'default_scene') {\r\n              const convertedSceneList = this.convertSceneTreeToApi(this.sceneConfigTree)     \r\n              baseConfig.industrySceneInfoVo = {\r\n                videoExplanationVo: {\r\n                  status: this.videoExplanation.status,\r\n                  backgroundFileUrl: this.videoExplanation.backgroundFileUrl || null,\r\n                  videoSegmentedVoList: this.videoExplanation.videoSegmentedVoList.length ? this.videoExplanation.videoSegmentedVoList : null\r\n                },\r\n                sceneListVo: convertedSceneList\r\n              }\r\n            } else {\r\n              baseConfig.industrySceneInfoVo = null\r\n            }\r\n            \r\n            return baseConfig\r\n          }),\r\n          commercialValueDTO: (this.businessValueDataMap[this.activeMenu] && Array.isArray(this.businessValueDataMap[this.activeMenu])) ? \r\n            this.businessValueDataMap[this.activeMenu].map(value => ({\r\n              id: value.id || null,\r\n              viewInfoId: value.viewInfoId || null,\r\n              type: 5,\r\n              tag: value.tag || null,\r\n              backgroundImgFileUrl: value.backgroundImgFileUrl || null,\r\n              backgroundFileUrl: value.backgroundFileUrl || null\r\n            })) : [],\r\n          vrInfoDtoList: (this.vrSceneDataMap[this.activeMenu] && Array.isArray(this.vrSceneDataMap[this.activeMenu])) ? \r\n            this.vrSceneDataMap[this.activeMenu]. map(vr => ({\r\n              id: vr.id || null,\r\n              industryId: vr.industryId || this.activeMenu,\r\n              type: vr.type || 6,\r\n              viewInfoId: vr.viewInfoId || null,\r\n              name: vr.name || '',\r\n              address: vr.address || ''\r\n            })) : [],\r\n        }\r\n      \r\n        const response = await sceneViewUpd(submitData)\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n\r\n        // 重新加载数据（不显示全局loading，避免与按钮loading冲突）\r\n        await this.handleSelect(this.activeMenu, false, false)\r\n\r\n        if (currentSelectedNodeId && this.sceneConfigTree.length > 0) {\r\n          // 强制查找并设置选中节点\r\n          const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNodeId)\r\n\r\n          if (nodeToSelect) {\r\n            // 计算并设置展开路径\r\n            const pathIds = []\r\n            const findPath = (nodes, targetId, currentPath = []) => {\r\n              for (const node of nodes) {\r\n                const newPath = [...currentPath, node.id]\r\n                if (node.id === targetId) {\r\n                  pathIds.push(...newPath)\r\n                  return true\r\n                }\r\n                if (node.children && node.children.length > 0) {\r\n                  if (findPath(node.children, targetId, newPath)) {\r\n                    return true\r\n                  }\r\n                }\r\n              }\r\n              return false\r\n            }\r\n\r\n            findPath(this.sceneConfigTree, currentSelectedNodeId)\r\n            this.treeExpandedKeys = pathIds.slice(0, -1)\r\n\r\n            // 先设置选中节点\r\n            this.selectedNode = nodeToSelect\r\n\r\n            // 强制更新树组件的选中状态\r\n            this.$nextTick(() => {\r\n              this.$nextTick(() => {\r\n                // 模拟点击节点来强制更新选中状态\r\n                this.handleSceneNodeClick(nodeToSelect)\r\n              })\r\n            })\r\n          }\r\n        }\r\n        \r\n        console.log('提交内容:', submitData)\r\n      } catch (error) {\r\n        console.error('提交失败:', error)\r\n        this.$message.error('提交失败')\r\n      } finally {\r\n        this.submitting = false\r\n      }\r\n    },\r\n    addVideoSegment() {\r\n      // 如果原数组为空，先初始化\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        this.videoExplanation.videoSegmentedVoList = [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      this.videoExplanation.videoSegmentedVoList.push({ time: '', sceneId: '', sceneName: '', sceneCode: '' })\r\n    },\r\n    removeVideoSegment(idx) {\r\n      this.videoExplanation.videoSegmentedVoList.splice(idx, 1)\r\n    },\r\n    //递归重构结构\r\n    getDeepTreeOptions(tree) {\r\n    return tree.map(item => {\r\n      // 复制当前节点的基础属性\r\n      const node = { ...item };\r\n      \r\n      // 如果存在 children 且不为空，则递归处理\r\n      if (node.children && node.children.length > 0) {\r\n        node.children = this.getDeepTreeOptions(node.children);\r\n      } else {\r\n        // 当 children 为空或不存在时，删除 children 属性（可选）\r\n        delete node.children;\r\n      }\r\n      \r\n      return node;\r\n    });\r\n  },\r\n    async loadSceneTreeOptions(id) {\r\n      try {\r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        const res = await getSceneTreeList({ industryCode: industryCode })\r\n        if (res.code === 0 && Array.isArray(res.data)) {\r\n          this.sceneTreeOptions = this.getDeepTreeOptions(res.data)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载场景树失败:', error)\r\n      }\r\n    },\r\n    handleSceneCascaderChange(val, idx) {\r\n      // 确保数组和索引位置的对象存在\r\n      if (!this.videoExplanation.videoSegmentedVoList || !this.videoExplanation.videoSegmentedVoList[idx]) {\r\n        return\r\n      }\r\n      \r\n      const findScene = (tree, id) => {\r\n        for (const node of tree) {\r\n          if (node.id === id) return node\r\n          if (node.children && node.children.length) {\r\n            const found = findScene(node.children, id)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      const node = findScene(this.sceneTreeOptions, val)\r\n      if (node) {\r\n        // 设置场景ID和相关信息\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneId = val\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneName = node.sceneName\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneCode = node.sceneCode\r\n      }\r\n    },\r\n    isSceneDisabled(id, currentIdx) {\r\n      // 除当前分段外，其他分段已选的id\r\n      return this.videoExplanation.videoSegmentedVoList.some((seg, idx) => idx !== currentIdx && seg.sceneId === id)\r\n    },\r\n    // 将场景树转换为接口格式\r\n    convertSceneTreeToApi(sceneTree) {\r\n      console.log(\"提交的数据:\", sceneTree);\r\n      return sceneTree.map(node => ({\r\n        sceneInfoId: node.sceneInfoId || null,\r\n        sceneId: node.id,\r\n        paramId: node.parent ? node.parent.id : null,\r\n        sceneName: node.name,\r\n        sceneCode: node.code,\r\n        x: node.x || null,\r\n        y: node.y || null,\r\n        type: node.type || null,\r\n        status: node.status,\r\n        isUnfold: (node.children && node.children.length > 0) ? (node.isUnfold || '1') : null,\r\n        displayLocation: (node.children && node.children.length > 0) ? (node.displayLocation || '0') : null,\r\n        treeClassification: (node.children && node.children.length > 0) ? (node.treeClassification || '3') : null,\r\n        introduceVideoVo: node.introduceVideoVo ? {\r\n          id: node.introduceVideoVo.id || null,\r\n          type: node.introduceVideoVo.type || null,\r\n          viewInfoId: node.introduceVideoVo.viewInfoId || null,\r\n          status: node.introduceVideoVo.status || null,\r\n          backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || null,\r\n          backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || null\r\n        } : null,\r\n        sceneTraditionVo: node.tradition ? {\r\n          name: node.tradition.name || null,\r\n          panoramicViewXmlKey: node.tradition.panoramicViewXmlKey || null,\r\n          sceneVideoList: node.tradition.backgroundResources && node.tradition.backgroundResources.length ? \r\n            node.tradition.backgroundResources.map(resource => ({\r\n              id: resource.id || null,\r\n              tag: resource.label || null,\r\n              wide: resource.wide || null,\r\n              high: resource.high || null,\r\n              status: resource.status || null,\r\n              type: 1,\r\n              viewInfoId: resource.viewInfoId || null,\r\n              backgroundImgFileUrl: resource.bgImg || '',\r\n              backgroundFileUrl: resource.bgFile || '',\r\n              sceneFileRelList: resource.coordinates && resource.coordinates.length ? \r\n                resource.coordinates.map(coord => ({\r\n                  id: coord.id || null,\r\n                  fileId: coord.fileId || null,\r\n                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,\r\n                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,\r\n                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,\r\n                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,\r\n                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,\r\n                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null\r\n                })) : []\r\n            })) : null,\r\n          painPointList: node.tradition.painPoints && node.tradition.painPoints.length ? \r\n            node.tradition.painPoints.map(pain => ({\r\n              painPointId: pain.painPointId || null,\r\n              bigTitle: pain.title || null,\r\n              content: pain.contents || [],\r\n              displayTime: pain.showTime || null\r\n            })) : null\r\n        } : null,\r\n        scene5gVo: node.wisdom5g ? {\r\n          name: node.wisdom5g.name || null,\r\n          panoramicViewXmlKey: node.wisdom5g.panoramicViewXmlKey || null,\r\n          sceneVideoList: node.wisdom5g.backgroundResources && node.wisdom5g.backgroundResources.length ? \r\n            node.wisdom5g.backgroundResources.map(resource => ({\r\n              id: resource.id || null,\r\n              tag: resource.tag || null,\r\n              status: resource.status || null,\r\n              type: 2,\r\n              viewInfoId: resource.viewInfoId || null,\r\n              backgroundImgFileUrl: resource.bgImg || '',\r\n              backgroundFileUrl: resource.bgFile || '',\r\n              sceneFileRelList: resource.coordinates && resource.coordinates.length ? \r\n                resource.coordinates.map(coord => ({\r\n                  id: coord.id || null,\r\n                  fileId: coord.fileId || null,\r\n                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,\r\n                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,\r\n                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,\r\n                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,\r\n                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,\r\n                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null\r\n                })) : []\r\n            })) : null,\r\n          painPointList: node.wisdom5g.painPoints && node.wisdom5g.painPoints.length ? \r\n            node.wisdom5g.painPoints.map(pain => ({\r\n              painPointId: pain.painPointId || null,\r\n              bigTitle: pain.title || null,\r\n              content: pain.contents || [],\r\n              displayTime: pain.showTime || null\r\n            })) : null\r\n        } : null,\r\n        costEstimationInfoVo: node.costEstimate ? {\r\n          painPointId: node.costEstimate.painPointId || null,\r\n          status: node.costEstimate.status || '0',\r\n          bigTitle: node.costEstimate.title || null,\r\n          content: node.costEstimate.contents && node.costEstimate.contents.length ? node.costEstimate.contents : null\r\n        } : null,\r\n        children: node.children && node.children.length ? this.convertSceneTreeToApi(node.children) : []\r\n      }))\r\n    },\r\n    handleTimeChange(val, idx) {\r\n      // 确保数组已初始化\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        this.videoExplanation.videoSegmentedVoList = [{ time: 0, sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      // 更新对应位置的时间值\r\n      if (this.videoExplanation.videoSegmentedVoList[idx]) {\r\n        this.videoExplanation.videoSegmentedVoList[idx].time = val\r\n      }\r\n    },\r\n    // 根据sceneCode查找对应的sceneId\r\n    findSceneIdByCode(sceneCode) {\r\n      if (!sceneCode || !this.sceneTreeOptions) return ''\r\n      \r\n      const findInTree = (tree) => {\r\n        for (const node of tree) {\r\n          if (node.sceneCode === sceneCode) {\r\n            return node.id\r\n          }\r\n          if (node.children && node.children.length) {\r\n            const found = findInTree(node.children)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      \r\n      return findInTree(this.sceneTreeOptions) || ''\r\n    },\r\n    // 处理背景文件删除\r\n    handleRemoveBgFile(file, fileList) {\r\n      this.form.bgFileUrl = ''\r\n      this.form.bgImgUrl = '' // 同时清空背景图片首帧\r\n      this.bgFileList = []\r\n      this.$message.success('文件已删除')\r\n    },\r\n    // 更新背景文件列表\r\n    updateBgFileList() {\r\n      if (this.form.bgFileUrl) {\r\n        const fileName = this.form.bgFileUrl.split('/').pop()\r\n        this.bgFileList = [{\r\n          name: fileName,\r\n          url: this.form.bgFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.bgFileList = []\r\n      }\r\n    },\r\n    // 处理介绍视频文件删除\r\n    handleRemoveIntroduceVideoFile(file, fileList) {\r\n      this.introduceVideo.backgroundFileUrl = ''\r\n      this.introduceVideo.backgroundImgFileUrl = '' // 同时清空首帧图片\r\n      this.introduceVideoFileList = []\r\n      this.$message.success('介绍视频已删除')\r\n    },\r\n    // 更新介绍视频文件列表\r\n    updateIntroduceVideoFileList() {\r\n      if (this.introduceVideo.backgroundFileUrl) {\r\n        const fileName = this.introduceVideo.backgroundFileUrl.split('/').pop()\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: this.introduceVideo.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.introduceVideoFileList = []\r\n      }\r\n    },\r\n    // 处理讲解视频文件删除\r\n    handleRemoveVideoExplanationFile(file, fileList) {\r\n      this.videoExplanation.backgroundFileUrl = ''\r\n      this.videoExplanationFileList = []\r\n      this.$message.success('讲解视频已删除')\r\n    },\r\n    // 更新讲解视频文件列表\r\n    updateVideoExplanationFileList() {\r\n      if (this.videoExplanation.backgroundFileUrl) {\r\n        const fileName = this.videoExplanation.backgroundFileUrl.split('/').pop()\r\n        this.videoExplanationFileList = [{\r\n          name: fileName,\r\n          url: this.videoExplanation.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.videoExplanationFileList = []\r\n      }\r\n    },\r\n    // 处理XML文件删除\r\n    handleRemoveXmlFile(file, fileList) {\r\n      this.form.panoramicViewXmlUrl = ''\r\n      this.xmlFileList = []\r\n      this.$message.success('XML文件已删除')\r\n    },\r\n    \r\n    // 更新XML文件列表\r\n    updateXmlFileList() {\r\n      if (this.form.panoramicViewXmlUrl) {\r\n        const fileName = this.form.panoramicViewXmlUrl.split('/').pop()\r\n        this.xmlFileList = [{\r\n          name: fileName,\r\n          url: this.form.panoramicViewXmlUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.xmlFileList = []\r\n      }\r\n    },\r\n    // 图片预览\r\n    previewImage(url) {\r\n      if (url) {\r\n        this.previewImageUrl = url\r\n        this.previewVisible = true\r\n      }\r\n    },\r\n    closePreview() {\r\n      this.previewVisible = false\r\n      this.previewImageUrl = ''\r\n    },\r\n    // 删除背景图片\r\n    deleteBgImage() {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.bgImgUrl = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    async beforeUploadXmlFile(file) {\r\n      // 检查文件类型\r\n      if (!file.name.toLowerCase().endsWith('.xml')) {\r\n        this.$message.error('只能上传XML文件！')\r\n        return false\r\n      }\r\n      \r\n      // 检查文件大小（50MB）\r\n      if (file.size > 50 * 1024 * 1024) {\r\n        this.$message.error('文件大小不能超过50MB！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传XML文件，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 设置XML文件URL\r\n          this.form.panoramicViewXmlUrl = res.data.fileUrl\r\n          \r\n          // 直接覆盖XML文件列表\r\n          const fileName = res.data.fileUrl.split('/').pop()\r\n          this.xmlFileList = [{\r\n            name: fileName,\r\n            url: res.data.fileUrl,\r\n            uid: Date.now()\r\n          }]\r\n          \r\n          this.$message.success('上传成功')\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    // 主题变更回调\r\n    onThemeChange(theme) {\r\n      // 如果主题有默认背景图，可以自动设置\r\n      if (theme && theme.defaultBgImage) {\r\n        this.form.bgImgUrl = theme.defaultBgImage\r\n      }\r\n    },\r\n    // 格式化坐标数据用于提交\r\n    formatCoordinatesForSubmit(coordinates) {\r\n      if (!coordinates || !Array.isArray(coordinates)) {\r\n        return { clickX: '', clickY: '' }\r\n      }\r\n      \r\n      const xValues = coordinates.map(coord => coord.x || '0').join(',')\r\n      const yValues = coordinates.map(coord => coord.y || '0').join(',')\r\n      \r\n      return {\r\n        clickX: xValues,\r\n        clickY: yValues\r\n      }\r\n    },\r\n    // 解析坐标字符串为坐标数组\r\n    parseCoordinatesFromApi(clickX, clickY) {\r\n      const xArray = clickX ? clickX.split(',') : ['']\r\n      const yArray = clickY ? clickY.split(',') : ['']\r\n      \r\n      // 取较长的数组长度，确保坐标对齐\r\n      const maxLength = Math.max(xArray.length, yArray.length)\r\n      const coordinates = []\r\n      \r\n      for (let i = 0; i < maxLength; i++) {\r\n        coordinates.push({\r\n          x: xArray[i] || '',\r\n          y: yArray[i] || ''\r\n        })\r\n      }\r\n      \r\n      // 至少保证有一个坐标组\r\n      return coordinates.length > 0 ? coordinates : [{ x: '', y: '' }]\r\n    },\r\n    // 开始编辑标题\r\n    startEditTitle(index) {\r\n      const category = this.categories[index]\r\n      category.editing = true\r\n      category.editingName = category.name\r\n      \r\n      // 下一帧聚焦输入框\r\n      this.$nextTick(() => {\r\n        // 使用动态ref名称\r\n        const inputRef = this.$refs[`titleInput_${index}`]\r\n        if (inputRef && inputRef[0]) {\r\n          inputRef[0].focus()\r\n          inputRef[0].select()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 完成编辑标题\r\n    finishEditTitle(index) {\r\n      const category = this.categories[index]\r\n      if (category.editingName && category.editingName.trim()) {\r\n        category.name = category.editingName.trim()\r\n      }\r\n      category.editing = false\r\n      category.editingName = ''\r\n    },\r\n\r\n    // 取消编辑标题\r\n    cancelEditTitle(index) {\r\n      const category = this.categories[index]\r\n      category.editing = false\r\n      category.editingName = ''\r\n    },\r\n    // 设置上传模式\r\n    setUploadMode(type, mode) {\r\n      this.$set(this.uploadModes, type, mode)\r\n    },\r\n    // 背景文件链接输入处理\r\n    handleBgFileUrlInput(value) {\r\n      this.bgFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.bgFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 视频讲解链接输入处理\r\n    handleVideoExplanationUrlInput(value) {\r\n      this.videoExplanationFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.videoExplanationFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 介绍视频链接输入处理\r\n    handleIntroduceVideoUrlInput(value) {\r\n      this.introduceVideoFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 同步文件\r\n    async handleSynchronizeFile() {\r\n      if (!this.form.sceneViewConfigId) {\r\n        this.$message.warning('请先保存配置后再同步文件')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.synchronizing = true\r\n        this.$modal.loading(\"正在同步文件，请稍候...\")\r\n        \r\n        // 使用FormData或URLSearchParams传递表单参数\r\n        const formData = new FormData()\r\n        formData.append('viewConfigId', this.form.sceneViewConfigId)\r\n        \r\n        const res = await synchronizationFile(formData)\r\n        \r\n        if (res.code === 0) {\r\n          this.$message.success(res.msg)\r\n        } else {\r\n          this.$message.error(res.msg || '文件同步失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('同步文件失败:', error)\r\n        this.$message.error('文件同步失败')\r\n      } finally {\r\n        this.synchronizing = false\r\n        this.$modal.closeLoading()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.page-container {\r\n  display: flex;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.menu-panel {\r\n  width: 250px;\r\n  background-color: #f5f7fa;\r\n  border-right: 1px solid #e4e7ed;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n  background-color: #f5f7fa;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.menu-tree {\r\n  background-color: #f5f7fa;\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 8px 0;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content:hover {\r\n  background-color: #e6f7ff;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.menu-tree::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-track {\r\n  background-color: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-thumb {\r\n  background-color: #c0c0c0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-thumb:hover {\r\n  background-color: #a0a0a0;\r\n}\r\n\r\n.content-panel {\r\n  flex: 1;\r\n  padding: 20px 20px 80px 20px;\r\n  overflow-y: auto;\r\n  background-color: #fff;\r\n  position: relative;\r\n}\r\n.mini-block {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.category-block {\r\n  margin-top: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.category-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.category-title:hover {\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n.category-title span {\r\n  display: inline-block;\r\n  min-width: 100px;\r\n}\r\n\r\n.category-body {\r\n  padding: 12px;\r\n  background: #ffffff;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.sub-category-block {\r\n  margin-bottom: 15px;\r\n}\r\n.sub-category-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.sub-category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px;\r\n  background-color: #fafafa;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.sub-category-title {\r\n  font-weight: 500;\r\n}\r\n\r\n.sub-category-body {\r\n  padding: 15px;\r\n}\r\n\r\n.segment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.pain-point-block {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.pain-point-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mini-block {\r\n  margin-bottom: 20px;\r\n  min-height: 450px; /* 设置统一的最小高度 */\r\n}\r\n\r\n.mini-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 确保卡片内容区域也有合适的高度 */\r\n.mini-block .el-card__body {\r\n  min-height: 450px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 视频卡片保持原有样式 */\r\n.video-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 450px;\r\n}\r\n\r\n.video-card .el-card__body {\r\n  flex: 1;\r\n  overflow: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 450px;\r\n}\r\n.video-card-yu{\r\n  min-height: 300px;\r\n}\r\n.video-card .el-card__body {\r\n  flex: 1;\r\n  overflow: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.segment-scroll {\r\n  max-height: 150px;\r\n  overflow-y: auto;\r\n  border: 1px solid #eee;\r\n  border-radius: 4px;\r\n  padding: 8px;\r\n  background: #fafbfc;\r\n}\r\n.scene-config-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 限制上传图片的显示大小 */\r\n.image-upload .el-upload--picture-card {\r\n  width: 148px;\r\n  height: 148px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.upload-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 介绍视频首帧图片大小控制 */\r\n.image-upload .el-upload-list__item-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 上传框也添加圆角 */\r\n.image-upload .el-upload--picture-card {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 图片预览样式 */\r\n.preview-container {\r\n  text-align: center;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 70vh;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 图片悬停操作样式 */\r\n.image-preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  border-radius: 6px;\r\n}\r\n\r\n.image-preview-container:hover .image-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.preview-icon,\r\n.delete-icon {\r\n  color: white;\r\n  font-size: 20px;\r\n  margin: 0 10px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.preview-icon:hover,\r\n.delete-icon:hover {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.submit-footer {\r\n  position: fixed;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 250px;\r\n  height: 60px;\r\n  background: #fff;\r\n  border-top: 1px solid #e4e7ed;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  padding: 0 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n}\r\n\r\n.submit-footer .el-button {\r\n  min-width: 100px;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.menu-search .el-input {\r\n  border-radius: 20px;\r\n}\r\n\r\n.menu-search .el-input__inner {\r\n  border-radius: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\n.highlight {\r\n  background-color: #ffeb3b;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.menu-tree {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content:hover {\r\n  background-color: #e6f7ff;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.custom-tree-node {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.highlight {\r\n  background-color: yellow;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n  justify-content: flex-end;\r\n  padding: 0 20px;\r\n}\r\n</style>\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n}\r\n\r\n.submit-footer .el-button {\r\n  min-width: 100px;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.menu-search .el-input {\r\n  border-radius: 20px;\r\n}\r\n\r\n.menu-search .el-input__inner {\r\n  border-radius: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\n.highlight {\r\n  background-color: #ffeb3b;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.menu-tree {\r\n  background\r\n"]}]}