{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754894183874}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRJbmR1c3RyeUxpc3QsIGdldFNjZW5lVHJlZUxpc3QgfSBmcm9tICdAL2FwaS92aWV3L2luZHVzdHJ5Jw0KaW1wb3J0IFNjZW5lQ29uZmlnTm9kZSBmcm9tICcuL1NjZW5lQ29uZmlnTm9kZS52dWUnDQppbXBvcnQgeyBnZXRTY2VuZVZpZXdDb25maWcsIHNjZW5lVmlld1VwZCwgdXBsb2FkU2NlbmVGaWxlLCBzeW5jaHJvbml6YXRpb25GaWxlIH0gZnJvbSAnQC9hcGkvdmlldy9zY2VuZVZpZXcnDQppbXBvcnQgTmV0d29ya1BsYW5Db25maWcgZnJvbSAnLi9OZXR3b3JrUGxhbkNvbmZpZy52dWUnDQppbXBvcnQgQnVzaW5lc3NWYWx1ZUNvbmZpZyBmcm9tICcuL0J1c2luZXNzVmFsdWVDb25maWcudnVlJw0KaW1wb3J0IFZyU2NlbmVDb25maWcgZnJvbSAnLi9WclNjZW5lQ29uZmlnLnZ1ZScNCmltcG9ydCBUaGVtZVNlbGVjdGlvbkRpYWxvZyBmcm9tICcuL1RoZW1lU2VsZWN0aW9uRGlhbG9nLnZ1ZScNCmltcG9ydCBheGlvcyBmcm9tICdheGlvcycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnSW5kdXN0cnlTY2VuZVBhZ2UnLA0KICBjb21wb25lbnRzOiB7DQogICAgU2NlbmVDb25maWdOb2RlLA0KICAgIE5ldHdvcmtQbGFuQ29uZmlnLA0KICAgIEJ1c2luZXNzVmFsdWVDb25maWcsDQogICAgVnJTY2VuZUNvbmZpZywNCiAgICBUaGVtZVNlbGVjdGlvbkRpYWxvZw0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBtZW51RGF0YTogW10sIC8vIOWOn+Wni+iPnOWNleaVsOaNrg0KICAgICAgZmxhdE1lbnVEYXRhOiBbXSwgLy8g5omB5bmz5YyW55qE6KGM5Lia5pWw5o2u77yM55So5LqO5pCc57Si5ZKM5Lia5Yqh6YC76L6RDQogICAgICBhY3RpdmVNZW51OiAnJywNCiAgICAgIGluZHVzdHJ5Q29kZTogJycsDQogICAgICBzZWxlY3RlZFRoZW1lOiBudWxsLCAvLyDlvZPliY3pgInmi6nnmoTkuLvpopgNCiAgICAgIGZvcm06IHsNCiAgICAgICAgbWFpblRpdGxlOiAnJywNCiAgICAgICAgc3ViVGl0bGU6ICcnLA0KICAgICAgICBiZ0ltZ1VybDogJycsDQogICAgICAgIGJnRmlsZVVybDogJycsDQogICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxVcmw6ICcnDQogICAgICB9LA0KICAgICAgc2NlbmVDb25maWdUcmVlOiBbXSwNCiAgICAgIHNlbGVjdGVkTm9kZTogbnVsbCwNCiAgICAgIGxvYWRpbmc6IGZhbHNlLCAvLyDpobXpnaLliqDovb3nirbmgIENCiAgICAgIHN3aXRjaGluZ0luZHVzdHJ5OiBmYWxzZSwgLy8g5paw5aKe77ya5YiH5o2i6KGM5Lia55qEbG9hZGluZ+eKtuaAgQ0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgaW50cm9kdWNlVmlkZW9JbWdVcmw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35LiK5Lyg5LuL57uN6KeG6aKR6aaW5binJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICBpbnRyb2R1Y2VWaWRlb0ZpbGVVcmw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35LiK5Lyg5LuL57uN6KeG6aKRJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICB2aWRlb0V4cGxhbmF0aW9uRmlsZVVybDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fkuIrkvKDorrLop6Pop4bpopEnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICB1cGxvYWRpbmdUeXBlOiAnJywNCiAgICAgIHVwbG9hZGluZ0tleTogJycsDQogICAgICBjYXRlZ29yaWVzOiBbXSwgLy8g5pS55Li656m65pWw57uE77yM5LuO5ZCO56uv5Yqo5oCB6I635Y+WDQogICAgICBpbnRyb2R1Y2VWaWRlbzogew0KICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6ICcnLA0KICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycNCiAgICAgIH0sDQogICAgICB2aWRlb0V4cGxhbmF0aW9uOiB7DQogICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycsDQogICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHNjZW5lVHJlZU9wdGlvbnM6IFtdLA0KICAgICAgc2NlbmVDYXNjYWRlclByb3BzOiB7DQogICAgICAgIGxhYmVsOiAnc2NlbmVOYW1lJywNCiAgICAgICAgdmFsdWU6ICdpZCcsDQogICAgICAgIGNoaWxkcmVuOiAnY2hpbGRyZW4nLA0KICAgICAgICBlbWl0UGF0aDogZmFsc2UsDQogICAgICAgIGNoZWNrU3RyaWN0bHk6IHRydWUsDQogICAgICAgIGRpc2FibGVkOiAoZGF0YSkgPT4gew0KICAgICAgICAgIC8vIOWFgeiuuOaJgOacieiKgueCueWPr+mAie+8jOWPquimgeayoeacieiiq+WFtuS7luWIhuautemAieS4rQ0KICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSB0aGlzLnZpZGVvRXhwbGFuYXRpb24gJiYgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0DQogICAgICAgICAgICA/IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5zb21lKHNlZyA9PiBzZWcuc2NlbmVJZCA9PT0gZGF0YS5pZCkNCiAgICAgICAgICAgIDogZmFsc2UNCiAgICAgICAgICByZXR1cm4gaXNTZWxlY3RlZA0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgYmdGaWxlTGlzdDogW10sIC8vIOiDjOaZr+aWh+S7tuWIl+ihqA0KICAgICAgdmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0OiBbXSwgLy8g6K6y6Kej6KeG6aKR5paH5Lu25YiX6KGoDQogICAgICB4bWxGaWxlTGlzdDogW10sIC8vIFhNTOaWh+S7tuWIl+ihqA0KICAgICAgbmV0d29ya1BsYW5EYXRhTWFwOiB7fSwgLy8g5pS55Li65a+56LGh77yM5oyJ6I+c5Y2VSUTlrZjlgqgNCiAgICAgIGJ1c2luZXNzVmFsdWVEYXRhTWFwOiB7fSwgLy8g5ZWG5Lia5Lu35YC85pWw5o2u5pig5bCEDQogICAgICB2clNjZW5lRGF0YU1hcDoge30sIC8vIFZS55yL546w5Zy65pWw5o2u5pig5bCEDQogICAgICAvLyDlm77niYfpooTop4gNCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHByZXZpZXdJbWFnZVVybDogJycsDQogICAgICBzZWFyY2hLZXl3b3JkOiAnJywNCiAgICAgIHRyZWVFeHBhbmRlZEtleXM6IFtdLCAvLyDmlrDlop7vvJrkv53lrZjmoJHnmoTlsZXlvIDnirbmgIENCiAgICAgIHVwbG9hZE1vZGVzOiB7DQogICAgICAgIGJnRmlsZTogJ3VwbG9hZCcsDQogICAgICAgIHZpZGVvRXhwbGFuYXRpb246ICd1cGxvYWQnLA0KICAgICAgICBpbnRyb2R1Y2VWaWRlbzogJ3VwbG9hZCcNCiAgICAgIH0sDQogICAgICBzeW5jaHJvbml6aW5nOiBmYWxzZSwNCiAgICAgIHN1Ym1pdHRpbmc6IGZhbHNlIC8vIOa3u+WKoOi/meS4quWxnuaApw0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICB2aWRlb1NlZ21lbnRlZExpc3QoKSB7DQogICAgICAvLyDlpoLmnpzmsqHmnInmlbDmja7vvIzpu5jorqTov5Tlm57kuIDooYznqbrmlbDmja4NCiAgICAgIGlmICghdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0IHx8IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIFt7IHRpbWU6ICcnLCBzY2VuZUlkOiAnJywgc2NlbmVOYW1lOiAnJywgc2NlbmVDb2RlOiAnJyB9XQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdA0KICAgIH0sDQogICAgbmV0d29ya1BsYW5EYXRhOiB7DQogICAgICBnZXQoKSB7DQogICAgICAgIGNvbnN0IG1lbnVEYXRhID0gdGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XQ0KICAgICAgICBpZiAoIW1lbnVEYXRhKSB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIG5ldHdvcmtWaWRlb0xpc3Q6IFtdLA0KICAgICAgICAgICAgdmlkZW9FeHBsYW5hdGlvblZvOiB7DQogICAgICAgICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycsDQogICAgICAgICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiBbXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gbWVudURhdGENCiAgICAgIH0sDQogICAgICBzZXQodmFsdWUpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubmV0d29ya1BsYW5EYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIHZhbHVlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYnVzaW5lc3NWYWx1ZURhdGE6IHsNCiAgICAgIGdldCgpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuYnVzaW5lc3NWYWx1ZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XSB8fCBbXQ0KICAgICAgfSwNCiAgICAgIHNldCh2YWx1ZSkgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5idXNpbmVzc1ZhbHVlRGF0YU1hcCwgdGhpcy5hY3RpdmVNZW51LCB2YWx1ZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIHZyU2NlbmVEYXRhOiB7DQogICAgICBnZXQoKSB7DQogICAgICAgIHJldHVybiB0aGlzLnZyU2NlbmVEYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0gfHwgW10NCiAgICAgIH0sDQogICAgICBzZXQodmFsKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLnZyU2NlbmVEYXRhTWFwLCB0aGlzLmFjdGl2ZU1lbnUsIHZhbCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGZpbHRlcmVkTWVudURhdGEoKSB7DQogICAgICBpZiAoIXRoaXMuc2VhcmNoS2V5d29yZCkgew0KICAgICAgICByZXR1cm4gdGhpcy5tZW51RGF0YQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDpgJLlvZLov4fmu6TmoJHlvaLmlbDmja4NCiAgICAgIGNvbnN0IGZpbHRlclRyZWUgPSAobm9kZXMpID0+IHsNCiAgICAgICAgcmV0dXJuIG5vZGVzLm1hcChub2RlID0+IHsNCiAgICAgICAgICBjb25zdCBmaWx0ZXJlZENoaWxkcmVuID0gbm9kZS5jaGlsZHJlbiA/IGZpbHRlclRyZWUobm9kZS5jaGlsZHJlbikgOiBbXQ0KICAgICAgICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBub2RlLm5hbWUgJiYgbm9kZS5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXModGhpcy5zZWFyY2hLZXl3b3JkLnRvTG93ZXJDYXNlKCkpDQogICAgICAgICAgDQogICAgICAgICAgaWYgKG1hdGNoZXNTZWFyY2ggfHwgZmlsdGVyZWRDaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAuLi5ub2RlLA0KICAgICAgICAgICAgICBjaGlsZHJlbjogZmlsdGVyZWRDaGlsZHJlbg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gbnVsbA0KICAgICAgICB9KS5maWx0ZXIoQm9vbGVhbikNCiAgICAgIH0NCiAgICAgIA0KICAgICAgcmV0dXJuIGZpbHRlclRyZWUodGhpcy5tZW51RGF0YSkNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8g5LuOVVJM6I635Y+WdG9rZW7lubborr7nva4NCiAgICB0aGlzLmluaXRUb2tlbkZyb21VcmwoKQ0KICAgIHRoaXMubG9hZEluZHVzdHJ5TWVudSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDku45VUkzojrflj5Z0b2tlbuW5tuiuvue9rg0KICAgIGluaXRUb2tlbkZyb21VcmwoKSB7DQogICAgICBjb25zdCB1cmxQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHdpbmRvdy5sb2NhdGlvbi5zZWFyY2gpDQogIGNvbnN0IHRva2VuID0gdXJsUGFyYW1zLmdldCgndG9rZW4nKQ0KICANCiAgaWYgKHRva2VuKSB7DQogICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2V4dGVybmFsLXRva2VuJywgdG9rZW4pDQogICAgYXhpb3MuZGVmYXVsdHMuaGVhZGVycy5jb21tb25bJ0F1dGhvcml6YXRpb24nXSA9IGBCZWFyZXIgJHt0b2tlbn1gDQogICAgY29uc29sZS5sb2coJ+S7jlVSTOiOt+WPluWIsHRva2VuOicsIHRva2VuKQ0KICB9IGVsc2Ugew0KICAgIGNvbnN0IHN0b3JlZFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2V4dGVybmFsLXRva2VuJykNCiAgICBpZiAoc3RvcmVkVG9rZW4pIHsNCiAgICAgIGF4aW9zLmRlZmF1bHRzLmhlYWRlcnMuY29tbW9uWydBdXRob3JpemF0aW9uJ10gPSBgQmVhcmVyICR7c3RvcmVkVG9rZW59YA0KICAgIH0NCiAgfQ0KICAgIH0sDQogICAgYXN5bmMgbG9hZEluZHVzdHJ5TWVudSgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldEluZHVzdHJ5TGlzdCgpDQogICAgICBpZiAocmVzLmNvZGUgPT09IDAgJiYgQXJyYXkuaXNBcnJheShyZXMuZGF0YSkpIHsNCiAgICAgICAgLy8g6L2s5o2i5pWw5o2u57uT5p6E5Li65qCR5b2i6I+c5Y2VDQogICAgICAgIHRoaXMubWVudURhdGEgPSByZXMuZGF0YS5tYXAocGxhdGUgPT4gKHsNCiAgICAgICAgICBpZDogYHBsYXRlXyR7cGxhdGUucGxhdGVLZXl9YCwNCiAgICAgICAgICBuYW1lOiBwbGF0ZS5wbGF0ZU5hbWUsDQogICAgICAgICAgdHlwZTogJ3BsYXRlJywNCiAgICAgICAgICBjaGlsZHJlbjogcGxhdGUuaW5kdXN0cnlUcmVlTGlzdFZvcyA/IHBsYXRlLmluZHVzdHJ5VHJlZUxpc3RWb3MubWFwKGluZHVzdHJ5ID0+ICh7DQogICAgICAgICAgICBpZDogaW5kdXN0cnkuaWQsDQogICAgICAgICAgICBuYW1lOiBpbmR1c3RyeS5pbmR1c3RyeU5hbWUsDQogICAgICAgICAgICBpbmR1c3RyeUNvZGU6IGluZHVzdHJ5LmluZHVzdHJ5Q29kZSwNCiAgICAgICAgICAgIHBsYXRlOiBpbmR1c3RyeS5wbGF0ZSwNCiAgICAgICAgICAgIHR5cGU6ICdpbmR1c3RyeScNCiAgICAgICAgICB9KSkgOiBbXQ0KICAgICAgICB9KSkNCiAgICAgICAgDQogICAgICAgIC8vIOWIm+W7uuaJgeW5s+WMlueahOihjOS4muaVsOaNru+8jOeUqOS6juS4muWKoemAu+i+kQ0KICAgICAgICB0aGlzLmZsYXRNZW51RGF0YSA9IFtdDQogICAgICAgIHJlcy5kYXRhLmZvckVhY2gocGxhdGUgPT4gew0KICAgICAgICAgIGlmIChwbGF0ZS5pbmR1c3RyeVRyZWVMaXN0Vm9zKSB7DQogICAgICAgICAgICB0aGlzLmZsYXRNZW51RGF0YS5wdXNoKC4uLnBsYXRlLmluZHVzdHJ5VHJlZUxpc3RWb3MpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICANCiAgICAgICAgLy8g6buY6K6k6YCJ5Lit56ys5LiA5Liq6KGM5LiaDQogICAgICAgIGlmICh0aGlzLmZsYXRNZW51RGF0YS5sZW5ndGgpIHsNCiAgICAgICAgICB0aGlzLmFjdGl2ZU1lbnUgPSBTdHJpbmcodGhpcy5mbGF0TWVudURhdGFbMF0uaWQpDQogICAgICAgICAgdGhpcy5pbmR1c3RyeUNvZGUgPSB0aGlzLmZsYXRNZW51RGF0YVswXS5pbmR1c3RyeUNvZGUNCiAgICAgICAgICAvLyDnrYnlvoVET03mm7TmlrDlkI7orr7nva7moJHnu4Tku7bnmoTlvZPliY3oioLngrkNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAvLyDnoa7kv53moJHnu4Tku7blt7LmuLLmn5Plubborr7nva7lvZPliY3pgInkuK3oioLngrkNCiAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLm1lbnVUcmVlICYmIHRoaXMuJHJlZnMubWVudVRyZWUuc2V0Q3VycmVudEtleSkgew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLm1lbnVUcmVlLnNldEN1cnJlbnRLZXkodGhpcy5hY3RpdmVNZW51KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgDQogICAgICAgICAgLy8g5Yqg6L2956ys5LiA5Liq6KGM5Lia55qE5pWw5o2uDQogICAgICAgICAgYXdhaXQgdGhpcy5oYW5kbGVTZWxlY3QodGhpcy5hY3RpdmVNZW51KQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICBoYW5kbGVUcmVlTm9kZUNsaWNrKGRhdGEpIHsNCiAgICAgIC8vIOWPquacieeCueWHu+ihjOS4muiKgueCueaJjeWkhOeQhg0KICAgICAgaWYgKGRhdGEudHlwZSA9PT0gJ2luZHVzdHJ5Jykgew0KICAgICAgICB0aGlzLmhhbmRsZVNlbGVjdChTdHJpbmcoZGF0YS5pZCkpDQogICAgICAgIHRoaXMuaW5kdXN0cnlDb2RlID0gZGF0YS5pbmR1c3RyeUNvZGU7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZVNlYXJjaCh2YWx1ZSkgew0KICAgICAgdGhpcy5zZWFyY2hLZXl3b3JkID0gdmFsdWUNCiAgICB9LA0KICAgIGhpZ2hsaWdodFRleHQodGV4dCkgew0KICAgICAgaWYgKCF0aGlzLnNlYXJjaEtleXdvcmQpIHJldHVybiB0ZXh0DQogICAgICBjb25zdCByZWdleCA9IG5ldyBSZWdFeHAoYCgke3RoaXMuc2VhcmNoS2V5d29yZH0pYCwgJ2dpJykNCiAgICAgIHJldHVybiB0ZXh0LnJlcGxhY2UocmVnZXgsICc8c3BhbiBjbGFzcz0iaGlnaGxpZ2h0Ij4kMTwvc3Bhbj4nKQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlU2VsZWN0KGlkLCBrZWVwU2VsZWN0ZWROb2RlID0gZmFsc2UsIHNob3dMb2FkaW5nID0gdHJ1ZSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5byA5ZCv5YiH5o2i6KGM5Lia55qEbG9hZGluZw0KICAgICAgICBpZiAoc2hvd0xvYWRpbmcpIHsNCiAgICAgICAgICB0aGlzLnN3aXRjaGluZ0luZHVzdHJ5ID0gdHJ1ZQ0KICAgICAgICAgIC8vIOemgeeUqOmhtemdoua7muWKqA0KICAgICAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnaGlkZGVuJw0KDQogICAgICAgICAgLy8g5Y+z5L6n57yW6L6R5qCP5Zue5Yiw6aG26YOoDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgY29uc3QgY29udGVudFBhbmVsID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmNvbnRlbnQtcGFuZWwnKQ0KICAgICAgICAgICAgaWYgKGNvbnRlbnRQYW5lbCkgew0KICAgICAgICAgICAgICBjb250ZW50UGFuZWwuc2Nyb2xsVG9wID0gMA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIHRoaXMuYWN0aXZlTWVudSA9IGlkDQogICAgICAgIGF3YWl0IHRoaXMubG9hZFNjZW5lVHJlZU9wdGlvbnModGhpcy5hY3RpdmVNZW51KQ0KICAgICAgICANCiAgICAgICAgLy8g5L+d5a2Y5b2T5YmN6YCJ5Lit55qE6IqC54K5DQogICAgICAgIGNvbnN0IGN1cnJlbnRTZWxlY3RlZE5vZGUgPSBrZWVwU2VsZWN0ZWROb2RlID8gdGhpcy5zZWxlY3RlZE5vZGUgOiBudWxsDQogICAgICAgIA0KICAgICAgICAvLyDph43nva7kuLvpopjpgInmi6kNCiAgICAgICAgdGhpcy5zZWxlY3RlZFRoZW1lID0gbnVsbA0KICAgICAgICANCiAgICAgICAgLy8g5LuO5omB5bmz5YyW6I+c5Y2V5pWw5o2u5Lit6I635Y+W5b2T5YmN6KGM5Lia55qEIGluZHVzdHJ5Q29kZQ0KICAgICAgICBjb25zdCBjdXJyZW50SW5kdXN0cnkgPSB0aGlzLmZsYXRNZW51RGF0YS5maW5kKGl0ZW0gPT4gU3RyaW5nKGl0ZW0uaWQpID09PSBpZCkNCiAgICAgICAgY29uc3QgaW5kdXN0cnlDb2RlID0gY3VycmVudEluZHVzdHJ5ID8gY3VycmVudEluZHVzdHJ5LmluZHVzdHJ5Q29kZSA6IG51bGwNCiAgICAgICAgDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldFNjZW5lVmlld0NvbmZpZyh7IGluZHVzdHJ5Q29kZTogaW5kdXN0cnlDb2RlIH0pDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCAmJiByZXMuZGF0YSkgew0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOWQjOatpeS4u+agh+mimOOAgeWJr+agh+mimOOAgeiDjOaZr+WbvueJh+OAgVhNTOaWh+S7tuetiQ0KICAgICAgICAgIHRoaXMuZm9ybS5zY2VuZVZpZXdDb25maWdJZCA9IHJlcy5kYXRhLnNjZW5lVmlld0NvbmZpZ0lkIHx8ICcnDQogICAgICAgICAgdGhpcy5mb3JtLm1haW5UaXRsZSA9IHJlcy5kYXRhLm1haW5UaXRsZSB8fCAnJw0KICAgICAgICAgIHRoaXMuZm9ybS5zdWJUaXRsZSA9IHJlcy5kYXRhLnN1YlRpdGxlIHx8ICcnDQogICAgICAgICAgdGhpcy5mb3JtLmJnSW1nVXJsID0gcmVzLmRhdGEuYmFja2dyb3VuZEltZ0ZpbGVVcmwgfHwgJycNCiAgICAgICAgICB0aGlzLmZvcm0uYmdGaWxlVXJsID0gcmVzLmRhdGEuYmFja2dyb3VuZEZpbGVVcmwgfHwgJycNCiAgICAgICAgICB0aGlzLmZvcm0ucGFub3JhbWljVmlld1htbFVybCA9IHJlcy5kYXRhLnBhbm9yYW1pY1ZpZXdYbWxVcmwgfHwgJycNCiAgICAgICAgICANCiAgICAgICAgICAvLyDmm7TmlrDog4zmma/mlofku7bliJfooagNCiAgICAgICAgICB0aGlzLnVwZGF0ZUJnRmlsZUxpc3QoKQ0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOabtOaWsFhNTOaWh+S7tuWIl+ihqA0KICAgICAgICAgIHRoaXMudXBkYXRlWG1sRmlsZUxpc3QoKQ0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOWbnuaYvuS4u+mimOmAieaLqQ0KICAgICAgICAgIGlmIChyZXMuZGF0YS50aGVtZUluZm9Wbykgew0KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFRoZW1lID0gew0KICAgICAgICAgICAgICB0aGVtZUlkOiByZXMuZGF0YS50aGVtZUluZm9Wby50aGVtZUlkLA0KICAgICAgICAgICAgICB0aGVtZU5hbWU6IHJlcy5kYXRhLnRoZW1lSW5mb1ZvLnRoZW1lTmFtZSwNCiAgICAgICAgICAgICAgdGhlbWVFZmZlY3RJbWc6IHJlcy5kYXRhLnRoZW1lSW5mb1ZvLnRoZW1lRWZmZWN0SW1nLA0KICAgICAgICAgICAgICByZW1hcms6IHJlcy5kYXRhLnRoZW1lSW5mb1ZvLnJlbWFyaw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkVGhlbWUgPSBudWxsDQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOWkhOeQhiBzY2VuZURlZmF1bHRDb25maWdWb0xpc3TvvIzliqjmgIHnlJ/miJAgY2F0ZWdvcmllcw0KICAgICAgICAgIGlmIChyZXMuZGF0YS5zY2VuZURlZmF1bHRDb25maWdWb0xpc3QgJiYgQXJyYXkuaXNBcnJheShyZXMuZGF0YS5zY2VuZURlZmF1bHRDb25maWdWb0xpc3QpKSB7DQogICAgICAgICAgICB0aGlzLmNhdGVnb3JpZXMgPSByZXMuZGF0YS5zY2VuZURlZmF1bHRDb25maWdWb0xpc3QubWFwKGNvbmZpZ0l0ZW0gPT4gKHsNCiAgICAgICAgICAgICAgaWQ6IGNvbmZpZ0l0ZW0uaWQsDQogICAgICAgICAgICAgIGtleTogY29uZmlnSXRlbS5rZXlOYW1lLA0KICAgICAgICAgICAgICBuYW1lOiBjb25maWdJdGVtLm5hbWUsDQogICAgICAgICAgICAgIGVuYWJsZWQ6IGNvbmZpZ0l0ZW0ua2V5VmFsdWUgPT09ICcwJywgLy8ga2V5VmFsdWXkuLonMCfooajnpLrlkK/nlKgNCiAgICAgICAgICAgICAgZWRpdGluZzogZmFsc2UsDQogICAgICAgICAgICAgIGVkaXRpbmdOYW1lOiAnJywNCiAgICAgICAgICAgICAgb3JpZ2luYWxOYW1lOiBjb25maWdJdGVtLm5hbWUsDQogICAgICAgICAgICAgIHJlbWFyazogY29uZmlnSXRlbS5yZW1hcmssDQogICAgICAgICAgICAgIGNsYXNzaWZpY2F0aW9uOiBjb25maWdJdGVtLmNsYXNzaWZpY2F0aW9uLA0KICAgICAgICAgICAgICBkZWZhdWx0U3RhdHVzOiBjb25maWdJdGVtLmRlZmF1bHRTdGF0dXMNCiAgICAgICAgICAgIH0pKQ0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDmn6Xmib7lnLrmma/phY3nva7liIbnsbsNCiAgICAgICAgICAgIGNvbnN0IHNjZW5lQ2F0ZWdvcnkgPSByZXMuZGF0YS5zY2VuZURlZmF1bHRDb25maWdWb0xpc3QuZmluZChpdGVtID0+IGl0ZW0ua2V5TmFtZSA9PT0gJ2RlZmF1bHRfc2NlbmUnKQ0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDlpITnkIbop4bpopHorrLop6PmlbDmja4NCiAgICAgICAgICAgIGlmIChzY2VuZUNhdGVnb3J5ICYmIHNjZW5lQ2F0ZWdvcnkuaW5kdXN0cnlTY2VuZUluZm9WbyAmJiBzY2VuZUNhdGVnb3J5LmluZHVzdHJ5U2NlbmVJbmZvVm8udmlkZW9FeHBsYW5hdGlvblZvKSB7DQogICAgICAgICAgICAgIGNvbnN0IHZpZGVvRGF0YSA9IHNjZW5lQ2F0ZWdvcnkuaW5kdXN0cnlTY2VuZUluZm9Wby52aWRlb0V4cGxhbmF0aW9uVm8NCiAgICAgICAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uID0gew0KICAgICAgICAgICAgICAgIHN0YXR1czogdmlkZW9EYXRhLnN0YXR1cyB8fCAnMCcsDQogICAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHZpZGVvRGF0YS5iYWNrZ3JvdW5kRmlsZVVybCB8fCAnJywNCiAgICAgICAgICAgICAgICB2aWRlb1NlZ21lbnRlZFZvTGlzdDogdmlkZW9EYXRhLnZpZGVvU2VnbWVudGVkVm9MaXN0ID8gdmlkZW9EYXRhLnZpZGVvU2VnbWVudGVkVm9MaXN0Lm1hcChzZWcgPT4gKHsNCiAgICAgICAgICAgICAgICAgIHRpbWU6IHNlZy50aW1lLA0KICAgICAgICAgICAgICAgICAgc2NlbmVDb2RlOiBzZWcuc2NlbmVDb2RlLA0KICAgICAgICAgICAgICAgICAgc2NlbmVOYW1lOiBzZWcuc2NlbmVOYW1lLA0KICAgICAgICAgICAgICAgICAgc2NlbmVJZDogdGhpcy5maW5kU2NlbmVJZEJ5Q29kZShzZWcuc2NlbmVDb2RlKSAvLyDmoLnmja5zY2VuZUNvZGXmn6Xmib5zY2VuZUlkDQogICAgICAgICAgICAgICAgfSkpIDogW10NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uID0gew0KICAgICAgICAgICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiAnJywNCiAgICAgICAgICAgICAgICB2aWRlb1NlZ21lbnRlZFZvTGlzdDogW10NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDmm7TmlrDop4bpopHorrLop6Pmlofku7bliJfooagNCiAgICAgICAgICAgIHRoaXMudXBkYXRlVmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0KCkNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5aSE55CG5Zy65pmv6YWN572u5qCRDQogICAgICAgICAgICBpZiAoc2NlbmVDYXRlZ29yeSAmJiBzY2VuZUNhdGVnb3J5LmluZHVzdHJ5U2NlbmVJbmZvVm8gJiYgc2NlbmVDYXRlZ29yeS5pbmR1c3RyeVNjZW5lSW5mb1ZvLnNjZW5lTGlzdFZvKSB7DQogICAgICAgICAgICAgIHRoaXMuc2NlbmVDb25maWdUcmVlID0gdGhpcy5hZGFwdFNjZW5lVHJlZShzY2VuZUNhdGVnb3J5LmluZHVzdHJ5U2NlbmVJbmZvVm8uc2NlbmVMaXN0Vm8pDQogICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAvLyDlpoLmnpzpnIDopoHkv53mjIHpgInkuK3oioLngrkNCiAgICAgICAgICAgICAgaWYgKGtlZXBTZWxlY3RlZE5vZGUgJiYgY3VycmVudFNlbGVjdGVkTm9kZSkgew0KICAgICAgICAgICAgICAgIGNvbnN0IG5vZGVUb1NlbGVjdCA9IHRoaXMuZmluZE5vZGVCeUlkKHRoaXMuc2NlbmVDb25maWdUcmVlLCBjdXJyZW50U2VsZWN0ZWROb2RlLmlkKQ0KICAgICAgICAgICAgICAgIGlmIChub2RlVG9TZWxlY3QpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWROb2RlID0gbm9kZVRvU2VsZWN0DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWROb2RlID0gdGhpcy5zY2VuZUNvbmZpZ1RyZWUubGVuZ3RoID4gMCA/IHRoaXMuc2NlbmVDb25maWdUcmVlWzBdIDogbnVsbA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAvLyDpu5jorqTpgInmi6nnrKzkuIDkuKroioLngrkNCiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkTm9kZSA9IHRoaXMuc2NlbmVDb25maWdUcmVlLmxlbmd0aCA+IDAgPyB0aGlzLnNjZW5lQ29uZmlnVHJlZVswXSA6IG51bGwNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgLy8g5rKh5pyJ5Zy65pmv5pWw5o2u5pe25riF56m6DQogICAgICAgICAgICAgIHRoaXMuc2NlbmVDb25maWdUcmVlID0gW10NCiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSBudWxsDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOWkhOeQhue9kee7nOaWueahiOaVsOaNrg0KICAgICAgICAgIGlmIChyZXMuZGF0YS5uZXR3b3JrU29sdXRpb25Wbykgew0KICAgICAgICAgICAgY29uc3QgbmV0d29ya0RhdGEgPSB7DQogICAgICAgICAgICAgIG5ldHdvcmtWaWRlb0xpc3Q6IHJlcy5kYXRhLm5ldHdvcmtTb2x1dGlvblZvLm5ldHdvcmtWaWRlb0xpc3QgfHwgW10sDQogICAgICAgICAgICAgIHZpZGVvRXhwbGFuYXRpb25WbzogcmVzLmRhdGEubmV0d29ya1NvbHV0aW9uVm8udmlkZW9FeHBsYW5hdGlvblZvIHx8IHsNCiAgICAgICAgICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogJycsDQogICAgICAgICAgICAgICAgdmlkZW9TZWdtZW50ZWRWb0xpc3Q6IFtdDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLm5ldHdvcmtQbGFuRGF0YU1hcCwgdGhpcy5hY3RpdmVNZW51LCBuZXR3b3JrRGF0YSkNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpITnkIbllYbkuJrku7flgLzmlbDmja4NCiAgICAgICAgICBpZiAocmVzLmRhdGEuY29tbWVyY2lhbFZhbHVlTGlzdFZvKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy5idXNpbmVzc1ZhbHVlRGF0YU1hcCwgdGhpcy5hY3RpdmVNZW51LCByZXMuZGF0YS5jb21tZXJjaWFsVmFsdWVMaXN0Vm8pDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5aSE55CGVlLnnIvnjrDlnLrmlbDmja4NCiAgICAgICAgICBpZiAocmVzLmRhdGEudnJJbmZvTGlzdFZvKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy52clNjZW5lRGF0YU1hcCwgdGhpcy5hY3RpdmVNZW51LCByZXMuZGF0YS52ckluZm9MaXN0Vm8pDQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOWFtuS7luaVsOaNruWkhOeQhumAu+i+keS/neaMgeS4jeWPmC4uLg0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veaVsOaNruWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICAvLyDlhbPpl63liIfmjaLooYzkuJrnmoRsb2FkaW5nDQogICAgICAgIGlmIChzaG93TG9hZGluZykgew0KICAgICAgICAgIHRoaXMuc3dpdGNoaW5nSW5kdXN0cnkgPSBmYWxzZQ0KICAgICAgICAgIC8vIOaBouWkjemhtemdoua7muWKqA0KICAgICAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnJw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVCZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgcmV0dXJuIGZhbHNlIC8vIOaLpuaIqum7mOiupOS4iuS8oOihjOS4ug0KICAgIH0sDQogICAgYWRkU2VnbWVudCgpIHsNCiAgICAgIHRoaXMuZm9ybS52aWRlb1NlZ21lbnRlZFZvTGlzdC5wdXNoKHsgdGltZTogJycsIHNjZW5lOiAnJyB9KQ0KICAgIH0sDQogICAgcmVtb3ZlU2VnbWVudChpbmRleCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS52aWRlb1NlZ21lbnRlZFZvTGlzdC5sZW5ndGggPj0gMSkgew0KICAgICAgICB0aGlzLmZvcm0udmlkZW9TZWdtZW50ZWRWb0xpc3Quc3BsaWNlKGluZGV4LCAxKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgYmVmb3JlVXBsb2FkSW50cm9kdWNlSW1nKGZpbGUsIHR5cGUsIGtleSkgew0KICAgICAgaWYgKCFmaWxlLnR5cGUuc3RhcnRzV2l0aCgnaW1hZ2UvJykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5Lyg5Zu+54mH5paH5Lu277yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4iuS8oOWbvueJh++8jOivt+eojeWAmS4uLiIpDQogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSkNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdpbmR1c3RyeUNvZGUnLCB0aGlzLmluZHVzdHJ5Q29kZSkNCiAgICAgICAgDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwbG9hZFNjZW5lRmlsZShmb3JtRGF0YSkNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIHJlcy5kYXRhKSB7DQogICAgICAgICAgaWYgKHR5cGUgJiYga2V5KSB7DQogICAgICAgICAgICAvLyDpkojlr7nku4vnu43op4bpopHlkozop4bpopHorrLop6PnmoTkuIrkvKDvvIzljZXni6zkuIrkvKDlm77niYfml7bkvb/nlKggZmlsZVVybA0KICAgICAgICAgICAgdGhpc1t0eXBlXVtrZXldID0gcmVzLmRhdGEuZmlsZVVybA0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDpkojlr7nkuLvog4zmma/lm77niYfnmoTkuIrkvKANCiAgICAgICAgICAgIHRoaXMuZm9ybS5iZ0ltZ1VybCA9IHJlcy5kYXRhLmZpbGVVcmwNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip8nKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5LiK5Lyg5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpDQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIGFzeW5jIGJlZm9yZVVwbG9hZEludHJvZHVjZVZpZGVvKGZpbGUsIHR5cGUsIGtleSkgew0KICAgICAgLy8g5aaC5p6c5piv5Li76IOM5pmv5paH5Lu25LiK5Lyg77yI5rKh5pyJdHlwZeWSjGtleeWPguaVsO+8iQ0KICAgICAgaWYgKCF0eXBlICYmICFrZXkpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjkuIrkvKDmlofku7bvvIzor7fnqI3lgJkuLi4iKQ0KICAgICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKQ0KICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnaW5kdXN0cnlDb2RlJywgdGhpcy5pbmR1c3RyeUNvZGUpDQogICAgICAgICAgDQogICAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgdXBsb2FkU2NlbmVGaWxlKGZvcm1EYXRhKQ0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCAmJiByZXMuZGF0YSkgew0KICAgICAgICAgICAgLy8g6K6+572u6IOM5pmv5paH5Lu2VVJMDQogICAgICAgICAgICB0aGlzLmZvcm0uYmdGaWxlVXJsID0gcmVzLmRhdGEuZmlsZVVybA0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDnm7TmjqXopobnm5bog4zmma/mlofku7bliJfooagNCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzLmRhdGEuZmlsZVVybC5zcGxpdCgnLycpLnBvcCgpDQogICAgICAgICAgICB0aGlzLmJnRmlsZUxpc3QgPSBbew0KICAgICAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICAgICAgdXJsOiByZXMuZGF0YS5maWxlVXJsLA0KICAgICAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgICAgIH1dDQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOWmguaenOaYr01QNOaWh+S7tuS4lOi/lOWbnuS6hmltZ1VybO+8jOiHquWKqOiuvue9ruiDjOaZr+WbvueJh+mmluW4pw0KICAgICAgICAgICAgaWYgKGZpbGUudHlwZSA9PT0gJ3ZpZGVvL21wNCcgJiYgcmVzLmRhdGEuaW1nVXJsKSB7DQogICAgICAgICAgICAgIHRoaXMuZm9ybS5iZ0ltZ1VybCA9IHJlcy5kYXRhLmltZ1VybA0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKn++8jOW3suiHquWKqOeUn+aIkOiDjOaZr+WbvueJh+mmluW4pycpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4iuS8oOaIkOWKnycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5LiK5Lyg5aSx6LSlJykNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5aSx6LSlJykNCiAgICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlhbbku5bop4bpopHkuIrkvKDpgLvovpHvvIjku4vnu43op4bpopHjgIHorrLop6Pop4bpopHnrYnvvIkNCiAgICAgIGlmICghZmlsZS50eXBlLnN0YXJ0c1dpdGgoJ3ZpZGVvLycpICYmICFmaWxlLm5hbWUuZW5kc1dpdGgoJy5tcDQnKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKBNUDTop4bpopHmlofku7bvvIEnKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIA0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5LiK5Lyg6KeG6aKR77yM6K+356iN5YCZLi4uIikNCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2luZHVzdHJ5Q29kZScsIHRoaXMuaW5kdXN0cnlDb2RlKQ0KICAgICAgICANCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgdXBsb2FkU2NlbmVGaWxlKGZvcm1EYXRhKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDAgJiYgcmVzLmRhdGEpIHsNCiAgICAgICAgICBpZiAodHlwZSAmJiBrZXkpIHsNCiAgICAgICAgICAgIC8vIOmSiOWvueS7i+e7jeinhumikeWSjOinhumikeiusuino+eahOS4iuS8oA0KICAgICAgICAgICAgdGhpc1t0eXBlXVtrZXldID0gcmVzLmRhdGEuZmlsZVVybA0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDnm7TmjqXopobnm5blr7nlupTnmoTmlofku7bliJfooagNCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gcmVzLmRhdGEuZmlsZVVybC5zcGxpdCgnLycpLnBvcCgpDQogICAgICAgICAgICBpZiAodHlwZSA9PT0gJ2ludHJvZHVjZVZpZGVvJyAmJiBrZXkgPT09ICdiYWNrZ3JvdW5kRmlsZVVybCcpIHsNCiAgICAgICAgICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW3sNCiAgICAgICAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICAgICAgICB1cmw6IHJlcy5kYXRhLmZpbGVVcmwsDQogICAgICAgICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgICAgICAgIH1dDQogICAgICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICd2aWRlb0V4cGxhbmF0aW9uJyAmJiBrZXkgPT09ICdiYWNrZ3JvdW5kRmlsZVVybCcpIHsNCiAgICAgICAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QgPSBbew0KICAgICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICAgIHVybDogcmVzLmRhdGEuZmlsZVVybCwNCiAgICAgICAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgICAgICAgfV0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5LuL57uN6KeG6aKR5LiK5Lyg77yM5LiU6L+U5Zue5LqGaW1nVXJs77yM6Ieq5Yqo6K6+572u5LuL57uN6KeG6aKR6aaW5binDQogICAgICAgICAgICBpZiAodHlwZSA9PT0gJ2ludHJvZHVjZVZpZGVvJyAmJiBrZXkgPT09ICdiYWNrZ3JvdW5kRmlsZVVybCcgJiYgcmVzLmRhdGEuaW1nVXJsKSB7DQogICAgICAgICAgICAgIHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEltZ0ZpbGVVcmwgPSByZXMuZGF0YS5pbWdVcmwNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip/vvIzlt7Loh6rliqjnlJ/miJDku4vnu43op4bpopHpppbluKcnKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkuIrkvKDmiJDlip8nKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+S4iuS8oOWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZhbHNlDQogICAgfSwNCiAgICBiZWZvcmVVcGxvYWRFeHBsYW5hdGlvblZpZGVvKGZpbGUpIHsNCiAgICAgIHRoaXMudXBsb2FkaW5nVHlwZSA9ICdtcDQnDQogICAgICB0aGlzLnVwbG9hZGluZ0tleSA9ICd2aWRlb0V4cGxhbmF0aW9uRmlsZVVybCcNCiAgICAgIHJldHVybiB0aGlzLmhhbmRsZUJlZm9yZVVwbG9hZChmaWxlKQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5re75Yqg5Zy65pmv6YWN572u6IqC54K5DQogICAgYWRkU2NlbmVDb25maWdOb2RlKHBhcmVudElkID0gbnVsbCkgew0KICAgICAgY29uc3QgbmV3Tm9kZSA9IHsNCiAgICAgICAgaWQ6IERhdGUubm93KCksIC8vIOeUn+aIkOWUr+S4gElEDQogICAgICAgIG5hbWU6ICfmlrDlnLrmma8nLA0KICAgICAgICB0eXBlOiAnc2NlbmUnLCAvLyDnsbvlnovkuLrlnLrmma8NCiAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgY2hpbGRyZW46IFtdLA0KICAgICAgICBwYXJlbnRJZDogcGFyZW50SWQNCiAgICAgIH0NCiAgICAgIGlmIChwYXJlbnRJZCkgew0KICAgICAgICBjb25zdCBwYXJlbnROb2RlID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIHBhcmVudElkKQ0KICAgICAgICBpZiAocGFyZW50Tm9kZSkgew0KICAgICAgICAgIHBhcmVudE5vZGUuY2hpbGRyZW4ucHVzaChuZXdOb2RlKQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNjZW5lQ29uZmlnVHJlZS5wdXNoKG5ld05vZGUpDQogICAgICB9DQogICAgICByZXR1cm4gbmV3Tm9kZS5pZA0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5Yig6Zmk5Zy65pmv6YWN572u6IqC54K5DQogICAgcmVtb3ZlU2NlbmVDb25maWdOb2RlKG5vZGVJZCkgew0KICAgICAgdGhpcy5zY2VuZUNvbmZpZ1RyZWUgPSB0aGlzLnNjZW5lQ29uZmlnVHJlZS5maWx0ZXIobm9kZSA9PiBub2RlLmlkICE9PSBub2RlSWQpDQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrmn6Xmib7oioLngrkNCiAgICBmaW5kTm9kZUJ5SWQobm9kZXMsIGlkKSB7DQogICAgICBmb3IgKGNvbnN0IG5vZGUgb2Ygbm9kZXMpIHsNCiAgICAgICAgaWYgKG5vZGUuaWQgPT09IGlkKSB7DQogICAgICAgICAgcmV0dXJuIG5vZGUNCiAgICAgICAgfQ0KICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICBjb25zdCBmb3VuZCA9IHRoaXMuZmluZE5vZGVCeUlkKG5vZGUuY2hpbGRyZW4sIGlkKQ0KICAgICAgICAgIGlmIChmb3VuZCkgew0KICAgICAgICAgICAgcmV0dXJuIGZvdW5kDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gbnVsbA0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5re75Yqg5Zy65pmv55qE55eb54K55Lu35YC8DQogICAgYWRkU2NlbmVQYWluUG9pbnQobm9kZUlkKSB7DQogICAgICBjb25zdCBub2RlID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIG5vZGVJZCkNCiAgICAgIGlmIChub2RlICYmIG5vZGUudHlwZSA9PT0gJ3NjZW5lJykgew0KICAgICAgICBub2RlLnBhaW5Qb2ludHMgPSBub2RlLnBhaW5Qb2ludHMgfHwgW10NCiAgICAgICAgbm9kZS5wYWluUG9pbnRzLnB1c2goeyB0aXRsZTogJycsIGNvbnRlbnRzOiBbJyddLCBzaG93VGltZTogJycgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8muWIoOmZpOWcuuaZr+eahOeXm+eCueS7t+WAvA0KICAgIHJlbW92ZVNjZW5lUGFpblBvaW50KG5vZGVJZCwgaWR4KSB7DQogICAgICBjb25zdCBub2RlID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIG5vZGVJZCkNCiAgICAgIGlmIChub2RlICYmIG5vZGUudHlwZSA9PT0gJ3NjZW5lJykgew0KICAgICAgICBub2RlLnBhaW5Qb2ludHMgPSBub2RlLnBhaW5Qb2ludHMgfHwgW10NCiAgICAgICAgbm9kZS5wYWluUG9pbnRzLnNwbGljZShpZHgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlop7mlrnms5XvvJrmt7vliqDlnLrmma/nl5vngrnlhoXlrrnnmoTpobkNCiAgICBhZGRTY2VuZVBhaW5Db250ZW50KG5vZGVJZCwgaWR4KSB7DQogICAgICBjb25zdCBub2RlID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIG5vZGVJZCkNCiAgICAgIGlmIChub2RlICYmIG5vZGUudHlwZSA9PT0gJ3NjZW5lJykgew0KICAgICAgICBub2RlLnBhaW5Qb2ludHMgPSBub2RlLnBhaW5Qb2ludHMgfHwgW10NCiAgICAgICAgbm9kZS5wYWluUG9pbnRzW2lkeF0uY29udGVudHMucHVzaCgnJykNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8muWIoOmZpOWcuuaZr+eXm+eCueWGheWuueeahOmhuQ0KICAgIHJlbW92ZVNjZW5lUGFpbkNvbnRlbnQobm9kZUlkLCBpZHgsIGNpZHgpIHsNCiAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgbm9kZUlkKQ0KICAgICAgaWYgKG5vZGUgJiYgbm9kZS50eXBlID09PSAnc2NlbmUnKSB7DQogICAgICAgIG5vZGUucGFpblBvaW50cyA9IG5vZGUucGFpblBvaW50cyB8fCBbXQ0KICAgICAgICBub2RlLnBhaW5Qb2ludHNbaWR4XS5jb250ZW50cy5zcGxpY2UoY2lkeCwgMSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8mua3u+WKoOWcuuaZr+eahOaIkOacrOmihOS8sOWGheWuuQ0KICAgIGFkZFNjZW5lQ29zdENvbnRlbnQobm9kZUlkKSB7DQogICAgICBjb25zdCBub2RlID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIG5vZGVJZCkNCiAgICAgIGlmIChub2RlICYmIG5vZGUudHlwZSA9PT0gJ2Nvc3RFc3RpbWF0ZScpIHsNCiAgICAgICAgbm9kZS5jb250ZW50cyA9IG5vZGUuY29udGVudHMgfHwgW10NCiAgICAgICAgbm9kZS5jb250ZW50cy5wdXNoKCcnKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5Yig6Zmk5Zy65pmv55qE5oiQ5pys6aKE5Lyw5YaF5a65DQogICAgcmVtb3ZlU2NlbmVDb3N0Q29udGVudChub2RlSWQsIGNpZHgpIHsNCiAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmZpbmROb2RlQnlJZCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgbm9kZUlkKQ0KICAgICAgaWYgKG5vZGUgJiYgbm9kZS50eXBlID09PSAnY29zdEVzdGltYXRlJykgew0KICAgICAgICBub2RlLmNvbnRlbnRzID0gbm9kZS5jb250ZW50cyB8fCBbXQ0KICAgICAgICBub2RlLmNvbnRlbnRzLnNwbGljZShjaWR4LCAxKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5paw5aKe5pa55rOV77ya5LiK5Lyg5Zy65pmv6YWN572u5Zu+54mHDQogICAgYmVmb3JlVXBsb2FkU2NlbmVDb25maWdJbWcoZmlsZSwgdHlwZSwga2V5KSB7DQogICAgICBpZiAoIWZpbGUudHlwZS5zdGFydHNXaXRoKCdpbWFnZS8nKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKDlm77niYfmlofku7bvvIEnKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIGNvbnN0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCkNCiAgICAgIHJlYWRlci5vbmxvYWQgPSBlID0+IHsNCiAgICAgICAgdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIHR5cGUpW2tleV0gPSBlLnRhcmdldC5yZXN1bHQNCiAgICAgIH0NCiAgICAgIHJlYWRlci5yZWFkQXNEYXRhVVJMKGZpbGUpDQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIC8vIOaWsOWinuaWueazle+8muS4iuS8oOWcuuaZr+mFjee9ruaWh+S7tg0KICAgIGJlZm9yZVVwbG9hZFNjZW5lQ29uZmlnRmlsZShmaWxlLCB0eXBlLCBrZXkpIHsNCiAgICAgIC8vIOi/memHjOWPquWBmuaWh+S7tuWQjeWbnuaYvg0KICAgICAgdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIHR5cGUpW2tleV0gPSBmaWxlLm5hbWUNCiAgICAgIHJldHVybiBmYWxzZQ0KICAgIH0sDQogICAgaGFuZGxlU2NlbmVOb2RlQ2xpY2sobm9kZSkgew0KICAgICAgdGhpcy5zZWxlY3RlZE5vZGUgPSBub2RlDQogICAgfSwNCiAgICAvLyDpgILphY3lh73mlbDnp7vliLBtZXRob2Rz5Lit77yM5L6b5o6l5Y+j5pWw5o2u6YCC6YWN5L2/55SoDQogICAgYWRhcHRTY2VuZVRyZWUocmF3VHJlZSwgcGFyZW50ID0gbnVsbCkgew0KICAgICAgaWYgKCFyYXdUcmVlKSByZXR1cm4gW10NCiAgICAgIGNvbnN0IGFyciA9IEFycmF5LmlzQXJyYXkocmF3VHJlZSkgPyByYXdUcmVlIDogW3Jhd1RyZWVdDQogICAgICByZXR1cm4gYXJyLm1hcCgobm9kZSkgPT4gew0KICAgICAgICBjb25zdCBhZGFwdGVkID0gew0KICAgICAgICAgIGlkOiBub2RlLnNjZW5lSWQsDQogICAgICAgICAgc2NlbmVJbmZvSWQ6IG5vZGUuc2NlbmVJbmZvSWQgfHwgbnVsbCwNCiAgICAgICAgICBuYW1lOiBub2RlLnNjZW5lTmFtZSwNCiAgICAgICAgICBjb2RlOiBub2RlLnNjZW5lQ29kZSwNCiAgICAgICAgICB4OiBub2RlLngsDQogICAgICAgICAgeTogbm9kZS55LA0KICAgICAgICAgIHR5cGU6IG5vZGUudHlwZSwNCiAgICAgICAgICBzdGF0dXM6IG5vZGUuc3RhdHVzICE9PSBudWxsID8gbm9kZS5zdGF0dXMgOiAnMCcsDQogICAgICAgICAgaXNVbmZvbGQ6IG5vZGUuaXNVbmZvbGQgIT09IG51bGwgJiYgbm9kZS5pc1VuZm9sZCAhPT0gdW5kZWZpbmVkID8gbm9kZS5pc1VuZm9sZCA6ICcxJywNCiAgICAgICAgICBkaXNwbGF5TG9jYXRpb246IG5vZGUuZGlzcGxheUxvY2F0aW9uICE9PSBudWxsICYmIG5vZGUuZGlzcGxheUxvY2F0aW9uICE9PSB1bmRlZmluZWQgPyBub2RlLmRpc3BsYXlMb2NhdGlvbiA6ICcwJywNCiAgICAgICAgICB0cmVlQ2xhc3NpZmljYXRpb246IG5vZGUudHJlZUNsYXNzaWZpY2F0aW9uICE9PSBudWxsICYmIG5vZGUudHJlZUNsYXNzaWZpY2F0aW9uICE9PSB1bmRlZmluZWQgPyBub2RlLnRyZWVDbGFzc2lmaWNhdGlvbiA6ICczJywNCiAgICAgICAgICBpbnRyb2R1Y2VWaWRlb1ZvOiBub2RlLmludHJvZHVjZVZpZGVvVm8gPyB7DQogICAgICAgICAgICBpZDogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmlkIHx8ICcnLA0KICAgICAgICAgICAgdHlwZTogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLnR5cGUgfHwgJycsDQogICAgICAgICAgICB2aWV3SW5mb0lkOiBub2RlLmludHJvZHVjZVZpZGVvVm8udmlld0luZm9JZCB8fCAnJywNCiAgICAgICAgICAgIHN0YXR1czogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLnN0YXR1cyB8fCAnMCcsDQogICAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvLmJhY2tncm91bmRJbWdGaWxlVXJsIHx8ICcnLA0KICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IG5vZGUuaW50cm9kdWNlVmlkZW9Wby5iYWNrZ3JvdW5kRmlsZVVybCB8fCAnJw0KICAgICAgICAgIH0gOiB7IGlkOiAnJywgdHlwZTogJycsIHZpZXdJbmZvSWQ6ICcnLCBzdGF0dXM6ICcwJywgYmFja2dyb3VuZEltZ0ZpbGVVcmw6ICcnLCBiYWNrZ3JvdW5kRmlsZVVybDogJycgfSwNCiAgICAgICAgICB0cmFkaXRpb246IG5vZGUuc2NlbmVUcmFkaXRpb25WbyA/IHsNCiAgICAgICAgICAgIG5hbWU6IG5vZGUuc2NlbmVUcmFkaXRpb25Wby5uYW1lIHx8ICcnLA0KICAgICAgICAgICAgcGFub3JhbWljVmlld1htbEtleTogbm9kZS5zY2VuZVRyYWRpdGlvblZvLnBhbm9yYW1pY1ZpZXdYbWxLZXkgfHwgJycsDQogICAgICAgICAgICBiYWNrZ3JvdW5kUmVzb3VyY2VzOiBub2RlLnNjZW5lVHJhZGl0aW9uVm8uc2NlbmVWaWRlb0xpc3QgPyANCiAgICAgICAgICAgICAgbm9kZS5zY2VuZVRyYWRpdGlvblZvLnNjZW5lVmlkZW9MaXN0Lm1hcCh2ID0+ICh7DQogICAgICAgICAgICAgICAgaWQ6IHYuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICBsYWJlbDogdi50YWcgfHwgJycsDQogICAgICAgICAgICAgICAgY29vcmRpbmF0ZXM6IHYuc2NlbmVGaWxlUmVsTGlzdCA/IHYuc2NlbmVGaWxlUmVsTGlzdC5tYXAocmVsID0+ICh7DQogICAgICAgICAgICAgICAgICBpZDogcmVsLmlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBmaWxlSWQ6IHJlbC5maWxlSWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIHg6IHJlbC5jbGlja1ggfHwgJycsDQogICAgICAgICAgICAgICAgICB5OiByZWwuY2xpY2tZIHx8ICcnLA0KICAgICAgICAgICAgICAgICAgd2lkZTogcmVsLndpZGUgfHwgJycsDQogICAgICAgICAgICAgICAgICBoaWdoOiByZWwuaGlnaCB8fCAnJywNCiAgICAgICAgICAgICAgICAgIHhtbEtleTogcmVsLnhtbEtleSB8fCAnJywNCiAgICAgICAgICAgICAgICAgIHNjZW5lSWQ6IHRoaXMuZmluZFNjZW5lSWRCeUNvZGUocmVsLmJpbmRTY2VuZUNvZGUpLA0KICAgICAgICAgICAgICAgICAgc2NlbmVDb2RlOiByZWwuYmluZFNjZW5lQ29kZSB8fCAnJw0KICAgICAgICAgICAgICAgIH0pKSA6IFt7IGlkOiBudWxsLCBmaWxlSWQ6IG51bGwsIHg6ICcnLCB5OiAnJywgd2lkZTogJycsIGhpZ2g6ICcnLCBzY2VuZUlkOiAnJywgc2NlbmVDb2RlOiAnJyB9XSwNCiAgICAgICAgICAgICAgICB3aWRlOiB2LndpZGUgfHwgMCwNCiAgICAgICAgICAgICAgICBoaWdoOiB2LmhpZ2ggfHwgMCwNCiAgICAgICAgICAgICAgICBiZ0ltZzogdi5iYWNrZ3JvdW5kSW1nRmlsZVVybCB8fCAnJywNCiAgICAgICAgICAgICAgICBiZ0ZpbGU6IHYuYmFja2dyb3VuZEZpbGVVcmwgfHwgJycsDQogICAgICAgICAgICAgICAgc3RhdHVzOiB2LnN0YXR1cyB8fCAnJywNCiAgICAgICAgICAgICAgICB0eXBlOiB2LnR5cGUgfHwgJycsDQogICAgICAgICAgICAgICAgdmlld0luZm9JZDogdi52aWV3SW5mb0lkIHx8ICcnDQogICAgICAgICAgICAgIH0pKSA6IFtdLA0KICAgICAgICAgICAgcGFpblBvaW50czogbm9kZS5zY2VuZVRyYWRpdGlvblZvLnBhaW5Qb2ludExpc3QgPw0KICAgICAgICAgICAgICAoQXJyYXkuaXNBcnJheShub2RlLnNjZW5lVHJhZGl0aW9uVm8ucGFpblBvaW50TGlzdCkgPyBub2RlLnNjZW5lVHJhZGl0aW9uVm8ucGFpblBvaW50TGlzdC5tYXAocCA9PiAoew0KICAgICAgICAgICAgICAgIHBhaW5Qb2ludElkOiBwLnBhaW5Qb2ludElkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgdGl0bGU6IHAuYmlnVGl0bGUgfHwgJycsDQogICAgICAgICAgICAgICAgY29udGVudHM6IHAuY29udGVudCB8fCBbXSwNCiAgICAgICAgICAgICAgICBzaG93VGltZTogcC5kaXNwbGF5VGltZSB8fCAnJw0KICAgICAgICAgICAgICB9KSkgOiBbXSkgOiBbXQ0KICAgICAgICAgIH0gOiB7IG5hbWU6ICcnLCBwYW5vcmFtaWNWaWV3WG1sS2V5OiAnJywgYmFja2dyb3VuZFJlc291cmNlczogW10sIHBhaW5Qb2ludHM6IFtdIH0sDQogICAgICAgICAgd2lzZG9tNWc6IG5vZGUuc2NlbmU1Z1ZvID8gew0KICAgICAgICAgICAgbmFtZTogbm9kZS5zY2VuZTVnVm8ubmFtZSB8fCAnJywNCiAgICAgICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxLZXk6IG5vZGUuc2NlbmU1Z1ZvLnBhbm9yYW1pY1ZpZXdYbWxLZXkgfHwgJycsDQogICAgICAgICAgICBiYWNrZ3JvdW5kUmVzb3VyY2VzOiBub2RlLnNjZW5lNWdWby5zY2VuZVZpZGVvTGlzdCA/IA0KICAgICAgICAgICAgICBub2RlLnNjZW5lNWdWby5zY2VuZVZpZGVvTGlzdC5tYXAodiA9PiAoew0KICAgICAgICAgICAgICAgIGlkOiB2LmlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgdGFnOiB2LnRhZyB8fCAnJywNCiAgICAgICAgICAgICAgICBzdGF0dXM6IHYuc3RhdHVzIHx8ICcnLA0KICAgICAgICAgICAgICAgIHR5cGU6IHYudHlwZSB8fCAnJywNCiAgICAgICAgICAgICAgICB2aWV3SW5mb0lkOiB2LnZpZXdJbmZvSWQgfHwgJycsDQogICAgICAgICAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6IHYuYmFja2dyb3VuZEltZ0ZpbGVVcmwgfHwgJycsDQogICAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHYuYmFja2dyb3VuZEZpbGVVcmwgfHwgJycsDQogICAgICAgICAgICAgICAgY29vcmRpbmF0ZXM6IHYuc2NlbmVGaWxlUmVsTGlzdCA/IHYuc2NlbmVGaWxlUmVsTGlzdC5tYXAocmVsID0+ICh7DQogICAgICAgICAgICAgICAgICBpZDogcmVsLmlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBmaWxlSWQ6IHJlbC5maWxlSWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIHg6IHJlbC5jbGlja1ggfHwgJycsDQogICAgICAgICAgICAgICAgICB5OiByZWwuY2xpY2tZIHx8ICcnLA0KICAgICAgICAgICAgICAgICAgd2lkZTogcmVsLndpZGUgfHwgJycsDQogICAgICAgICAgICAgICAgICBoaWdoOiByZWwuaGlnaCB8fCAnJywNCiAgICAgICAgICAgICAgICAgIHhtbEtleTogcmVsLnhtbEtleSB8fCAnJywNCiAgICAgICAgICAgICAgICAgIHNjZW5lSWQ6IHRoaXMuZmluZFNjZW5lSWRCeUNvZGUocmVsLmJpbmRTY2VuZUNvZGUpLA0KICAgICAgICAgICAgICAgICAgc2NlbmVDb2RlOiByZWwuYmluZFNjZW5lQ29kZSB8fCAnJw0KICAgICAgICAgICAgICAgIH0pKSA6IFt7IGlkOiBudWxsLCBmaWxlSWQ6IG51bGwsIHg6ICcnLCB5OiAnJywgd2lkZTogJycsIGhpZ2g6ICcnLCBzY2VuZUlkOiAnJywgc2NlbmVDb2RlOiAnJyB9XQ0KICAgICAgICAgICAgICB9KSkgOiBbXSwNCiAgICAgICAgICAgIHBhaW5Qb2ludHM6IG5vZGUuc2NlbmU1Z1ZvLnBhaW5Qb2ludExpc3QgPyANCiAgICAgICAgICAgICAgKEFycmF5LmlzQXJyYXkobm9kZS5zY2VuZTVnVm8ucGFpblBvaW50TGlzdCkgPyBub2RlLnNjZW5lNWdWby5wYWluUG9pbnRMaXN0Lm1hcChwID0+ICh7DQogICAgICAgICAgICAgICAgcGFpblBvaW50SWQ6IHAucGFpblBvaW50SWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICB0aXRsZTogcC5iaWdUaXRsZSB8fCAnJywNCiAgICAgICAgICAgICAgICBjb250ZW50czogcC5jb250ZW50IHx8IFtdLA0KICAgICAgICAgICAgICAgIHNob3dUaW1lOiBwLmRpc3BsYXlUaW1lIHx8ICcnDQogICAgICAgICAgICAgIH0pKSA6IFtdKSA6IFtdDQogICAgICAgICAgfSA6IHsgbmFtZTogJycsIHBhbm9yYW1pY1ZpZXdYbWxLZXk6ICcnLCBiYWNrZ3JvdW5kUmVzb3VyY2VzOiBbXSwgcGFpblBvaW50czogW10gfSwNCiAgICAgICAgICBjb3N0RXN0aW1hdGU6IG5vZGUuY29zdEVzdGltYXRpb25JbmZvVm8gPyB7DQogICAgICAgICAgICBwYWluUG9pbnRJZDogbm9kZS5jb3N0RXN0aW1hdGlvbkluZm9Wby5wYWluUG9pbnRJZCB8fCBudWxsLA0KICAgICAgICAgICAgc3RhdHVzOiBub2RlLmNvc3RFc3RpbWF0aW9uSW5mb1ZvLnN0YXR1cyB8fCAnMCcsDQogICAgICAgICAgICB0aXRsZTogbm9kZS5jb3N0RXN0aW1hdGlvbkluZm9Wby5iaWdUaXRsZSB8fCAnJywNCiAgICAgICAgICAgIGNvbnRlbnRzOiBub2RlLmNvc3RFc3RpbWF0aW9uSW5mb1ZvLmNvbnRlbnQgfHwgW10NCiAgICAgICAgICB9IDogeyBwYWluUG9pbnRJZDogbnVsbCwgc3RhdHVzOiAnMCcsIHRpdGxlOiAnJywgY29udGVudHM6IFtdIH0sDQogICAgICAgICAgY2hpbGRyZW46IFtdLA0KICAgICAgICAgIHBhcmVudA0KICAgICAgICB9DQogICAgICAgIC8vIOmAkuW9kuWkhOeQhuWtkOiKgueCue+8jOS/neaMgeWQjuerr+i/lOWbnueahOWOn+Wni+mhuuW6jw0KICAgICAgICBhZGFwdGVkLmNoaWxkcmVuID0gbm9kZS5jaGlsZHJlbiA/IHRoaXMuYWRhcHRTY2VuZVRyZWUobm9kZS5jaGlsZHJlbiwgYWRhcHRlZCkgOiBbXQ0KICAgICAgICByZXR1cm4gYWRhcHRlZA0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuc3VibWl0dGluZyA9IHRydWUNCiAgICAgICAgLy8g5LuO5omB5bmz5YyW6I+c5Y2V5pWw5o2u5Lit6I635Y+W5b2T5YmN6KGM5Lia55qEIGluZHVzdHJ5Q29kZQ0KICAgICAgICBjb25zdCBjdXJyZW50SW5kdXN0cnkgPSB0aGlzLmZsYXRNZW51RGF0YS5maW5kKGl0ZW0gPT4gU3RyaW5nKGl0ZW0uaWQpID09PSB0aGlzLmFjdGl2ZU1lbnUpDQogICAgICAgIGNvbnN0IGluZHVzdHJ5Q29kZSA9IGN1cnJlbnRJbmR1c3RyeSA/IGN1cnJlbnRJbmR1c3RyeS5pbmR1c3RyeUNvZGUgOiBudWxsDQogICAgICAgIA0KICAgICAgICAvLyDkv53lrZjlvZPliY3pgInkuK3nmoToioLngrnnmoTlrozmlbTkv6Hmga8NCiAgICAgICAgY29uc3QgY3VycmVudFNlbGVjdGVkTm9kZUlkID0gdGhpcy5zZWxlY3RlZE5vZGUgPyB0aGlzLnNlbGVjdGVkTm9kZS5pZCA6IG51bGwNCiAgICAgICAgY29uc3QgY3VycmVudFNlbGVjdGVkTm9kZU5hbWUgPSB0aGlzLnNlbGVjdGVkTm9kZSA/IHRoaXMuc2VsZWN0ZWROb2RlLm5hbWUgOiBudWxsDQogICAgICAgIA0KICAgICAgICAvLyDmnoTlu7rmj5DkuqTmlbDmja4NCiAgICAgICAgY29uc3Qgc3VibWl0RGF0YSA9IHsNCiAgICAgICAgICBpbmR1c3RyeUlkOiB0aGlzLmFjdGl2ZU1lbnUsDQogICAgICAgICAgaW5kdXN0cnlDb2RlOiBpbmR1c3RyeUNvZGUsIC8vIOaWsOWinuWPguaVsA0KICAgICAgICAgIHNjZW5lVmlld0NvbmZpZ0lkOiB0aGlzLmZvcm0uc2NlbmVWaWV3Q29uZmlnSWQgfHwgbnVsbCwNCiAgICAgICAgICBtYWluVGl0bGU6IHRoaXMuZm9ybS5tYWluVGl0bGUgfHwgbnVsbCwNCiAgICAgICAgICBzdWJUaXRsZTogdGhpcy5mb3JtLnN1YlRpdGxlIHx8IG51bGwsDQogICAgICAgICAgdGhlbWVJZDogdGhpcy5zZWxlY3RlZFRoZW1lID8gdGhpcy5zZWxlY3RlZFRoZW1lLnRoZW1lSWQgOiBudWxsLA0KICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiB0aGlzLmZvcm0uYmdJbWdVcmwgfHwgbnVsbCwNCiAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogdGhpcy5mb3JtLmJnRmlsZVVybCB8fCBudWxsLA0KICAgICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxVcmw6IHRoaXMuZm9ybS5wYW5vcmFtaWNWaWV3WG1sVXJsIHx8IG51bGwsDQogICAgICAgICAgbmV0d29ya1NvbHV0aW9uSW5mb1ZvOiB7DQogICAgICAgICAgICBuZXR3b3JrVmlkZW9MaXN0OiAodGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XT8ubmV0d29ya1ZpZGVvTGlzdCAmJiBBcnJheS5pc0FycmF5KHRoaXMubmV0d29ya1BsYW5EYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0ubmV0d29ya1ZpZGVvTGlzdCkpID8gDQogICAgICAgICAgICAgIHRoaXMubmV0d29ya1BsYW5EYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0ubmV0d29ya1ZpZGVvTGlzdC5tYXAocGxhbiA9PiAoew0KICAgICAgICAgICAgICAgIGlkOiBwbGFuLmlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgdHlwZTogNCwNCiAgICAgICAgICAgICAgICB0YWc6IHBsYW4udGFnIHx8IG51bGwsDQogICAgICAgICAgICAgICAgY2xpY2tYOiBwbGFuLmNsaWNrWCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIGNsaWNrWTogcGxhbi5jbGlja1kgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICB3aWRlOiBwbGFuLndpZGUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICBoaWdoOiBwbGFuLmhpZ2ggfHwgbnVsbCwNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kSW1nRmlsZVVybDogcGxhbi5iYWNrZ3JvdW5kSW1nRmlsZVVybCB8fCBudWxsLA0KICAgICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiBwbGFuLmJhY2tncm91bmRGaWxlVXJsIHx8IG51bGwsDQogICAgICAgICAgICAgICAgc3RhdHVzOiBudWxsLA0KICAgICAgICAgICAgICAgIHZpZXdJbmZvSWQ6IG51bGwNCiAgICAgICAgICAgICAgfSkpIDogW10sDQogICAgICAgICAgICB2aWRlb0V4cGxhbmF0aW9uVm86IHsNCiAgICAgICAgICAgICAgc3RhdHVzOiB0aGlzLm5ldHdvcmtQbGFuRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdPy52aWRlb0V4cGxhbmF0aW9uVm8/LnN0YXR1cyB8fCAnMCcsDQogICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiB0aGlzLm5ldHdvcmtQbGFuRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdPy52aWRlb0V4cGxhbmF0aW9uVm8/LmJhY2tncm91bmRGaWxlVXJsIHx8IG51bGwsDQogICAgICAgICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiAodGhpcy5uZXR3b3JrUGxhbkRhdGFNYXBbdGhpcy5hY3RpdmVNZW51XT8udmlkZW9FeHBsYW5hdGlvblZvPy52aWRlb1NlZ21lbnRlZFZvTGlzdD8ubGVuZ3RoKSA/IA0KICAgICAgICAgICAgICAgIHRoaXMubmV0d29ya1BsYW5EYXRhTWFwW3RoaXMuYWN0aXZlTWVudV0udmlkZW9FeHBsYW5hdGlvblZvLnZpZGVvU2VnbWVudGVkVm9MaXN0Lm1hcChzZWcgPT4gKHsNCiAgICAgICAgICAgICAgICAgIHRpbWU6IHNlZy50aW1lIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBzY2VuZUNvZGU6IHNlZy5zY2VuZUNvZGUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIHNjZW5lTmFtZTogc2VnLnNjZW5lTmFtZSB8fCBudWxsDQogICAgICAgICAgICAgICAgfSkpIDogbnVsbA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2NlbmVEZWZhdWx0Q29uZmlnVm9MaXN0OiB0aGlzLmNhdGVnb3JpZXMubWFwKGNhdCA9PiB7DQogICAgICAgICAgICBjb25zdCBiYXNlQ29uZmlnID0gew0KICAgICAgICAgICAgICBpZDogY2F0LmlkIHx8IG51bGwsDQogICAgICAgICAgICAgIGluZHVzdHJ5SWQ6IHRoaXMuYWN0aXZlTWVudSB8fCBudWxsLA0KICAgICAgICAgICAgICBuYW1lOiBjYXQubmFtZSwNCiAgICAgICAgICAgICAga2V5TmFtZTogY2F0LmtleSwNCiAgICAgICAgICAgICAga2V5VmFsdWU6IGNhdC5lbmFibGVkID8gJzAnIDogJzEnLA0KICAgICAgICAgICAgICByZW1hcms6IGNhdC5yZW1hcmsgfHwgY2F0Lm5hbWUsDQogICAgICAgICAgICAgIGRlZmF1bHRTdGF0dXM6ICcwJw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBpZiAoY2F0LmtleSA9PT0gJ2RlZmF1bHRfc2NlbmUnKSB7DQogICAgICAgICAgICAgIGNvbnN0IGNvbnZlcnRlZFNjZW5lTGlzdCA9IHRoaXMuY29udmVydFNjZW5lVHJlZVRvQXBpKHRoaXMuc2NlbmVDb25maWdUcmVlKSAgICAgDQogICAgICAgICAgICAgIGJhc2VDb25maWcuaW5kdXN0cnlTY2VuZUluZm9WbyA9IHsNCiAgICAgICAgICAgICAgICB2aWRlb0V4cGxhbmF0aW9uVm86IHsNCiAgICAgICAgICAgICAgICAgIHN0YXR1czogdGhpcy52aWRlb0V4cGxhbmF0aW9uLnN0YXR1cywNCiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiB0aGlzLnZpZGVvRXhwbGFuYXRpb24uYmFja2dyb3VuZEZpbGVVcmwgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIHZpZGVvU2VnbWVudGVkVm9MaXN0OiB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3QubGVuZ3RoID8gdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0IDogbnVsbA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgc2NlbmVMaXN0Vm86IGNvbnZlcnRlZFNjZW5lTGlzdA0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBiYXNlQ29uZmlnLmluZHVzdHJ5U2NlbmVJbmZvVm8gPSBudWxsDQogICAgICAgICAgICB9DQogICAgICAgICAgICANCiAgICAgICAgICAgIHJldHVybiBiYXNlQ29uZmlnDQogICAgICAgICAgfSksDQogICAgICAgICAgY29tbWVyY2lhbFZhbHVlRFRPOiAodGhpcy5idXNpbmVzc1ZhbHVlRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdICYmIEFycmF5LmlzQXJyYXkodGhpcy5idXNpbmVzc1ZhbHVlRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdKSkgPyANCiAgICAgICAgICAgIHRoaXMuYnVzaW5lc3NWYWx1ZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XS5tYXAodmFsdWUgPT4gKHsNCiAgICAgICAgICAgICAgaWQ6IHZhbHVlLmlkIHx8IG51bGwsDQogICAgICAgICAgICAgIHZpZXdJbmZvSWQ6IHZhbHVlLnZpZXdJbmZvSWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgdHlwZTogNSwNCiAgICAgICAgICAgICAgdGFnOiB2YWx1ZS50YWcgfHwgbnVsbCwNCiAgICAgICAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6IHZhbHVlLmJhY2tncm91bmRJbWdGaWxlVXJsIHx8IG51bGwsDQogICAgICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiB2YWx1ZS5iYWNrZ3JvdW5kRmlsZVVybCB8fCBudWxsDQogICAgICAgICAgICB9KSkgOiBbXSwNCiAgICAgICAgICB2ckluZm9EdG9MaXN0OiAodGhpcy52clNjZW5lRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdICYmIEFycmF5LmlzQXJyYXkodGhpcy52clNjZW5lRGF0YU1hcFt0aGlzLmFjdGl2ZU1lbnVdKSkgPyANCiAgICAgICAgICAgIHRoaXMudnJTY2VuZURhdGFNYXBbdGhpcy5hY3RpdmVNZW51XS4gbWFwKHZyID0+ICh7DQogICAgICAgICAgICAgIGlkOiB2ci5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICBpbmR1c3RyeUlkOiB2ci5pbmR1c3RyeUlkIHx8IHRoaXMuYWN0aXZlTWVudSwNCiAgICAgICAgICAgICAgdHlwZTogdnIudHlwZSB8fCA2LA0KICAgICAgICAgICAgICB2aWV3SW5mb0lkOiB2ci52aWV3SW5mb0lkIHx8IG51bGwsDQogICAgICAgICAgICAgIG5hbWU6IHZyLm5hbWUgfHwgJycsDQogICAgICAgICAgICAgIGFkZHJlc3M6IHZyLmFkZHJlc3MgfHwgJycNCiAgICAgICAgICAgIH0pKSA6IFtdLA0KICAgICAgICB9DQogICAgICANCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzY2VuZVZpZXdVcGQoc3VibWl0RGF0YSkNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQoNCiAgICAgICAgLy8g6YeN5paw5Yqg6L295pWw5o2u77yI5LiN5pi+56S65YWo5bGAbG9hZGluZ++8jOmBv+WFjeS4juaMiemSrmxvYWRpbmflhrLnqoHvvIkNCiAgICAgICAgYXdhaXQgdGhpcy5oYW5kbGVTZWxlY3QodGhpcy5hY3RpdmVNZW51LCBmYWxzZSwgZmFsc2UpDQoNCiAgICAgICAgaWYgKGN1cnJlbnRTZWxlY3RlZE5vZGVJZCAmJiB0aGlzLnNjZW5lQ29uZmlnVHJlZS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g5by65Yi25p+l5om+5bm26K6+572u6YCJ5Lit6IqC54K5DQogICAgICAgICAgY29uc3Qgbm9kZVRvU2VsZWN0ID0gdGhpcy5maW5kTm9kZUJ5SWQodGhpcy5zY2VuZUNvbmZpZ1RyZWUsIGN1cnJlbnRTZWxlY3RlZE5vZGVJZCkNCg0KICAgICAgICAgIGlmIChub2RlVG9TZWxlY3QpIHsNCiAgICAgICAgICAgIC8vIOiuoeeul+W5tuiuvue9ruWxleW8gOi3r+W+hA0KICAgICAgICAgICAgY29uc3QgcGF0aElkcyA9IFtdDQogICAgICAgICAgICBjb25zdCBmaW5kUGF0aCA9IChub2RlcywgdGFyZ2V0SWQsIGN1cnJlbnRQYXRoID0gW10pID0+IHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBub2RlIG9mIG5vZGVzKSB7DQogICAgICAgICAgICAgICAgY29uc3QgbmV3UGF0aCA9IFsuLi5jdXJyZW50UGF0aCwgbm9kZS5pZF0NCiAgICAgICAgICAgICAgICBpZiAobm9kZS5pZCA9PT0gdGFyZ2V0SWQpIHsNCiAgICAgICAgICAgICAgICAgIHBhdGhJZHMucHVzaCguLi5uZXdQYXRoKQ0KICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgICBpZiAoZmluZFBhdGgobm9kZS5jaGlsZHJlbiwgdGFyZ2V0SWQsIG5ld1BhdGgpKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBmaW5kUGF0aCh0aGlzLnNjZW5lQ29uZmlnVHJlZSwgY3VycmVudFNlbGVjdGVkTm9kZUlkKQ0KICAgICAgICAgICAgdGhpcy50cmVlRXhwYW5kZWRLZXlzID0gcGF0aElkcy5zbGljZSgwLCAtMSkNCg0KICAgICAgICAgICAgLy8g5YWI6K6+572u6YCJ5Lit6IqC54K5DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkTm9kZSA9IG5vZGVUb1NlbGVjdA0KDQogICAgICAgICAgICAvLyDlvLrliLbmm7TmlrDmoJHnu4Tku7bnmoTpgInkuK3nirbmgIENCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICAgIC8vIOaooeaLn+eCueWHu+iKgueCueadpeW8uuWItuabtOaWsOmAieS4reeKtuaAgQ0KICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlU2NlbmVOb2RlQ2xpY2sobm9kZVRvU2VsZWN0KQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIGNvbnNvbGUubG9nKCfmj5DkuqTlhoXlrrk6Jywgc3VibWl0RGF0YSkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aPkOS6pOWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5o+Q5Lqk5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuc3VibWl0dGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBhZGRWaWRlb1NlZ21lbnQoKSB7DQogICAgICAvLyDlpoLmnpzljp/mlbDnu4TkuLrnqbrvvIzlhYjliJ3lp4vljJYNCiAgICAgIGlmICghdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0IHx8IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0ID0gW3sgdGltZTogJycsIHNjZW5lSWQ6ICcnLCBzY2VuZU5hbWU6ICcnLCBzY2VuZUNvZGU6ICcnIH1dDQogICAgICB9DQogICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3QucHVzaCh7IHRpbWU6ICcnLCBzY2VuZUlkOiAnJywgc2NlbmVOYW1lOiAnJywgc2NlbmVDb2RlOiAnJyB9KQ0KICAgIH0sDQogICAgcmVtb3ZlVmlkZW9TZWdtZW50KGlkeCkgew0KICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0LnNwbGljZShpZHgsIDEpDQogICAgfSwNCiAgICAvL+mAkuW9kumHjeaehOe7k+aehA0KICAgIGdldERlZXBUcmVlT3B0aW9ucyh0cmVlKSB7DQogICAgcmV0dXJuIHRyZWUubWFwKGl0ZW0gPT4gew0KICAgICAgLy8g5aSN5Yi25b2T5YmN6IqC54K555qE5Z+656GA5bGe5oCnDQogICAgICBjb25zdCBub2RlID0geyAuLi5pdGVtIH07DQogICAgICANCiAgICAgIC8vIOWmguaenOWtmOWcqCBjaGlsZHJlbiDkuJTkuI3kuLrnqbrvvIzliJnpgJLlvZLlpITnkIYNCiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICBub2RlLmNoaWxkcmVuID0gdGhpcy5nZXREZWVwVHJlZU9wdGlvbnMobm9kZS5jaGlsZHJlbik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlvZMgY2hpbGRyZW4g5Li656m65oiW5LiN5a2Y5Zyo5pe277yM5Yig6ZmkIGNoaWxkcmVuIOWxnuaAp++8iOWPr+mAie+8iQ0KICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbjsNCiAgICAgIH0NCiAgICAgIA0KICAgICAgcmV0dXJuIG5vZGU7DQogICAgfSk7DQogIH0sDQogICAgYXN5bmMgbG9hZFNjZW5lVHJlZU9wdGlvbnMoaWQpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOS7juaJgeW5s+WMluiPnOWNleaVsOaNruS4reiOt+WPluW9k+WJjeihjOS4mueahCBpbmR1c3RyeUNvZGUNCiAgICAgICAgY29uc3QgY3VycmVudEluZHVzdHJ5ID0gdGhpcy5mbGF0TWVudURhdGEuZmluZChpdGVtID0+IFN0cmluZyhpdGVtLmlkKSA9PT0gaWQpDQogICAgICAgIGNvbnN0IGluZHVzdHJ5Q29kZSA9IGN1cnJlbnRJbmR1c3RyeSA/IGN1cnJlbnRJbmR1c3RyeS5pbmR1c3RyeUNvZGUgOiBudWxsDQogICAgICAgIA0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBnZXRTY2VuZVRyZWVMaXN0KHsgaW5kdXN0cnlDb2RlOiBpbmR1c3RyeUNvZGUgfSkNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAwICYmIEFycmF5LmlzQXJyYXkocmVzLmRhdGEpKSB7DQogICAgICAgICAgdGhpcy5zY2VuZVRyZWVPcHRpb25zID0gdGhpcy5nZXREZWVwVHJlZU9wdGlvbnMocmVzLmRhdGEpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veWcuuaZr+agkeWksei0pTonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVNjZW5lQ2FzY2FkZXJDaGFuZ2UodmFsLCBpZHgpIHsNCiAgICAgIC8vIOehruS/neaVsOe7hOWSjOe0ouW8leS9jee9rueahOWvueixoeWtmOWcqA0KICAgICAgaWYgKCF0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3QgfHwgIXRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdFtpZHhdKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgDQogICAgICBjb25zdCBmaW5kU2NlbmUgPSAodHJlZSwgaWQpID0+IHsNCiAgICAgICAgZm9yIChjb25zdCBub2RlIG9mIHRyZWUpIHsNCiAgICAgICAgICBpZiAobm9kZS5pZCA9PT0gaWQpIHJldHVybiBub2RlDQogICAgICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgICAgIGNvbnN0IGZvdW5kID0gZmluZFNjZW5lKG5vZGUuY2hpbGRyZW4sIGlkKQ0KICAgICAgICAgICAgaWYgKGZvdW5kKSByZXR1cm4gZm91bmQNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIG51bGwNCiAgICAgIH0NCiAgICAgIGNvbnN0IG5vZGUgPSBmaW5kU2NlbmUodGhpcy5zY2VuZVRyZWVPcHRpb25zLCB2YWwpDQogICAgICBpZiAobm9kZSkgew0KICAgICAgICAvLyDorr7nva7lnLrmma9JROWSjOebuOWFs+S/oeaBrw0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb24udmlkZW9TZWdtZW50ZWRWb0xpc3RbaWR4XS5zY2VuZUlkID0gdmFsDQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdFtpZHhdLnNjZW5lTmFtZSA9IG5vZGUuc2NlbmVOYW1lDQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdFtpZHhdLnNjZW5lQ29kZSA9IG5vZGUuc2NlbmVDb2RlDQogICAgICB9DQogICAgfSwNCiAgICBpc1NjZW5lRGlzYWJsZWQoaWQsIGN1cnJlbnRJZHgpIHsNCiAgICAgIC8vIOmZpOW9k+WJjeWIhuauteWklu+8jOWFtuS7luWIhuauteW3sumAieeahGlkDQogICAgICByZXR1cm4gdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0LnNvbWUoKHNlZywgaWR4KSA9PiBpZHggIT09IGN1cnJlbnRJZHggJiYgc2VnLnNjZW5lSWQgPT09IGlkKQ0KICAgIH0sDQogICAgLy8g5bCG5Zy65pmv5qCR6L2s5o2i5Li65o6l5Y+j5qC85byPDQogICAgY29udmVydFNjZW5lVHJlZVRvQXBpKHNjZW5lVHJlZSkgew0KICAgICAgY29uc29sZS5sb2coIuaPkOS6pOeahOaVsOaNrjoiLCBzY2VuZVRyZWUpOw0KICAgICAgcmV0dXJuIHNjZW5lVHJlZS5tYXAobm9kZSA9PiAoew0KICAgICAgICBzY2VuZUluZm9JZDogbm9kZS5zY2VuZUluZm9JZCB8fCBudWxsLA0KICAgICAgICBzY2VuZUlkOiBub2RlLmlkLA0KICAgICAgICBwYXJhbUlkOiBub2RlLnBhcmVudCA/IG5vZGUucGFyZW50LmlkIDogbnVsbCwNCiAgICAgICAgc2NlbmVOYW1lOiBub2RlLm5hbWUsDQogICAgICAgIHNjZW5lQ29kZTogbm9kZS5jb2RlLA0KICAgICAgICB4OiBub2RlLnggfHwgbnVsbCwNCiAgICAgICAgeTogbm9kZS55IHx8IG51bGwsDQogICAgICAgIHR5cGU6IG5vZGUudHlwZSB8fCBudWxsLA0KICAgICAgICBzdGF0dXM6IG5vZGUuc3RhdHVzLA0KICAgICAgICBpc1VuZm9sZDogKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSA/IChub2RlLmlzVW5mb2xkIHx8ICcxJykgOiBudWxsLA0KICAgICAgICBkaXNwbGF5TG9jYXRpb246IChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgPyAobm9kZS5kaXNwbGF5TG9jYXRpb24gfHwgJzAnKSA6IG51bGwsDQogICAgICAgIHRyZWVDbGFzc2lmaWNhdGlvbjogKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSA/IChub2RlLnRyZWVDbGFzc2lmaWNhdGlvbiB8fCAnMycpIDogbnVsbCwNCiAgICAgICAgaW50cm9kdWNlVmlkZW9Wbzogbm9kZS5pbnRyb2R1Y2VWaWRlb1ZvID8gew0KICAgICAgICAgIGlkOiBub2RlLmludHJvZHVjZVZpZGVvVm8uaWQgfHwgbnVsbCwNCiAgICAgICAgICB0eXBlOiBub2RlLmludHJvZHVjZVZpZGVvVm8udHlwZSB8fCBudWxsLA0KICAgICAgICAgIHZpZXdJbmZvSWQ6IG5vZGUuaW50cm9kdWNlVmlkZW9Wby52aWV3SW5mb0lkIHx8IG51bGwsDQogICAgICAgICAgc3RhdHVzOiBub2RlLmludHJvZHVjZVZpZGVvVm8uc3RhdHVzIHx8IG51bGwsDQogICAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6IG5vZGUuaW50cm9kdWNlVmlkZW9Wby5iYWNrZ3JvdW5kSW1nRmlsZVVybCB8fCBudWxsLA0KICAgICAgICAgIGJhY2tncm91bmRGaWxlVXJsOiBub2RlLmludHJvZHVjZVZpZGVvVm8uYmFja2dyb3VuZEZpbGVVcmwgfHwgbnVsbA0KICAgICAgICB9IDogbnVsbCwNCiAgICAgICAgc2NlbmVUcmFkaXRpb25Wbzogbm9kZS50cmFkaXRpb24gPyB7DQogICAgICAgICAgbmFtZTogbm9kZS50cmFkaXRpb24ubmFtZSB8fCBudWxsLA0KICAgICAgICAgIHBhbm9yYW1pY1ZpZXdYbWxLZXk6IG5vZGUudHJhZGl0aW9uLnBhbm9yYW1pY1ZpZXdYbWxLZXkgfHwgbnVsbCwNCiAgICAgICAgICBzY2VuZVZpZGVvTGlzdDogbm9kZS50cmFkaXRpb24uYmFja2dyb3VuZFJlc291cmNlcyAmJiBub2RlLnRyYWRpdGlvbi5iYWNrZ3JvdW5kUmVzb3VyY2VzLmxlbmd0aCA/IA0KICAgICAgICAgICAgbm9kZS50cmFkaXRpb24uYmFja2dyb3VuZFJlc291cmNlcy5tYXAocmVzb3VyY2UgPT4gKHsNCiAgICAgICAgICAgICAgaWQ6IHJlc291cmNlLmlkIHx8IG51bGwsDQogICAgICAgICAgICAgIHRhZzogcmVzb3VyY2UubGFiZWwgfHwgbnVsbCwNCiAgICAgICAgICAgICAgd2lkZTogcmVzb3VyY2Uud2lkZSB8fCBudWxsLA0KICAgICAgICAgICAgICBoaWdoOiByZXNvdXJjZS5oaWdoIHx8IG51bGwsDQogICAgICAgICAgICAgIHN0YXR1czogcmVzb3VyY2Uuc3RhdHVzIHx8IG51bGwsDQogICAgICAgICAgICAgIHR5cGU6IDEsDQogICAgICAgICAgICAgIHZpZXdJbmZvSWQ6IHJlc291cmNlLnZpZXdJbmZvSWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgYmFja2dyb3VuZEltZ0ZpbGVVcmw6IHJlc291cmNlLmJnSW1nIHx8ICcnLA0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kRmlsZVVybDogcmVzb3VyY2UuYmdGaWxlIHx8ICcnLA0KICAgICAgICAgICAgICBzY2VuZUZpbGVSZWxMaXN0OiByZXNvdXJjZS5jb29yZGluYXRlcyAmJiByZXNvdXJjZS5jb29yZGluYXRlcy5sZW5ndGggPyANCiAgICAgICAgICAgICAgICByZXNvdXJjZS5jb29yZGluYXRlcy5tYXAoY29vcmQgPT4gKHsNCiAgICAgICAgICAgICAgICAgIGlkOiBjb29yZC5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICAgICAgZmlsZUlkOiBjb29yZC5maWxlSWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIGNsaWNrWDogY29vcmQueCAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLnggIT09IG51bGwgPyAoY29vcmQueCA9PT0gJycgPyAnJyA6IGNvb3JkLngpIDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIGNsaWNrWTogY29vcmQueSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLnkgIT09IG51bGwgPyAoY29vcmQueSA9PT0gJycgPyAnJyA6IGNvb3JkLnkpIDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIHdpZGU6IGNvb3JkLndpZGUgIT09IHVuZGVmaW5lZCAmJiBjb29yZC53aWRlICE9PSBudWxsID8gKGNvb3JkLndpZGUgPT09ICcnIHx8IGNvb3JkLndpZGUgPT09IDAgPyAnJyA6IGNvb3JkLndpZGUpIDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIGhpZ2g6IGNvb3JkLmhpZ2ggIT09IHVuZGVmaW5lZCAmJiBjb29yZC5oaWdoICE9PSBudWxsID8gKGNvb3JkLmhpZ2ggPT09ICcnIHx8IGNvb3JkLmhpZ2ggPT09IDAgPyAnJyA6IGNvb3JkLmhpZ2gpIDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIHhtbEtleTogY29vcmQueG1sS2V5ICE9PSB1bmRlZmluZWQgJiYgY29vcmQueG1sS2V5ICE9PSBudWxsID8gKGNvb3JkLnhtbEtleSA9PT0gJycgPyAnJyA6IGNvb3JkLnhtbEtleSkgOiBudWxsLA0KICAgICAgICAgICAgICAgICAgYmluZFNjZW5lQ29kZTogY29vcmQuc2NlbmVDb2RlICE9PSB1bmRlZmluZWQgJiYgY29vcmQuc2NlbmVDb2RlICE9PSBudWxsID8gKGNvb3JkLnNjZW5lQ29kZSA9PT0gJycgPyAnJyA6IGNvb3JkLnNjZW5lQ29kZSkgOiBudWxsDQogICAgICAgICAgICAgICAgfSkpIDogW10NCiAgICAgICAgICAgIH0pKSA6IG51bGwsDQogICAgICAgICAgcGFpblBvaW50TGlzdDogbm9kZS50cmFkaXRpb24ucGFpblBvaW50cyAmJiBub2RlLnRyYWRpdGlvbi5wYWluUG9pbnRzLmxlbmd0aCA/IA0KICAgICAgICAgICAgbm9kZS50cmFkaXRpb24ucGFpblBvaW50cy5tYXAocGFpbiA9PiAoew0KICAgICAgICAgICAgICBwYWluUG9pbnRJZDogcGFpbi5wYWluUG9pbnRJZCB8fCBudWxsLA0KICAgICAgICAgICAgICBiaWdUaXRsZTogcGFpbi50aXRsZSB8fCBudWxsLA0KICAgICAgICAgICAgICBjb250ZW50OiBwYWluLmNvbnRlbnRzIHx8IFtdLA0KICAgICAgICAgICAgICBkaXNwbGF5VGltZTogcGFpbi5zaG93VGltZSB8fCBudWxsDQogICAgICAgICAgICB9KSkgOiBudWxsDQogICAgICAgIH0gOiBudWxsLA0KICAgICAgICBzY2VuZTVnVm86IG5vZGUud2lzZG9tNWcgPyB7DQogICAgICAgICAgbmFtZTogbm9kZS53aXNkb201Zy5uYW1lIHx8IG51bGwsDQogICAgICAgICAgcGFub3JhbWljVmlld1htbEtleTogbm9kZS53aXNkb201Zy5wYW5vcmFtaWNWaWV3WG1sS2V5IHx8IG51bGwsDQogICAgICAgICAgc2NlbmVWaWRlb0xpc3Q6IG5vZGUud2lzZG9tNWcuYmFja2dyb3VuZFJlc291cmNlcyAmJiBub2RlLndpc2RvbTVnLmJhY2tncm91bmRSZXNvdXJjZXMubGVuZ3RoID8gDQogICAgICAgICAgICBub2RlLndpc2RvbTVnLmJhY2tncm91bmRSZXNvdXJjZXMubWFwKHJlc291cmNlID0+ICh7DQogICAgICAgICAgICAgIGlkOiByZXNvdXJjZS5pZCB8fCBudWxsLA0KICAgICAgICAgICAgICB0YWc6IHJlc291cmNlLnRhZyB8fCBudWxsLA0KICAgICAgICAgICAgICBzdGF0dXM6IHJlc291cmNlLnN0YXR1cyB8fCBudWxsLA0KICAgICAgICAgICAgICB0eXBlOiAyLA0KICAgICAgICAgICAgICB2aWV3SW5mb0lkOiByZXNvdXJjZS52aWV3SW5mb0lkIHx8IG51bGwsDQogICAgICAgICAgICAgIGJhY2tncm91bmRJbWdGaWxlVXJsOiByZXNvdXJjZS5iZ0ltZyB8fCAnJywNCiAgICAgICAgICAgICAgYmFja2dyb3VuZEZpbGVVcmw6IHJlc291cmNlLmJnRmlsZSB8fCAnJywNCiAgICAgICAgICAgICAgc2NlbmVGaWxlUmVsTGlzdDogcmVzb3VyY2UuY29vcmRpbmF0ZXMgJiYgcmVzb3VyY2UuY29vcmRpbmF0ZXMubGVuZ3RoID8gDQogICAgICAgICAgICAgICAgcmVzb3VyY2UuY29vcmRpbmF0ZXMubWFwKGNvb3JkID0+ICh7DQogICAgICAgICAgICAgICAgICBpZDogY29vcmQuaWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgICAgIGZpbGVJZDogY29vcmQuZmlsZUlkIHx8IG51bGwsDQogICAgICAgICAgICAgICAgICBjbGlja1g6IGNvb3JkLnggIT09IHVuZGVmaW5lZCAmJiBjb29yZC54ICE9PSBudWxsID8gKGNvb3JkLnggPT09ICcnID8gJycgOiBjb29yZC54KSA6IG51bGwsDQogICAgICAgICAgICAgICAgICBjbGlja1k6IGNvb3JkLnkgIT09IHVuZGVmaW5lZCAmJiBjb29yZC55ICE9PSBudWxsID8gKGNvb3JkLnkgPT09ICcnID8gJycgOiBjb29yZC55KSA6IG51bGwsDQogICAgICAgICAgICAgICAgICB3aWRlOiBjb29yZC53aWRlICE9PSB1bmRlZmluZWQgJiYgY29vcmQud2lkZSAhPT0gbnVsbCA/IChjb29yZC53aWRlID09PSAnJyB8fCBjb29yZC53aWRlID09PSAwID8gJycgOiBjb29yZC53aWRlKSA6IG51bGwsDQogICAgICAgICAgICAgICAgICBoaWdoOiBjb29yZC5oaWdoICE9PSB1bmRlZmluZWQgJiYgY29vcmQuaGlnaCAhPT0gbnVsbCA/IChjb29yZC5oaWdoID09PSAnJyB8fCBjb29yZC5oaWdoID09PSAwID8gJycgOiBjb29yZC5oaWdoKSA6IG51bGwsDQogICAgICAgICAgICAgICAgICB4bWxLZXk6IGNvb3JkLnhtbEtleSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLnhtbEtleSAhPT0gbnVsbCA/IChjb29yZC54bWxLZXkgPT09ICcnID8gJycgOiBjb29yZC54bWxLZXkpIDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIGJpbmRTY2VuZUNvZGU6IGNvb3JkLnNjZW5lQ29kZSAhPT0gdW5kZWZpbmVkICYmIGNvb3JkLnNjZW5lQ29kZSAhPT0gbnVsbCA/IChjb29yZC5zY2VuZUNvZGUgPT09ICcnID8gJycgOiBjb29yZC5zY2VuZUNvZGUpIDogbnVsbA0KICAgICAgICAgICAgICAgIH0pKSA6IFtdDQogICAgICAgICAgICB9KSkgOiBudWxsLA0KICAgICAgICAgIHBhaW5Qb2ludExpc3Q6IG5vZGUud2lzZG9tNWcucGFpblBvaW50cyAmJiBub2RlLndpc2RvbTVnLnBhaW5Qb2ludHMubGVuZ3RoID8gDQogICAgICAgICAgICBub2RlLndpc2RvbTVnLnBhaW5Qb2ludHMubWFwKHBhaW4gPT4gKHsNCiAgICAgICAgICAgICAgcGFpblBvaW50SWQ6IHBhaW4ucGFpblBvaW50SWQgfHwgbnVsbCwNCiAgICAgICAgICAgICAgYmlnVGl0bGU6IHBhaW4udGl0bGUgfHwgbnVsbCwNCiAgICAgICAgICAgICAgY29udGVudDogcGFpbi5jb250ZW50cyB8fCBbXSwNCiAgICAgICAgICAgICAgZGlzcGxheVRpbWU6IHBhaW4uc2hvd1RpbWUgfHwgbnVsbA0KICAgICAgICAgICAgfSkpIDogbnVsbA0KICAgICAgICB9IDogbnVsbCwNCiAgICAgICAgY29zdEVzdGltYXRpb25JbmZvVm86IG5vZGUuY29zdEVzdGltYXRlID8gew0KICAgICAgICAgIHBhaW5Qb2ludElkOiBub2RlLmNvc3RFc3RpbWF0ZS5wYWluUG9pbnRJZCB8fCBudWxsLA0KICAgICAgICAgIHN0YXR1czogbm9kZS5jb3N0RXN0aW1hdGUuc3RhdHVzIHx8ICcwJywNCiAgICAgICAgICBiaWdUaXRsZTogbm9kZS5jb3N0RXN0aW1hdGUudGl0bGUgfHwgbnVsbCwNCiAgICAgICAgICBjb250ZW50OiBub2RlLmNvc3RFc3RpbWF0ZS5jb250ZW50cyAmJiBub2RlLmNvc3RFc3RpbWF0ZS5jb250ZW50cy5sZW5ndGggPyBub2RlLmNvc3RFc3RpbWF0ZS5jb250ZW50cyA6IG51bGwNCiAgICAgICAgfSA6IG51bGwsDQogICAgICAgIGNoaWxkcmVuOiBub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID8gdGhpcy5jb252ZXJ0U2NlbmVUcmVlVG9BcGkobm9kZS5jaGlsZHJlbikgOiBbXQ0KICAgICAgfSkpDQogICAgfSwNCiAgICBoYW5kbGVUaW1lQ2hhbmdlKHZhbCwgaWR4KSB7DQogICAgICAvLyDnoa7kv53mlbDnu4Tlt7LliJ3lp4vljJYNCiAgICAgIGlmICghdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0IHx8IHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uLnZpZGVvU2VnbWVudGVkVm9MaXN0ID0gW3sgdGltZTogMCwgc2NlbmVJZDogJycsIHNjZW5lTmFtZTogJycsIHNjZW5lQ29kZTogJycgfV0NCiAgICAgIH0NCiAgICAgIC8vIOabtOaWsOWvueW6lOS9jee9rueahOaXtumXtOWAvA0KICAgICAgaWYgKHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdFtpZHhdKSB7DQogICAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbi52aWRlb1NlZ21lbnRlZFZvTGlzdFtpZHhdLnRpbWUgPSB2YWwNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOagueaNrnNjZW5lQ29kZeafpeaJvuWvueW6lOeahHNjZW5lSWQNCiAgICBmaW5kU2NlbmVJZEJ5Q29kZShzY2VuZUNvZGUpIHsNCiAgICAgIGlmICghc2NlbmVDb2RlIHx8ICF0aGlzLnNjZW5lVHJlZU9wdGlvbnMpIHJldHVybiAnJw0KICAgICAgDQogICAgICBjb25zdCBmaW5kSW5UcmVlID0gKHRyZWUpID0+IHsNCiAgICAgICAgZm9yIChjb25zdCBub2RlIG9mIHRyZWUpIHsNCiAgICAgICAgICBpZiAobm9kZS5zY2VuZUNvZGUgPT09IHNjZW5lQ29kZSkgew0KICAgICAgICAgICAgcmV0dXJuIG5vZGUuaWQNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgICAgIGNvbnN0IGZvdW5kID0gZmluZEluVHJlZShub2RlLmNoaWxkcmVuKQ0KICAgICAgICAgICAgaWYgKGZvdW5kKSByZXR1cm4gZm91bmQNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIG51bGwNCiAgICAgIH0NCiAgICAgIA0KICAgICAgcmV0dXJuIGZpbmRJblRyZWUodGhpcy5zY2VuZVRyZWVPcHRpb25zKSB8fCAnJw0KICAgIH0sDQogICAgLy8g5aSE55CG6IOM5pmv5paH5Lu25Yig6ZmkDQogICAgaGFuZGxlUmVtb3ZlQmdGaWxlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmZvcm0uYmdGaWxlVXJsID0gJycNCiAgICAgIHRoaXMuZm9ybS5iZ0ltZ1VybCA9ICcnIC8vIOWQjOaXtua4heepuuiDjOaZr+WbvueJh+mmluW4pw0KICAgICAgdGhpcy5iZ0ZpbGVMaXN0ID0gW10NCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5paH5Lu25bey5Yig6ZmkJykNCiAgICB9LA0KICAgIC8vIOabtOaWsOiDjOaZr+aWh+S7tuWIl+ihqA0KICAgIHVwZGF0ZUJnRmlsZUxpc3QoKSB7DQogICAgICBpZiAodGhpcy5mb3JtLmJnRmlsZVVybCkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHRoaXMuZm9ybS5iZ0ZpbGVVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICB0aGlzLmJnRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdGhpcy5mb3JtLmJnRmlsZVVybCwNCiAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgfV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuYmdGaWxlTGlzdCA9IFtdDQogICAgICB9DQogICAgfSwNCiAgICAvLyDlpITnkIbku4vnu43op4bpopHmlofku7bliKDpmaQNCiAgICBoYW5kbGVSZW1vdmVJbnRyb2R1Y2VWaWRlb0ZpbGUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEZpbGVVcmwgPSAnJw0KICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlby5iYWNrZ3JvdW5kSW1nRmlsZVVybCA9ICcnIC8vIOWQjOaXtua4heepuummluW4p+WbvueJhw0KICAgICAgdGhpcy5pbnRyb2R1Y2VWaWRlb0ZpbGVMaXN0ID0gW10NCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LuL57uN6KeG6aKR5bey5Yig6ZmkJykNCiAgICB9LA0KICAgIC8vIOabtOaWsOS7i+e7jeinhumikeaWh+S7tuWIl+ihqA0KICAgIHVwZGF0ZUludHJvZHVjZVZpZGVvRmlsZUxpc3QoKSB7DQogICAgICBpZiAodGhpcy5pbnRyb2R1Y2VWaWRlby5iYWNrZ3JvdW5kRmlsZVVybCkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHRoaXMuaW50cm9kdWNlVmlkZW8uYmFja2dyb3VuZEZpbGVVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdGhpcy5pbnRyb2R1Y2VWaWRlby5iYWNrZ3JvdW5kRmlsZVVybCwNCiAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgfV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaW50cm9kdWNlVmlkZW9GaWxlTGlzdCA9IFtdDQogICAgICB9DQogICAgfSwNCiAgICAvLyDlpITnkIborrLop6Pop4bpopHmlofku7bliKDpmaQNCiAgICBoYW5kbGVSZW1vdmVWaWRlb0V4cGxhbmF0aW9uRmlsZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uLmJhY2tncm91bmRGaWxlVXJsID0gJycNCiAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0ID0gW10NCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6K6y6Kej6KeG6aKR5bey5Yig6ZmkJykNCiAgICB9LA0KICAgIC8vIOabtOaWsOiusuino+inhumikeaWh+S7tuWIl+ihqA0KICAgIHVwZGF0ZVZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCgpIHsNCiAgICAgIGlmICh0aGlzLnZpZGVvRXhwbGFuYXRpb24uYmFja2dyb3VuZEZpbGVVcmwpIHsNCiAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB0aGlzLnZpZGVvRXhwbGFuYXRpb24uYmFja2dyb3VuZEZpbGVVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCA9IFt7DQogICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgdXJsOiB0aGlzLnZpZGVvRXhwbGFuYXRpb24uYmFja2dyb3VuZEZpbGVVcmwsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnZpZGVvRXhwbGFuYXRpb25GaWxlTGlzdCA9IFtdDQogICAgICB9DQogICAgfSwNCiAgICAvLyDlpITnkIZYTUzmlofku7bliKDpmaQNCiAgICBoYW5kbGVSZW1vdmVYbWxGaWxlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmZvcm0ucGFub3JhbWljVmlld1htbFVybCA9ICcnDQogICAgICB0aGlzLnhtbEZpbGVMaXN0ID0gW10NCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcygnWE1M5paH5Lu25bey5Yig6ZmkJykNCiAgICB9LA0KICAgIA0KICAgIC8vIOabtOaWsFhNTOaWh+S7tuWIl+ihqA0KICAgIHVwZGF0ZVhtbEZpbGVMaXN0KCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5wYW5vcmFtaWNWaWV3WG1sVXJsKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gdGhpcy5mb3JtLnBhbm9yYW1pY1ZpZXdYbWxVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICB0aGlzLnhtbEZpbGVMaXN0ID0gW3sNCiAgICAgICAgICBuYW1lOiBmaWxlTmFtZSwNCiAgICAgICAgICB1cmw6IHRoaXMuZm9ybS5wYW5vcmFtaWNWaWV3WG1sVXJsLA0KICAgICAgICAgIHVpZDogRGF0ZS5ub3coKQ0KICAgICAgICB9XQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy54bWxGaWxlTGlzdCA9IFtdDQogICAgICB9DQogICAgfSwNCiAgICAvLyDlm77niYfpooTop4gNCiAgICBwcmV2aWV3SW1hZ2UodXJsKSB7DQogICAgICBpZiAodXJsKSB7DQogICAgICAgIHRoaXMucHJldmlld0ltYWdlVXJsID0gdXJsDQogICAgICAgIHRoaXMucHJldmlld1Zpc2libGUgPSB0cnVlDQogICAgICB9DQogICAgfSwNCiAgICBjbG9zZVByZXZpZXcoKSB7DQogICAgICB0aGlzLnByZXZpZXdWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMucHJldmlld0ltYWdlVXJsID0gJycNCiAgICB9LA0KICAgIC8vIOWIoOmZpOiDjOaZr+WbvueJhw0KICAgIGRlbGV0ZUJnSW1hZ2UoKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTmraTlm77niYflkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmZvcm0uYmdJbWdVcmwgPSAnJw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WbvueJh+W3suWIoOmZpCcpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIGFzeW5jIGJlZm9yZVVwbG9hZFhtbEZpbGUoZmlsZSkgew0KICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6LDQogICAgICBpZiAoIWZpbGUubmFtZS50b0xvd2VyQ2FzZSgpLmVuZHNXaXRoKCcueG1sJykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5LygWE1M5paH5Lu277yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOajgOafpeaWh+S7tuWkp+Wwj++8iDUwTULvvIkNCiAgICAgIGlmIChmaWxlLnNpemUgPiA1MCAqIDEwMjQgKiAxMDI0KSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzUwTULvvIEnKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIA0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5LiK5LygWE1M5paH5Lu277yM6K+356iN5YCZLi4uIikNCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKQ0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2luZHVzdHJ5Q29kZScsIHRoaXMuaW5kdXN0cnlDb2RlKQ0KICAgICAgICANCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgdXBsb2FkU2NlbmVGaWxlKGZvcm1EYXRhKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDAgJiYgcmVzLmRhdGEpIHsNCiAgICAgICAgICAvLyDorr7nva5YTUzmlofku7ZVUkwNCiAgICAgICAgICB0aGlzLmZvcm0ucGFub3JhbWljVmlld1htbFVybCA9IHJlcy5kYXRhLmZpbGVVcmwNCiAgICAgICAgICANCiAgICAgICAgICAvLyDnm7TmjqXopobnm5ZYTUzmlofku7bliJfooagNCiAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHJlcy5kYXRhLmZpbGVVcmwuc3BsaXQoJy8nKS5wb3AoKQ0KICAgICAgICAgIHRoaXMueG1sRmlsZUxpc3QgPSBbew0KICAgICAgICAgICAgbmFtZTogZmlsZU5hbWUsDQogICAgICAgICAgICB1cmw6IHJlcy5kYXRhLmZpbGVVcmwsDQogICAgICAgICAgICB1aWQ6IERhdGUubm93KCkNCiAgICAgICAgICB9XQ0KICAgICAgICAgIA0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiK5Lyg5oiQ5YqfJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+S4iuS8oOWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZhbHNlDQogICAgfSwNCiAgICAvLyDkuLvpopjlj5jmm7Tlm57osIMNCiAgICBvblRoZW1lQ2hhbmdlKHRoZW1lKSB7DQogICAgICAvLyDlpoLmnpzkuLvpopjmnInpu5jorqTog4zmma/lm77vvIzlj6/ku6Xoh6rliqjorr7nva4NCiAgICAgIGlmICh0aGVtZSAmJiB0aGVtZS5kZWZhdWx0QmdJbWFnZSkgew0KICAgICAgICB0aGlzLmZvcm0uYmdJbWdVcmwgPSB0aGVtZS5kZWZhdWx0QmdJbWFnZQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5qC85byP5YyW5Z2Q5qCH5pWw5o2u55So5LqO5o+Q5LqkDQogICAgZm9ybWF0Q29vcmRpbmF0ZXNGb3JTdWJtaXQoY29vcmRpbmF0ZXMpIHsNCiAgICAgIGlmICghY29vcmRpbmF0ZXMgfHwgIUFycmF5LmlzQXJyYXkoY29vcmRpbmF0ZXMpKSB7DQogICAgICAgIHJldHVybiB7IGNsaWNrWDogJycsIGNsaWNrWTogJycgfQ0KICAgICAgfQ0KICAgICAgDQogICAgICBjb25zdCB4VmFsdWVzID0gY29vcmRpbmF0ZXMubWFwKGNvb3JkID0+IGNvb3JkLnggfHwgJzAnKS5qb2luKCcsJykNCiAgICAgIGNvbnN0IHlWYWx1ZXMgPSBjb29yZGluYXRlcy5tYXAoY29vcmQgPT4gY29vcmQueSB8fCAnMCcpLmpvaW4oJywnKQ0KICAgICAgDQogICAgICByZXR1cm4gew0KICAgICAgICBjbGlja1g6IHhWYWx1ZXMsDQogICAgICAgIGNsaWNrWTogeVZhbHVlcw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6Kej5p6Q5Z2Q5qCH5a2X56ym5Liy5Li65Z2Q5qCH5pWw57uEDQogICAgcGFyc2VDb29yZGluYXRlc0Zyb21BcGkoY2xpY2tYLCBjbGlja1kpIHsNCiAgICAgIGNvbnN0IHhBcnJheSA9IGNsaWNrWCA/IGNsaWNrWC5zcGxpdCgnLCcpIDogWycnXQ0KICAgICAgY29uc3QgeUFycmF5ID0gY2xpY2tZID8gY2xpY2tZLnNwbGl0KCcsJykgOiBbJyddDQogICAgICANCiAgICAgIC8vIOWPlui+g+mVv+eahOaVsOe7hOmVv+W6pu+8jOehruS/neWdkOagh+Wvuem9kA0KICAgICAgY29uc3QgbWF4TGVuZ3RoID0gTWF0aC5tYXgoeEFycmF5Lmxlbmd0aCwgeUFycmF5Lmxlbmd0aCkNCiAgICAgIGNvbnN0IGNvb3JkaW5hdGVzID0gW10NCiAgICAgIA0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBtYXhMZW5ndGg7IGkrKykgew0KICAgICAgICBjb29yZGluYXRlcy5wdXNoKHsNCiAgICAgICAgICB4OiB4QXJyYXlbaV0gfHwgJycsDQogICAgICAgICAgeTogeUFycmF5W2ldIHx8ICcnDQogICAgICAgIH0pDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOiHs+WwkeS/neivgeacieS4gOS4quWdkOagh+e7hA0KICAgICAgcmV0dXJuIGNvb3JkaW5hdGVzLmxlbmd0aCA+IDAgPyBjb29yZGluYXRlcyA6IFt7IHg6ICcnLCB5OiAnJyB9XQ0KICAgIH0sDQogICAgLy8g5byA5aeL57yW6L6R5qCH6aKYDQogICAgc3RhcnRFZGl0VGl0bGUoaW5kZXgpIHsNCiAgICAgIGNvbnN0IGNhdGVnb3J5ID0gdGhpcy5jYXRlZ29yaWVzW2luZGV4XQ0KICAgICAgY2F0ZWdvcnkuZWRpdGluZyA9IHRydWUNCiAgICAgIGNhdGVnb3J5LmVkaXRpbmdOYW1lID0gY2F0ZWdvcnkubmFtZQ0KICAgICAgDQogICAgICAvLyDkuIvkuIDluKfogZrnhKbovpPlhaXmoYYNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g5L2/55So5Yqo5oCBcmVm5ZCN56ewDQogICAgICAgIGNvbnN0IGlucHV0UmVmID0gdGhpcy4kcmVmc1tgdGl0bGVJbnB1dF8ke2luZGV4fWBdDQogICAgICAgIGlmIChpbnB1dFJlZiAmJiBpbnB1dFJlZlswXSkgew0KICAgICAgICAgIGlucHV0UmVmWzBdLmZvY3VzKCkNCiAgICAgICAgICBpbnB1dFJlZlswXS5zZWxlY3QoKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDlrozmiJDnvJbovpHmoIfpopgNCiAgICBmaW5pc2hFZGl0VGl0bGUoaW5kZXgpIHsNCiAgICAgIGNvbnN0IGNhdGVnb3J5ID0gdGhpcy5jYXRlZ29yaWVzW2luZGV4XQ0KICAgICAgaWYgKGNhdGVnb3J5LmVkaXRpbmdOYW1lICYmIGNhdGVnb3J5LmVkaXRpbmdOYW1lLnRyaW0oKSkgew0KICAgICAgICBjYXRlZ29yeS5uYW1lID0gY2F0ZWdvcnkuZWRpdGluZ05hbWUudHJpbSgpDQogICAgICB9DQogICAgICBjYXRlZ29yeS5lZGl0aW5nID0gZmFsc2UNCiAgICAgIGNhdGVnb3J5LmVkaXRpbmdOYW1lID0gJycNCiAgICB9LA0KDQogICAgLy8g5Y+W5raI57yW6L6R5qCH6aKYDQogICAgY2FuY2VsRWRpdFRpdGxlKGluZGV4KSB7DQogICAgICBjb25zdCBjYXRlZ29yeSA9IHRoaXMuY2F0ZWdvcmllc1tpbmRleF0NCiAgICAgIGNhdGVnb3J5LmVkaXRpbmcgPSBmYWxzZQ0KICAgICAgY2F0ZWdvcnkuZWRpdGluZ05hbWUgPSAnJw0KICAgIH0sDQogICAgLy8g6K6+572u5LiK5Lyg5qih5byPDQogICAgc2V0VXBsb2FkTW9kZSh0eXBlLCBtb2RlKSB7DQogICAgICB0aGlzLiRzZXQodGhpcy51cGxvYWRNb2RlcywgdHlwZSwgbW9kZSkNCiAgICB9LA0KICAgIC8vIOiDjOaZr+aWh+S7tumTvuaOpei+k+WFpeWkhOeQhg0KICAgIGhhbmRsZUJnRmlsZVVybElucHV0KHZhbHVlKSB7DQogICAgICB0aGlzLmJnRmlsZUxpc3QgPSBbXQ0KICAgICAgaWYgKHZhbHVlKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gdmFsdWUuc3BsaXQoJy8nKS5wb3AoKSB8fCAn5aSW6YOo6ZO+5o6l5paH5Lu2Jw0KICAgICAgICB0aGlzLmJnRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdmFsdWUsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9DQogICAgfSwNCiAgICAvLyDop4bpopHorrLop6Ppk77mjqXovpPlhaXlpITnkIYNCiAgICBoYW5kbGVWaWRlb0V4cGxhbmF0aW9uVXJsSW5wdXQodmFsdWUpIHsNCiAgICAgIHRoaXMudmlkZW9FeHBsYW5hdGlvbkZpbGVMaXN0ID0gW10NCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHZhbHVlLnNwbGl0KCcvJykucG9wKCkgfHwgJ+WklumDqOmTvuaOpeaWh+S7ticNCiAgICAgICAgdGhpcy52aWRlb0V4cGxhbmF0aW9uRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdmFsdWUsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9DQogICAgfSwNCiAgICAvLyDku4vnu43op4bpopHpk77mjqXovpPlhaXlpITnkIYNCiAgICBoYW5kbGVJbnRyb2R1Y2VWaWRlb1VybElucHV0KHZhbHVlKSB7DQogICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbXQ0KICAgICAgaWYgKHZhbHVlKSB7DQogICAgICAgIGNvbnN0IGZpbGVOYW1lID0gdmFsdWUuc3BsaXQoJy8nKS5wb3AoKSB8fCAn5aSW6YOo6ZO+5o6l5paH5Lu2Jw0KICAgICAgICB0aGlzLmludHJvZHVjZVZpZGVvRmlsZUxpc3QgPSBbew0KICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgIHVybDogdmFsdWUsDQogICAgICAgICAgdWlkOiBEYXRlLm5vdygpDQogICAgICAgIH1dDQogICAgICB9DQogICAgfSwNCiAgICAvLyDlkIzmraXmlofku7YNCiAgICBhc3luYyBoYW5kbGVTeW5jaHJvbml6ZUZpbGUoKSB7DQogICAgICBpZiAoIXRoaXMuZm9ybS5zY2VuZVZpZXdDb25maWdJZCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOS/neWtmOmFjee9ruWQjuWGjeWQjOatpeaWh+S7ticpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgDQogICAgICB0cnkgew0KICAgICAgICB0aGlzLnN5bmNocm9uaXppbmcgPSB0cnVlDQogICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWQjOatpeaWh+S7tu+8jOivt+eojeWAmS4uLiIpDQogICAgICAgIA0KICAgICAgICAvLyDkvb/nlKhGb3JtRGF0YeaIllVSTFNlYXJjaFBhcmFtc+S8oOmAkuihqOWNleWPguaVsA0KICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgndmlld0NvbmZpZ0lkJywgdGhpcy5mb3JtLnNjZW5lVmlld0NvbmZpZ0lkKQ0KICAgICAgICANCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgc3luY2hyb25pemF0aW9uRmlsZShmb3JtRGF0YSkNCiAgICAgICAgDQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXMubXNnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5paH5Lu25ZCM5q2l5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5ZCM5q2l5paH5Lu25aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blkIzmraXlpLHotKUnKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5zeW5jaHJvbml6aW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCkNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0VA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <!-- ✅ 左侧菜单区域 -->\r\n    <div class=\"menu-panel\">\r\n      <!-- 搜索框 -->\r\n      <div class=\"menu-search\">\r\n        <el-input\r\n          v-model=\"searchKeyword\"\r\n          placeholder=\"搜索菜单...\"\r\n          prefix-icon=\"el-icon-search\"\r\n          clearable\r\n          @input=\"handleSearch\"\r\n        />\r\n      </div>\r\n      \r\n      <el-tree\r\n        ref=\"menuTree\"\r\n        :data=\"filteredMenuData\"\r\n        :props=\"{ label: 'name', children: 'children' }\"\r\n        node-key=\"id\"\r\n        :current-node-key=\"activeMenu\"\r\n        @node-click=\"handleTreeNodeClick\"\r\n        highlight-current\r\n        :expand-on-click-node=\"false\"\r\n        :default-expanded-keys=\"menuData.map(item => item.id)\"\r\n        class=\"menu-tree\"\r\n      >\r\n        <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\r\n          <span v-html=\"highlightText(data.name)\"></span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n\r\n    <!-- ✅ 右侧内容区域 -->\r\n    <div class=\"content-panel\" :class=\"{ 'loading-no-scroll': switchingIndustry }\" v-loading=\"switchingIndustry\" element-loading-text=\"正在切换行业...\">\r\n      <el-form :model=\"form\" ref=\"sceneForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <!-- 左侧：基本信息 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"mini-block\" shadow=\"never\">\r\n              <div slot=\"header\">基本信息</div>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"主标题\" required>\r\n                    <el-input v-model=\"form.mainTitle\" placeholder=\"请输入主标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"副标题\" required>\r\n                    <el-input v-model=\"form.subTitle\" placeholder=\"请输入副标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"beforeUploadIntroduceImg\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"form.bgImgUrl\" class=\"image-preview-container\">\r\n                        <img :src=\"form.bgImgUrl\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(form.bgImgUrl)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBgImage\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"主题选择\">\r\n                    <theme-selection-dialog \r\n                      v-model=\"selectedTheme\"\r\n                      @change=\"onThemeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\">\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.bgFile || 'upload'\" @input=\"value => setUploadMode('bgFile', value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.bgFile || 'upload') === 'upload'\"\r\n                      class=\"upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"bgFileList\"\r\n                      :before-upload=\"file => beforeUploadIntroduceVideo(file)\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveBgFile\"\r\n                    >\r\n                      <el-button type=\"primary\">上传背景文件</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"form.bgFileUrl\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"handleBgFileUrlInput\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"XML文件\">\r\n                    <el-upload\r\n                      class=\"upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"xmlFileList\"\r\n                      accept=\".xml\"\r\n                      :before-upload=\"beforeUploadXmlFile\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveXmlFile\"\r\n                    >\r\n                      <el-button type=\"primary\">上传XML文件</el-button>\r\n                      <div slot=\"tip\" class=\"el-upload__tip\">只能上传xml文件，且不超过50MB</div>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-card>\r\n          </el-col>\r\n          \r\n          <!-- 右侧：视频讲解 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"mini-block video-card\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"video-header-row\">\r\n                <span>视频讲解</span>\r\n                <el-switch v-model=\"videoExplanation.status\" :active-value=\"'0'\" :inactive-value=\"'1'\" style=\"float:right;\" />\r\n              </div>\r\n              <div v-show=\"videoExplanation.status === '0'\">\r\n              <el-collapse-transition>\r\n                <div class=\"video-card-yu\">\r\n                  <el-form-item label=\"讲解视频\">\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.videoExplanation || 'upload'\" @input=\"value => setUploadMode('videoExplanation', value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.videoExplanation || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"videoExplanationFileList\"\r\n                      accept=\".mp4\"\r\n                      :before-upload=\"file => beforeUploadIntroduceVideo(file, 'videoExplanation', 'backgroundFileUrl')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveVideoExplanationFile\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                      <div slot=\"tip\" class=\"el-upload__tip\">只能上传mp4文件</div>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"videoExplanation.backgroundFileUrl\"\r\n                      placeholder=\"请输入视频链接\"\r\n                      @input=\"handleVideoExplanationUrlInput\"\r\n                    />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"视频分段说明\">\r\n                    <div class=\"segment-scroll\">\r\n                      <div v-for=\"(seg, idx) in videoSegmentedList\" :key=\"idx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                        <el-input-number\r\n                          v-model=\"seg.time\"\r\n                          :min=\"0\"\r\n                          :max=\"999999\"\r\n                          placeholder=\"时间\"\r\n                          style=\"width: 120px; margin-right: 10px;\"\r\n                          @change=\"val => handleTimeChange(val, idx)\"\r\n                        />\r\n                        <span style=\"margin-right: 10px; color: #606266;\">秒</span>\r\n                        <el-cascader\r\n                          v-model=\"seg.sceneId\"\r\n                          :options=\"sceneTreeOptions\"\r\n                          :props=\"sceneCascaderProps\"\r\n                          filterable\r\n                          check-strictly\r\n                          placeholder=\"所属场景\"\r\n                          style=\"width: 200px; margin-right: 10px;\"\r\n                          @change=\"val => handleSceneCascaderChange(val, idx)\"\r\n                        />\r\n                        <el-button type=\"danger\" icon=\"el-icon-delete\" circle @click=\"removeVideoSegment(idx)\" />\r\n                      </div>\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addVideoSegment\" style=\"margin-top: 8px;\">增加分段</el-button>\r\n                  </el-form-item>\r\n                </div>\r\n              </el-collapse-transition>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <!-- ✅ 分类区域（支持折叠） -->\r\n      <div\r\n        v-for=\"(category, index) in categories\"\r\n        :key=\"category.key\"\r\n        class=\"category-block\"\r\n      >\r\n        <div class=\"category-header\">\r\n          <div class=\"category-title\" @dblclick=\"startEditTitle(index)\">\r\n            <el-input\r\n              v-if=\"category.editing\"\r\n              v-model=\"category.editingName\"\r\n              @blur=\"finishEditTitle(index)\"\r\n              @keyup.enter=\"finishEditTitle(index)\"\r\n              @keyup.esc=\"cancelEditTitle(index)\"\r\n              :data-edit-index=\"index\"\r\n              size=\"small\"\r\n              style=\"width: 200px;\"\r\n              placeholder=\"请输入标题\"\r\n            />\r\n            <span v-else>{{ category.name }}</span>\r\n          </div>\r\n          <el-switch\r\n            v-model=\"category.enabled\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ccc\"\r\n          />\r\n        </div>\r\n\r\n        <div v-show=\"category.enabled\" class=\"category-body\">\r\n          <div v-if=\"category.key === 'default_scene'\">\r\n            <el-form label-width=\"120px\">\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"24\">\r\n                  <!-- 场景配置分块 -->\r\n                  <el-card class=\"mini-block scene-config-container\" shadow=\"never\" style=\"margin-top: 20px;\">\r\n                    <div slot=\"header\">场景配置</div>\r\n                    <div>\r\n                      <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                          <el-tree\r\n                            ref=\"sceneTree\"\r\n                            :data=\"sceneConfigTree\"\r\n                            node-key=\"id\"\r\n                            :props=\"{ label: 'name', children: 'children' }\"\r\n                            @node-click=\"handleSceneNodeClick\"\r\n                            highlight-current\r\n                            :expand-on-click-node=\"false\"\r\n                            :default-expanded-keys=\"treeExpandedKeys.length > 0 ? treeExpandedKeys : (sceneConfigTree.length ? [sceneConfigTree[0].id] : [])\"\r\n                            :current-node-key=\"selectedNode ? selectedNode.id : null\"\r\n                            :sort=\"false\"\r\n                          />\r\n                        </el-col>\r\n                        <el-col :span=\"18\">\r\n                          <SceneConfigNode \r\n                            v-if=\"selectedNode\" \r\n                            :node=\"selectedNode\" \r\n                            :root-tree=\"sceneConfigTree\"\r\n                            :scene-tree-options=\"sceneTreeOptions\"\r\n                            :left-tree-industry-code=\"industryCode\"\r\n                          />\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                  </el-card>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n\r\n          <!-- 其他分类的占位内容 -->\r\n          <div v-else-if=\"category.key === 'default_plan'\">\r\n            <network-plan-config v-model=\"networkPlanData\" :left-tree-industry-code=\"industryCode\" />\r\n          </div>\r\n          <div v-else-if=\"category.key === 'default_value'\">\r\n            <business-value-config v-model=\"businessValueData\" :left-tree-industry-code=\"industryCode\" />\r\n          </div>\r\n          <div v-else-if=\"category.key === 'default_vr'\">\r\n            <vr-scene-config v-model=\"vrSceneData\" />\r\n          </div>\r\n          <div v-else>\r\n            <p>这里是 <strong>{{ category.name }}</strong> 分类的内容区域。</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit-footer\">\r\n      <div class=\"form-actions\">\r\n        <!-- 同步文件按钮加 tooltip -->\r\n        <el-tooltip \r\n          effect=\"dark\" \r\n          content=\"链接填写完成后，点击【提交】后再点击【同步】按钮\" \r\n          placement=\"top\"\r\n        >\r\n          <el-button \r\n            type=\"success\" \r\n            @click=\"handleSynchronizeFile\" \r\n            :disabled=\"!form.sceneViewConfigId\"\r\n            :loading=\"synchronizing\"\r\n          >\r\n            {{ synchronizing ? '同步中...' : '同步文件' }}\r\n          </el-button>\r\n        </el-tooltip>\r\n\r\n        <el-button \r\n          type=\"primary\" \r\n          @click=\"handleSubmit\" \r\n          :loading=\"submitting\" \r\n          style=\"margin-left: 30px;\"\r\n        >\r\n          {{ submitting ? '提交中...' : '提交' }}\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    </div>\r\n    \r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"previewVisible\"\r\n      title=\"图片预览\"\r\n      width=\"60%\"\r\n      append-to-body\r\n      @close=\"closePreview\"\r\n    >\r\n      <div class=\"preview-container\">\r\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getIndustryList, getSceneTreeList } from '@/api/view/industry'\r\nimport SceneConfigNode from './SceneConfigNode.vue'\r\nimport { getSceneViewConfig, sceneViewUpd, uploadSceneFile, synchronizationFile } from '@/api/view/sceneView'\r\nimport NetworkPlanConfig from './NetworkPlanConfig.vue'\r\nimport BusinessValueConfig from './BusinessValueConfig.vue'\r\nimport VrSceneConfig from './VrSceneConfig.vue'\r\nimport ThemeSelectionDialog from './ThemeSelectionDialog.vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'IndustryScenePage',\r\n  components: {\r\n    SceneConfigNode,\r\n    NetworkPlanConfig,\r\n    BusinessValueConfig,\r\n    VrSceneConfig,\r\n    ThemeSelectionDialog\r\n  },\r\n  data() {\r\n    return {\r\n      menuData: [], // 原始菜单数据\r\n      flatMenuData: [], // 扁平化的行业数据，用于搜索和业务逻辑\r\n      activeMenu: '',\r\n      industryCode: '',\r\n      selectedTheme: null, // 当前选择的主题\r\n      form: {\r\n        mainTitle: '',\r\n        subTitle: '',\r\n        bgImgUrl: '',\r\n        bgFileUrl: '',\r\n        panoramicViewXmlUrl: ''\r\n      },\r\n      sceneConfigTree: [],\r\n      selectedNode: null,\r\n      loading: false, // 页面加载状态\r\n      switchingIndustry: false, // 新增：切换行业的loading状态\r\n      rules: {\r\n        introduceVideoImgUrl: [\r\n          { required: true, message: '请上传介绍视频首帧', trigger: 'change' }\r\n        ],\r\n        introduceVideoFileUrl: [\r\n          { required: true, message: '请上传介绍视频', trigger: 'change' }\r\n        ],\r\n        videoExplanationFileUrl: [\r\n          { required: true, message: '请上传讲解视频', trigger: 'change' }\r\n        ]\r\n      },\r\n      uploadingType: '',\r\n      uploadingKey: '',\r\n      categories: [], // 改为空数组，从后端动态获取\r\n      introduceVideo: {\r\n        status: '0',\r\n        backgroundImgFileUrl: '',\r\n        backgroundFileUrl: ''\r\n      },\r\n      videoExplanation: {\r\n        status: '0',\r\n        backgroundFileUrl: '',\r\n        videoSegmentedVoList: []\r\n      },\r\n      sceneTreeOptions: [],\r\n      sceneCascaderProps: {\r\n        label: 'sceneName',\r\n        value: 'id',\r\n        children: 'children',\r\n        emitPath: false,\r\n        checkStrictly: true,\r\n        disabled: (data) => {\r\n          // 允许所有节点可选，只要没有被其他分段选中\r\n          const isSelected = this.videoExplanation && this.videoExplanation.videoSegmentedVoList\r\n            ? this.videoExplanation.videoSegmentedVoList.some(seg => seg.sceneId === data.id)\r\n            : false\r\n          return isSelected\r\n        }\r\n      },\r\n      bgFileList: [], // 背景文件列表\r\n      videoExplanationFileList: [], // 讲解视频文件列表\r\n      xmlFileList: [], // XML文件列表\r\n      networkPlanDataMap: {}, // 改为对象，按菜单ID存储\r\n      businessValueDataMap: {}, // 商业价值数据映射\r\n      vrSceneDataMap: {}, // VR看现场数据映射\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      searchKeyword: '',\r\n      treeExpandedKeys: [], // 新增：保存树的展开状态\r\n      uploadModes: {\r\n        bgFile: 'upload',\r\n        videoExplanation: 'upload',\r\n        introduceVideo: 'upload'\r\n      },\r\n      synchronizing: false,\r\n      submitting: false // 添加这个属性\r\n    }\r\n  },\r\n  computed: {\r\n    videoSegmentedList() {\r\n      // 如果没有数据，默认返回一行空数据\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        return [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      return this.videoExplanation.videoSegmentedVoList\r\n    },\r\n    networkPlanData: {\r\n      get() {\r\n        const menuData = this.networkPlanDataMap[this.activeMenu]\r\n        if (!menuData) {\r\n          return {\r\n            networkVideoList: [],\r\n            videoExplanationVo: {\r\n              status: '0',\r\n              backgroundFileUrl: '',\r\n              videoSegmentedVoList: []\r\n            }\r\n          }\r\n        }\r\n        return menuData\r\n      },\r\n      set(value) {\r\n        this.$set(this.networkPlanDataMap, this.activeMenu, value)\r\n      }\r\n    },\r\n    businessValueData: {\r\n      get() {\r\n        return this.businessValueDataMap[this.activeMenu] || []\r\n      },\r\n      set(value) {\r\n        this.$set(this.businessValueDataMap, this.activeMenu, value)\r\n      }\r\n    },\r\n    vrSceneData: {\r\n      get() {\r\n        return this.vrSceneDataMap[this.activeMenu] || []\r\n      },\r\n      set(val) {\r\n        this.$set(this.vrSceneDataMap, this.activeMenu, val)\r\n      }\r\n    },\r\n    filteredMenuData() {\r\n      if (!this.searchKeyword) {\r\n        return this.menuData\r\n      }\r\n      \r\n      // 递归过滤树形数据\r\n      const filterTree = (nodes) => {\r\n        return nodes.map(node => {\r\n          const filteredChildren = node.children ? filterTree(node.children) : []\r\n          const matchesSearch = node.name && node.name.toLowerCase().includes(this.searchKeyword.toLowerCase())\r\n          \r\n          if (matchesSearch || filteredChildren.length > 0) {\r\n            return {\r\n              ...node,\r\n              children: filteredChildren\r\n            }\r\n          }\r\n          return null\r\n        }).filter(Boolean)\r\n      }\r\n      \r\n      return filterTree(this.menuData)\r\n    }\r\n  },\r\n  created() {\r\n    // 从URL获取token并设置\r\n    this.initTokenFromUrl()\r\n    this.loadIndustryMenu()\r\n  },\r\n  methods: {\r\n    // 从URL获取token并设置\r\n    initTokenFromUrl() {\r\n      const urlParams = new URLSearchParams(window.location.search)\r\n  const token = urlParams.get('token')\r\n  \r\n  if (token) {\r\n    localStorage.setItem('external-token', token)\r\n    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    console.log('从URL获取到token:', token)\r\n  } else {\r\n    const storedToken = localStorage.getItem('external-token')\r\n    if (storedToken) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`\r\n    }\r\n  }\r\n    },\r\n    async loadIndustryMenu() {\r\n      const res = await getIndustryList()\r\n      if (res.code === 0 && Array.isArray(res.data)) {\r\n        // 转换数据结构为树形菜单\r\n        this.menuData = res.data.map(plate => ({\r\n          id: `plate_${plate.plateKey}`,\r\n          name: plate.plateName,\r\n          type: 'plate',\r\n          children: plate.industryTreeListVos ? plate.industryTreeListVos.map(industry => ({\r\n            id: industry.id,\r\n            name: industry.industryName,\r\n            industryCode: industry.industryCode,\r\n            plate: industry.plate,\r\n            type: 'industry'\r\n          })) : []\r\n        }))\r\n        \r\n        // 创建扁平化的行业数据，用于业务逻辑\r\n        this.flatMenuData = []\r\n        res.data.forEach(plate => {\r\n          if (plate.industryTreeListVos) {\r\n            this.flatMenuData.push(...plate.industryTreeListVos)\r\n          }\r\n        })\r\n        \r\n        // 默认选中第一个行业\r\n        if (this.flatMenuData.length) {\r\n          this.activeMenu = String(this.flatMenuData[0].id)\r\n          this.industryCode = this.flatMenuData[0].industryCode\r\n          // 等待DOM更新后设置树组件的当前节点\r\n          this.$nextTick(() => {\r\n            // 确保树组件已渲染并设置当前选中节点\r\n            if (this.$refs.menuTree && this.$refs.menuTree.setCurrentKey) {\r\n              this.$refs.menuTree.setCurrentKey(this.activeMenu)\r\n            }\r\n          })\r\n          \r\n          // 加载第一个行业的数据\r\n          await this.handleSelect(this.activeMenu)\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleTreeNodeClick(data) {\r\n      // 只有点击行业节点才处理\r\n      if (data.type === 'industry') {\r\n        this.handleSelect(String(data.id))\r\n        this.industryCode = data.industryCode;\r\n      }\r\n    },\r\n\r\n    handleSearch(value) {\r\n      this.searchKeyword = value\r\n    },\r\n    highlightText(text) {\r\n      if (!this.searchKeyword) return text\r\n      const regex = new RegExp(`(${this.searchKeyword})`, 'gi')\r\n      return text.replace(regex, '<span class=\"highlight\">$1</span>')\r\n    },\r\n    async handleSelect(id, keepSelectedNode = false, showLoading = true) {\r\n      try {\r\n        // 开启切换行业的loading\r\n        if (showLoading) {\r\n          this.switchingIndustry = true\r\n          // 禁用页面滚动\r\n          document.body.style.overflow = 'hidden'\r\n\r\n          // 右侧编辑栏回到顶部\r\n          this.$nextTick(() => {\r\n            const contentPanel = document.querySelector('.content-panel')\r\n            if (contentPanel) {\r\n              contentPanel.scrollTop = 0\r\n            }\r\n          })\r\n        }\r\n        \r\n        this.activeMenu = id\r\n        await this.loadSceneTreeOptions(this.activeMenu)\r\n        \r\n        // 保存当前选中的节点\r\n        const currentSelectedNode = keepSelectedNode ? this.selectedNode : null\r\n        \r\n        // 重置主题选择\r\n        this.selectedTheme = null\r\n        \r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        const res = await getSceneViewConfig({ industryCode: industryCode })\r\n        if (res.code === 0 && res.data) {\r\n          \r\n          // 同步主标题、副标题、背景图片、XML文件等\r\n          this.form.sceneViewConfigId = res.data.sceneViewConfigId || ''\r\n          this.form.mainTitle = res.data.mainTitle || ''\r\n          this.form.subTitle = res.data.subTitle || ''\r\n          this.form.bgImgUrl = res.data.backgroundImgFileUrl || ''\r\n          this.form.bgFileUrl = res.data.backgroundFileUrl || ''\r\n          this.form.panoramicViewXmlUrl = res.data.panoramicViewXmlUrl || ''\r\n          \r\n          // 更新背景文件列表\r\n          this.updateBgFileList()\r\n          \r\n          // 更新XML文件列表\r\n          this.updateXmlFileList()\r\n          \r\n          // 回显主题选择\r\n          if (res.data.themeInfoVo) {\r\n            this.selectedTheme = {\r\n              themeId: res.data.themeInfoVo.themeId,\r\n              themeName: res.data.themeInfoVo.themeName,\r\n              themeEffectImg: res.data.themeInfoVo.themeEffectImg,\r\n              remark: res.data.themeInfoVo.remark\r\n            }\r\n          } else {\r\n            this.selectedTheme = null\r\n          }\r\n          \r\n          // 处理 sceneDefaultConfigVoList，动态生成 categories\r\n          if (res.data.sceneDefaultConfigVoList && Array.isArray(res.data.sceneDefaultConfigVoList)) {\r\n            this.categories = res.data.sceneDefaultConfigVoList.map(configItem => ({\r\n              id: configItem.id,\r\n              key: configItem.keyName,\r\n              name: configItem.name,\r\n              enabled: configItem.keyValue === '0', // keyValue为'0'表示启用\r\n              editing: false,\r\n              editingName: '',\r\n              originalName: configItem.name,\r\n              remark: configItem.remark,\r\n              classification: configItem.classification,\r\n              defaultStatus: configItem.defaultStatus\r\n            }))\r\n            \r\n            // 查找场景配置分类\r\n            const sceneCategory = res.data.sceneDefaultConfigVoList.find(item => item.keyName === 'default_scene')\r\n            \r\n            // 处理视频讲解数据\r\n            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.videoExplanationVo) {\r\n              const videoData = sceneCategory.industrySceneInfoVo.videoExplanationVo\r\n              this.videoExplanation = {\r\n                status: videoData.status || '0',\r\n                backgroundFileUrl: videoData.backgroundFileUrl || '',\r\n                videoSegmentedVoList: videoData.videoSegmentedVoList ? videoData.videoSegmentedVoList.map(seg => ({\r\n                  time: seg.time,\r\n                  sceneCode: seg.sceneCode,\r\n                  sceneName: seg.sceneName,\r\n                  sceneId: this.findSceneIdByCode(seg.sceneCode) // 根据sceneCode查找sceneId\r\n                })) : []\r\n              }\r\n            } else {\r\n              this.videoExplanation = {\r\n                status: '0',\r\n                backgroundFileUrl: '',\r\n                videoSegmentedVoList: []\r\n              }\r\n            }\r\n            \r\n            // 更新视频讲解文件列表\r\n            this.updateVideoExplanationFileList()\r\n            \r\n            // 处理场景配置树\r\n            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.sceneListVo) {\r\n              this.sceneConfigTree = this.adaptSceneTree(sceneCategory.industrySceneInfoVo.sceneListVo)\r\n              \r\n              // 如果需要保持选中节点\r\n              if (keepSelectedNode && currentSelectedNode) {\r\n                const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNode.id)\r\n                if (nodeToSelect) {\r\n                  this.selectedNode = nodeToSelect\r\n                } else {\r\n                  this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null\r\n                }\r\n              } else {\r\n                // 默认选择第一个节点\r\n                this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null\r\n              }\r\n            } else {\r\n              // 没有场景数据时清空\r\n              this.sceneConfigTree = []\r\n              this.selectedNode = null\r\n            }\r\n          }\r\n          \r\n          // 处理网络方案数据\r\n          if (res.data.networkSolutionVo) {\r\n            const networkData = {\r\n              networkVideoList: res.data.networkSolutionVo.networkVideoList || [],\r\n              videoExplanationVo: res.data.networkSolutionVo.videoExplanationVo || {\r\n                status: '0',\r\n                backgroundFileUrl: '',\r\n                videoSegmentedVoList: []\r\n              }\r\n            }\r\n            this.$set(this.networkPlanDataMap, this.activeMenu, networkData)\r\n          }\r\n\r\n          // 处理商业价值数据\r\n          if (res.data.commercialValueListVo) {\r\n            this.$set(this.businessValueDataMap, this.activeMenu, res.data.commercialValueListVo)\r\n          }\r\n\r\n          // 处理VR看现场数据\r\n          if (res.data.vrInfoListVo) {\r\n            this.$set(this.vrSceneDataMap, this.activeMenu, res.data.vrInfoListVo)\r\n          }\r\n          \r\n          // 其他数据处理逻辑保持不变...\r\n        }\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n        this.$message.error('加载数据失败')\r\n      } finally {\r\n        // 关闭切换行业的loading\r\n        if (showLoading) {\r\n          this.switchingIndustry = false\r\n          // 恢复页面滚动\r\n          document.body.style.overflow = ''\r\n        }\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n      return false // 拦截默认上传行为\r\n    },\r\n    addSegment() {\r\n      this.form.videoSegmentedVoList.push({ time: '', scene: '' })\r\n    },\r\n    removeSegment(index) {\r\n      if (this.form.videoSegmentedVoList.length >= 1) {\r\n        this.form.videoSegmentedVoList.splice(index, 1)\r\n      }\r\n    },\r\n    async beforeUploadIntroduceImg(file, type, key) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传图片，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          if (type && key) {\r\n            // 针对介绍视频和视频讲解的上传，单独上传图片时使用 fileUrl\r\n            this[type][key] = res.data.fileUrl\r\n          } else {\r\n            // 针对主背景图片的上传\r\n            this.form.bgImgUrl = res.data.fileUrl\r\n          }\r\n          this.$message.success('上传成功')\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    async beforeUploadIntroduceVideo(file, type, key) {\r\n      // 如果是主背景文件上传（没有type和key参数）\r\n      if (!type && !key) {\r\n        try {\r\n          this.$modal.loading(\"正在上传文件，请稍候...\")\r\n          const formData = new FormData()\r\n          formData.append('file', file)\r\n          formData.append('industryCode', this.industryCode)\r\n          \r\n          const res = await uploadSceneFile(formData)\r\n          if (res.code === 0 && res.data) {\r\n            // 设置背景文件URL\r\n            this.form.bgFileUrl = res.data.fileUrl\r\n            \r\n            // 直接覆盖背景文件列表\r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            this.bgFileList = [{\r\n              name: fileName,\r\n              url: res.data.fileUrl,\r\n              uid: Date.now()\r\n            }]\r\n            \r\n            // 如果是MP4文件且返回了imgUrl，自动设置背景图片首帧\r\n            if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n              this.form.bgImgUrl = res.data.imgUrl\r\n              this.$message.success('上传成功，已自动生成背景图片首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || '上传失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('上传失败')\r\n        } finally {\r\n          this.$modal.closeLoading()\r\n        }\r\n        return false\r\n      }\r\n      \r\n      // 其他视频上传逻辑（介绍视频、讲解视频等）\r\n      if (!file.type.startsWith('video/') && !file.name.endsWith('.mp4')) {\r\n        this.$message.error('只能上传MP4视频文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传视频，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          if (type && key) {\r\n            // 针对介绍视频和视频讲解的上传\r\n            this[type][key] = res.data.fileUrl\r\n            \r\n            // 直接覆盖对应的文件列表\r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            if (type === 'introduceVideo' && key === 'backgroundFileUrl') {\r\n              this.introduceVideoFileList = [{\r\n                name: fileName,\r\n                url: res.data.fileUrl,\r\n                uid: Date.now()\r\n              }]\r\n            } else if (type === 'videoExplanation' && key === 'backgroundFileUrl') {\r\n              this.videoExplanationFileList = [{\r\n                name: fileName,\r\n                url: res.data.fileUrl,\r\n                uid: Date.now()\r\n              }]\r\n            }\r\n            \r\n            // 如果是介绍视频上传，且返回了imgUrl，自动设置介绍视频首帧\r\n            if (type === 'introduceVideo' && key === 'backgroundFileUrl' && res.data.imgUrl) {\r\n              this.introduceVideo.backgroundImgFileUrl = res.data.imgUrl\r\n              this.$message.success('上传成功，已自动生成介绍视频首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    beforeUploadExplanationVideo(file) {\r\n      this.uploadingType = 'mp4'\r\n      this.uploadingKey = 'videoExplanationFileUrl'\r\n      return this.handleBeforeUpload(file)\r\n    },\r\n    // 新增方法：添加场景配置节点\r\n    addSceneConfigNode(parentId = null) {\r\n      const newNode = {\r\n        id: Date.now(), // 生成唯一ID\r\n        name: '新场景',\r\n        type: 'scene', // 类型为场景\r\n        enabled: true,\r\n        children: [],\r\n        parentId: parentId\r\n      }\r\n      if (parentId) {\r\n        const parentNode = this.findNodeById(this.sceneConfigTree, parentId)\r\n        if (parentNode) {\r\n          parentNode.children.push(newNode)\r\n        }\r\n      } else {\r\n        this.sceneConfigTree.push(newNode)\r\n      }\r\n      return newNode.id\r\n    },\r\n    // 新增方法：删除场景配置节点\r\n    removeSceneConfigNode(nodeId) {\r\n      this.sceneConfigTree = this.sceneConfigTree.filter(node => node.id !== nodeId)\r\n    },\r\n    // 新增方法：查找节点\r\n    findNodeById(nodes, id) {\r\n      for (const node of nodes) {\r\n        if (node.id === id) {\r\n          return node\r\n        }\r\n        if (node.children && node.children.length > 0) {\r\n          const found = this.findNodeById(node.children, id)\r\n          if (found) {\r\n            return found\r\n          }\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    // 新增方法：添加场景的痛点价值\r\n    addScenePainPoint(nodeId) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints.push({ title: '', contents: [''], showTime: '' })\r\n      }\r\n    },\r\n    // 新增方法：删除场景的痛点价值\r\n    removeScenePainPoint(nodeId, idx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints.splice(idx, 1)\r\n      }\r\n    },\r\n    // 新增方法：添加场景痛点内容的项\r\n    addScenePainContent(nodeId, idx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints[idx].contents.push('')\r\n      }\r\n    },\r\n    // 新增方法：删除场景痛点内容的项\r\n    removeScenePainContent(nodeId, idx, cidx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints[idx].contents.splice(cidx, 1)\r\n      }\r\n    },\r\n    // 新增方法：添加场景的成本预估内容\r\n    addSceneCostContent(nodeId) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'costEstimate') {\r\n        node.contents = node.contents || []\r\n        node.contents.push('')\r\n      }\r\n    },\r\n    // 新增方法：删除场景的成本预估内容\r\n    removeSceneCostContent(nodeId, cidx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'costEstimate') {\r\n        node.contents = node.contents || []\r\n        node.contents.splice(cidx, 1)\r\n      }\r\n    },\r\n    // 新增方法：上传场景配置图片\r\n    beforeUploadSceneConfigImg(file, type, key) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      const reader = new FileReader()\r\n      reader.onload = e => {\r\n        this.findNodeById(this.sceneConfigTree, type)[key] = e.target.result\r\n      }\r\n      reader.readAsDataURL(file)\r\n      return false\r\n    },\r\n    // 新增方法：上传场景配置文件\r\n    beforeUploadSceneConfigFile(file, type, key) {\r\n      // 这里只做文件名回显\r\n      this.findNodeById(this.sceneConfigTree, type)[key] = file.name\r\n      return false\r\n    },\r\n    handleSceneNodeClick(node) {\r\n      this.selectedNode = node\r\n    },\r\n    // 适配函数移到methods中，供接口数据适配使用\r\n    adaptSceneTree(rawTree, parent = null) {\r\n      if (!rawTree) return []\r\n      const arr = Array.isArray(rawTree) ? rawTree : [rawTree]\r\n      return arr.map((node) => {\r\n        const adapted = {\r\n          id: node.sceneId,\r\n          sceneInfoId: node.sceneInfoId || null,\r\n          name: node.sceneName,\r\n          code: node.sceneCode,\r\n          x: node.x,\r\n          y: node.y,\r\n          type: node.type,\r\n          status: node.status !== null ? node.status : '0',\r\n          isUnfold: node.isUnfold !== null && node.isUnfold !== undefined ? node.isUnfold : '1',\r\n          displayLocation: node.displayLocation !== null && node.displayLocation !== undefined ? node.displayLocation : '0',\r\n          treeClassification: node.treeClassification !== null && node.treeClassification !== undefined ? node.treeClassification : '3',\r\n          introduceVideoVo: node.introduceVideoVo ? {\r\n            id: node.introduceVideoVo.id || '',\r\n            type: node.introduceVideoVo.type || '',\r\n            viewInfoId: node.introduceVideoVo.viewInfoId || '',\r\n            status: node.introduceVideoVo.status || '0',\r\n            backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || '',\r\n            backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || ''\r\n          } : { id: '', type: '', viewInfoId: '', status: '0', backgroundImgFileUrl: '', backgroundFileUrl: '' },\r\n          tradition: node.sceneTraditionVo ? {\r\n            name: node.sceneTraditionVo.name || '',\r\n            panoramicViewXmlKey: node.sceneTraditionVo.panoramicViewXmlKey || '',\r\n            backgroundResources: node.sceneTraditionVo.sceneVideoList ? \r\n              node.sceneTraditionVo.sceneVideoList.map(v => ({\r\n                id: v.id || null,\r\n                label: v.tag || '',\r\n                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({\r\n                  id: rel.id || null,\r\n                  fileId: rel.fileId || null,\r\n                  x: rel.clickX || '',\r\n                  y: rel.clickY || '',\r\n                  wide: rel.wide || '',\r\n                  high: rel.high || '',\r\n                  xmlKey: rel.xmlKey || '',\r\n                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),\r\n                  sceneCode: rel.bindSceneCode || ''\r\n                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }],\r\n                wide: v.wide || 0,\r\n                high: v.high || 0,\r\n                bgImg: v.backgroundImgFileUrl || '',\r\n                bgFile: v.backgroundFileUrl || '',\r\n                status: v.status || '',\r\n                type: v.type || '',\r\n                viewInfoId: v.viewInfoId || ''\r\n              })) : [],\r\n            painPoints: node.sceneTraditionVo.painPointList ?\r\n              (Array.isArray(node.sceneTraditionVo.painPointList) ? node.sceneTraditionVo.painPointList.map(p => ({\r\n                painPointId: p.painPointId || null,\r\n                title: p.bigTitle || '',\r\n                contents: p.content || [],\r\n                showTime: p.displayTime || ''\r\n              })) : []) : []\r\n          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },\r\n          wisdom5g: node.scene5gVo ? {\r\n            name: node.scene5gVo.name || '',\r\n            panoramicViewXmlKey: node.scene5gVo.panoramicViewXmlKey || '',\r\n            backgroundResources: node.scene5gVo.sceneVideoList ? \r\n              node.scene5gVo.sceneVideoList.map(v => ({\r\n                id: v.id || null,\r\n                tag: v.tag || '',\r\n                status: v.status || '',\r\n                type: v.type || '',\r\n                viewInfoId: v.viewInfoId || '',\r\n                backgroundImgFileUrl: v.backgroundImgFileUrl || '',\r\n                backgroundFileUrl: v.backgroundFileUrl || '',\r\n                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({\r\n                  id: rel.id || null,\r\n                  fileId: rel.fileId || null,\r\n                  x: rel.clickX || '',\r\n                  y: rel.clickY || '',\r\n                  wide: rel.wide || '',\r\n                  high: rel.high || '',\r\n                  xmlKey: rel.xmlKey || '',\r\n                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),\r\n                  sceneCode: rel.bindSceneCode || ''\r\n                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }]\r\n              })) : [],\r\n            painPoints: node.scene5gVo.painPointList ? \r\n              (Array.isArray(node.scene5gVo.painPointList) ? node.scene5gVo.painPointList.map(p => ({\r\n                painPointId: p.painPointId || null,\r\n                title: p.bigTitle || '',\r\n                contents: p.content || [],\r\n                showTime: p.displayTime || ''\r\n              })) : []) : []\r\n          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },\r\n          costEstimate: node.costEstimationInfoVo ? {\r\n            painPointId: node.costEstimationInfoVo.painPointId || null,\r\n            status: node.costEstimationInfoVo.status || '0',\r\n            title: node.costEstimationInfoVo.bigTitle || '',\r\n            contents: node.costEstimationInfoVo.content || []\r\n          } : { painPointId: null, status: '0', title: '', contents: [] },\r\n          children: [],\r\n          parent\r\n        }\r\n        // 递归处理子节点，保持后端返回的原始顺序\r\n        adapted.children = node.children ? this.adaptSceneTree(node.children, adapted) : []\r\n        return adapted\r\n      })\r\n    },\r\n    async handleSubmit() {\r\n      try {\r\n        this.submitting = true\r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === this.activeMenu)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        // 保存当前选中的节点的完整信息\r\n        const currentSelectedNodeId = this.selectedNode ? this.selectedNode.id : null\r\n        const currentSelectedNodeName = this.selectedNode ? this.selectedNode.name : null\r\n        \r\n        // 构建提交数据\r\n        const submitData = {\r\n          industryId: this.activeMenu,\r\n          industryCode: industryCode, // 新增参数\r\n          sceneViewConfigId: this.form.sceneViewConfigId || null,\r\n          mainTitle: this.form.mainTitle || null,\r\n          subTitle: this.form.subTitle || null,\r\n          themeId: this.selectedTheme ? this.selectedTheme.themeId : null,\r\n          backgroundImgFileUrl: this.form.bgImgUrl || null,\r\n          backgroundFileUrl: this.form.bgFileUrl || null,\r\n          panoramicViewXmlUrl: this.form.panoramicViewXmlUrl || null,\r\n          networkSolutionInfoVo: {\r\n            networkVideoList: (this.networkPlanDataMap[this.activeMenu]?.networkVideoList && Array.isArray(this.networkPlanDataMap[this.activeMenu].networkVideoList)) ? \r\n              this.networkPlanDataMap[this.activeMenu].networkVideoList.map(plan => ({\r\n                id: plan.id || null,\r\n                type: 4,\r\n                tag: plan.tag || null,\r\n                clickX: plan.clickX || null,\r\n                clickY: plan.clickY || null,\r\n                wide: plan.wide || null,\r\n                high: plan.high || null,\r\n                backgroundImgFileUrl: plan.backgroundImgFileUrl || null,\r\n                backgroundFileUrl: plan.backgroundFileUrl || null,\r\n                status: null,\r\n                viewInfoId: null\r\n              })) : [],\r\n            videoExplanationVo: {\r\n              status: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.status || '0',\r\n              backgroundFileUrl: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.backgroundFileUrl || null,\r\n              videoSegmentedVoList: (this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.videoSegmentedVoList?.length) ? \r\n                this.networkPlanDataMap[this.activeMenu].videoExplanationVo.videoSegmentedVoList.map(seg => ({\r\n                  time: seg.time || null,\r\n                  sceneCode: seg.sceneCode || null,\r\n                  sceneName: seg.sceneName || null\r\n                })) : null\r\n            }\r\n          },\r\n          sceneDefaultConfigVoList: this.categories.map(cat => {\r\n            const baseConfig = {\r\n              id: cat.id || null,\r\n              industryId: this.activeMenu || null,\r\n              name: cat.name,\r\n              keyName: cat.key,\r\n              keyValue: cat.enabled ? '0' : '1',\r\n              remark: cat.remark || cat.name,\r\n              defaultStatus: '0'\r\n            }\r\n\r\n            if (cat.key === 'default_scene') {\r\n              const convertedSceneList = this.convertSceneTreeToApi(this.sceneConfigTree)     \r\n              baseConfig.industrySceneInfoVo = {\r\n                videoExplanationVo: {\r\n                  status: this.videoExplanation.status,\r\n                  backgroundFileUrl: this.videoExplanation.backgroundFileUrl || null,\r\n                  videoSegmentedVoList: this.videoExplanation.videoSegmentedVoList.length ? this.videoExplanation.videoSegmentedVoList : null\r\n                },\r\n                sceneListVo: convertedSceneList\r\n              }\r\n            } else {\r\n              baseConfig.industrySceneInfoVo = null\r\n            }\r\n            \r\n            return baseConfig\r\n          }),\r\n          commercialValueDTO: (this.businessValueDataMap[this.activeMenu] && Array.isArray(this.businessValueDataMap[this.activeMenu])) ? \r\n            this.businessValueDataMap[this.activeMenu].map(value => ({\r\n              id: value.id || null,\r\n              viewInfoId: value.viewInfoId || null,\r\n              type: 5,\r\n              tag: value.tag || null,\r\n              backgroundImgFileUrl: value.backgroundImgFileUrl || null,\r\n              backgroundFileUrl: value.backgroundFileUrl || null\r\n            })) : [],\r\n          vrInfoDtoList: (this.vrSceneDataMap[this.activeMenu] && Array.isArray(this.vrSceneDataMap[this.activeMenu])) ? \r\n            this.vrSceneDataMap[this.activeMenu]. map(vr => ({\r\n              id: vr.id || null,\r\n              industryId: vr.industryId || this.activeMenu,\r\n              type: vr.type || 6,\r\n              viewInfoId: vr.viewInfoId || null,\r\n              name: vr.name || '',\r\n              address: vr.address || ''\r\n            })) : [],\r\n        }\r\n      \r\n        const response = await sceneViewUpd(submitData)\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n\r\n        // 重新加载数据（不显示全局loading，避免与按钮loading冲突）\r\n        await this.handleSelect(this.activeMenu, false, false)\r\n\r\n        if (currentSelectedNodeId && this.sceneConfigTree.length > 0) {\r\n          // 强制查找并设置选中节点\r\n          const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNodeId)\r\n\r\n          if (nodeToSelect) {\r\n            // 计算并设置展开路径\r\n            const pathIds = []\r\n            const findPath = (nodes, targetId, currentPath = []) => {\r\n              for (const node of nodes) {\r\n                const newPath = [...currentPath, node.id]\r\n                if (node.id === targetId) {\r\n                  pathIds.push(...newPath)\r\n                  return true\r\n                }\r\n                if (node.children && node.children.length > 0) {\r\n                  if (findPath(node.children, targetId, newPath)) {\r\n                    return true\r\n                  }\r\n                }\r\n              }\r\n              return false\r\n            }\r\n\r\n            findPath(this.sceneConfigTree, currentSelectedNodeId)\r\n            this.treeExpandedKeys = pathIds.slice(0, -1)\r\n\r\n            // 先设置选中节点\r\n            this.selectedNode = nodeToSelect\r\n\r\n            // 强制更新树组件的选中状态\r\n            this.$nextTick(() => {\r\n              this.$nextTick(() => {\r\n                // 模拟点击节点来强制更新选中状态\r\n                this.handleSceneNodeClick(nodeToSelect)\r\n              })\r\n            })\r\n          }\r\n        }\r\n        \r\n        console.log('提交内容:', submitData)\r\n      } catch (error) {\r\n        console.error('提交失败:', error)\r\n        this.$message.error('提交失败')\r\n      } finally {\r\n        this.submitting = false\r\n      }\r\n    },\r\n    addVideoSegment() {\r\n      // 如果原数组为空，先初始化\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        this.videoExplanation.videoSegmentedVoList = [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      this.videoExplanation.videoSegmentedVoList.push({ time: '', sceneId: '', sceneName: '', sceneCode: '' })\r\n    },\r\n    removeVideoSegment(idx) {\r\n      this.videoExplanation.videoSegmentedVoList.splice(idx, 1)\r\n    },\r\n    //递归重构结构\r\n    getDeepTreeOptions(tree) {\r\n    return tree.map(item => {\r\n      // 复制当前节点的基础属性\r\n      const node = { ...item };\r\n      \r\n      // 如果存在 children 且不为空，则递归处理\r\n      if (node.children && node.children.length > 0) {\r\n        node.children = this.getDeepTreeOptions(node.children);\r\n      } else {\r\n        // 当 children 为空或不存在时，删除 children 属性（可选）\r\n        delete node.children;\r\n      }\r\n      \r\n      return node;\r\n    });\r\n  },\r\n    async loadSceneTreeOptions(id) {\r\n      try {\r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        const res = await getSceneTreeList({ industryCode: industryCode })\r\n        if (res.code === 0 && Array.isArray(res.data)) {\r\n          this.sceneTreeOptions = this.getDeepTreeOptions(res.data)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载场景树失败:', error)\r\n      }\r\n    },\r\n    handleSceneCascaderChange(val, idx) {\r\n      // 确保数组和索引位置的对象存在\r\n      if (!this.videoExplanation.videoSegmentedVoList || !this.videoExplanation.videoSegmentedVoList[idx]) {\r\n        return\r\n      }\r\n      \r\n      const findScene = (tree, id) => {\r\n        for (const node of tree) {\r\n          if (node.id === id) return node\r\n          if (node.children && node.children.length) {\r\n            const found = findScene(node.children, id)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      const node = findScene(this.sceneTreeOptions, val)\r\n      if (node) {\r\n        // 设置场景ID和相关信息\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneId = val\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneName = node.sceneName\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneCode = node.sceneCode\r\n      }\r\n    },\r\n    isSceneDisabled(id, currentIdx) {\r\n      // 除当前分段外，其他分段已选的id\r\n      return this.videoExplanation.videoSegmentedVoList.some((seg, idx) => idx !== currentIdx && seg.sceneId === id)\r\n    },\r\n    // 将场景树转换为接口格式\r\n    convertSceneTreeToApi(sceneTree) {\r\n      console.log(\"提交的数据:\", sceneTree);\r\n      return sceneTree.map(node => ({\r\n        sceneInfoId: node.sceneInfoId || null,\r\n        sceneId: node.id,\r\n        paramId: node.parent ? node.parent.id : null,\r\n        sceneName: node.name,\r\n        sceneCode: node.code,\r\n        x: node.x || null,\r\n        y: node.y || null,\r\n        type: node.type || null,\r\n        status: node.status,\r\n        isUnfold: (node.children && node.children.length > 0) ? (node.isUnfold || '1') : null,\r\n        displayLocation: (node.children && node.children.length > 0) ? (node.displayLocation || '0') : null,\r\n        treeClassification: (node.children && node.children.length > 0) ? (node.treeClassification || '3') : null,\r\n        introduceVideoVo: node.introduceVideoVo ? {\r\n          id: node.introduceVideoVo.id || null,\r\n          type: node.introduceVideoVo.type || null,\r\n          viewInfoId: node.introduceVideoVo.viewInfoId || null,\r\n          status: node.introduceVideoVo.status || null,\r\n          backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || null,\r\n          backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || null\r\n        } : null,\r\n        sceneTraditionVo: node.tradition ? {\r\n          name: node.tradition.name || null,\r\n          panoramicViewXmlKey: node.tradition.panoramicViewXmlKey || null,\r\n          sceneVideoList: node.tradition.backgroundResources && node.tradition.backgroundResources.length ? \r\n            node.tradition.backgroundResources.map(resource => ({\r\n              id: resource.id || null,\r\n              tag: resource.label || null,\r\n              wide: resource.wide || null,\r\n              high: resource.high || null,\r\n              status: resource.status || null,\r\n              type: 1,\r\n              viewInfoId: resource.viewInfoId || null,\r\n              backgroundImgFileUrl: resource.bgImg || '',\r\n              backgroundFileUrl: resource.bgFile || '',\r\n              sceneFileRelList: resource.coordinates && resource.coordinates.length ? \r\n                resource.coordinates.map(coord => ({\r\n                  id: coord.id || null,\r\n                  fileId: coord.fileId || null,\r\n                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,\r\n                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,\r\n                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,\r\n                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,\r\n                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,\r\n                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null\r\n                })) : []\r\n            })) : null,\r\n          painPointList: node.tradition.painPoints && node.tradition.painPoints.length ? \r\n            node.tradition.painPoints.map(pain => ({\r\n              painPointId: pain.painPointId || null,\r\n              bigTitle: pain.title || null,\r\n              content: pain.contents || [],\r\n              displayTime: pain.showTime || null\r\n            })) : null\r\n        } : null,\r\n        scene5gVo: node.wisdom5g ? {\r\n          name: node.wisdom5g.name || null,\r\n          panoramicViewXmlKey: node.wisdom5g.panoramicViewXmlKey || null,\r\n          sceneVideoList: node.wisdom5g.backgroundResources && node.wisdom5g.backgroundResources.length ? \r\n            node.wisdom5g.backgroundResources.map(resource => ({\r\n              id: resource.id || null,\r\n              tag: resource.tag || null,\r\n              status: resource.status || null,\r\n              type: 2,\r\n              viewInfoId: resource.viewInfoId || null,\r\n              backgroundImgFileUrl: resource.bgImg || '',\r\n              backgroundFileUrl: resource.bgFile || '',\r\n              sceneFileRelList: resource.coordinates && resource.coordinates.length ? \r\n                resource.coordinates.map(coord => ({\r\n                  id: coord.id || null,\r\n                  fileId: coord.fileId || null,\r\n                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,\r\n                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,\r\n                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,\r\n                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,\r\n                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,\r\n                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null\r\n                })) : []\r\n            })) : null,\r\n          painPointList: node.wisdom5g.painPoints && node.wisdom5g.painPoints.length ? \r\n            node.wisdom5g.painPoints.map(pain => ({\r\n              painPointId: pain.painPointId || null,\r\n              bigTitle: pain.title || null,\r\n              content: pain.contents || [],\r\n              displayTime: pain.showTime || null\r\n            })) : null\r\n        } : null,\r\n        costEstimationInfoVo: node.costEstimate ? {\r\n          painPointId: node.costEstimate.painPointId || null,\r\n          status: node.costEstimate.status || '0',\r\n          bigTitle: node.costEstimate.title || null,\r\n          content: node.costEstimate.contents && node.costEstimate.contents.length ? node.costEstimate.contents : null\r\n        } : null,\r\n        children: node.children && node.children.length ? this.convertSceneTreeToApi(node.children) : []\r\n      }))\r\n    },\r\n    handleTimeChange(val, idx) {\r\n      // 确保数组已初始化\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        this.videoExplanation.videoSegmentedVoList = [{ time: 0, sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      // 更新对应位置的时间值\r\n      if (this.videoExplanation.videoSegmentedVoList[idx]) {\r\n        this.videoExplanation.videoSegmentedVoList[idx].time = val\r\n      }\r\n    },\r\n    // 根据sceneCode查找对应的sceneId\r\n    findSceneIdByCode(sceneCode) {\r\n      if (!sceneCode || !this.sceneTreeOptions) return ''\r\n      \r\n      const findInTree = (tree) => {\r\n        for (const node of tree) {\r\n          if (node.sceneCode === sceneCode) {\r\n            return node.id\r\n          }\r\n          if (node.children && node.children.length) {\r\n            const found = findInTree(node.children)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      \r\n      return findInTree(this.sceneTreeOptions) || ''\r\n    },\r\n    // 处理背景文件删除\r\n    handleRemoveBgFile(file, fileList) {\r\n      this.form.bgFileUrl = ''\r\n      this.form.bgImgUrl = '' // 同时清空背景图片首帧\r\n      this.bgFileList = []\r\n      this.$message.success('文件已删除')\r\n    },\r\n    // 更新背景文件列表\r\n    updateBgFileList() {\r\n      if (this.form.bgFileUrl) {\r\n        const fileName = this.form.bgFileUrl.split('/').pop()\r\n        this.bgFileList = [{\r\n          name: fileName,\r\n          url: this.form.bgFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.bgFileList = []\r\n      }\r\n    },\r\n    // 处理介绍视频文件删除\r\n    handleRemoveIntroduceVideoFile(file, fileList) {\r\n      this.introduceVideo.backgroundFileUrl = ''\r\n      this.introduceVideo.backgroundImgFileUrl = '' // 同时清空首帧图片\r\n      this.introduceVideoFileList = []\r\n      this.$message.success('介绍视频已删除')\r\n    },\r\n    // 更新介绍视频文件列表\r\n    updateIntroduceVideoFileList() {\r\n      if (this.introduceVideo.backgroundFileUrl) {\r\n        const fileName = this.introduceVideo.backgroundFileUrl.split('/').pop()\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: this.introduceVideo.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.introduceVideoFileList = []\r\n      }\r\n    },\r\n    // 处理讲解视频文件删除\r\n    handleRemoveVideoExplanationFile(file, fileList) {\r\n      this.videoExplanation.backgroundFileUrl = ''\r\n      this.videoExplanationFileList = []\r\n      this.$message.success('讲解视频已删除')\r\n    },\r\n    // 更新讲解视频文件列表\r\n    updateVideoExplanationFileList() {\r\n      if (this.videoExplanation.backgroundFileUrl) {\r\n        const fileName = this.videoExplanation.backgroundFileUrl.split('/').pop()\r\n        this.videoExplanationFileList = [{\r\n          name: fileName,\r\n          url: this.videoExplanation.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.videoExplanationFileList = []\r\n      }\r\n    },\r\n    // 处理XML文件删除\r\n    handleRemoveXmlFile(file, fileList) {\r\n      this.form.panoramicViewXmlUrl = ''\r\n      this.xmlFileList = []\r\n      this.$message.success('XML文件已删除')\r\n    },\r\n    \r\n    // 更新XML文件列表\r\n    updateXmlFileList() {\r\n      if (this.form.panoramicViewXmlUrl) {\r\n        const fileName = this.form.panoramicViewXmlUrl.split('/').pop()\r\n        this.xmlFileList = [{\r\n          name: fileName,\r\n          url: this.form.panoramicViewXmlUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.xmlFileList = []\r\n      }\r\n    },\r\n    // 图片预览\r\n    previewImage(url) {\r\n      if (url) {\r\n        this.previewImageUrl = url\r\n        this.previewVisible = true\r\n      }\r\n    },\r\n    closePreview() {\r\n      this.previewVisible = false\r\n      this.previewImageUrl = ''\r\n    },\r\n    // 删除背景图片\r\n    deleteBgImage() {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.bgImgUrl = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    async beforeUploadXmlFile(file) {\r\n      // 检查文件类型\r\n      if (!file.name.toLowerCase().endsWith('.xml')) {\r\n        this.$message.error('只能上传XML文件！')\r\n        return false\r\n      }\r\n      \r\n      // 检查文件大小（50MB）\r\n      if (file.size > 50 * 1024 * 1024) {\r\n        this.$message.error('文件大小不能超过50MB！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传XML文件，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 设置XML文件URL\r\n          this.form.panoramicViewXmlUrl = res.data.fileUrl\r\n          \r\n          // 直接覆盖XML文件列表\r\n          const fileName = res.data.fileUrl.split('/').pop()\r\n          this.xmlFileList = [{\r\n            name: fileName,\r\n            url: res.data.fileUrl,\r\n            uid: Date.now()\r\n          }]\r\n          \r\n          this.$message.success('上传成功')\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    // 主题变更回调\r\n    onThemeChange(theme) {\r\n      // 如果主题有默认背景图，可以自动设置\r\n      if (theme && theme.defaultBgImage) {\r\n        this.form.bgImgUrl = theme.defaultBgImage\r\n      }\r\n    },\r\n    // 格式化坐标数据用于提交\r\n    formatCoordinatesForSubmit(coordinates) {\r\n      if (!coordinates || !Array.isArray(coordinates)) {\r\n        return { clickX: '', clickY: '' }\r\n      }\r\n      \r\n      const xValues = coordinates.map(coord => coord.x || '0').join(',')\r\n      const yValues = coordinates.map(coord => coord.y || '0').join(',')\r\n      \r\n      return {\r\n        clickX: xValues,\r\n        clickY: yValues\r\n      }\r\n    },\r\n    // 解析坐标字符串为坐标数组\r\n    parseCoordinatesFromApi(clickX, clickY) {\r\n      const xArray = clickX ? clickX.split(',') : ['']\r\n      const yArray = clickY ? clickY.split(',') : ['']\r\n      \r\n      // 取较长的数组长度，确保坐标对齐\r\n      const maxLength = Math.max(xArray.length, yArray.length)\r\n      const coordinates = []\r\n      \r\n      for (let i = 0; i < maxLength; i++) {\r\n        coordinates.push({\r\n          x: xArray[i] || '',\r\n          y: yArray[i] || ''\r\n        })\r\n      }\r\n      \r\n      // 至少保证有一个坐标组\r\n      return coordinates.length > 0 ? coordinates : [{ x: '', y: '' }]\r\n    },\r\n    // 开始编辑标题\r\n    startEditTitle(index) {\r\n      const category = this.categories[index]\r\n      category.editing = true\r\n      category.editingName = category.name\r\n      \r\n      // 下一帧聚焦输入框\r\n      this.$nextTick(() => {\r\n        // 使用动态ref名称\r\n        const inputRef = this.$refs[`titleInput_${index}`]\r\n        if (inputRef && inputRef[0]) {\r\n          inputRef[0].focus()\r\n          inputRef[0].select()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 完成编辑标题\r\n    finishEditTitle(index) {\r\n      const category = this.categories[index]\r\n      if (category.editingName && category.editingName.trim()) {\r\n        category.name = category.editingName.trim()\r\n      }\r\n      category.editing = false\r\n      category.editingName = ''\r\n    },\r\n\r\n    // 取消编辑标题\r\n    cancelEditTitle(index) {\r\n      const category = this.categories[index]\r\n      category.editing = false\r\n      category.editingName = ''\r\n    },\r\n    // 设置上传模式\r\n    setUploadMode(type, mode) {\r\n      this.$set(this.uploadModes, type, mode)\r\n    },\r\n    // 背景文件链接输入处理\r\n    handleBgFileUrlInput(value) {\r\n      this.bgFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.bgFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 视频讲解链接输入处理\r\n    handleVideoExplanationUrlInput(value) {\r\n      this.videoExplanationFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.videoExplanationFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 介绍视频链接输入处理\r\n    handleIntroduceVideoUrlInput(value) {\r\n      this.introduceVideoFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 同步文件\r\n    async handleSynchronizeFile() {\r\n      if (!this.form.sceneViewConfigId) {\r\n        this.$message.warning('请先保存配置后再同步文件')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.synchronizing = true\r\n        this.$modal.loading(\"正在同步文件，请稍候...\")\r\n        \r\n        // 使用FormData或URLSearchParams传递表单参数\r\n        const formData = new FormData()\r\n        formData.append('viewConfigId', this.form.sceneViewConfigId)\r\n        \r\n        const res = await synchronizationFile(formData)\r\n        \r\n        if (res.code === 0) {\r\n          this.$message.success(res.msg)\r\n        } else {\r\n          this.$message.error(res.msg || '文件同步失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('同步文件失败:', error)\r\n        this.$message.error('文件同步失败')\r\n      } finally {\r\n        this.synchronizing = false\r\n        this.$modal.closeLoading()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.page-container {\r\n  display: flex;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.menu-panel {\r\n  width: 250px;\r\n  background-color: #f5f7fa;\r\n  border-right: 1px solid #e4e7ed;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n  background-color: #f5f7fa;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.menu-tree {\r\n  background-color: #f5f7fa;\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 8px 0;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content:hover {\r\n  background-color: #e6f7ff;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.menu-tree::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-track {\r\n  background-color: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-thumb {\r\n  background-color: #c0c0c0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-thumb:hover {\r\n  background-color: #a0a0a0;\r\n}\r\n\r\n.content-panel {\r\n  flex: 1;\r\n  padding: 20px 20px 80px 20px;\r\n  overflow-y: auto;\r\n  background-color: #fff;\r\n  position: relative;\r\n}\r\n\r\n/* 在切换行业loading期间禁止滚动 */\r\n.content-panel.loading-no-scroll {\r\n  overflow: hidden;\r\n}\r\n.mini-block {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.category-block {\r\n  margin-top: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.category-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.category-title:hover {\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n.category-title span {\r\n  display: inline-block;\r\n  min-width: 100px;\r\n}\r\n\r\n.category-body {\r\n  padding: 12px;\r\n  background: #ffffff;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.sub-category-block {\r\n  margin-bottom: 15px;\r\n}\r\n.sub-category-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.sub-category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px;\r\n  background-color: #fafafa;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.sub-category-title {\r\n  font-weight: 500;\r\n}\r\n\r\n.sub-category-body {\r\n  padding: 15px;\r\n}\r\n\r\n.segment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.pain-point-block {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.pain-point-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mini-block {\r\n  margin-bottom: 20px;\r\n  min-height: 450px; /* 设置统一的最小高度 */\r\n}\r\n\r\n.mini-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 确保卡片内容区域也有合适的高度 */\r\n.mini-block .el-card__body {\r\n  min-height: 450px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 视频卡片保持原有样式 */\r\n.video-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 450px;\r\n}\r\n\r\n.video-card .el-card__body {\r\n  flex: 1;\r\n  overflow: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 450px;\r\n}\r\n.video-card-yu{\r\n  min-height: 300px;\r\n}\r\n.video-card .el-card__body {\r\n  flex: 1;\r\n  overflow: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.segment-scroll {\r\n  max-height: 150px;\r\n  overflow-y: auto;\r\n  border: 1px solid #eee;\r\n  border-radius: 4px;\r\n  padding: 8px;\r\n  background: #fafbfc;\r\n}\r\n.scene-config-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 限制上传图片的显示大小 */\r\n.image-upload .el-upload--picture-card {\r\n  width: 148px;\r\n  height: 148px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.upload-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 介绍视频首帧图片大小控制 */\r\n.image-upload .el-upload-list__item-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 上传框也添加圆角 */\r\n.image-upload .el-upload--picture-card {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 图片预览样式 */\r\n.preview-container {\r\n  text-align: center;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 70vh;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 图片悬停操作样式 */\r\n.image-preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  border-radius: 6px;\r\n}\r\n\r\n.image-preview-container:hover .image-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.preview-icon,\r\n.delete-icon {\r\n  color: white;\r\n  font-size: 20px;\r\n  margin: 0 10px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.preview-icon:hover,\r\n.delete-icon:hover {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.submit-footer {\r\n  position: fixed;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 250px;\r\n  height: 60px;\r\n  background: #fff;\r\n  border-top: 1px solid #e4e7ed;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  padding: 0 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n}\r\n\r\n.submit-footer .el-button {\r\n  min-width: 100px;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.menu-search .el-input {\r\n  border-radius: 20px;\r\n}\r\n\r\n.menu-search .el-input__inner {\r\n  border-radius: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\n.highlight {\r\n  background-color: #ffeb3b;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.menu-tree {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content:hover {\r\n  background-color: #e6f7ff;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.custom-tree-node {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.highlight {\r\n  background-color: yellow;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n  justify-content: flex-end;\r\n  padding: 0 20px;\r\n}\r\n</style>\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n}\r\n\r\n.submit-footer .el-button {\r\n  min-width: 100px;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.menu-search .el-input {\r\n  border-radius: 20px;\r\n}\r\n\r\n.menu-search .el-input__inner {\r\n  border-radius: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\n.highlight {\r\n  background-color: #ffeb3b;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.menu-tree {\r\n  background\r\n"]}]}