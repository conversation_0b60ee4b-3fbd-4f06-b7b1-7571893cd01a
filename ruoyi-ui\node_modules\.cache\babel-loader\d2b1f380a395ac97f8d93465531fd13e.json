{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\SceneConfigNode.vue", "mtime": 1754893563836}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\babel.config.js", "mtime": 1753326339083}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_sceneView", "require", "name", "props", "node", "type", "Object", "required", "rootTree", "Array", "default", "_default", "sceneTreeOptions", "leftTreeIndustryCode", "String", "watch", "nodeStatus", "val", "findAndOpenParent", "id", "handler", "newNode", "oldNode", "_this", "$nextTick", "initNodeData", "introduceVideoVo", "hasOwnProperty", "updateIntroduceVideoFileList", "initIntroduceVideo", "initFileLists", "immediate", "deep", "newVal", "status", "undefined", "data", "fileLists", "tradition", "wisdom5g", "introduceVideo", "uploadModes", "introduceVideoFileList", "previewVisible", "previewImageUrl", "sceneCascaderProps", "label", "value", "children", "emitPath", "checkStrictly", "created", "mounted", "initData", "methods", "$set", "panoramicViewXmlKey", "backgroundResources", "painPoints", "wisdom5gCopy", "JSON", "parse", "stringify", "for<PERSON>ach", "resource", "backgroundImgFileUrl", "bgImg", "backgroundFileUrl", "bgFile", "tag", "traditionCopy", "_this2", "idx", "trim", "fileName", "split", "pop", "url", "uid", "Date", "now", "viewInfoId", "isUnfold", "displayLocation", "treeClassification", "costEstimate", "title", "contents", "onStatusChange", "p", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "child", "tree", "helper", "nodes", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "err", "e", "f", "addBackgroundResource", "newIdx", "newResource", "coordinates", "independentResource", "push", "console", "log", "removeBackgroundResource", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee2", "wrap", "_callee2$", "_context2", "prev", "next", "$confirm", "confirmButtonText", "cancelButtonText", "then", "_callee", "res", "_callee$", "_context", "$modal", "loading", "backgroundFileDel", "sent", "code", "splice", "$message", "success", "error", "msg", "t0", "closeLoading", "finish", "stop", "catch", "addCoordinate", "resourceIdx", "fileId", "x", "y", "wide", "high", "sceneId", "sceneCode", "xmlKey", "removeCoordinate", "coordIdx", "_this4", "_callee4", "coord", "_callee4$", "_context4", "_callee3", "_callee3$", "_context3", "fileBindDel", "handleSceneCascaderChange", "scene", "findSceneById", "isArray", "_iterator2", "_step2", "found", "formatCoordinatesForSubmit", "clickX", "clickY", "xValues", "map", "join", "yV<PERSON><PERSON>", "beforeUploadSceneConfigImg", "file", "arrayKeyOrKey", "indexOrUndefined", "keyOrUndefined", "_this5", "_callee5", "formData", "imageUrl", "_callee5$", "_context5", "startsWith", "abrupt", "FormData", "append", "uploadSceneFile", "fileUrl", "beforeUploadSceneConfigFile", "_this6", "_callee6", "_callee6$", "_context6", "imgUrl", "addPainPoint", "showTime", "removePainPoint", "add<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cidx", "addCostContent", "remove<PERSON>ost<PERSON><PERSON>nt", "getBackgroundFileList", "handleRemoveBackgroundFile", "getFileList", "fileList", "newFileList", "getUploadMode", "hasFile", "setUploadMode", "handleUrlInput", "getIntroduceVideoFileList", "handleRemoveIntroduceVideo", "onIntroduceVideoStatusChange", "previewImage", "deleteIntroduceVideoImg", "_this7", "deleteBackgroundImg", "_this8", "validateXmlKey", "filteredValue", "replace", "handleIntroduceVideoUrlInput", "handleCoordNumberInput", "field", "computed", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components", "SceneConfigNode"], "sources": ["src/views/SceneConfigNode.vue"], "sourcesContent": ["<template>\r\n  <el-card class=\"mini-block\" shadow=\"never\">\r\n    <div slot=\"header\">\r\n      <span>{{ node.name }}</span>\r\n      <el-switch v-model=\"node.status\" style=\"margin-left: 16px;\" :active-value=\"'0'\" :inactive-value=\"'1'\" @change=\"onStatusChange\" />\r\n    </div>\r\n    <div v-show=\"node.status === '0'\" class=\"sub-category-body\">\r\n      <!-- 主表单项 - 编码名称并排，坐标并排 -->\r\n      <el-form label-width=\"120px\">\r\n        <el-row :gutter=\"16\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"场景编码\">\r\n              <el-input v-model=\"node.code\" disabled type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"场景名称\">\r\n              <el-input v-model=\"node.name\" disabled type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"16\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"坐标X\" required>\r\n              <el-input v-model=\"node.x\" placeholder=\"请输入坐标X（百分比）\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"坐标Y\" required>\r\n              <el-input v-model=\"node.y\" placeholder=\"请输入坐标Y（百分比）\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"场景类型\" required>\r\n          <el-select v-model=\"node.type\" placeholder=\"请选择类型\">\r\n            <el-option label=\"默认\" value=\"1\" />\r\n            <el-option label=\"AI\" value=\"2\" />\r\n            <el-option label=\"三化\" value=\"3\" />\r\n            <el-option label=\"AI+三化\" value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <!-- 只有存在下级菜单时才显示 -->\r\n        <el-row :gutter=\"16\" v-if=\"hasChildren\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否展开下级\" required>\r\n              <el-radio-group v-model=\"node.isUnfold\">\r\n                <el-radio label=\"0\">展示</el-radio>\r\n                <el-radio label=\"1\">关闭</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"下级展示位置\" required>\r\n              <el-radio-group v-model=\"node.displayLocation\">\r\n                <el-radio label=\"0\">默认</el-radio>\r\n                <el-radio label=\"1\">右下</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"下级菜单分类\" required>\r\n              <el-radio-group v-model=\"node.treeClassification\">\r\n                <el-radio label=\"1\">传统</el-radio>\r\n                <el-radio label=\"2\">5G</el-radio>\r\n                <el-radio label=\"3\">全部</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 介绍视频和成本预估并排布局 -->\r\n      <el-row :gutter=\"20\">\r\n        <!-- 左侧：介绍视频模块 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"mini-block\" shadow=\"never\">\r\n            <div slot=\"header\">\r\n              <span>介绍视频</span>\r\n              <el-switch \r\n                v-model=\"node.introduceVideoVo.status\" \r\n                style=\"float:right;\"\r\n                :active-value=\"'0'\" \r\n                :inactive-value=\"'1'\" \r\n                @change=\"onIntroduceVideoStatusChange\" />\r\n            </div>\r\n            <div v-show=\"node.introduceVideoVo.status === '0'\">\r\n              <el-form label-width=\"120px\">\r\n                <el-form-item label=\"介绍视频首帧\">\r\n                  <el-upload\r\n                    class=\"upload image-upload\"\r\n                    action=\"#\"\r\n                    :show-file-list=\"false\"\r\n                    list-type=\"picture-card\"\r\n                    accept=\"image/*\"\r\n                    :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'introduceVideoVo', 'backgroundImgFileUrl', null, null)\"\r\n                    :http-request=\"() => {}\"\r\n                  >\r\n                    <div v-if=\"node.introduceVideoVo.backgroundImgFileUrl\" class=\"image-preview-container\">\r\n                      <img :src=\"node.introduceVideoVo.backgroundImgFileUrl\" class=\"upload-image\" />\r\n                      <div class=\"image-overlay\">\r\n                        <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(node.introduceVideoVo.backgroundImgFileUrl)\" title=\"预览\"></i>\r\n                        <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteIntroduceVideoImg\" title=\"删除\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <i v-else class=\"el-icon-plus\"></i>\r\n                  </el-upload>\r\n                </el-form-item>\r\n                <el-form-item label=\"介绍视频\">\r\n                  <div style=\"margin-bottom: 8px;\">\r\n                    <el-radio-group :value=\"uploadModes.introduceVideo || 'upload'\" @input=\"value => setUploadMode('introduceVideo', value)\" size=\"small\">\r\n                      <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                      <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                  \r\n                  <!-- 上传模式 -->\r\n                  <el-upload\r\n                    v-if=\"(uploadModes.introduceVideo || 'upload') === 'upload'\"\r\n                    action=\"#\"\r\n                    :show-file-list=\"true\"\r\n                    :file-list=\"getIntroduceVideoFileList()\"\r\n                    accept=\".mp4\"\r\n                    :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'introduceVideoVo', 'backgroundFileUrl', null, null)\"\r\n                    :http-request=\"() => {}\"\r\n                    :on-remove=\"handleRemoveIntroduceVideo\"\r\n                  >\r\n                    <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    <div slot=\"tip\" class=\"el-upload__tip\">只能上传mp4文件</div>\r\n                  </el-upload>\r\n                  \r\n                  <!-- 链接模式 -->\r\n                  <el-input\r\n                    v-else\r\n                    v-model=\"node.introduceVideoVo.backgroundFileUrl\"\r\n                    placeholder=\"请输入视频链接\"\r\n                    @input=\"handleIntroduceVideoUrlInput\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        \r\n        <!-- 右侧：成本预估模块 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"mini-block\" shadow=\"never\">\r\n            <div slot=\"header\">\r\n              <span>成本预估</span>\r\n              <el-switch v-model=\"node.costEstimate.status\" style=\"float:right;\" :active-value=\"'0'\" :inactive-value=\"'1'\" />\r\n            </div>\r\n            <div v-show=\"node.costEstimate.status === '0'\">\r\n              <el-form label-width=\"120px\">\r\n                <el-form-item label=\"大标题\" required>\r\n                  <el-input v-model=\"node.costEstimate.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"内容\" required>\r\n                  <div v-for=\"(content, cidx) in node.costEstimate.contents\" :key=\"'costEstimate-content-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                    <el-input v-model=\"node.costEstimate.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                    <el-button icon=\"el-icon-delete\" @click=\"removeCostContent(cidx)\" circle size=\"mini\" />\r\n                  </div>\r\n                  <el-button type=\"primary\" plain @click=\"addCostContent\">增加内容</el-button>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <!-- 传统小类 -->\r\n      <el-card class=\"mini-block\" shadow=\"never\">\r\n        <div slot=\"header\">传统</div>\r\n        <el-form label-width=\"120px\">\r\n          <el-form-item label=\"名称\" required>\r\n            <el-input v-model=\"node.tradition.name\" placeholder=\"请输入名称\" type=\"text\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"全景图唯一标识\" required>\r\n            <el-input \r\n              v-model=\"node.tradition.panoramicViewXmlKey\" \r\n              placeholder=\"请输入全景图唯一标识（仅英文）\" \r\n              type=\"text\"\r\n              @input=\"validateXmlKey($event, 'tradition')\"\r\n            />\r\n          </el-form-item>\r\n          \r\n          <!-- 传统模块背景资源 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">背景资源</div>\r\n            <div v-for=\"(resource, idx) in node.tradition.backgroundResources\" :key=\"idx\" class=\"background-resource-item\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">背景资源 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removeBackgroundResource('tradition', idx)\">删除</el-button>\r\n              </div>\r\n              <!-- <el-form-item label=\"标签\" required>\r\n                <el-input v-model=\"resource.label\" placeholder=\"请输入标签\" type=\"text\" />\r\n              </el-form-item> -->\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"坐标组\" required>\r\n                    <div v-for=\"(coord, coordIdx) in resource.coordinates\" :key=\"coordIdx\" class=\"coordinate-group\">\r\n                      <div class=\"coordinate-header\">\r\n                        <span>坐标组 {{ coordIdx + 1 }}</span>\r\n                        <el-button \r\n                          type=\"danger\" \r\n                          size=\"mini\" \r\n                          plain \r\n                          @click=\"removeCoordinate('tradition', idx, coordIdx)\"\r\n                        >\r\n                          删除\r\n                        </el-button>\r\n                      </div>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"X坐标\">\r\n                            <el-input v-model=\"coord.x\" placeholder=\"请输入X坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"Y坐标\">\r\n                            <el-input v-model=\"coord.y\" placeholder=\"请输入Y坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"绑定场景\">\r\n                            <el-cascader\r\n                              v-model=\"coord.sceneId\"\r\n                              :options=\"sceneTreeOptions\"\r\n                              :props=\"sceneCascaderProps\"\r\n                              filterable\r\n                              check-strictly\r\n                              placeholder=\"选择场景\"\r\n                              style=\"width: 100%;\"\r\n                              @change=\"val => handleSceneCascaderChange(val, 'tradition', idx, coordIdx)\"\r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"宽度\">\r\n                            <el-input-number \r\n                              :value=\"coord.wide\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'wide')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入宽度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"高度\">\r\n                            <el-input-number \r\n                              :value=\"coord.high\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'high')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入高度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"全景xml标签\">\r\n                            <el-input v-model=\"coord.xmlKey\" placeholder=\"请输入全景xml标签\" style=\"width: 100%\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                    <el-button type=\"primary\" size=\"mini\" plain @click=\"addCoordinate('tradition', idx)\">\r\n                      增加坐标组\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'tradition', 'backgroundResources', idx, 'bgImg')\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"resource.bgImg\" class=\"image-preview-container\">\r\n                        <img :src=\"resource.bgImg\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(resource.bgImg)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBackgroundImg('tradition', idx)\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\" required>\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.tradition[idx] || 'upload'\" @input=\"value => setUploadMode('tradition', idx, value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.tradition[idx] || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"getFileList('tradition', idx)\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'tradition', 'backgroundResources', idx, 'bgFile')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"(file, fileList) => handleRemoveBackgroundFile(node, 'tradition', idx)\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"resource.bgFile\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"value => handleUrlInput('tradition', idx, value)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addBackgroundResource('tradition')\">增加背景资源</el-button>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 传统模块痛点价值 - 统一输入框长度 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">痛点价值</div>\r\n            <div v-for=\"(point, idx) in node.tradition.painPoints\" :key=\"'tradition-' + idx\" class=\"pain-point-block\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">痛点价值 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removePainPoint('tradition', idx)\">删除</el-button>\r\n              </div>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"大标题\" required>\r\n                    <el-input v-model=\"point.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"展示时间\" required>\r\n                    <el-input-number v-model=\"point.showTime\" :min=\"0\" style=\"width: 100%\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-form-item label=\"内容\" required>\r\n                <el-row :gutter=\"16\">\r\n                  <el-col :span=\"16\">\r\n                    <div v-for=\"(content, cidx) in point.contents\" :key=\"'tradition-content-' + idx + '-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                      <el-input v-model=\"point.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                      <el-button icon=\"el-icon-delete\" @click=\"removePainContent('tradition', idx, cidx)\" circle size=\"mini\" />\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addPainContent('tradition', idx)\">增加内容</el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addPainPoint('tradition')\">增加痛点价值</el-button>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </el-card>\r\n      <!-- 5G智慧小类 -->\r\n      <el-card class=\"mini-block\" shadow=\"never\">\r\n        <div slot=\"header\">5G智慧</div>\r\n        <el-form label-width=\"120px\">\r\n          <el-form-item label=\"名称\" required>\r\n            <el-input v-model=\"node.wisdom5g.name\" placeholder=\"请输入名称\" type=\"text\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"全景图唯一标识\" required>\r\n            <el-input \r\n              v-model=\"node.wisdom5g.panoramicViewXmlKey\" \r\n              placeholder=\"请输入全景图唯一标识（仅英文）\" \r\n              type=\"text\"\r\n              @input=\"validateXmlKey($event, 'wisdom5g')\"\r\n            />\r\n          </el-form-item>\r\n          \r\n          <!-- 5G智慧模块背景资源 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">背景资源</div>\r\n            <div v-for=\"(resource, idx) in node.wisdom5g.backgroundResources\" :key=\"'wisdom5g-bg-' + idx\" class=\"background-resource-item\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">背景资源 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removeBackgroundResource('wisdom5g', idx)\">删除</el-button>\r\n              </div>\r\n              <!-- <el-form-item label=\"标签\" required>\r\n                <el-input v-model=\"resource.label\" placeholder=\"请输入标签\" type=\"text\" />\r\n              </el-form-item> -->\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"坐标组\" required>\r\n                    <div v-for=\"(coord, coordIdx) in resource.coordinates\" :key=\"coordIdx\" class=\"coordinate-group\">\r\n                      <div class=\"coordinate-header\">\r\n                        <span>坐标组 {{ coordIdx + 1 }}</span>\r\n                        <el-button \r\n                          type=\"danger\" \r\n                          size=\"mini\" \r\n                          plain \r\n                          @click=\"removeCoordinate('wisdom5g', idx, coordIdx)\"\r\n                        >\r\n                          删除\r\n                        </el-button>\r\n                      </div>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"X坐标\">\r\n                            <el-input v-model=\"coord.x\" placeholder=\"请输入X坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"Y坐标\">\r\n                            <el-input v-model=\"coord.y\" placeholder=\"请输入Y坐标\" type=\"text\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"8\">\r\n                          <el-form-item label=\"绑定场景\">\r\n                            <el-cascader\r\n                              v-model=\"coord.sceneId\"\r\n                              :options=\"sceneTreeOptions\"\r\n                              :props=\"sceneCascaderProps\"\r\n                              filterable\r\n                              check-strictly\r\n                              placeholder=\"选择场景\"\r\n                              style=\"width: 100%;\"\r\n                              @change=\"val => handleSceneCascaderChange(val, 'wisdom5g', idx, coordIdx)\"\r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                      <el-row :gutter=\"16\">\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"宽度\">\r\n                            <el-input-number \r\n                              :value=\"coord.wide\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'wide')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入宽度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"高度\">\r\n                            <el-input-number \r\n                              :value=\"coord.high\" \r\n                              @input=\"val => handleCoordNumberInput(val, coord, 'high')\"\r\n                              :min=\"0\" \r\n                              :max=\"999\" \r\n                              placeholder=\"请输入高度\" \r\n                              style=\"width: 100%\" \r\n                            />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"10\">\r\n                          <el-form-item label=\"全景xml标签\">\r\n                            <el-input v-model=\"coord.xmlKey\" placeholder=\"请输入全景xml标签\" style=\"width: 100%\" />\r\n                          </el-form-item>\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                    <el-button type=\"primary\" size=\"mini\" plain @click=\"addCoordinate('wisdom5g', idx)\">\r\n                      增加坐标组\r\n                    </el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigImg(file, node, 'wisdom5g', 'backgroundResources', idx, 'bgImg')\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"resource.bgImg\" class=\"image-preview-container\">\r\n                        <img :src=\"resource.bgImg\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(resource.bgImg)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBackgroundImg('wisdom5g', idx)\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\" required>\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.wisdom5g[idx] || 'upload'\" @input=\"value => setUploadMode('wisdom5g', idx, value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.wisdom5g[idx] || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"getFileList('wisdom5g', idx)\"\r\n                      :before-upload=\"file => beforeUploadSceneConfigFile(file, node, 'wisdom5g', 'backgroundResources', idx, 'bgFile')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"(file, fileList) => handleRemoveBackgroundFile(node, 'wisdom5g', idx)\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"resource.bgFile\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"value => handleUrlInput('wisdom5g', idx, value)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addBackgroundResource('wisdom5g')\">增加背景资源</el-button>\r\n            </el-form-item>\r\n          </div>\r\n          \r\n          <!-- 5G智慧模块痛点价值 - 统一输入框长度 -->\r\n          <div class=\"mini-block\" style=\"margin-bottom:0;\">\r\n            <div style=\"font-weight:bold;margin-bottom:8px;\">痛点价值</div>\r\n            <div v-for=\"(point, idx) in node.wisdom5g.painPoints\" :key=\"'wisdom5g-' + idx\" class=\"pain-point-block\">\r\n              <div class=\"resource-header\">\r\n                <span class=\"resource-title\">痛点价值 {{ idx + 1 }}</span>\r\n                <el-button type=\"danger\" size=\"mini\" plain @click=\"removePainPoint('wisdom5g', idx)\">删除</el-button>\r\n              </div>\r\n              <el-row :gutter=\"16\">\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"大标题\" required>\r\n                    <el-input v-model=\"point.title\" placeholder=\"请输入大标题\" type=\"text\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"展示时间\" required>\r\n                    <el-input-number v-model=\"point.showTime\" :min=\"0\" style=\"width: 100%\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-form-item label=\"内容\" required>\r\n                <el-row :gutter=\"16\">\r\n                  <el-col :span=\"16\">\r\n                    <div v-for=\"(content, cidx) in point.contents\" :key=\"'wisdom5g-content-' + idx + '-' + cidx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                      <el-input v-model=\"point.contents[cidx]\" placeholder=\"请输入内容\" style=\"width:calc(100% - 40px);margin-right:8px;\" type=\"text\" />\r\n                      <el-button icon=\"el-icon-delete\" @click=\"removePainContent('wisdom5g', idx, cidx)\" circle size=\"mini\" />\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addPainContent('wisdom5g', idx)\">增加内容</el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" plain @click=\"addPainPoint('wisdom5g')\">增加痛点价值</el-button>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </el-card>\r\n    </div>\r\n    \r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"previewVisible\"\r\n      title=\"图片预览\"\r\n      width=\"60%\"\r\n      append-to-body\r\n    >\r\n      <div class=\"preview-container\">\r\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\r\n      </div>\r\n    </el-dialog>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport { uploadSceneFile, backgroundFileDel, fileBindDel } from '@/api/view/sceneView'\r\n\r\nexport default {\r\n  name: 'SceneConfigNode',\r\n  props: {\r\n    node: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    rootTree: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    sceneTreeOptions: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    leftTreeIndustryCode: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  watch: {\r\n    'node.status'(val) {\r\n      if (val === '0') {\r\n        this.findAndOpenParent(this.node.id, this.rootTree)\r\n      }\r\n    },\r\n    node: {\r\n      handler(newNode, oldNode) {\r\n        if (newNode && newNode !== oldNode) {\r\n          this.$nextTick(() => {\r\n            // 初始化节点数据\r\n            this.initNodeData()\r\n            \r\n            // 确保introduceVideoVo存在且有完整数据时才处理\r\n            if (newNode.introduceVideoVo && newNode.introduceVideoVo.hasOwnProperty('status')) {\r\n              // 数据已存在，不需要初始化，直接更新文件列表\r\n              this.updateIntroduceVideoFileList()\r\n            } else if (!newNode.introduceVideoVo) {\r\n              // 数据不存在时才初始化\r\n              this.initIntroduceVideo()\r\n            }\r\n            // 重新初始化文件列表，清除可能的继承问题\r\n            this.initFileLists()\r\n          })\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    // 监听introduceVideoVo整个对象的变化\r\n    'node.introduceVideoVo': {\r\n      handler(newVal) {\r\n        if (newVal && newVal.status !== undefined) {\r\n          this.updateIntroduceVideoFileList()\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      fileLists: {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: []\r\n      },\r\n      uploadModes: {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: 'upload'\r\n      },\r\n      // 添加介绍视频文件列表缓存\r\n      introduceVideoFileList: [],\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      // 场景级联选择器配置\r\n      sceneCascaderProps: {\r\n        label: 'sceneName',\r\n        value: 'id',\r\n        children: 'children',\r\n        emitPath: false,\r\n        checkStrictly: true\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.initIntroduceVideo()\r\n    this.initNodeData()\r\n  },\r\n  mounted() {\r\n    this.initData()\r\n    this.initFileLists()\r\n  },\r\n  methods: {\r\n    initData() {\r\n      // 确保5G智慧模块有默认结构并深拷贝避免引用共享\r\n      if (!this.node.wisdom5g) {\r\n        this.$set(this.node, 'wisdom5g', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 深拷贝现有的5G智慧数据，避免多个场景共享引用\r\n        const wisdom5gCopy = JSON.parse(JSON.stringify(this.node.wisdom5g))\r\n        this.$set(this.node, 'wisdom5g', wisdom5gCopy)\r\n        \r\n        // 修正字段映射\r\n        if (this.node.wisdom5g.backgroundResources) {\r\n          this.node.wisdom5g.backgroundResources.forEach(resource => {\r\n            if (resource.backgroundImgFileUrl && !resource.bgImg) {\r\n              resource.bgImg = resource.backgroundImgFileUrl\r\n            }\r\n            if (resource.backgroundFileUrl && !resource.bgFile) {\r\n              resource.bgFile = resource.backgroundFileUrl\r\n            }\r\n            if (resource.tag && !resource.label) {\r\n              resource.label = resource.tag\r\n            }\r\n          })\r\n        }\r\n      }\r\n      \r\n      // 同样处理传统模块\r\n      if (!this.node.tradition) {\r\n        this.$set(this.node, 'tradition', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 深拷贝传统模块数据\r\n        const traditionCopy = JSON.parse(JSON.stringify(this.node.tradition))\r\n        this.$set(this.node, 'tradition', traditionCopy)\r\n      }\r\n    },\r\n    // 初始化文件列表\r\n    initFileLists() {\r\n      // 清空所有文件列表，避免继承问题\r\n      this.fileLists = {\r\n        tradition: {},\r\n        wisdom5g: {},\r\n        introduceVideo: []\r\n      }\r\n      \r\n      // 初始化传统模块的文件列表\r\n      if (this.node.tradition && this.node.tradition.backgroundResources) {\r\n        this.node.tradition.backgroundResources.forEach((resource, idx) => {\r\n          // 只有当资源确实有文件时才创建文件列表\r\n          if (resource.bgFile && resource.bgFile.trim()) {\r\n            const fileName = resource.bgFile.split('/').pop()\r\n            this.$set(this.fileLists.tradition, idx, [{\r\n              name: fileName,\r\n              url: resource.bgFile,\r\n              uid: Date.now() + idx\r\n            }])\r\n          } else {\r\n            // 明确设置为空数组\r\n            this.$set(this.fileLists.tradition, idx, [])\r\n          }\r\n        })\r\n      }\r\n      \r\n      // 初始化5G智慧模块的文件列表\r\n      if (this.node.wisdom5g && this.node.wisdom5g.backgroundResources) {\r\n        this.node.wisdom5g.backgroundResources.forEach((resource, idx) => {\r\n          // 只有当资源确实有文件时才创建文件列表\r\n          if (resource.bgFile && resource.bgFile.trim()) {\r\n            const fileName = resource.bgFile.split('/').pop()\r\n            this.$set(this.fileLists.wisdom5g, idx, [{\r\n              name: fileName,\r\n              url: resource.bgFile,\r\n              uid: Date.now() + idx + 1000\r\n            }])\r\n          } else {\r\n            // 明确设置为空数组\r\n            this.$set(this.fileLists.wisdom5g, idx, [])\r\n          }\r\n        })\r\n      }\r\n    },\r\n    // 初始化介绍视频对象\r\n    initIntroduceVideo() {\r\n      if (!this.node.introduceVideoVo) {\r\n        this.$set(this.node, 'introduceVideoVo', {\r\n          id: '',\r\n          type: '',\r\n          viewInfoId: '',\r\n          status: '0',\r\n          backgroundImgFileUrl: '',\r\n          backgroundFileUrl: ''\r\n        })\r\n      }\r\n      // 完全删除status的重新设置，保持接口返回的原始值\r\n      this.updateIntroduceVideoFileList()\r\n    },\r\n    // 确保其他数据结构也有默认值\r\n    initNodeData() {\r\n      // 确保基础字段有默认值 - 只在真正没有值时才设置默认值\r\n      if (this.node.isUnfold === undefined || this.node.isUnfold === null || this.node.isUnfold === '') {\r\n        this.$set(this.node, 'isUnfold', '1')\r\n      }\r\n      if (this.node.displayLocation === undefined || this.node.displayLocation === null || this.node.displayLocation === '') {\r\n        this.$set(this.node, 'displayLocation', '0')\r\n      }\r\n      if (this.node.treeClassification === undefined || this.node.treeClassification === null || this.node.treeClassification === '') {\r\n        this.$set(this.node, 'treeClassification', '3')\r\n      }\r\n      \r\n      // 确保传统模块有默认结构\r\n      if (!this.node.tradition) {\r\n        this.$set(this.node, 'tradition', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      }\r\n      \r\n      // 确保5G智慧模块有默认结构并修正字段映射\r\n      if (!this.node.wisdom5g) {\r\n        this.$set(this.node, 'wisdom5g', {\r\n          name: '',\r\n          panoramicViewXmlKey: '',\r\n          backgroundResources: [],\r\n          painPoints: []\r\n        })\r\n      } else {\r\n        // 修正5G智慧模块的字段映射\r\n        if (this.node.wisdom5g.backgroundResources) {\r\n          this.node.wisdom5g.backgroundResources.forEach(resource => {\r\n            // 确保字段名称正确\r\n            if (resource.backgroundImgFileUrl && !resource.bgImg) {\r\n              resource.bgImg = resource.backgroundImgFileUrl\r\n            }\r\n            if (resource.backgroundFileUrl && !resource.bgFile) {\r\n              resource.bgFile = resource.backgroundFileUrl\r\n            }\r\n            if (resource.tag && !resource.label) {\r\n              resource.label = resource.tag\r\n            }\r\n          })\r\n        }\r\n      }\r\n      \r\n      // 确保成本预估有默认结构\r\n      if (!this.node.costEstimate) {\r\n        this.$set(this.node, 'costEstimate', {\r\n          status: '0',\r\n          title: '',\r\n          contents: []\r\n        })\r\n      }\r\n      \r\n      // 确保成本预估有status字段\r\n      if (this.node.costEstimate && this.node.costEstimate.status === undefined) {\r\n        this.$set(this.node.costEstimate, 'status', '0')\r\n      }\r\n    },\r\n    // 开关联动\r\n    onStatusChange(val) {\r\n      if (val === '0') {\r\n        // 开启时递归开启所有父级\r\n        let p = this.parent\r\n        while (p) {\r\n          if (p.status !== '0') p.status = '0'\r\n          p = p.parent\r\n        }\r\n      } else {\r\n        // 关闭时递归关闭所有子级\r\n        function closeChildren(node) {\r\n          if (node.children && node.children.length) {\r\n            node.children.forEach(child => {\r\n              child.status = '1'\r\n              closeChildren(child)\r\n            })\r\n          }\r\n        }\r\n        closeChildren(this.node)\r\n      }\r\n    },\r\n    // 递归查找并开启所有父级\r\n    findAndOpenParent(id, tree) {\r\n      function helper(nodes, parent) {\r\n        for (let node of nodes) {\r\n          if (node.id === id) {\r\n            if (parent) parent.status = '0'\r\n            return true\r\n          }\r\n          if (node.children && node.children.length) {\r\n            if (helper(node.children, node)) {\r\n              if (parent) parent.status = '0'\r\n              return true\r\n            }\r\n          }\r\n        }\r\n        return false\r\n      }\r\n      helper(tree, null)\r\n    },\r\n    // 新增背景资源管理方法\r\n    addBackgroundResource(type) {\r\n      if (!this.node[type].backgroundResources) {\r\n        this.$set(this.node[type], 'backgroundResources', [])\r\n      }\r\n      \r\n      const newIdx = this.node[type].backgroundResources.length\r\n      \r\n      // 创建完全独立的新资源对象，不包含默认坐标组\r\n      const newResource = { \r\n        id: null,\r\n        tag: '', \r\n        status: '',\r\n        type: '',\r\n        viewInfoId: '',\r\n        bgImg: '', \r\n        bgFile: '',\r\n        coordinates: [] // 改为空数组，不默认添加坐标组\r\n      }\r\n      \r\n      // 使用深拷贝确保对象完全独立\r\n      const independentResource = JSON.parse(JSON.stringify(newResource))\r\n      this.node[type].backgroundResources.push(independentResource)\r\n      \r\n      // 初始化对应的独立文件列表\r\n      this.$set(this.fileLists[type], newIdx, [])\r\n      console.log('添加新背景资源:', independentResource)\r\n    },\r\n    \r\n    // 删除背景资源\r\n    async removeBackgroundResource(type, idx) {\r\n      const resource = this.node[type].backgroundResources[idx]\r\n      \r\n      this.$confirm('确定删除此背景资源吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        // 如果背景资源有ID，调用删除接口\r\n        if (resource.id) {\r\n          try {\r\n            this.$modal.loading(\"正在删除背景资源，请稍候...\")\r\n            const res = await backgroundFileDel({ id: resource.id })\r\n            if (res.code === 0) {\r\n              this.node[type].backgroundResources.splice(idx, 1)\r\n              this.$message.success('删除成功')\r\n            } else {\r\n              this.$message.error(res.msg || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('删除失败')\r\n          } finally {\r\n            this.$modal.closeLoading()\r\n          }\r\n        } else {\r\n          // 没有ID的新背景资源，直接从数组中移除\r\n          this.node[type].backgroundResources.splice(idx, 1)\r\n          this.$message.success('删除成功')\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 添加坐标组\r\n    addCoordinate(type, resourceIdx) {\r\n      const resource = this.node[type].backgroundResources[resourceIdx]\r\n      if (!resource.coordinates) {\r\n        this.$set(resource, 'coordinates', [])\r\n      }\r\n      resource.coordinates.push({ \r\n        id: 0,\r\n        fileId: 0,\r\n        x: '', \r\n        y: '', \r\n        wide: '', \r\n        high: '', \r\n        sceneId: '', \r\n        sceneCode: '',\r\n        xmlKey: ''\r\n      })\r\n    },\r\n    \r\n    // 删除坐标组\r\n    async removeCoordinate(type, resourceIdx, coordIdx) {\r\n      const coord = this.node[type].backgroundResources[resourceIdx].coordinates[coordIdx]\r\n      \r\n      this.$confirm('确定删除此坐标组吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        // 如果坐标组有ID，调用删除接口\r\n        if (coord.id) {\r\n          try {\r\n            this.$modal.loading(\"正在删除坐标组，请稍候...\")\r\n            const res = await fileBindDel({ id: coord.id })\r\n            if (res.code === 0) {\r\n              this.node[type].backgroundResources[resourceIdx].coordinates.splice(coordIdx, 1)\r\n              this.$message.success('删除成功')\r\n            } else {\r\n              this.$message.error(res.msg || '删除失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('删除失败')\r\n          } finally {\r\n            this.$modal.closeLoading()\r\n          }\r\n        } else {\r\n          // 没有ID的新坐标组，直接从数组中移除\r\n          this.node[type].backgroundResources[resourceIdx].coordinates.splice(coordIdx, 1)\r\n          this.$message.success('删除成功')\r\n        }\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 场景级联选择器变化处理\r\n    handleSceneCascaderChange(val, type, resourceIdx, coordIdx) {\r\n      const scene = this.findSceneById(this.sceneTreeOptions, val)\r\n      if (scene) {\r\n        const coord = this.node[type].backgroundResources[resourceIdx].coordinates[coordIdx]\r\n        coord.sceneCode = scene.sceneCode || ''\r\n        console.log('选择的场景:', scene, '设置sceneCode:', coord.sceneCode)\r\n      }\r\n    },\r\n    \r\n    // 根据ID查找场景\r\n    findSceneById(tree, id) {\r\n      if (!tree || !Array.isArray(tree)) return null\r\n      \r\n      for (const node of tree) {\r\n        if (node.id === id) {\r\n          return node\r\n        }\r\n        if (node.children && node.children.length) {\r\n          const found = this.findSceneById(node.children, id)\r\n          if (found) return found\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    \r\n    // 格式化坐标数据用于提交\r\n    formatCoordinatesForSubmit(coordinates) {\r\n      if (!coordinates || !Array.isArray(coordinates)) {\r\n        return { clickX: '', clickY: '' }\r\n      }\r\n      \r\n      const xValues = coordinates.map(coord => coord.x || '0').join(',')\r\n      const yValues = coordinates.map(coord => coord.y || '0').join(',')\r\n      \r\n      return {\r\n        clickX: xValues,\r\n        clickY: yValues\r\n      }\r\n    },\r\n    \r\n    // 修改现有的上传方法\r\n    async beforeUploadSceneConfigImg(file, node, type, arrayKeyOrKey, indexOrUndefined, keyOrUndefined) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传图片，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.leftTreeIndustryCode)\r\n        formData.append('sceneCode', node.code)\r\n\r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 单独上传背景图片首帧时，使用 fileUrl\r\n          const imageUrl = res.data.fileUrl\r\n          \r\n          // 判断是否为数组形式的上传\r\n          if (typeof indexOrUndefined === 'number' && keyOrUndefined) {\r\n            // 数组形式：node[type][arrayKey][index][key]\r\n            this.$set(node[type][arrayKeyOrKey][indexOrUndefined], keyOrUndefined, imageUrl)\r\n            console.log('上传成功，设置图片URL:', imageUrl)\r\n            console.log('当前resource:', node[type][arrayKeyOrKey][indexOrUndefined])\r\n            this.$message.success('上传成功')\r\n          } else {\r\n            // 单个字段形式：node[type][key]\r\n            this.$set(node[type], arrayKeyOrKey, imageUrl)\r\n            this.$message.success('上传成功')\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('上传错误:', error)\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    \r\n    async beforeUploadSceneConfigFile(file, node, type, arrayKeyOrKey, indexOrUndefined, keyOrUndefined) {\r\n      try {\r\n        this.$modal.loading(\"正在上传文件，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.leftTreeIndustryCode)\r\n        formData.append('sceneCode', node.code)\r\n\r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 判断是否为数组形式的上传\r\n          if (typeof indexOrUndefined === 'number' && keyOrUndefined) {\r\n            // 数组形式处理...\r\n            this.$set(node[type][arrayKeyOrKey][indexOrUndefined], keyOrUndefined, res.data.fileUrl)\r\n            \r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            this.$set(this.fileLists[type], indexOrUndefined, [{\r\n              name: fileName,\r\n              url: res.data.fileUrl,\r\n              uid: Date.now()\r\n            }])\r\n            \r\n            if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n              this.$set(node[type][arrayKeyOrKey][indexOrUndefined], 'bgImg', res.data.imgUrl)\r\n              this.$message.success('上传成功，已自动生成背景图片首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          } else {\r\n            // 单个字段形式：node[type][key]\r\n            this.$set(node[type], arrayKeyOrKey, res.data.fileUrl)\r\n            \r\n            // 如果是介绍视频上传\r\n            if (type === 'introduceVideoVo' && arrayKeyOrKey === 'backgroundFileUrl') {\r\n              // 更新介绍视频文件列表\r\n              this.updateIntroduceVideoFileList()\r\n              \r\n              if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n                this.$set(node[type], 'backgroundImgFileUrl', res.data.imgUrl)\r\n                this.$message.success('上传成功，已自动生成介绍视频首帧')\r\n              } else {\r\n                this.$message.success('上传成功')\r\n              }\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    addPainPoint(type) {\r\n      this.node[type].painPoints = this.node[type].painPoints || []\r\n      this.node[type].painPoints.push({ title: '', contents: [''], showTime: '' })\r\n    },\r\n    removePainPoint(type, idx) {\r\n      this.node[type].painPoints.splice(idx, 1)\r\n    },\r\n    addPainContent(type, idx) {\r\n      this.node[type].painPoints[idx].contents.push('')\r\n    },\r\n    removePainContent(type, idx, cidx) {\r\n      this.node[type].painPoints[idx].contents.splice(cidx, 1)\r\n    },\r\n    addCostContent() {\r\n      this.node.costEstimate.contents.push('')\r\n    },\r\n    removeCostContent(cidx) {\r\n      this.node.costEstimate.contents.splice(cidx, 1)\r\n    },\r\n    // 获取背景文件列表\r\n    getBackgroundFileList(node, type, idx) {\r\n      const resource = node[type].backgroundResources[idx]\r\n      if (resource && resource.bgFile) {\r\n        const fileName = resource.bgFile.split('/').pop()\r\n        return [{\r\n          name: fileName,\r\n          url: resource.bgFile,\r\n          uid: Date.now() + idx\r\n        }]\r\n      }\r\n      return []\r\n    },\r\n    // 处理背景文件删除\r\n    handleRemoveBackgroundFile(node, type, idx) {\r\n      const resource = node[type].backgroundResources[idx]\r\n      resource.bgFile = ''\r\n      resource.bgImg = '' // 同时清空背景图片首帧\r\n      this.$set(this.fileLists[type], idx, [])\r\n      this.$message.success('文件已删除')\r\n    },\r\n    // 获取文件列表 - 确保返回正确的文件列表\r\n    getFileList(type, idx) {\r\n      // 检查对应的资源是否真的有文件\r\n      const resource = this.node[type] && this.node[type].backgroundResources && this.node[type].backgroundResources[idx]\r\n      \r\n      if (!resource || !resource.bgFile || !resource.bgFile.trim()) {\r\n        // 如果没有文件，返回空数组\r\n        return []\r\n      }\r\n      \r\n      // 如果文件列表中有数据且与资源文件匹配，返回文件列表\r\n      if (this.fileLists[type] && this.fileLists[type][idx] && this.fileLists[type][idx].length > 0) {\r\n        const fileList = this.fileLists[type][idx]\r\n        // 验证文件列表中的URL是否与资源中的文件URL匹配\r\n        if (fileList[0].url === resource.bgFile) {\r\n          return fileList\r\n        }\r\n      }\r\n      \r\n      // 如果文件列表不匹配或为空，但资源有文件，重新创建文件列表\r\n      if (resource.bgFile && resource.bgFile.trim()) {\r\n        const fileName = resource.bgFile.split('/').pop()\r\n        const newFileList = [{\r\n          name: fileName,\r\n          url: resource.bgFile,\r\n          uid: Date.now()\r\n        }]\r\n        this.$set(this.fileLists[type], idx, newFileList)\r\n        return newFileList\r\n      }\r\n      \r\n      return []\r\n    },\r\n    getUploadMode(type, idx) {\r\n      if (!this.uploadModes[type][idx]) {\r\n        // 默认根据是否已有文件URL来判断模式\r\n        const resource = this.node[type].backgroundResources[idx]\r\n        const hasFile = resource && resource.bgFile\r\n        this.$set(this.uploadModes[type], idx, hasFile ? 'upload' : 'upload')\r\n      }\r\n      return this.uploadModes[type][idx]\r\n    },\r\n    setUploadMode(type, idx, value) {\r\n      if (typeof idx === 'string') {\r\n        // 介绍视频模式：type='introduceVideo', idx='upload'或'url'\r\n        this.$set(this.uploadModes, type, idx)\r\n      } else {\r\n        // 背景资源模式：type='tradition'或'wisdom5g', idx=数字索引, value='upload'或'url'\r\n        this.$set(this.uploadModes[type], idx, value)\r\n      }\r\n    },\r\n    handleUrlInput(type, idx, value) {\r\n      // 清空文件列表\r\n      this.$set(this.fileLists[type], idx, [])\r\n      \r\n      // 如果输入了链接，创建对应的文件列表项\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.$set(this.fileLists[type], idx, [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }])\r\n      }\r\n    },\r\n    // 更新介绍视频文件列表\r\n    updateIntroduceVideoFileList() {\r\n      if (this.node.introduceVideoVo && this.node.introduceVideoVo.backgroundFileUrl) {\r\n        const fileName = this.node.introduceVideoVo.backgroundFileUrl.split('/').pop()\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: this.node.introduceVideoVo.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.introduceVideoFileList = []\r\n      }\r\n    },\r\n    getIntroduceVideoFileList() {\r\n      return this.introduceVideoFileList\r\n    },\r\n    handleRemoveIntroduceVideo() {\r\n      if (this.node.introduceVideoVo) {\r\n        this.node.introduceVideoVo.backgroundFileUrl = ''\r\n        this.node.introduceVideoVo.backgroundImgFileUrl = ''\r\n        this.$message.success('介绍视频已删除')\r\n        // 同时更新文件列表\r\n        this.updateIntroduceVideoFileList()\r\n      }\r\n    },\r\n    // 介绍视频开关变化\r\n    onIntroduceVideoStatusChange(val) {\r\n      // 简单处理，不需要复杂逻辑\r\n    },\r\n    \r\n    // 图片预览\r\n    previewImage(url) {\r\n      if (url) {\r\n        this.previewImageUrl = url\r\n        this.previewVisible = true\r\n      }\r\n    },\r\n    \r\n    // 删除介绍视频图片\r\n    deleteIntroduceVideoImg() {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.node.introduceVideoVo.backgroundImgFileUrl = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    \r\n    // 删除背景资源图片\r\n    deleteBackgroundImg(type, idx) {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.node[type].backgroundResources[idx].bgImg = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    // 验证XML Key输入（只允许英文、数字和下划线）\r\n    validateXmlKey(value, type) {\r\n      // 只保留英文字母、数字和下划线\r\n      const filteredValue = value.replace(/[^a-zA-Z0-9_]/g, '')\r\n      this.node[type].panoramicViewXmlKey = filteredValue\r\n    },\r\n    handleIntroduceVideoUrlInput(value) {\r\n      this.introduceVideoFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n      this.updateIntroduceVideoFileList()\r\n    },\r\n    // 处理坐标数字输入\r\n    handleCoordNumberInput(val, coord, field) {\r\n      // 如果val为null或undefined，设置为空字符串\r\n      if (val === null || val === undefined) {\r\n        coord[field] = ''\r\n      } else {\r\n        coord[field] = val\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断当前节点是否有子节点\r\n    hasChildren() {\r\n      return this.node && this.node.children && this.node.children.length > 0\r\n    }\r\n  },\r\n  components: {\r\n    SceneConfigNode: null // 递归注册，主页面import时补全\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 限制上传图片的显示大小 */\r\n.image-upload .el-upload--picture-card {\r\n  width: 148px;\r\n  height: 148px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.upload-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 背景图片首帧图片大小控制 */\r\n.image-upload .el-upload-list__item-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 上传框也添加圆角 */\r\n.image-upload .el-upload--picture-card {\r\n  border-radius: 8px;\r\n}\r\n\r\n.mini-block {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.background-resource-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  background-color: #fafafa;\r\n  position: relative;\r\n}\r\n\r\n.resource-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.resource-title {\r\n  font-weight: bold;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.pain-point-block {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.pain-point-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 图片预览样式 */\r\n.preview-container {\r\n  text-align: center;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 70vh;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 图片悬停操作样式 */\r\n.image-preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  border-radius: 6px;\r\n}\r\n\r\n.image-preview-container:hover .image-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.preview-icon,\r\n.delete-icon {\r\n  color: white;\r\n  font-size: 20px;\r\n  margin: 0 10px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.preview-icon:hover,\r\n.delete-icon:hover {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.coordinate-group {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  margin-bottom: 12px;\r\n  background-color: #fafbfc;\r\n}\r\n\r\n.coordinate-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: bold;\r\n  color: #606266;\r\n}\r\n\r\n/* 确保介绍视频和成本预估卡片高度一致 */\r\n.mini-block .el-card {\r\n  height: 100%;\r\n}\r\n\r\n.mini-block .el-card__body {\r\n  min-height: 300px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 介绍视频和成本预估并排时的高度统一 */\r\n.el-row .el-col .mini-block {\r\n  height: 100%;\r\n}\r\n\r\n.el-row .el-col .mini-block .el-card {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.el-row .el-col .mini-block .el-card__body {\r\n  flex: 1;\r\n  min-height: 300px;\r\n}\r\n</style> \r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAolBA,IAAAA,UAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAC,gBAAA;MACAP,IAAA,EAAAI,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAE,oBAAA;MACAR,IAAA,EAAAS,MAAA;MACAJ,OAAA;IACA;EACA;EACAK,KAAA;IACA,wBAAAC,WAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,iBAAA,MAAAd,IAAA,CAAAe,EAAA,OAAAX,QAAA;MACA;IACA;IACAJ,IAAA;MACAgB,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QAAA,IAAAC,KAAA;QACA,IAAAF,OAAA,IAAAA,OAAA,KAAAC,OAAA;UACA,KAAAE,SAAA;YACA;YACAD,KAAA,CAAAE,YAAA;;YAEA;YACA,IAAAJ,OAAA,CAAAK,gBAAA,IAAAL,OAAA,CAAAK,gBAAA,CAAAC,cAAA;cACA;cACAJ,KAAA,CAAAK,4BAAA;YACA,YAAAP,OAAA,CAAAK,gBAAA;cACA;cACAH,KAAA,CAAAM,kBAAA;YACA;YACA;YACAN,KAAA,CAAAO,aAAA;UACA;QACA;MACA;MACAC,SAAA;MACAC,IAAA;IACA;IACA;IACA;MACAZ,OAAA,WAAAA,QAAAa,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,MAAA,KAAAC,SAAA;UACA,KAAAP,4BAAA;QACA;MACA;MACAG,SAAA;MACAC,IAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;QACAC,SAAA;QACAC,QAAA;QACAC,cAAA;MACA;MACAC,WAAA;QACAH,SAAA;QACAC,QAAA;QACAC,cAAA;MACA;MACA;MACAE,sBAAA;MACA;MACAC,cAAA;MACAC,eAAA;MACA;MACAC,kBAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,aAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAtB,kBAAA;IACA,KAAAJ,YAAA;EACA;EACA2B,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,KAAAvB,aAAA;EACA;EACAwB,OAAA;IACAD,QAAA,WAAAA,SAAA;MACA;MACA,UAAAjD,IAAA,CAAAmC,QAAA;QACA,KAAAgB,IAAA,MAAAnD,IAAA;UACAF,IAAA;UACAsD,mBAAA;UACAC,mBAAA;UACAC,UAAA;QACA;MACA;QACA;QACA,IAAAC,YAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA1D,IAAA,CAAAmC,QAAA;QACA,KAAAgB,IAAA,MAAAnD,IAAA,cAAAuD,YAAA;;QAEA;QACA,SAAAvD,IAAA,CAAAmC,QAAA,CAAAkB,mBAAA;UACA,KAAArD,IAAA,CAAAmC,QAAA,CAAAkB,mBAAA,CAAAM,OAAA,WAAAC,QAAA;YACA,IAAAA,QAAA,CAAAC,oBAAA,KAAAD,QAAA,CAAAE,KAAA;cACAF,QAAA,CAAAE,KAAA,GAAAF,QAAA,CAAAC,oBAAA;YACA;YACA,IAAAD,QAAA,CAAAG,iBAAA,KAAAH,QAAA,CAAAI,MAAA;cACAJ,QAAA,CAAAI,MAAA,GAAAJ,QAAA,CAAAG,iBAAA;YACA;YACA,IAAAH,QAAA,CAAAK,GAAA,KAAAL,QAAA,CAAAlB,KAAA;cACAkB,QAAA,CAAAlB,KAAA,GAAAkB,QAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;;MAEA;MACA,UAAAjE,IAAA,CAAAkC,SAAA;QACA,KAAAiB,IAAA,MAAAnD,IAAA;UACAF,IAAA;UACAsD,mBAAA;UACAC,mBAAA;UACAC,UAAA;QACA;MACA;QACA;QACA,IAAAY,aAAA,GAAAV,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA1D,IAAA,CAAAkC,SAAA;QACA,KAAAiB,IAAA,MAAAnD,IAAA,eAAAkE,aAAA;MACA;IACA;IACA;IACAxC,aAAA,WAAAA,cAAA;MAAA,IAAAyC,MAAA;MACA;MACA,KAAAlC,SAAA;QACAC,SAAA;QACAC,QAAA;QACAC,cAAA;MACA;;MAEA;MACA,SAAApC,IAAA,CAAAkC,SAAA,SAAAlC,IAAA,CAAAkC,SAAA,CAAAmB,mBAAA;QACA,KAAArD,IAAA,CAAAkC,SAAA,CAAAmB,mBAAA,CAAAM,OAAA,WAAAC,QAAA,EAAAQ,GAAA;UACA;UACA,IAAAR,QAAA,CAAAI,MAAA,IAAAJ,QAAA,CAAAI,MAAA,CAAAK,IAAA;YACA,IAAAC,QAAA,GAAAV,QAAA,CAAAI,MAAA,CAAAO,KAAA,MAAAC,GAAA;YACAL,MAAA,CAAAhB,IAAA,CAAAgB,MAAA,CAAAlC,SAAA,CAAAC,SAAA,EAAAkC,GAAA;cACAtE,IAAA,EAAAwE,QAAA;cACAG,GAAA,EAAAb,QAAA,CAAAI,MAAA;cACAU,GAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAR;YACA;UACA;YACA;YACAD,MAAA,CAAAhB,IAAA,CAAAgB,MAAA,CAAAlC,SAAA,CAAAC,SAAA,EAAAkC,GAAA;UACA;QACA;MACA;;MAEA;MACA,SAAApE,IAAA,CAAAmC,QAAA,SAAAnC,IAAA,CAAAmC,QAAA,CAAAkB,mBAAA;QACA,KAAArD,IAAA,CAAAmC,QAAA,CAAAkB,mBAAA,CAAAM,OAAA,WAAAC,QAAA,EAAAQ,GAAA;UACA;UACA,IAAAR,QAAA,CAAAI,MAAA,IAAAJ,QAAA,CAAAI,MAAA,CAAAK,IAAA;YACA,IAAAC,QAAA,GAAAV,QAAA,CAAAI,MAAA,CAAAO,KAAA,MAAAC,GAAA;YACAL,MAAA,CAAAhB,IAAA,CAAAgB,MAAA,CAAAlC,SAAA,CAAAE,QAAA,EAAAiC,GAAA;cACAtE,IAAA,EAAAwE,QAAA;cACAG,GAAA,EAAAb,QAAA,CAAAI,MAAA;cACAU,GAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAR,GAAA;YACA;UACA;YACA;YACAD,MAAA,CAAAhB,IAAA,CAAAgB,MAAA,CAAAlC,SAAA,CAAAE,QAAA,EAAAiC,GAAA;UACA;QACA;MACA;IACA;IACA;IACA3C,kBAAA,WAAAA,mBAAA;MACA,UAAAzB,IAAA,CAAAsB,gBAAA;QACA,KAAA6B,IAAA,MAAAnD,IAAA;UACAe,EAAA;UACAd,IAAA;UACA4E,UAAA;UACA/C,MAAA;UACA+B,oBAAA;UACAE,iBAAA;QACA;MACA;MACA;MACA,KAAAvC,4BAAA;IACA;IACA;IACAH,YAAA,WAAAA,aAAA;MACA;MACA,SAAArB,IAAA,CAAA8E,QAAA,KAAA/C,SAAA,SAAA/B,IAAA,CAAA8E,QAAA,kBAAA9E,IAAA,CAAA8E,QAAA;QACA,KAAA3B,IAAA,MAAAnD,IAAA;MACA;MACA,SAAAA,IAAA,CAAA+E,eAAA,KAAAhD,SAAA,SAAA/B,IAAA,CAAA+E,eAAA,kBAAA/E,IAAA,CAAA+E,eAAA;QACA,KAAA5B,IAAA,MAAAnD,IAAA;MACA;MACA,SAAAA,IAAA,CAAAgF,kBAAA,KAAAjD,SAAA,SAAA/B,IAAA,CAAAgF,kBAAA,kBAAAhF,IAAA,CAAAgF,kBAAA;QACA,KAAA7B,IAAA,MAAAnD,IAAA;MACA;;MAEA;MACA,UAAAA,IAAA,CAAAkC,SAAA;QACA,KAAAiB,IAAA,MAAAnD,IAAA;UACAF,IAAA;UACAsD,mBAAA;UACAC,mBAAA;UACAC,UAAA;QACA;MACA;;MAEA;MACA,UAAAtD,IAAA,CAAAmC,QAAA;QACA,KAAAgB,IAAA,MAAAnD,IAAA;UACAF,IAAA;UACAsD,mBAAA;UACAC,mBAAA;UACAC,UAAA;QACA;MACA;QACA;QACA,SAAAtD,IAAA,CAAAmC,QAAA,CAAAkB,mBAAA;UACA,KAAArD,IAAA,CAAAmC,QAAA,CAAAkB,mBAAA,CAAAM,OAAA,WAAAC,QAAA;YACA;YACA,IAAAA,QAAA,CAAAC,oBAAA,KAAAD,QAAA,CAAAE,KAAA;cACAF,QAAA,CAAAE,KAAA,GAAAF,QAAA,CAAAC,oBAAA;YACA;YACA,IAAAD,QAAA,CAAAG,iBAAA,KAAAH,QAAA,CAAAI,MAAA;cACAJ,QAAA,CAAAI,MAAA,GAAAJ,QAAA,CAAAG,iBAAA;YACA;YACA,IAAAH,QAAA,CAAAK,GAAA,KAAAL,QAAA,CAAAlB,KAAA;cACAkB,QAAA,CAAAlB,KAAA,GAAAkB,QAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;;MAEA;MACA,UAAAjE,IAAA,CAAAiF,YAAA;QACA,KAAA9B,IAAA,MAAAnD,IAAA;UACA8B,MAAA;UACAoD,KAAA;UACAC,QAAA;QACA;MACA;;MAEA;MACA,SAAAnF,IAAA,CAAAiF,YAAA,SAAAjF,IAAA,CAAAiF,YAAA,CAAAnD,MAAA,KAAAC,SAAA;QACA,KAAAoB,IAAA,MAAAnD,IAAA,CAAAiF,YAAA;MACA;IACA;IACA;IACAG,cAAA,WAAAA,eAAAvE,GAAA;MACA,IAAAA,GAAA;QACA;QACA,IAAAwE,CAAA,QAAAC,MAAA;QACA,OAAAD,CAAA;UACA,IAAAA,CAAA,CAAAvD,MAAA,UAAAuD,CAAA,CAAAvD,MAAA;UACAuD,CAAA,GAAAA,CAAA,CAAAC,MAAA;QACA;MACA;QACA;QAAA,IACAC,aAAA,YAAAA,cAAAvF,IAAA;UACA,IAAAA,IAAA,CAAA4C,QAAA,IAAA5C,IAAA,CAAA4C,QAAA,CAAA4C,MAAA;YACAxF,IAAA,CAAA4C,QAAA,CAAAe,OAAA,WAAA8B,KAAA;cACAA,KAAA,CAAA3D,MAAA;cACAyD,aAAA,CAAAE,KAAA;YACA;UACA;QACA;QACAF,aAAA,MAAAvF,IAAA;MACA;IACA;IACA;IACAc,iBAAA,WAAAA,kBAAAC,EAAA,EAAA2E,IAAA;MACA,SAAAC,OAAAC,KAAA,EAAAN,MAAA;QAAA,IAAAO,SAAA,OAAAC,2BAAA,CAAAxF,OAAA,EACAsF,KAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAlG,IAAA,GAAA+F,KAAA,CAAApD,KAAA;YACA,IAAA3C,IAAA,CAAAe,EAAA,KAAAA,EAAA;cACA,IAAAuE,MAAA,EAAAA,MAAA,CAAAxD,MAAA;cACA;YACA;YACA,IAAA9B,IAAA,CAAA4C,QAAA,IAAA5C,IAAA,CAAA4C,QAAA,CAAA4C,MAAA;cACA,IAAAG,MAAA,CAAA3F,IAAA,CAAA4C,QAAA,EAAA5C,IAAA;gBACA,IAAAsF,MAAA,EAAAA,MAAA,CAAAxD,MAAA;gBACA;cACA;YACA;UACA;QAAA,SAAAqE,GAAA;UAAAN,SAAA,CAAAO,CAAA,CAAAD,GAAA;QAAA;UAAAN,SAAA,CAAAQ,CAAA;QAAA;QACA;MACA;MACAV,MAAA,CAAAD,IAAA;IACA;IACA;IACAY,qBAAA,WAAAA,sBAAArG,IAAA;MACA,UAAAD,IAAA,CAAAC,IAAA,EAAAoD,mBAAA;QACA,KAAAF,IAAA,MAAAnD,IAAA,CAAAC,IAAA;MACA;MAEA,IAAAsG,MAAA,QAAAvG,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAmC,MAAA;;MAEA;MACA,IAAAgB,WAAA;QACAzF,EAAA;QACAkD,GAAA;QACAnC,MAAA;QACA7B,IAAA;QACA4E,UAAA;QACAf,KAAA;QACAE,MAAA;QACAyC,WAAA;MACA;;MAEA;MACA,IAAAC,mBAAA,GAAAlD,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA8C,WAAA;MACA,KAAAxG,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAsD,IAAA,CAAAD,mBAAA;;MAEA;MACA,KAAAvD,IAAA,MAAAlB,SAAA,CAAAhC,IAAA,GAAAsG,MAAA;MACAK,OAAA,CAAAC,GAAA,aAAAH,mBAAA;IACA;IAEA;IACAI,wBAAA,WAAAA,yBAAA7G,IAAA,EAAAmE,GAAA;MAAA,IAAA2C,MAAA;MAAA,WAAAC,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAAC,SAAA;QAAA,IAAAvD,QAAA;QAAA,WAAAqD,oBAAA,CAAA3G,OAAA,IAAA8G,IAAA,UAAAC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;YAAA;cACA5D,QAAA,GAAAmD,MAAA,CAAA/G,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAe,GAAA;cAEA2C,MAAA,CAAAU,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACA1H,IAAA;cACA,GAAA2H,IAAA,kBAAAZ,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAAW,QAAA;gBAAA,IAAAC,GAAA;gBAAA,WAAAb,oBAAA,CAAA3G,OAAA,IAAA8G,IAAA,UAAAW,SAAAC,QAAA;kBAAA,kBAAAA,QAAA,CAAAT,IAAA,GAAAS,QAAA,CAAAR,IAAA;oBAAA;sBAAA,KAEA5D,QAAA,CAAA7C,EAAA;wBAAAiH,QAAA,CAAAR,IAAA;wBAAA;sBAAA;sBAAAQ,QAAA,CAAAT,IAAA;sBAEAR,MAAA,CAAAkB,MAAA,CAAAC,OAAA;sBAAAF,QAAA,CAAAR,IAAA;sBAAA,OACA,IAAAW,4BAAA;wBAAApH,EAAA,EAAA6C,QAAA,CAAA7C;sBAAA;oBAAA;sBAAA+G,GAAA,GAAAE,QAAA,CAAAI,IAAA;sBACA,IAAAN,GAAA,CAAAO,IAAA;wBACAtB,MAAA,CAAA/G,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAiF,MAAA,CAAAlE,GAAA;wBACA2C,MAAA,CAAAwB,QAAA,CAAAC,OAAA;sBACA;wBACAzB,MAAA,CAAAwB,QAAA,CAAAE,KAAA,CAAAX,GAAA,CAAAY,GAAA;sBACA;sBAAAV,QAAA,CAAAR,IAAA;sBAAA;oBAAA;sBAAAQ,QAAA,CAAAT,IAAA;sBAAAS,QAAA,CAAAW,EAAA,GAAAX,QAAA;sBAEAjB,MAAA,CAAAwB,QAAA,CAAAE,KAAA;oBAAA;sBAAAT,QAAA,CAAAT,IAAA;sBAEAR,MAAA,CAAAkB,MAAA,CAAAW,YAAA;sBAAA,OAAAZ,QAAA,CAAAa,MAAA;oBAAA;sBAAAb,QAAA,CAAAR,IAAA;sBAAA;oBAAA;sBAGA;sBACAT,MAAA,CAAA/G,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAiF,MAAA,CAAAlE,GAAA;sBACA2C,MAAA,CAAAwB,QAAA,CAAAC,OAAA;oBAAA;oBAAA;sBAAA,OAAAR,QAAA,CAAAc,IAAA;kBAAA;gBAAA,GAAAjB,OAAA;cAAA,CAEA,IAAAkB,KAAA;YAAA;YAAA;cAAA,OAAAzB,SAAA,CAAAwB,IAAA;UAAA;QAAA,GAAA3B,QAAA;MAAA;IACA;IAEA;IACA6B,aAAA,WAAAA,cAAA/I,IAAA,EAAAgJ,WAAA;MACA,IAAArF,QAAA,QAAA5D,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAA4F,WAAA;MACA,KAAArF,QAAA,CAAA6C,WAAA;QACA,KAAAtD,IAAA,CAAAS,QAAA;MACA;MACAA,QAAA,CAAA6C,WAAA,CAAAE,IAAA;QACA5F,EAAA;QACAmI,MAAA;QACAC,CAAA;QACAC,CAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;QACAC,SAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAzJ,IAAA,EAAAgJ,WAAA,EAAAU,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAA5C,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAA2C,SAAA;QAAA,IAAAC,KAAA;QAAA,WAAA7C,oBAAA,CAAA3G,OAAA,IAAA8G,IAAA,UAAA2C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzC,IAAA,GAAAyC,SAAA,CAAAxC,IAAA;YAAA;cACAsC,KAAA,GAAAF,MAAA,CAAA5J,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAA4F,WAAA,EAAAxC,WAAA,CAAAkD,QAAA;cAEAC,MAAA,CAAAnC,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACA1H,IAAA;cACA,GAAA2H,IAAA,kBAAAZ,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAA+C,SAAA;gBAAA,IAAAnC,GAAA;gBAAA,WAAAb,oBAAA,CAAA3G,OAAA,IAAA8G,IAAA,UAAA8C,UAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;oBAAA;sBAAA,KAEAsC,KAAA,CAAA/I,EAAA;wBAAAoJ,SAAA,CAAA3C,IAAA;wBAAA;sBAAA;sBAAA2C,SAAA,CAAA5C,IAAA;sBAEAqC,MAAA,CAAA3B,MAAA,CAAAC,OAAA;sBAAAiC,SAAA,CAAA3C,IAAA;sBAAA,OACA,IAAA4C,sBAAA;wBAAArJ,EAAA,EAAA+I,KAAA,CAAA/I;sBAAA;oBAAA;sBAAA+G,GAAA,GAAAqC,SAAA,CAAA/B,IAAA;sBACA,IAAAN,GAAA,CAAAO,IAAA;wBACAuB,MAAA,CAAA5J,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAA4F,WAAA,EAAAxC,WAAA,CAAA6B,MAAA,CAAAqB,QAAA;wBACAC,MAAA,CAAArB,QAAA,CAAAC,OAAA;sBACA;wBACAoB,MAAA,CAAArB,QAAA,CAAAE,KAAA,CAAAX,GAAA,CAAAY,GAAA;sBACA;sBAAAyB,SAAA,CAAA3C,IAAA;sBAAA;oBAAA;sBAAA2C,SAAA,CAAA5C,IAAA;sBAAA4C,SAAA,CAAAxB,EAAA,GAAAwB,SAAA;sBAEAP,MAAA,CAAArB,QAAA,CAAAE,KAAA;oBAAA;sBAAA0B,SAAA,CAAA5C,IAAA;sBAEAqC,MAAA,CAAA3B,MAAA,CAAAW,YAAA;sBAAA,OAAAuB,SAAA,CAAAtB,MAAA;oBAAA;sBAAAsB,SAAA,CAAA3C,IAAA;sBAAA;oBAAA;sBAGA;sBACAoC,MAAA,CAAA5J,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAA4F,WAAA,EAAAxC,WAAA,CAAA6B,MAAA,CAAAqB,QAAA;sBACAC,MAAA,CAAArB,QAAA,CAAAC,OAAA;oBAAA;oBAAA;sBAAA,OAAA2B,SAAA,CAAArB,IAAA;kBAAA;gBAAA,GAAAmB,QAAA;cAAA,CAEA,IAAAlB,KAAA;YAAA;YAAA;cAAA,OAAAiB,SAAA,CAAAlB,IAAA;UAAA;QAAA,GAAAe,QAAA;MAAA;IACA;IAEA;IACAQ,yBAAA,WAAAA,0BAAAxJ,GAAA,EAAAZ,IAAA,EAAAgJ,WAAA,EAAAU,QAAA;MACA,IAAAW,KAAA,QAAAC,aAAA,MAAA/J,gBAAA,EAAAK,GAAA;MACA,IAAAyJ,KAAA;QACA,IAAAR,KAAA,QAAA9J,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAA4F,WAAA,EAAAxC,WAAA,CAAAkD,QAAA;QACAG,KAAA,CAAAN,SAAA,GAAAc,KAAA,CAAAd,SAAA;QACA5C,OAAA,CAAAC,GAAA,WAAAyD,KAAA,kBAAAR,KAAA,CAAAN,SAAA;MACA;IACA;IAEA;IACAe,aAAA,WAAAA,cAAA7E,IAAA,EAAA3E,EAAA;MACA,KAAA2E,IAAA,KAAArF,KAAA,CAAAmK,OAAA,CAAA9E,IAAA;MAAA,IAAA+E,UAAA,OAAA3E,2BAAA,CAAAxF,OAAA,EAEAoF,IAAA;QAAAgF,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAzE,CAAA,MAAA0E,MAAA,GAAAD,UAAA,CAAAxE,CAAA,IAAAC,IAAA;UAAA,IAAAlG,IAAA,GAAA0K,MAAA,CAAA/H,KAAA;UACA,IAAA3C,IAAA,CAAAe,EAAA,KAAAA,EAAA;YACA,OAAAf,IAAA;UACA;UACA,IAAAA,IAAA,CAAA4C,QAAA,IAAA5C,IAAA,CAAA4C,QAAA,CAAA4C,MAAA;YACA,IAAAmF,KAAA,QAAAJ,aAAA,CAAAvK,IAAA,CAAA4C,QAAA,EAAA7B,EAAA;YACA,IAAA4J,KAAA,SAAAA,KAAA;UACA;QACA;MAAA,SAAAxE,GAAA;QAAAsE,UAAA,CAAArE,CAAA,CAAAD,GAAA;MAAA;QAAAsE,UAAA,CAAApE,CAAA;MAAA;MACA;IACA;IAEA;IACAuE,0BAAA,WAAAA,2BAAAnE,WAAA;MACA,KAAAA,WAAA,KAAApG,KAAA,CAAAmK,OAAA,CAAA/D,WAAA;QACA;UAAAoE,MAAA;UAAAC,MAAA;QAAA;MACA;MAEA,IAAAC,OAAA,GAAAtE,WAAA,CAAAuE,GAAA,WAAAlB,KAAA;QAAA,OAAAA,KAAA,CAAAX,CAAA;MAAA,GAAA8B,IAAA;MACA,IAAAC,OAAA,GAAAzE,WAAA,CAAAuE,GAAA,WAAAlB,KAAA;QAAA,OAAAA,KAAA,CAAAV,CAAA;MAAA,GAAA6B,IAAA;MAEA;QACAJ,MAAA,EAAAE,OAAA;QACAD,MAAA,EAAAI;MACA;IACA;IAEA;IACAC,0BAAA,WAAAA,2BAAAC,IAAA,EAAApL,IAAA,EAAAC,IAAA,EAAAoL,aAAA,EAAAC,gBAAA,EAAAC,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAAxE,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAAuE,SAAA;QAAA,IAAAC,QAAA,EAAA5D,GAAA,EAAA6D,QAAA;QAAA,WAAA1E,oBAAA,CAAA3G,OAAA,IAAA8G,IAAA,UAAAwE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtE,IAAA,GAAAsE,SAAA,CAAArE,IAAA;YAAA;cAAA,IACA4D,IAAA,CAAAnL,IAAA,CAAA6L,UAAA;gBAAAD,SAAA,CAAArE,IAAA;gBAAA;cAAA;cACAgE,MAAA,CAAAjD,QAAA,CAAAE,KAAA;cAAA,OAAAoD,SAAA,CAAAE,MAAA,WACA;YAAA;cAAAF,SAAA,CAAAtE,IAAA;cAIAiE,MAAA,CAAAvD,MAAA,CAAAC,OAAA;cACAwD,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,SAAAb,IAAA;cACAM,QAAA,CAAAO,MAAA,iBAAAT,MAAA,CAAA/K,oBAAA;cACAiL,QAAA,CAAAO,MAAA,cAAAjM,IAAA,CAAAqI,IAAA;cAAAwD,SAAA,CAAArE,IAAA;cAAA,OAEA,IAAA0E,0BAAA,EAAAR,QAAA;YAAA;cAAA5D,GAAA,GAAA+D,SAAA,CAAAzD,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAA9F,IAAA;gBACA;gBACA2J,QAAA,GAAA7D,GAAA,CAAA9F,IAAA,CAAAmK,OAAA,EAEA;gBACA,WAAAb,gBAAA,iBAAAC,cAAA;kBACA;kBACAC,MAAA,CAAArI,IAAA,CAAAnD,IAAA,CAAAC,IAAA,EAAAoL,aAAA,EAAAC,gBAAA,GAAAC,cAAA,EAAAI,QAAA;kBACA/E,OAAA,CAAAC,GAAA,kBAAA8E,QAAA;kBACA/E,OAAA,CAAAC,GAAA,gBAAA7G,IAAA,CAAAC,IAAA,EAAAoL,aAAA,EAAAC,gBAAA;kBACAE,MAAA,CAAAjD,QAAA,CAAAC,OAAA;gBACA;kBACA;kBACAgD,MAAA,CAAArI,IAAA,CAAAnD,IAAA,CAAAC,IAAA,GAAAoL,aAAA,EAAAM,QAAA;kBACAH,MAAA,CAAAjD,QAAA,CAAAC,OAAA;gBACA;cACA;gBACAgD,MAAA,CAAAjD,QAAA,CAAAE,KAAA,CAAAX,GAAA,CAAAY,GAAA;cACA;cAAAmD,SAAA,CAAArE,IAAA;cAAA;YAAA;cAAAqE,SAAA,CAAAtE,IAAA;cAAAsE,SAAA,CAAAlD,EAAA,GAAAkD,SAAA;cAEAjF,OAAA,CAAA6B,KAAA,UAAAoD,SAAA,CAAAlD,EAAA;cACA6C,MAAA,CAAAjD,QAAA,CAAAE,KAAA;YAAA;cAAAoD,SAAA,CAAAtE,IAAA;cAEAiE,MAAA,CAAAvD,MAAA,CAAAW,YAAA;cAAA,OAAAiD,SAAA,CAAAhD,MAAA;YAAA;cAAA,OAAAgD,SAAA,CAAAE,MAAA,WAEA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAA/C,IAAA;UAAA;QAAA,GAAA2C,QAAA;MAAA;IACA;IAEAW,2BAAA,WAAAA,4BAAAhB,IAAA,EAAApL,IAAA,EAAAC,IAAA,EAAAoL,aAAA,EAAAC,gBAAA,EAAAC,cAAA;MAAA,IAAAc,MAAA;MAAA,WAAArF,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAAoF,SAAA;QAAA,IAAAZ,QAAA,EAAA5D,GAAA,EAAAxD,QAAA;QAAA,WAAA2C,oBAAA,CAAA3G,OAAA,IAAA8G,IAAA,UAAAmF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjF,IAAA,GAAAiF,SAAA,CAAAhF,IAAA;YAAA;cAAAgF,SAAA,CAAAjF,IAAA;cAEA8E,MAAA,CAAApE,MAAA,CAAAC,OAAA;cACAwD,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,SAAAb,IAAA;cACAM,QAAA,CAAAO,MAAA,iBAAAI,MAAA,CAAA5L,oBAAA;cACAiL,QAAA,CAAAO,MAAA,cAAAjM,IAAA,CAAAqI,IAAA;cAAAmE,SAAA,CAAAhF,IAAA;cAAA,OAEA,IAAA0E,0BAAA,EAAAR,QAAA;YAAA;cAAA5D,GAAA,GAAA0E,SAAA,CAAApE,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAA9F,IAAA;gBACA;gBACA,WAAAsJ,gBAAA,iBAAAC,cAAA;kBACA;kBACAc,MAAA,CAAAlJ,IAAA,CAAAnD,IAAA,CAAAC,IAAA,EAAAoL,aAAA,EAAAC,gBAAA,GAAAC,cAAA,EAAAzD,GAAA,CAAA9F,IAAA,CAAAmK,OAAA;kBAEA7H,QAAA,GAAAwD,GAAA,CAAA9F,IAAA,CAAAmK,OAAA,CAAA5H,KAAA,MAAAC,GAAA;kBACA6H,MAAA,CAAAlJ,IAAA,CAAAkJ,MAAA,CAAApK,SAAA,CAAAhC,IAAA,GAAAqL,gBAAA;oBACAxL,IAAA,EAAAwE,QAAA;oBACAG,GAAA,EAAAqD,GAAA,CAAA9F,IAAA,CAAAmK,OAAA;oBACAzH,GAAA,EAAAC,IAAA,CAAAC,GAAA;kBACA;kBAEA,IAAAwG,IAAA,CAAAnL,IAAA,oBAAA6H,GAAA,CAAA9F,IAAA,CAAAyK,MAAA;oBACAJ,MAAA,CAAAlJ,IAAA,CAAAnD,IAAA,CAAAC,IAAA,EAAAoL,aAAA,EAAAC,gBAAA,YAAAxD,GAAA,CAAA9F,IAAA,CAAAyK,MAAA;oBACAJ,MAAA,CAAA9D,QAAA,CAAAC,OAAA;kBACA;oBACA6D,MAAA,CAAA9D,QAAA,CAAAC,OAAA;kBACA;gBACA;kBACA;kBACA6D,MAAA,CAAAlJ,IAAA,CAAAnD,IAAA,CAAAC,IAAA,GAAAoL,aAAA,EAAAvD,GAAA,CAAA9F,IAAA,CAAAmK,OAAA;;kBAEA;kBACA,IAAAlM,IAAA,2BAAAoL,aAAA;oBACA;oBACAgB,MAAA,CAAA7K,4BAAA;oBAEA,IAAA4J,IAAA,CAAAnL,IAAA,oBAAA6H,GAAA,CAAA9F,IAAA,CAAAyK,MAAA;sBACAJ,MAAA,CAAAlJ,IAAA,CAAAnD,IAAA,CAAAC,IAAA,2BAAA6H,GAAA,CAAA9F,IAAA,CAAAyK,MAAA;sBACAJ,MAAA,CAAA9D,QAAA,CAAAC,OAAA;oBACA;sBACA6D,MAAA,CAAA9D,QAAA,CAAAC,OAAA;oBACA;kBACA;oBACA6D,MAAA,CAAA9D,QAAA,CAAAC,OAAA;kBACA;gBACA;cACA;gBACA6D,MAAA,CAAA9D,QAAA,CAAAE,KAAA,CAAAX,GAAA,CAAAY,GAAA;cACA;cAAA8D,SAAA,CAAAhF,IAAA;cAAA;YAAA;cAAAgF,SAAA,CAAAjF,IAAA;cAAAiF,SAAA,CAAA7D,EAAA,GAAA6D,SAAA;cAEAH,MAAA,CAAA9D,QAAA,CAAAE,KAAA;YAAA;cAAA+D,SAAA,CAAAjF,IAAA;cAEA8E,MAAA,CAAApE,MAAA,CAAAW,YAAA;cAAA,OAAA4D,SAAA,CAAA3D,MAAA;YAAA;cAAA,OAAA2D,SAAA,CAAAT,MAAA,WAEA;YAAA;YAAA;cAAA,OAAAS,SAAA,CAAA1D,IAAA;UAAA;QAAA,GAAAwD,QAAA;MAAA;IACA;IACAI,YAAA,WAAAA,aAAAzM,IAAA;MACA,KAAAD,IAAA,CAAAC,IAAA,EAAAqD,UAAA,QAAAtD,IAAA,CAAAC,IAAA,EAAAqD,UAAA;MACA,KAAAtD,IAAA,CAAAC,IAAA,EAAAqD,UAAA,CAAAqD,IAAA;QAAAzB,KAAA;QAAAC,QAAA;QAAAwH,QAAA;MAAA;IACA;IACAC,eAAA,WAAAA,gBAAA3M,IAAA,EAAAmE,GAAA;MACA,KAAApE,IAAA,CAAAC,IAAA,EAAAqD,UAAA,CAAAgF,MAAA,CAAAlE,GAAA;IACA;IACAyI,cAAA,WAAAA,eAAA5M,IAAA,EAAAmE,GAAA;MACA,KAAApE,IAAA,CAAAC,IAAA,EAAAqD,UAAA,CAAAc,GAAA,EAAAe,QAAA,CAAAwB,IAAA;IACA;IACAmG,iBAAA,WAAAA,kBAAA7M,IAAA,EAAAmE,GAAA,EAAA2I,IAAA;MACA,KAAA/M,IAAA,CAAAC,IAAA,EAAAqD,UAAA,CAAAc,GAAA,EAAAe,QAAA,CAAAmD,MAAA,CAAAyE,IAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,KAAAhN,IAAA,CAAAiF,YAAA,CAAAE,QAAA,CAAAwB,IAAA;IACA;IACAsG,iBAAA,WAAAA,kBAAAF,IAAA;MACA,KAAA/M,IAAA,CAAAiF,YAAA,CAAAE,QAAA,CAAAmD,MAAA,CAAAyE,IAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAlN,IAAA,EAAAC,IAAA,EAAAmE,GAAA;MACA,IAAAR,QAAA,GAAA5D,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAe,GAAA;MACA,IAAAR,QAAA,IAAAA,QAAA,CAAAI,MAAA;QACA,IAAAM,QAAA,GAAAV,QAAA,CAAAI,MAAA,CAAAO,KAAA,MAAAC,GAAA;QACA;UACA1E,IAAA,EAAAwE,QAAA;UACAG,GAAA,EAAAb,QAAA,CAAAI,MAAA;UACAU,GAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAR;QACA;MACA;MACA;IACA;IACA;IACA+I,0BAAA,WAAAA,2BAAAnN,IAAA,EAAAC,IAAA,EAAAmE,GAAA;MACA,IAAAR,QAAA,GAAA5D,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAe,GAAA;MACAR,QAAA,CAAAI,MAAA;MACAJ,QAAA,CAAAE,KAAA;MACA,KAAAX,IAAA,MAAAlB,SAAA,CAAAhC,IAAA,GAAAmE,GAAA;MACA,KAAAmE,QAAA,CAAAC,OAAA;IACA;IACA;IACA4E,WAAA,WAAAA,YAAAnN,IAAA,EAAAmE,GAAA;MACA;MACA,IAAAR,QAAA,QAAA5D,IAAA,CAAAC,IAAA,UAAAD,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,SAAArD,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAe,GAAA;MAEA,KAAAR,QAAA,KAAAA,QAAA,CAAAI,MAAA,KAAAJ,QAAA,CAAAI,MAAA,CAAAK,IAAA;QACA;QACA;MACA;;MAEA;MACA,SAAApC,SAAA,CAAAhC,IAAA,UAAAgC,SAAA,CAAAhC,IAAA,EAAAmE,GAAA,UAAAnC,SAAA,CAAAhC,IAAA,EAAAmE,GAAA,EAAAoB,MAAA;QACA,IAAA6H,QAAA,QAAApL,SAAA,CAAAhC,IAAA,EAAAmE,GAAA;QACA;QACA,IAAAiJ,QAAA,IAAA5I,GAAA,KAAAb,QAAA,CAAAI,MAAA;UACA,OAAAqJ,QAAA;QACA;MACA;;MAEA;MACA,IAAAzJ,QAAA,CAAAI,MAAA,IAAAJ,QAAA,CAAAI,MAAA,CAAAK,IAAA;QACA,IAAAC,QAAA,GAAAV,QAAA,CAAAI,MAAA,CAAAO,KAAA,MAAAC,GAAA;QACA,IAAA8I,WAAA;UACAxN,IAAA,EAAAwE,QAAA;UACAG,GAAA,EAAAb,QAAA,CAAAI,MAAA;UACAU,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;QACA,KAAAzB,IAAA,MAAAlB,SAAA,CAAAhC,IAAA,GAAAmE,GAAA,EAAAkJ,WAAA;QACA,OAAAA,WAAA;MACA;MAEA;IACA;IACAC,aAAA,WAAAA,cAAAtN,IAAA,EAAAmE,GAAA;MACA,UAAA/B,WAAA,CAAApC,IAAA,EAAAmE,GAAA;QACA;QACA,IAAAR,QAAA,QAAA5D,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAe,GAAA;QACA,IAAAoJ,OAAA,GAAA5J,QAAA,IAAAA,QAAA,CAAAI,MAAA;QACA,KAAAb,IAAA,MAAAd,WAAA,CAAApC,IAAA,GAAAmE,GAAA,EAAAoJ,OAAA;MACA;MACA,YAAAnL,WAAA,CAAApC,IAAA,EAAAmE,GAAA;IACA;IACAqJ,aAAA,WAAAA,cAAAxN,IAAA,EAAAmE,GAAA,EAAAzB,KAAA;MACA,WAAAyB,GAAA;QACA;QACA,KAAAjB,IAAA,MAAAd,WAAA,EAAApC,IAAA,EAAAmE,GAAA;MACA;QACA;QACA,KAAAjB,IAAA,MAAAd,WAAA,CAAApC,IAAA,GAAAmE,GAAA,EAAAzB,KAAA;MACA;IACA;IACA+K,cAAA,WAAAA,eAAAzN,IAAA,EAAAmE,GAAA,EAAAzB,KAAA;MACA;MACA,KAAAQ,IAAA,MAAAlB,SAAA,CAAAhC,IAAA,GAAAmE,GAAA;;MAEA;MACA,IAAAzB,KAAA;QACA,IAAA2B,QAAA,GAAA3B,KAAA,CAAA4B,KAAA,MAAAC,GAAA;QACA,KAAArB,IAAA,MAAAlB,SAAA,CAAAhC,IAAA,GAAAmE,GAAA;UACAtE,IAAA,EAAAwE,QAAA;UACAG,GAAA,EAAA9B,KAAA;UACA+B,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACApD,4BAAA,WAAAA,6BAAA;MACA,SAAAxB,IAAA,CAAAsB,gBAAA,SAAAtB,IAAA,CAAAsB,gBAAA,CAAAyC,iBAAA;QACA,IAAAO,QAAA,QAAAtE,IAAA,CAAAsB,gBAAA,CAAAyC,iBAAA,CAAAQ,KAAA,MAAAC,GAAA;QACA,KAAAlC,sBAAA;UACAxC,IAAA,EAAAwE,QAAA;UACAG,GAAA,OAAAzE,IAAA,CAAAsB,gBAAA,CAAAyC,iBAAA;UACAW,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAAtC,sBAAA;MACA;IACA;IACAqL,yBAAA,WAAAA,0BAAA;MACA,YAAArL,sBAAA;IACA;IACAsL,0BAAA,WAAAA,2BAAA;MACA,SAAA5N,IAAA,CAAAsB,gBAAA;QACA,KAAAtB,IAAA,CAAAsB,gBAAA,CAAAyC,iBAAA;QACA,KAAA/D,IAAA,CAAAsB,gBAAA,CAAAuC,oBAAA;QACA,KAAA0E,QAAA,CAAAC,OAAA;QACA;QACA,KAAAhH,4BAAA;MACA;IACA;IACA;IACAqM,4BAAA,WAAAA,6BAAAhN,GAAA;MACA;IAAA,CACA;IAEA;IACAiN,YAAA,WAAAA,aAAArJ,GAAA;MACA,IAAAA,GAAA;QACA,KAAAjC,eAAA,GAAAiC,GAAA;QACA,KAAAlC,cAAA;MACA;IACA;IAEA;IACAwL,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA,KAAAvG,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA1H,IAAA;MACA,GAAA2H,IAAA;QACAoG,MAAA,CAAAhO,IAAA,CAAAsB,gBAAA,CAAAuC,oBAAA;QACAmK,MAAA,CAAAzF,QAAA,CAAAC,OAAA;MACA,GAAAO,KAAA;IACA;IAEA;IACAkF,mBAAA,WAAAA,oBAAAhO,IAAA,EAAAmE,GAAA;MAAA,IAAA8J,MAAA;MACA,KAAAzG,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA1H,IAAA;MACA,GAAA2H,IAAA;QACAsG,MAAA,CAAAlO,IAAA,CAAAC,IAAA,EAAAoD,mBAAA,CAAAe,GAAA,EAAAN,KAAA;QACAoK,MAAA,CAAA3F,QAAA,CAAAC,OAAA;MACA,GAAAO,KAAA;IACA;IACA;IACAoF,cAAA,WAAAA,eAAAxL,KAAA,EAAA1C,IAAA;MACA;MACA,IAAAmO,aAAA,GAAAzL,KAAA,CAAA0L,OAAA;MACA,KAAArO,IAAA,CAAAC,IAAA,EAAAmD,mBAAA,GAAAgL,aAAA;IACA;IACAE,4BAAA,WAAAA,6BAAA3L,KAAA;MACA,KAAAL,sBAAA;MACA,IAAAK,KAAA;QACA,IAAA2B,QAAA,GAAA3B,KAAA,CAAA4B,KAAA,MAAAC,GAAA;QACA,KAAAlC,sBAAA;UACAxC,IAAA,EAAAwE,QAAA;UACAG,GAAA,EAAA9B,KAAA;UACA+B,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;MACA,KAAApD,4BAAA;IACA;IACA;IACA+M,sBAAA,WAAAA,uBAAA1N,GAAA,EAAAiJ,KAAA,EAAA0E,KAAA;MACA;MACA,IAAA3N,GAAA,aAAAA,GAAA,KAAAkB,SAAA;QACA+H,KAAA,CAAA0E,KAAA;MACA;QACA1E,KAAA,CAAA0E,KAAA,IAAA3N,GAAA;MACA;IACA;EACA;EACA4N,QAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAA1O,IAAA,SAAAA,IAAA,CAAA4C,QAAA,SAAA5C,IAAA,CAAA4C,QAAA,CAAA4C,MAAA;IACA;EACA;EACAmJ,UAAA;IACAC,eAAA;EACA;AACA", "ignoreList": []}]}